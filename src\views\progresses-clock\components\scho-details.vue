<template>
  <common-dialog
    width="800px"
    :title="title"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <ul v-loading="tableLoading">
        <li v-for="(value, key) in titleObj" :key="key" class="real-li">
          <div class="real-li-title">{{ value }}</div>
          <div v-if="key === 'actDesc' && !tableLoading " class="real-li-value">
            <tinymce ref="desc" v-model="tableData[key]" :readonly="true" :height="300" />
          </div>
          <div v-else-if="key === 'annexList'">
            <el-image
              v-for="item in tableData[key]"
              :key="item.actId"
              style="width: 100px; height: 100px"
              :src="baseUrl+ item.annexUrl"
              fit="cover"
            />
          </div>
          <div v-else-if="key === 'actBanner'">
            <el-image
              style="width: 100px; height: 100px"
              :src="baseUrl+tableData[key]"
              fit="cover"
            />
          </div>
          <div v-else class="real-li-value">{{ tableData[key] }}</div>
        </li>
      </ul>
    </div>
  </common-dialog>
</template>

<script>
import { getTextFromDict } from '@/utils';
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      show: false,
      baseUrl: ossUri,
      tableLoading: false,
      titleObj: {
        actName: '活动名称：',
        actStart: '活动开始时间：',
        actEnd: '活动结束时间：',
        scholarship: '优惠类型：',
        actGroup: '活动分组：',
        actType: '活动类型：',
        actStatus: '活动状态：',
        actUrl: '活动地址：',
        // actBanner: '活动banner：',
        funcName: '营销服务：',
        // annexList: '活动附件：',
        actDesc: '活动描述：'
      },
      tableData:
        {
          /* 模拟数据 */
          // actName: '张学友',
          // actStart: '2021-12-15 14:20:32',
          // actEnd: '进行中',
          // learnCount: '19天',
          // lackCardCount: '2次',
          // patchCardCount: '5次',
          // runningStatus: '进行中',
          // runningCount: '20次',
          // remainRunningCount: '9天',
          // remain1: 'hhhhh',
          // remain2: '活动附件',
          // actDesc: '活动描述'
        }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    open() {
      this.tableLoading = true;
      const { scholarship } = this.row;
      this.$http({
        method: 'get',
        url: '/mktAct/toViewData',
        params: { scholarship }
      })
        .then(res => {
          const { fail, body } = res;
          if (!fail && !!body) {
            body.actGroup = getTextFromDict(body.actGroup, 'actGroup');
            body.actType = getTextFromDict(body.actType, 'actType');
            body.scholarship = body.actName;
            body.actStatus = Number(body.actStatus) === 1 ? '启用' : '禁用';
            this.tableData = body;
          }
          this.tableLoading = false;
        });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.real-li {
  display: flex;
  line-height: 36px;
  .real-li-title {
    flex: none;
    min-width: 120px;
    text-align: right;
  }
  .real-li-value {
    flex: 4;
    text-align: left;
    color: #303133;
  }
}
</style>
