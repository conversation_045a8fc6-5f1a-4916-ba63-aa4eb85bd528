<template>
  <common-dialog
    :show-footer="true"
    is-full
    title="选择老师"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='姓名' prop='empName'>
          <el-input v-model="form.empName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item width="200" label='部门' prop='dpId'>

          <SearchSelect
            v-model="form.dpId"
            clearable
            :options="selectDepartList"
            :props="{label:'dpName',value:'dpId'}"
            @loadmore='loadmore'
            @search="searchSelect"
            @select="select"
            @open="openSelect"
          />
          <!-- <remote-search-selects
            ref="remoteSearch"
            v-model="form.dpName"
            :props="{apiName: 'selectDepartList', value: 'dpId', label: 'dpName', query: 'sName'}"
            :param="{sName:''}"
          /> -->
        </el-form-item>
        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>

      <!-- 表格 -->
      <el-table
        ref="multipleTable"
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column width="55" align="center">
          <template slot="header" slot-scope="scope">
            <el-checkbox v-model="allCheckState" @change="allCheck(scope)" />
          </template>
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.checked" @change="handleChange(scope.row)" />
          </template>
        </el-table-column>
        <el-table-column prop="empName" label="助学老师" align="center" />
        <el-table-column prop="dpName" label="所在部门" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    childTeachRow: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      selectDepartList: [],
      allCheckState: false,
      selects: [],
      page: 1,
      limit: 10,
      total: 0,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      form: {
        empName: '',
        dpId: '',
        mobile: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 全选
    allCheck() {
      this.allCheckState = !this.allCheckState;
      if (!this.allCheckState) {
        this.selects = this.tableData;
      } else {
        this.selects = [];
      }
      this.tableData.forEach(item => {
        item.checked = !this.allCheckState;
      });
      this.allCheckState = !this.allCheckState;
    },
    // 单选
    handleChange(row) {
      if (row.checked) {
        this.selects.push(row);
      } else {
        this.selects.find((item, index) => {
          if (item.userId === row.userId) {
            this.selects.splice(index, 1);
            return true;
          }
        });
      }
    },
    getTableList() {
      this.init();
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit

      };

      this.$post('getTeacherList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          body.data.forEach(item => {
            item.checked = false;
          });
          this.tableData = body.data;
          this.pagination.total = Number(body.recordsTotal);

          if (this.selects.length !== 0) {
            this.selects.map((childItem, i) => {
              this.tableData.map((tableItem, j) => {
                if (this.selects[i].userId === this.tableData[j].userId) {
                  tableItem.checked = true;
                }
              });
            });
          }
        }
      });
    },
    open() {
      this.getTableList();
    },
    init() {
      this.selects = this.childTeachRow;
    },
    submit() {
      this.$emit('selectTeach', this.selects);
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    close() {
      this.$emit('selectTeach', this.selects);
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // SearchSelect 封装方法
    loadmore() {
      if (this.selectDepartList.length === this.total) {
        return;
      }
      this.page += 1;
      this.getSelectList();
    },
    // 搜索
    async searchSelect(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.page = 1;
        this.selectDepartList = [];
        this.getSelectList(value);
      }, 500);
    },
    openSelect() {
      this.page = 1;
      this.selectDepartList = [];
      this.getSelectList();
    },
    // 选中部门
    select(value) {
      if (value === undefined) { return; }
    },
    getSelectList(keyword = '') {
      const data = {
        page: (this.page - 1) * this.limit,
        length: this.limit
      };
      data['sName'] = keyword;

      this.$post('selectDepartList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.selectDepartList = this.selectDepartList.concat(body.data);
          this.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
