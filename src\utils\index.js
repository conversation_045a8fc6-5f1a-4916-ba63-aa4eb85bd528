import moment from 'moment';
import { ossUri } from '@/config/request';
import { getScholarship, api } from '@/api';
import { validate } from './validate';
import downFile from '@/utils/downFile';

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/');
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value ]; }
    return value.toString().padStart(2, '0');
  });
  return time_str;
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split('&');
  searchArr.forEach(v => {
    const index = v.indexOf('=');
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

// 获取url后面的参数
export function getUrlParams() {
  let str = window.location.href; // 取得整个地址栏
  let num = str.indexOf('?');
  str = str.substr(num + 1); // 取得所有参数 stringlet.substr(start [, length ]
  const arr = str.split('&'); // 各个参数放到数组里
  const json = {};
  for (let i = 0; i < arr.length; i += 1) {
    num = arr[i].indexOf('=');
    if (num > 0) {
      const name = arr[i].substring(0, num);
      const value = arr[i].substr(num + 1);
      json[name] = value;
    }
  }
  return json;
}

/**
 * 在头部插入带时间戳的字典和地址数据
 */
export function insertDictAndPcd() {
  return new Promise((resolve) => {
    const dictScript = document.getElementById('dict-script');
    const src = dictScript.getAttribute('src');
    dictScript.setAttribute('src', `${src}?v=${new Date().getTime()}`);
    resolve();
  });
}

/**
 * 获取省市区/广东省的地区数据
 * @param {Number} type 全部数据还是广东省数据 0是全部 1是广东
 */
export function getCityOptions(type) {
  // 级联选择器原始数据拷贝
  const pcdDatas = JSON.parse(JSON.stringify(pcdJson));
  const pcdJdDatas = JSON.parse(JSON.stringify(pcdJdJson));
  const datas = type === 1 ? pcdJdDatas : pcdDatas;
  // 省级
  for (const prv of datas) {
    prv.label = prv.provinceName;
    prv.value = prv.provinceCode;
    prv.children = prv.city;
    if (prv.city && prv.city.length > 0) {
      for (const c of prv.city) {
        c.label = c.cityName;
        c.value = c.cityCode;
        c.children = c.district;
        if (c.district && c.district.length > 0) {
          for (const area of c.district) {
            area.label = area.districtName;
            area.value = area.districtCode;
          }
        }
      }
    }
  }
  return datas;
}

/**
 * 从字典获取文案
 * @param {String} key 字典对应的数据键值
 * @param {String} val 需要获取的数据的健
 */
export function getTextFromDict(val, key) {
  // const { dictJson } = window.parent;
  const { dictJson } = window;
  if (dictJson[key]) {
    const current = dictJson[key].find((v) => v.dictValue === val);
    return current ? current.dictName : '';
  }
  return '';
}

/**
 * 根据code从省市区字典获取文案
 * @param {String} provinceCode 省级code
 * @param {String} cityCode 城市code
 * @param {String} districtCode 区级code
 * @param {String} dataType 数据类型 0是全部 1是广东
 * @param {Array} codeArray [provinceCode, cityCode, districtCode]; 请严格按照这个格式传入
 */
export function getAddressTextbyDict(codeArray, dataType = 0) {
  // provinceCode, cityCode, districtCode
  if (!codeArray.length) {
    console.log('传入的数据不是数组');
    return '';
  }
  const data = [];
  const pcdDatas = JSON.parse(JSON.stringify(getCityOptions(dataType)));
  const loop = (list, val) => {
    list.forEach((item) => {
      if (item.value === val) {
        data.push(item.label);
      } else {
        if (item.children && item.children.length > 0) {
          loop(item.children, val);
        }
      }
    });
  };

  for (const val of codeArray) {
    if (val && codeArray.length < 4) {
      loop(pcdDatas, val);
    }
  }

  return data;
}

/**
 * 把一维数组转换成树结构数据
 * @param {Array} data 需要转换的一维地址数组
 */
export function transformDataToTree(data) {
  data.forEach((item) => {
    item.key = item.id;
    item.title = item.name;
    if (!item.children) {
      item.children = [];
    }
  });
  const province = data.filter((item) => item.flag.toUpperCase() === 'P');
  const city = data.filter((item) => item.flag.toUpperCase() === 'C');
  const area = data.filter((item) => item.flag.toUpperCase() === 'T');
  province.forEach((p) => {
    city.forEach((c) => {
      if (p.id === c.pId) {
        p.children.push(c);
      }
      area.forEach((a) => {
        if (c.id === a.pId) {
          c.children.push(a);
        }
      });
    });
  });
  return JSON.parse(JSON.stringify(province));
}

/**
 * 通过身份证获取生日性别
 * @param {String} idCard 身份证号码
 */
export function getMsgFromIdCard(idCard) {
  // const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!validate('idCard', idCard)) {
    return false;
  }
  const sex = parseInt(idCard.substr(16, 1), 10) % 2;

  return {
    birthday: `${idCard.substring(6, 10)}-${idCard.substring(10, 12)}-${idCard.substring(12, 14)}`,
    sex: sex === 1 ? '1' : '2'
  };
}

/**
 * 拼接oss服务器上的图片链接
 * @param {String} relativeUrl 相对地址
 */
export function splitOssImgUrl(relativeUrl) {
  return ossUri + relativeUrl;
}

/**
 * 格式化时间戳
 * @param {Number} val 时间戳
 * @param {String} format 格式化的方法
 */
export function formatTimeStamp(val, format = 'YYYY-MM-DD hh:mm:ss') {
  if (typeof val !== 'number') {
    return val;
  }
  return moment(val).format(format);
}

// 获取优惠类型
export async function getScholarshipList() {
  const { fail, body } = await getScholarship();
  if (!fail) {
    window.parent.dictJson.scholarship = body.filter((item) => item.dictValue !== '68');
  }
}

// 去重 -- 根据数组中某个对象的属性对该数组去重
export function unique(arr, u_key) {
  const map = new Map();
  arr.forEach((item, index) => {
    if (!map.has(item[u_key])) {
      map.set(item[u_key], item);
    }
  });
  return [...map.values()];
}

/**
 * 转换时间为天时分秒
 * @param {Number} secondTime 时间(秒)
 * @param {NUmber} type 类型，0是返回字符串，1是返回日时分秒数据
 */
export function transformSecond(secondTime, type = 0) {
  if (typeof secondTime !== 'number') {
    return '';
  }
  let time = `${secondTime}秒`;
  let day = 0;
  let hour = 0;
  let min = 0;
  let second = 0;
  if (secondTime > 60) {
    second = secondTime % 60;
    min = parseInt(secondTime / 60, 10);
    time = `${min}分${second}秒`;
    if (min >= 60) {
      min = parseInt((secondTime / 60) % 60, 10);
      hour = parseInt(secondTime / 3600, 10);
      time = `${hour}小时${min}分${second}秒`;
      if (hour > 24) {
        hour = parseInt(secondTime / 3600, 10) % 24;
        day = parseInt(secondTime / 3600 / 24, 10);
        time = `${day}天${hour}小时${min}分${second}秒`;
      }
    }
  }
  return type === 0 ? time : { day, hour, min, second };
}

/**
 * 补零
 */
export const zeroFill = (_nms, _max = 10) => {
  return _nms < _max ? `0${_nms}` : _nms;
};

/**
 * 获取当前日期前后三天的值（一周）
 */
export function getCutDay(time = '', type = 'YMDHMS', nums = 0) {
  const today = time ? new Date(time) : new Date();
  const targetday = today.getTime() + 1000 * 60 * 60 * 24 * nums;
  today.setTime(targetday);
  // 年月日
  const tYear = today.getFullYear();
  const tMonth = zeroFill(today.getMonth() + 1);
  const tDate = zeroFill(today.getDate());
  // 时分秒
  const tHours = zeroFill(today.getHours());
  const tMinutes = zeroFill(today.getMinutes());
  const tSeconds = zeroFill(today.getSeconds());
  // 数值拼接
  let str = '';
  switch (type) {
    case 'YMD':
      str = `${tYear}-${tMonth}-${tDate}`;
      break;
    case 'CYMD':
      str = `${tYear}年${tMonth}月${tDate}日`;
      break;
    case 'HMS':
      str = `${tHours}:${tMinutes}:${tSeconds}`;
      break;
    case 'YMDHMS':
      str = `${tYear}-${tMonth}-${tDate} ${tHours}:${tMinutes}:${tSeconds}`;
      break;
    case 'CYMDHMS':
      str = `${tYear}年${tMonth}月${tDate}日 ${tHours}时${tMinutes}分${tSeconds}秒`;
      break;
    default:
      break;
  }
  return str;
}

/**
 * 导出Excel
 * @param {*} options 配置
 * 示例：
 * generateExcel({
    header: ['员工id', '标题'], // 导出数据的表头
    data: [{id:"1", title:"标题"}], // 导出的具体数据
    filterVal: ['id', 'title'],
    filename: '会员卡数据'
   });
 */
export function generateExcel(options) {
  const config = {
    header: [], // 导出数据的表头
    data: [], // 导出的具体数据
    filterVal: [], // 导出的数据进行一个过滤
    filename: 'excel-list', // 导出文件名
    autoWidth: true, // 单元格是否要自适应宽度 Boolean 可选：true / false
    bookType: 'xlsx' // 导出文件类型 String  可选：xlsx, csv, txt, more
  };

  if (options) {
    for (const key in config) {
      if (options[key]) {
        config[key] = options[key];
      }
    }
  }

  import('@/vendor/Export2Excel').then(excel => {
    const list = config.data;
    const data = formatJson(config.filterVal, list);
    excel.export_json_to_excel({
      header: config.header,
      data,
      filename: config.filename, // 文件名
      autoWidth: config.autoWidth,
      bookType: config.bookType
    });
  });
}

export function formatJson(filterVal, jsonData) {
  return jsonData.map(v => filterVal.map(j => {
    if (j === 'timestamp') {
      return parseTime(v[j]);
    } else {
      return v[j];
    }
  }));
}

// 处理element控件
export function handleDateControl(arr) {
  if (Object.prototype.toString.call(arr) !== '[object Array]') {
    return ['', ''];
  }
  return arr;
}

// 字符串分割成数组
export function splitChar(str, Joiner = ',') {
  if (Object.prototype.toString.call(str) === '[object String]') {
    return str.split(Joiner);
  } else {
    str = '';
    return str.split();
  }
}

export function uuid() {
  const s = [];
  const hexDigits = '0123456789abcdef';
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-';

  const uuid = s.join('');
  return uuid;
}

export function SplicingParams(obj) {
  if (Object.prototype.toString.call(obj) === '[object Object]') {
    let str = '';
    for (const key in obj) {
      str += key + '=' + obj[key] + '&';
    }
    str = str.slice(0, str.length - 1);
    return str;
  }
}

// 导出
export function exportExcel(key, params) {
  const url = `${api[key]}?${SplicingParams(params)}`;
  // window.open(url, '_blank');
  downFile(url, `${key}.xlsx`);
}

export function jsonParse(str) {
  try {
    if (typeof JSON.parse(str) === 'object') {
      return JSON.parse(str);
    }
  } catch (e) {
    return {};
  }
  return {};
}

/**
 * 分割字符串
 * @param {(string|number)} value 需要分割value
 * @param { number } length 分割长度
 * @returns {number | null}
 */
export function sliceString(value, length) {
  if (!length) return null;

  let val = String(value);
  console.log(val);
  if (val.length > length) {
    val = val.slice(0, length);
  }
  return Number(val);
}

/**
 * 将数组转为枚举对象
 * @param { Array } arr 数组
 * @param { String } name 枚举对象输出的键名
 * @param { String } value 枚举对象输出的键值
 * @returns { Object } 返回一个枚举对象
 * eg: [{ name: '微信渠道', value: 1 }] => { 1: '微信渠道' }
 */
export function arrToEnum(arr, name = 'value', value = 'name') {
  return Object.fromEntries(arr.map((item) => [item[name], item[value]]));
}

/**
 * 深拷贝对象或数组
 * @param {*} source 需要进行深拷贝的源数据
 * @returns {*} 返回深拷贝后的数据
 * @example
 * const obj = { a: 1, b: { c: 2 } };
 * const copyObj = deepClone(obj);
 */
export function deepClone(source) {
  // 处理null或undefined
  if (source === null || typeof source !== 'object') {
    return source;
  }

  // 处理日期对象
  if (source instanceof Date) {
    return new Date(source);
  }

  // 处理正则对象
  if (source instanceof RegExp) {
    return new RegExp(source);
  }

  // 根据源数据类型创建空数组或对象
  const target = Array.isArray(source) ? [] : {};

  // 递归复制所有属性
  for (const key in source) {
    if (Object.prototype.hasOwnProperty.call(source, key)) {
      target[key] = deepClone(source[key]);
    }
  }

  return target;
}

