<template>
  <!-- 双选择框组件：实现两个选择框的联动效果 -->
  <div class="double-select">
    <!-- 第一个选择框 -->
    <common-select
      v-model="localValue1"
      :type="type1"
      :placeholder="placeholder1"
      @change="handleChange1"
      :extraParams="extraParams1"
    />
    <!-- 第二个选择框（依赖于第一个选择框的值） -->
    <common-select
      v-model="localValue2"
      :type="type2"
      :placeholder="getPlaceholder2"
      @change="handleChange2"
      :disabled="!localValue1"
      :extraParams="getExtraParams2"
    />
  </div>
</template>

<script>
export default {
  name: "DoubleSelect",
  components: {
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  props: {
    // 第一个选择框的值，支持sync修饰符双向绑定
    value1: {
      type: [String, Number],
      default: "",
    },
    // 第二个选择框的值，支持sync修饰符双向绑定
    value2: {
      type: [String, Number],
      default: "",
    },
    // 第一个选择框的类型，对应CommonSelect的type属性
    type1: {
      type: String,
      default: "isOrNot_2",
    },
    // 第二个选择框的类型，对应CommonSelect的type属性
    type2: {
      type: String,
      default: "isOrNot_2",
    },
    // 第一个选择框的占位文本
    placeholder1: {
      type: String,
      default: "请选择",
    },
    // 第二个选择框有值时的占位文本
    placeholder2: {
      type: String,
      default: "请选择",
    },
    // 第二个选择框在第一个为空时的占位文本
    placeholder2WhenEmpty: {
      type: String,
      default: "请先选择一级分类",
    },
    // 联动参数的键名，用于将第一个选择框的值作为参数传递给第二个选择框
    paramKey: {
      type: String,
      default: "parentId",
    },
    // 额外的参数，会传递给第一个选择框
    extraParams1: {
      type: Object,
      default: () => ({}),
    },
    // 额外的参数，会与联动参数合并传递给第二个选择框
    extraParams2: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      // 本地状态，用于v-model绑定
      localValue1: this.value1,
      localValue2: this.value2,
    };
  },
  computed: {
    /**
     * 获取第二个选择框的占位文本
     * 当第一个选择框有值时，显示placeholder2
     * 当第一个选择框无值时，显示placeholder2WhenEmpty
     */
    getPlaceholder2() {
      return this.localValue1 ? this.placeholder2 : this.placeholder2WhenEmpty;
    },
    /**
     * 获取传递给第二个选择框的参数
     * 合并extraParams和联动参数
     */
    getExtraParams2() {
      const params = { ...this.extraParams2 };
      if (this.localValue1) {
        params[this.paramKey] = this.localValue1;
      }
      return params;
    },
  },
  watch: {
    // 监听props变化，更新本地状态
    value1(newVal) {
      this.localValue1 = newVal;
    },
    value2(newVal) {
      this.localValue2 = newVal;
    },
  },
  methods: {
    /**
     * 处理第一个选择框值变化
     * 同步更新父组件绑定的值，并清空第二个选择框的值
     * @param {String|Number} val - 选中的值
     */
    handleChange1(val, item) {
      this.$emit("update:value1", val, item);
      // 当第一个值变化时，清空第二个值
      if (this.localValue2) {
        this.localValue2 = "";
        this.$emit("update:value2", "");
      }
    },
    /**
     * 处理第二个选择框值变化
     * 同步更新父组件绑定的值
     * @param {String|Number} val - 选中的值
     */
    handleChange2(val) {
      this.$emit("update:value2", val);
    },
  },
};
</script>

<style lang="scss" scoped>
/* 双选择框样式 */
.double-select {
  width: 100%;
  display: flex;
  align-items: center;
  /* 选择框样式 */
  .el-select,
  .common-select {
    width: 48%;
    /* 第一个选择框右边距 */
    &:first-child {
      margin-right: 4%;
    }
  }
}
</style>
