<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='分销人' prop='distributionName'>
        <el-input v-model="form.distributionName" placeholder="请输入分销人姓名" />
      </el-form-item>
      <el-form-item label='手机号' prop='distributionMobile'>
        <el-input v-model="form.distributionMobile" placeholder="请输入手机号" />
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain @click="exportData()">佣金结算导出</el-button>
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      :default-sort="{ prop: 'divideAmount', order: 'descending' }"
      @sort-change="sortChange"
    >
      <el-table-column prop="empName" label="分销人" align="center" />
      <el-table-column prop="campusName" label="校区" align="center" />
      <el-table-column prop="dpName" label="部门" align="center" />
      <el-table-column prop="distributionOrderNum" label="分销订单" align="center" sortable="custom" />
      <el-table-column prop="distributionRefundOrderNum" label="退费订单" align="center" sortable="custom" />
      <el-table-column prop="distributionAmount" label="分销金额" align="center" sortable="custom" />
      <el-table-column prop="divideAmount" label="累计佣金" align="center" sortable="custom">
        <template slot-scope="scope">
          <el-button type="text" @click="handleKickback(scope.row)">{{ scope.row.divideAmount }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 弹框区 -->
    <order-detail-dialog :visible.sync="orderDetailVisible" :empId="empId" />
    <export-dialog :visible.sync="markVisible" />
  </div>
</template>
<script>
import orderDetailDialog from './order-detail-dialog';
import exportDialog from './export-dialog';

export default {
  components: {
    orderDetailDialog,
    exportDialog
  },

  props: {
    orderParams: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      orderDetailVisible: false,
      markVisible: false,
      form: {
        distributionName: '',
        distributionMobile: '',
        orderBy: 'divideAmount',
        sort: 'desc' // 升序，asc, 降序 desc
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      empId: ''
    };
  },
  watch: {
    orderParams(val) {
      this.getTableList();
    },
    immediate: true
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('performanceList', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    exportData() {
      this.markVisible = true;
    },
    handleKickback(row) {
      const { empId } = row;
      this.empId = empId;
      this.orderDetailVisible = true;
    },
    sortChange(obj) {
      const { order, prop } = obj;
      switch (order) {
        case 'descending':
          this.form.orderBy = prop;
          this.form.sort = 'desc';
          break;
        case 'ascending':
          this.form.orderBy = prop;
          this.form.sort = 'asc';
          break;
        default:
          this.form.orderBy = '';
          this.form.sort = '';
          break;
      }
      this.getTableList();
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
.settle-txt{
  padding-bottom: 10px;
  color: red;
}
</style>
