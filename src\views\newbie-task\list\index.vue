<template>
  <div class="yz-base-container">
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      @submit.native.prevent='search'
    >
      <el-form-item label="任务id" prop="id">
        <el-input v-model="form.id" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="任务模式" prop="taskMode">
        <el-select v-model="form.taskMode" clearable>
          <el-option v-for="item in taskMode" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务类型" prop="taskTypeConfigId">
        <el-select v-model="form.taskTypeConfigId" clearable>
          <el-option v-for="item in taskTypeList" :key="item.value" :label="item.taskName" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务标题" prop="taskTitle">
        <el-input v-model="form.taskTitle" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="form.status" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          :disabled="tableLoading"
        >
          搜索
        </el-button>
        <el-button
          :disabled="tableLoading"
          icon="el-icon-refresh"
          size="mini"
          @click="search(0)"
        />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <div class="tip">备注：请最少保留1个启用状态的每日任务</div>
      <div class="right">
        <el-button
          type="danger"
          size="small"
          @click="taskSort"
        >
          新手任务排序
        </el-button>
        <el-button
          type="success"
          size="small"
          @click="newbieTaskConfig"
        >
          新手任务配置
        </el-button>
        <el-button
          type="primary"
          size="small"
          @click="addNewTask"
        >
          新建任务
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column prop="id" label="任务id" align="center" />
      <el-table-column prop="taskMode" label="任务模式" align="center">
        <template slot-scope="{ row }">
          {{ row.taskMode | taskModeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="taskTypeName" label="任务类型" align="center" />
      <el-table-column prop="taskTitle" label="任务标题" align="center" />
      <el-table-column prop="taskDesc" label="任务明细" align="center" />
      <el-table-column prop="taskZhimi" label="单次奖励智米数" align="center" />
      <el-table-column prop="taskCompleteTimes" label="每日任务上限" align="center" />
      <el-table-column prop="isAllow" label="启用状态" align="center">
        <template slot-scope="{ row }">
          <div class="yz-button-area">
            {{ row.isAllow === 1 ? "启用" : "禁用" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="240">
        <template slot-scope="{ row }">
          <el-button size="small" type="primary" @click="editNewTask(row)">编辑</el-button>
          <el-button size="small" type="primary" @click="handleOperateLog(row)">操作记录</el-button>
          <el-button size="small" :type="row.isAllow === 1 ? 'info': 'primary'" @click="updateStatus(row)">{{
            row.isAllow === 1 ? "禁用" : "启用"
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 新建任务 -->
    <new-task :visible.sync="newTaskVisible" :currentRow="currentRow" />
    <!-- 新手任务配置 -->
    <newbie-task-config :visible.sync="newbieTaskConfigVisible" :checkStatus="checkStatus" />
    <!-- 操作记录弹框 -->
    <operate-log-modal :visible.sync="operateLogVisible" :currentRow="currentRow" />
    <!-- 任务排序 -->
    <task-sort :visible.sync="taskSortVisible" />
  </div>
</template>
<script>
import newTask from './components/new-task';
import newbieTaskConfig from './components/newbie-task-config';
import operateLogModal from './components/operate-log-modal';
import taskSort from './components/task-sort.vue';
import { taskMode } from './../type';
import { arrToEnum } from '@/utils';
const taskModeEnum = arrToEnum(taskMode);
export default {
  components: {
    newTask,
    newbieTaskConfig,
    operateLogModal,
    taskSort
  },
  filters: {
    taskModeEnum(val) {
      return taskModeEnum[val] || '/';
    }
  },
  data() {
    return {
      taskMode,
      singleTasksList: [],
      dailyTasksList: [],
      taskTypeList: [],
      currentRow: {},
      newTaskVisible: false, // 新增任务弹框
      newbieTaskConfigVisible: false, // 新手任务配置弹框
      operateLogVisible: false, // 操作记录弹框
      taskSortVisible: false, // 任务排序弹框
      tableLoading: false,
      form: {
        id: '',
        taskMode: '',
        taskTypeConfigId: '',
        taskTitle: '',
        status: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      checkStatus: '' // ADD:新增，UPDATE:修改
    };
  },
  provide() {
    return {
      taskTypeList: this.taskTypeList,
      singleTasksList: this.singleTasksList,
      dailyTasksList: this.dailyTasksList
    };
  },
  mounted() {
    this.getTableList();
    this.getTaskTypeList();
    this.getNewbieTaskCheck();
  },
  methods: {
    // 新手任务--判读配置新增or修改
    getNewbieTaskCheck() {
      this.$http.get('/newbietask/check').then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.checkStatus = body;
        }
      });
    },
    // 新手任务排序
    taskSort() {
      this.taskSortVisible = true;
    },
    newbieTaskConfig() {
      this.newbieTaskConfigVisible = true;
    },
    // 编辑新手任务
    editNewTask(row) {
      this.currentRow = row;
      this.newTaskVisible = true;
    },
    // 新建新手任务
    addNewTask() {
      this.currentRow = {};
      this.newTaskVisible = true;
    },
    // 打开操作记录弹框
    handleOperateLog(row) {
      this.currentRow = row;
      this.operateLogVisible = true;
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        pageNum: (this.pagination.page - 1) * this.pagination.limit,
        pageSize: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const data = this.handleQueryParams();
      this.$post('getNewbieTaskConfigList', data, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body?.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 启用禁用
    async updateStatus(row) {
      const text = row.isAllow === 1 ? '禁用' : '启用';
      this.$confirm(`请确定是否${text}？`, '请确认', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
          status: row.isAllow === 1 ? 0 : 1
        };
        this.$http.post('/newbietask/config/update/status', params, { json: true }).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message.success('操作成功');
            this.getTableList();
          }
        });
      }).catch(() => {});
    },
    // 获取任务类型列表
    getTaskTypeList() {
      this.$http.get('/newbietask/type/configs').then((res) => {
        const { fail, body } = res;
        if (!fail) {
          body.forEach(item => {
            if (item.taskMode === 1) {
              this.singleTasksList.push(item);
            } else {
              this.dailyTasksList.push(item);
            }
          });
          this.taskTypeList = body;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  .tip {
    font-size: 14px;
    margin-left: 20px;
    display: flex;
    align-items: center;
    color: #f00;
  }
}
</style>
