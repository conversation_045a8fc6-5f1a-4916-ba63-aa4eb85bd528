<template>
  <common-dialog
    is-full
    title="派券管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='派发对象' prop='pieRollTeacherName'>
          <el-input
            v-model.trim="form.pieRollTeacherName"
            placeholder="请输入"
            show-word-limit
            maxlength='50'
          />
        </el-form-item>
        <el-form-item label='优惠券' prop='couponId'>
          <yz-select
            v-model="form.couponId"
            url="/puCoupon/findBaseList.do"
            method="post"
            :props="{
              label: 'couponName',
              value: 'couponId',
              query: 'couponName'
            }"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="addCoupon">新增</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <!-- @selection-change="handleSelectionChange" -->
        <!-- <el-table-column type="selection" width="55" align="center" /> -->
        <el-table-column prop="pieRollTeacherName" label="派发对象" align="center" />
        <el-table-column prop="couponName" label="派发优惠券" align="center" />
        <el-table-column prop="quantityReceived" label="领取数量" align="center">
          <template slot-scope="scope">
            <el-link v-if="scope.row.quantityReceived !== '0'" type="success" @click="receiveDetails(scope.row)">{{ scope.row.quantityReceived }} </el-link>
            <el-link v-if="scope.row.quantityReceived === '0'" type="success">{{ scope.row.quantityReceived }} </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="是否启用" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status==1" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" align="center" width="180px">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="editStatus(scope.row)">{{ scope.row.status==1?'禁用':"启用" }}</el-button>
            </div>
          </template>
        </el-table-column>

      </el-table>
      <!-- 新增/编辑 弹窗 -->
      <add-send-coupon
        :id='couponManId'
        :title="cmTitle"
        :visible.sync="cmVisible"
        @refresh-list="getTableList"
      />

      <!-- 领取详情 弹窗 -->
      <receive-dialog
        :id='currentReId'
        type="2"
        :visible.sync="reVisible"
        @refresh-list="getTableList"
      />
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
import addSendCoupon from './add-send-coupon';
import receiveDialog from '../receive-dialog';
export default {
  components: {
    addSendCoupon,
    receiveDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      cmVisible: false,
      reVisible: false, // 领取详情
      currentReId: null,
      keyName: 'couponName',
      cmTitle: '新增',
      reTitle: '领取详情',
      couponManId: '',
      tableData: [],
      page: 1,
      limit: 10,
      total: 0,
      getEmpNameList: [],
      findBaseList: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      form: {
        couponId: '',
        pieRollTeacherName: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.getTableList();
    },
    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    getTableList() {
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit

      };
      this.$post('pieList', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = Number(body.recordsTotal);
        }
      });
    },

    // 新增
    addCoupon() {
      this.cmVisible = true;
    },
    // 启用禁用
    editStatus(row) {
      const data = {
        status: row.status === '1' ? '2' : '1',
        rollChildId: row.rollChildId
      };
      this.$post('updatePieRollChild', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    // 领取明细
    receiveDetails(row) {
      this.currentReId = row.rollChildId;
      this.reVisible = true;
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form {
  margin-bottom: 20px;
}
::v-deep .el-date-editor .el-range__close-icon{
  line-height: 25px;
}
::v-deep .el-input__icon .el-range__close-icon{
  line-height: 25px;
}
::v-deep .el-input .el-input__count .el-input__count-inner{
  display: none;
}
</style>
