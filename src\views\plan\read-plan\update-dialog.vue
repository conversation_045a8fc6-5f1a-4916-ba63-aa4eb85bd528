<template>
  <common-dialog
    show-footer
    :title="title"
    is-full
    width="95%"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <div v-if="show" class="main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='计划名称' prop='readPlanName'>
          <el-input
            v-model="form.readPlanName"
            placeholder="请输入"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label='读书计划类型' prop='readPlanType'>
          <el-select v-model="form.readPlanType">
            <el-option label="上进学社" value="puSociety" />
            <el-option label="营销活动" value="mktAct" />
          </el-select>
        </el-form-item>
        <el-form-item label='计划摘要' prop='planSummary'>
          <el-input
            v-model="form.planSummary"
            type="textarea"
            placeholder="请输入"
            maxlength="200"
            :rows="4"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='学习基数' prop='learnBaseNum'>
          <el-input-number
            v-model="form.learnBaseNum"
            v-width="150"
            :max="100"
            class="yz-input-number people padding-left"
            :controls="false"
          />
        </el-form-item>
        <el-form-item label='计划天数' prop='planDays'>
          <el-input-number
            v-model="form.planDays"
            v-width="150"
            :disabled="!isFormItemEdit"
            :min="1"
            :max="999"
            class="yz-input-number day padding-left"
            :controls="false"
          />
        </el-form-item>
        <el-form-item label='活动价' prop='actPrice'>
          <el-input-number
            v-model="form.actPrice"
            v-width="150"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </el-form-item>
        <el-form-item label='原价' prop='marketPrice'>
          <el-input-number
            v-model="form.marketPrice"
            v-width="150"
            :max="1000000000"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </el-form-item>
        <el-form-item label='抵扣方式' prop='purchaseRule'>
          <el-checkbox-group
            v-model="form.purchaseRule"
            :disabled="!isFormItemEdit"
          >
            <el-checkbox label="zhimi">智米</el-checkbox>
            <el-checkbox label="retention">滞留金</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label='是否支持退款' prop='refundSupport' @change="handleValidateRule">
          <el-radio-group
            v-model="form.refundSupport"
            :disabled="!isFormItemEdit"
          >
            <el-radio :label="1">支持</el-radio>
            <el-radio :label="2">不支持</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='可退款项目' prop='refundRule'>
          <el-checkbox-group
            v-model="form.refundRule"
            :disabled="!isFormItemEdit"
          >
            <el-checkbox label="cash">现金</el-checkbox>
            <el-checkbox label="zhimi">智米</el-checkbox>
            <el-checkbox label="retention">滞留金</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label='服务有效期' prop='serviceValidDay'>
          <el-input-number
            v-model="form.serviceValidDay"
            v-width="150"
            :min="1"
            :max="1000000000"
            class="yz-input-number day padding-left"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label='列表图片（正方形图）' prop='listPicFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='listImgs'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>
        <el-form-item label='详情' prop='detailsPicFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='detailsList'
            @remove="bannerRemoveImg"
            @success="bannerUploadSuccess"
          />
        </el-form-item>

        <el-form-item label='精选书单' prop='featuredPicFile.fileUrl'>
          <div>
            <upload-file
              :max-limit="1"
              :file-list='IntroduceList'
              @remove="introduceRemoveImg"
              @success="introduceUploadSuccess"
            />
          </div>
        </el-form-item>

        <el-form-item label='如何打卡' prop='clockPicFile.fileUrl'>
          <div>
            <upload-file
              :max-limit="1"
              :file-list='puClockList'
              @remove="puClockRemoveImg"
              @success="puClockUploadSuccess"
            />
          </div>
        </el-form-item>

        <el-form-item label='二维码背景图片' prop='backgroundPicFile.fileUrl'>
          <div>
            <upload-file
              :max-limit="1"
              :file-list='backgroundList'
              @remove="backgroundRemoveImg"
              @success="backgroundUploadSuccess"
            />
          </div>
        </el-form-item>

        <el-form-item label='打卡规则' prop='clockText'>
          <el-input
            v-model="form.clockText"
            type="textarea"
            placeholder="请输入"
            maxlength="1000"
            :rows="4"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='分享标题' prop='shareTitle'>
          <el-input
            v-model="form.shareTitle"
            placeholder="请输入"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='分享摘要' prop='shareSummary'>
          <el-input
            v-model="form.shareSummary"
            type="textarea"
            placeholder="请输入"
            maxlength="30"
            :rows="1"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='关联一级菜单' prop='menuDirectoryVOS'>
          <!-- 按钮区 -->
          <div class='yz-table-btnbox'>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="menuAdd">新增</el-button>
          </div>
          <!-- 关联一级菜单 -->
          <el-table
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            :height="table_height"
            header-cell-class-name='table-cell-header'
            :data="menuDirectoryVOS"
          >
            <el-table-column prop="sort" label="顺序" align="center" />
            <el-table-column prop="menuName" label="一级菜单名称" align="center" />
            <el-table-column prop="puMenuBookVOS.length" label="书本数量" align="center">
              <!-- {{ menuDirectoryVOS.PuMenuBookVOS.length }} -->
            </el-table-column>
            <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="menuEdit(scope.row)">编辑</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

      </el-form>

    </div>
    <relation-menu-dialog
      :visible.sync="udVisible"
      :title="udTitle"
      :course-id="courseId"
      :menu-list="menuList"
      :isFormItemEdit='isFormItemEdit'
      @getAddMenuListInfo='getAddMenuListInfo'
      @getEditMenuListInfo='getEditMenuListInfo'
    />
  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
import { splitChar } from '@/utils';
import relationMenuDialog from './relation-menu-dialog';
import { TABLE_HEIGHT } from '@/config/constant';
import { positiveInteger } from '@/common/vali';
export default {
  components: {
    relationMenuDialog
  },
  props: {
    title: {
      type: String,
      default: '新增'
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const checkMenuDirectoryVOs = (rule, value, callback) => {
      if (this.menuDirectoryVOS.length === 0) {
        callback('请关联菜单');
      } else {
        callback();
      }
    };

    const checkNum = (rule, value, callback) => {
      // /^[+]{0,1}(\d+)$/ 正整数
      var pattern = /^-?[0-9]\d*$/; // 整数的正则表达式
      // 不符合正整数时
      if (!pattern.test(value)) {
        // input 框绑定的内容为空
        value = '';
        callback('请输入整数');
      } else {
        callback();
      }
    };
    return {
      fileList: [],
      listImgs: [], // 列表
      detailsList: [], // 详情
      IntroduceList: [], // 精选
      puClockList: [], // 打卡
      backgroundList: [], // 背景
      show: false,
      withoutNetworkPath: [],
      // 关联一级菜单
      menuDirectoryVOS: [],
      tableData: [],
      udVisible: false,
      udTitle: '新增',
      courseId: null,
      menuList: {},
      tableLoading: false,
      table_height: TABLE_HEIGHT,
      rules: {
        readPlanName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        planSummary: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        learnBaseNum: [
          { required: true, validator: checkNum, trigger: 'blur' }
        ],
        planDays: [
          { required: true, validator: checkNum, trigger: 'blur' }
        ],
        actPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        marketPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        refundRule: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        purchaseRule: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        refundSupport: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        'listPicFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        'detailsPicFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        'featuredPicFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        'clockPicFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        'backgroundPicFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        clockText: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        shareTitle: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        shareSummary: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        menuDirectoryVOS: [
          { required: true, validator: checkMenuDirectoryVOs, trigger: 'blur' }
        ],
        serviceValidDay: [
          { required: true, validator: positiveInteger, trigger: 'change' }
        ]
      },

      form: {
        // readPlanId: '', // 读书计划id
        readPlanName: '', // 读书计划名称
        readPlanType: '', // 读书计划类型
        planSummary: '', // 读书计划摘要
        learnBaseNum: undefined, // 学习基数
        planDays: undefined, // 计划天数
        actPrice: undefined, // 活动价格
        marketPrice: undefined, // 基本价格
        refundSupport: '', // 是否支持退费
        purchaseRule: ['zhimi', 'retention'], // 抵扣方式
        refundRule: ['cash', 'zhimi', 'retention'], // 可退款项目

        // 列表图片
        listPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 详情
        detailsPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 精品
        featuredPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 打卡
        clockPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        backgroundPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        serviceValidDay: undefined, // 服务有效期
        clockText: '', // 打卡规则文案
        shareTitle: '', // 分享标题
        shareSummary: '', // 分享摘要
        menuDirectoryVOS: [] // 菜单集合
      },
      isFormItemEdit: true // true可以编辑，false不可编辑
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  beforeDestroy() {
    window.onscroll = null;
  },
  methods: {
    // 新增
    getAddMenuListInfo(type) {
      this.menuDirectoryVOS.push(type);
    },
    // 编辑
    getEditMenuListInfo(type) {
      // 有菜单id就不添加新的菜单
      // console.log(this.menuDirectoryVOS);
      // console.log(type);
      // if (this.menuDirectoryVOS.length === 0) {
      //   this.menuDirectoryVOS.push(type);
      // }
    },
    menuAdd() {
      this.udVisible = true;
      this.udTitle = '新增';
      this.courseId = null;
      this.menuList = {};
    },
    menuEdit(row) {
      this.udVisible = true;
      this.udTitle = '编辑';
      this.menuList = row;
    },
    handleValidateRule(value) {
      if (value === 1) {
        this.$set(this.rules, 'refundRule', [{ required: true, message: '请选择', trigger: 'change' }]);
      } else {
        this.$delete(this.rules, 'refundRule');
      }
    },
    reset() {
      this.$nextTick(() => {
        this.$refs['form'].resetFields();
      });
    },
    init() {
      // 根据id获取读书计划详情
      if (this.id) {
        this.$post('getReadPlanInfo', { readPlanId: this.id }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.readPlanName = body.readPlanName;
            this.form.planSummary = body.planSummary;
            this.form.learnBaseNum = body.learnBaseNum;
            this.form.planDays = body.planDays;
            this.form.actPrice = body.actPrice;
            this.form.marketPrice = body.marketPrice;
            this.form.refundRule = splitChar(body.refundRule);
            this.form.purchaseRule = splitChar(body.purchaseRule);
            this.form.refundSupport = body.refundSupport;
            this.form.clockText = body.clockText;
            this.form.shareTitle = body.shareTitle;
            this.form.shareSummary = body.shareSummary;
            this.form.menuDirectoryVOS = body.menuDirectoryVOS;
            this.form.readPlanType = body.readPlanType;
            this.menuDirectoryVOS = body.menuDirectoryVOS;
            this.form.PuMenuBookVOS = body.PuMenuBookVOS;
            this.form.serviceValidDay = body.serviceValidDay;
            if (body.applyNum > 0) {
              this.isFormItemEdit = false;
            }
            // 图片
            this.form.listPicFile.fileUrl = body.listPic;
            this.form.detailsPicFile.fileUrl = body.detailsPic;
            this.form.featuredPicFile.fileUrl = body.featuredPic;
            this.form.backgroundPicFile.fileUrl = body.backgroundPic;
            this.form.clockPicFile.fileUrl = body.clockPic;

            this.listImgs.push({ url: ossUri + body.listPic });
            this.detailsList.push({ url: ossUri + body.detailsPic });
            this.IntroduceList.push({ url: ossUri + body.featuredPic });
            this.puClockList.push({ url: ossUri + body.clockPic });
            this.backgroundList.push({ url: ossUri + body.backgroundPic });
          }
        });
      }
    },
    submit() {
      this.add();
    },
    // 增加读书计划
    add() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apikey = 'addReadPlan';
          const formData = JSON.parse(JSON.stringify(this.form));

          formData.purchaseRule = formData.purchaseRule.join();
          formData.refundRule = formData.refundRule.join();
          const data = {
            ...formData
          };
          data.menuDirectoryVOS = this.menuDirectoryVOS;
          // data.menuDirectoryVOS
          data.menuDirectoryVOS.forEach(item => {
            item.puMenuBookVOSJson = JSON.stringify(item.puMenuBookVOS);
          });
          data.menuDirectoryVOS.puMenuBookVOSJson = JSON.stringify(data.menuDirectoryVOS.puMenuBookVOS);
          if (this.id) {
            apikey = 'updateReadPlan';
            data.readPlanId = this.id;
          }
          this.$post(apikey, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 列表图片回显
    handleRemoveImg({ file, fileList }) {
      this.form.listPicFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.listPicFile.fileUrl = response;
      this.form.listPicFile.isAdd = 1;
    },
    // 详情图片回显
    bannerRemoveImg({ file, fileList }) {
      this.form.detailsPicFile.fileUrl = '';
    },
    bannerUploadSuccess({ response, file, fileList }) {
      this.form.detailsPicFile.fileUrl = response;
      this.form.detailsPicFile.isAdd = 1;
    },
    // 精选图片回显
    introduceRemoveImg({ file, fileList }) {
      this.form.featuredPicFile.fileUrl = '';
    },
    introduceUploadSuccess({ response, file, fileList }) {
      this.form.featuredPicFile.fileUrl = response;
      this.form.featuredPicFile.isAdd = 1;
    },
    // 打卡图片回显
    puClockRemoveImg({ file, fileList }) {
      this.form.clockPicFile.fileUrl = '';
    },
    puClockUploadSuccess({ response, file, fileList }) {
      this.form.clockPicFile.fileUrl = response;
      this.form.clockPicFile.isAdd = 1;
    },
    // 背景图片回显
    backgroundRemoveImg({ file, fileList }) {
      this.form.backgroundPicFile.fileUrl = '';
    },
    backgroundUploadSuccess({ response, file, fileList }) {
      this.form.backgroundPicFile.fileUrl = response;
      this.form.backgroundPicFile.isAdd = 1;
    }
  }
};
</script>
<style lang="scss" scoped>
.main{
  padding:20px;
}
</style>
