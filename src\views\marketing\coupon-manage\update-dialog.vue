<template>
  <common-dialog
    :show-footer="true"
    width="60%"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='优惠券名称' prop='couponName'>
          <el-input
            v-model.trim="form.couponName"
            placeholder="请输入"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item width="200" label='优惠券类型' prop='couponType' class='couponType'>
          <el-select v-model="form.couponType" width="200" filterable placeholder="请选择" :disabled='isEdit'>
            <el-option label="抵扣券" value="2" />
            <el-option label="满减券" value="1" />
          </el-select>
          <div v-if="form.couponType === '2'" class="amount-box">
            抵扣金额 <el-input-number v-model="form.deduceNum" :controls="false" placeholder="请输入内容" :disabled='isEdit' />
          </div>
          <div v-if="form.couponType === '1'" class="amount-box">
            满<el-input-number v-model="form.amountLimit" :controls="false" placeholder="请输入内容" :disabled='isEdit' />
            减<el-input-number v-model="form.amount" :controls="false" placeholder="请输入内容" :disabled='isEdit' />
          </div>
        </el-form-item>

        <el-form-item label='可领取时间' prop='receiveTime'>
          <el-date-picker
            v-model="form.receiveTime"
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="timeOptions"
          />
        </el-form-item>

        <el-form-item label='券有效期' prop='effectiveType' class="effectiveType">
          <el-select v-model="form.effectiveType" width="200" filterable placeholder="请选择" :disabled="!isShowEffect || isEdit">
            <el-option label="限定日期有效" :value="1" />
            <el-option label="领后N天有效" :value="2" />
          </el-select>
          <el-date-picker
            v-if="form.effectiveType === 1"
            v-model="form.effectiveTime"
            :disabled='isEdit'
            type="datetimerange"
            range-separator="至"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="effectTimeOptions"
          />
          <div v-if="form.effectiveType === 2" class="amount-box">
            <el-input-number v-model="form.day" :controls="false" placeholder="请输入" :disabled='isEdit' />天
            <el-input-number v-model="form.hour" :controls="false" placeholder="请输入" :disabled='isEdit' />时
            <el-input-number v-model="form.minute" :controls="false" placeholder="请输入" :disabled='isEdit' />分
          </div>
        </el-form-item>

        <el-form-item v-if="!id" label='发放张数' prop='couponLimitCount'>
          <el-input-number
            v-model="form.couponLimitCount"
            :controls="false"
            placeholder="请输入"
            show-word-limit
          />
        </el-form-item>
        <el-form-item width="200" label='同样券叠加使用' prop='identicalUse'>
          <el-select v-model="form.identicalUse" width="200" filterable placeholder="请选择" :disabled='isEdit'>
            <el-option label="可以" :value="true" />
            <el-option label="不可以" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item width="200" label='其他券叠加使用' prop='differentUse'>
          <el-select v-model="form.differentUse" width="200" filterable placeholder="请选择" :disabled='isEdit'>
            <el-option label="可以" :value="true" />
            <el-option label="不可以" :value="false" />
          </el-select>
        </el-form-item>

        <el-form-item label='可领人群' prop='collarCrowd'>
          <el-checkbox-group
            v-model="form.collarCrowd"
            :disabled='isEdit'
          >
            <el-checkbox label="1">会员</el-checkbox>
            <el-checkbox label="2">付费用户（不含会员）</el-checkbox>
            <el-checkbox label="3">未付费用户</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label='可用人群' prop='availableCrowd'>
          <el-checkbox-group
            v-model="form.availableCrowd"
            :disabled='isEdit'
          >
            <el-checkbox label="1">会员</el-checkbox>
            <el-checkbox label="2">付费用户（不含会员）</el-checkbox>
            <el-checkbox label="3">未付费用户</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item width="200" label='使用范围' prop='scopeUse'>
          <el-select v-model="form.scopeUse" width="200" filterable placeholder="请选择" :disabled='isEdit' @change="scopeUseChange">
            <el-option label="全部课程可用" value="1" />
            <el-option label="指定课程可用" value="2" />
            <el-option label="指定课程不可用" value="3" />
          </el-select>
          <div v-if="!isEdit" class='yz-table-btnbox'>
            <el-button type="primary" size="small" icon="el-icon-plus" :disabled='form.scopeUse === "1"' @click="showCourse = true">添加</el-button>
          </div>
          <!-- 关联 -->
          <el-table
            v-if="form.scopeUse !== '1'"
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            header-cell-class-name='table-cell-header'
            :data="courseSelectList"
          >
            <el-table-column prop="goodsShelfId" label="商品id" align="center" />
            <el-table-column prop="goodsShelfName" label="名称" align="center" />
            <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" :disabled="isEdit" @click="menuMove(scope.row)">移除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label='优惠券展示渠道' prop='isShow'>
          <el-checkbox-group
            v-model="form.isShow"
          >
            <el-checkbox label="2">商品介绍页</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <!-- 关联课程弹窗 -->
      <common-dialog
        :show-footer="true"
        width="60%"
        title="选择课程"
        :visible.sync='showCourse'
        @open="initCourseDialog"
        @confirm="submitCourse"
        @close='closeCourseDialog'
      >
        <div class="course-main">
          <el-transfer
            v-model="courseList"
            filterable
            :titles="['选择课程', '已选课程']"
            :button-texts="['移除', '选中 ']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            :props="{key: 'goodsShelfId',label: 'goodsShelfName'}"
            :data="courseData"
          >
            <span slot-scope="{ option }">
              {{ option.goodsShelfName }}
            </span>
          </el-transfer>
        </div>
      </common-dialog>
    </div>
  </common-dialog>
</template>

<script>
import { validate } from '@/utils/validate';
import { formatTimeStamp } from '@/utils';
// import { handleDateControl } from '@/utils';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    // 校验优惠券类型
    const checkDeduceNum = (rule, value, callback) => {
      if (!this.form) return;
      if (this.id) { callback(); }
      if (!this.form.couponType) {
        callback('请选择');
      }
      if (this.form.couponType === '2') {
        if (this.form.deduceNum === undefined || this.form.deduceNum === '') {
          callback('请输入');
        } else if (!validate('positiveInteger2', this.form.deduceNum)) {
          callback('请输入正整数');
        } else {
          callback();
        }
      } else {
        if (!this.form.amountLimit || !this.form.amount) {
          console.log(!this.form.amountLimit, this.form.amount);
          callback('请输入  (满减不能为0)');
        } else if (!validate('positiveInteger2', this.form.amountLimit) || !validate('positiveInteger2', this.form.amount)) {
          callback('请输入正整数');
        } else if (this.form.amountLimit < this.form.amount) {
          console.log(typeof this.form.amountLimit);
          callback('不符合,满X减Y，X必须大于或者等于Y');
        } else {
          callback();
        }
      }
    };

    const checkReTime = (rule, value, callback) => {
      if (!value) {
        callback('请选择');
      } else {
        if (value[0] === '' && value[1] === '') {
          callback('请选择');
        } else {
          callback();
        }
      }
    };
    // 校验券有效期
    const checkEffectType = (rule, value, callback) => {
      if (!this.form) return;
      if (this.id) { callback(); }
      if (!this.form.receiveTime) {
        callback('请先选择领取时间');
      }
      if (!this.form.effectiveType) {
        callback('请选择');
      } else {
        if (this.form.effectiveType === 1) {
          if (this.form.effectiveTime === '') {
            callback('请选择');
          } else {
            callback();
          }
        } else {
          if (this.form.day === undefined || this.form.day === '' ||
          this.form.hour === undefined || this.form.hour === '' ||
          this.form.minute === undefined || this.form.minute === '') {
            callback('请输入');
          } else if (this.form.day === 0 && this.form.hour === 0 && this.form.minute === 0) {
            callback('日期时间不能都为0');
          } else if (!validate('positiveInteger', this.form.day) || !validate('positiveInteger', this.form.hour) || !validate('positiveInteger', this.form.minute)) {
            callback('日期时间需要输入正整数');
          } else {
            callback();
          }
        }
      }
    };
    // 使用范围
    const checkScopeUse = (rule, value, callback) => {
      if (this.id) { callback(); }
      if (this.courseSelectList === undefined) {
        callback();
        return;
      }
      if (!value) {
        callback('请选择');
      } else if (this.courseSelectList.length === 0 && !this.id && !this.scopeStatus) {
        callback('请添加表格数据');
      } else {
        callback();
      }
    };
    const checkLimitCount = (rule, value, callback) => {
      if (value === '') {
        callback('请输入');
      } else if (!validate('positiveInteger2', value)) {
        callback('请输入正整数');
      } else {
        callback();
      }
    };

    return {
      courseData: [],
      show: false,
      tableLoading: false,
      isShowEffect: false,
      isEdit: false,
      showCourse: false, // 弹窗
      courseList: [],
      courseSelectList: [], // 已选中的数组
      scopeStatus: false, // 使用范围-全部课程
      form: {
        couponName: '',
        deduceNum: undefined,
        amount: undefined,
        receiveTime: ['', ''],
        limitStartTime: '',
        limitEndTime: '',
        amountLimit: undefined,
        couponType: '',
        effectiveTime: '', // 券有效期
        couponLimitCount: undefined, // 发放张数
        startTime: '',
        endTime: '',
        day: undefined,
        hour: undefined,
        minute: undefined,
        puGoodsShelfSelectVos: [],
        effectiveType: '', // 券有效类型
        identicalUse: '', // 同样券叠加使用
        differentUse: '', // 其他券叠加使用
        availableCrowd: [], // 可领人群
        collarCrowd: [], // 可用人群
        isShow: [], // 优惠券展示渠道
        scopeUse: '' // 使用范围
      },
      goodsData: [],
      timeOptions: {
        disabledDate: (time) => {
          const dateTime = new Date();
          // dateTime.setHours(0);
          // dateTime.setMinutes(0);
          // dateTime.setSeconds(0);
          return dateTime.getTime() - 60 * 60 * 24 * 1000 > time.getTime();
        }
      },
      effectTimeOptions: {
        disabledDate: (time) => {
          console.log(new Date(this.form.receiveTime[0]).getTime(), new Date(this.form.receiveTime[1]).getTime());
          // 券有效开始时间 >= 可领取的开始时间
          if (new Date(this.form.receiveTime[0]).getTime()) {
            return new Date(this.form.receiveTime[0]).getTime() > time.getTime();
          }
        }
      },
      rules: {
        couponName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        couponType: [
          { required: true, validator: checkDeduceNum, trigger: 'blur' }
        ],
        receiveTime: [
          { required: true, validator: checkReTime, trigger: 'blur' }
        ],
        effectiveType: [
          { required: true, validator: checkEffectType, trigger: 'blur' }
        ],
        couponLimitCount: [
          { required: true, validator: checkLimitCount, trigger: 'blur' }
        ],
        identicalUse: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        differentUse: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        availableCrowd: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        collarCrowd: [
          { required: true, message: '请选择', trigger: 'blur' }
        ],
        scopeUse: [
          { required: true, validator: checkScopeUse, trigger: 'blur' }
        ]
      },
      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10000000
      }
    };
  },

  watch: {
    visible(val) {
      this.show = val;
    },
    'form.receiveTime'(val) {
      if (val) {
        this.isShowEffect = true;
      }
    },
    courseSelectList(val) {
      console.log('courseSelectList');
      console.log(val);
    }
  },
  mounted() {
  },
  methods: {
    // 移除
    menuMove(val) {
      this.courseSelectList = this.courseSelectList.filter(item => {
        return item.goodsShelfId !== val.goodsShelfId;
      });
      this.courseList = [];
      this.courseSelectList.map(csItem => {
        this.courseList.push(csItem.goodsShelfId);
      });
    },
    open() {
      // 编辑 -- 获取数据
      if (this.id) {
        this.$post('getCouponDetail', { couponId: this.id }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.isEdit = true;
            this.form.couponName = body.couponName;
            this.form.receiveTime = [formatTimeStamp(body.limitStartTime, 'YYYY-MM-DD HH:mm:ss'), formatTimeStamp(body.limitEndTime, 'YYYY-MM-DD HH:mm:ss')];
            console.log(this.form.receiveTime);

            this.form.couponType = body.couponType;

            this.form.effectiveType = body.effectiveType;
            this.form.identicalUse = body.identicalUse;
            this.form.differentUse = body.differentUse;
            this.form.availableCrowd = body.availableCrowd.split(',');
            this.form.collarCrowd = body.collarCrowd.split(',');
            if (body.isShow === '1') {
              this.form.isShow = [];
            } else {
              this.form.isShow = body.isShow.split(',');
            }
            console.log(body.isShow, 'body.isShow');
            console.log(this.form.isShow, 'form.isShow');
            this.form.scopeUse = body.scopeUse;
            if (body.scopeUse !== '1') {
              this.courseSelectList = body.puGoodsShelfSelectVos;
            }

            if (body.couponType === '2') {
              this.form.deduceNum = body.amount;
            } else {
              this.form.amountLimit = body.amountLimit;
              this.form.amount = body.amount;
            }
            if (body.effectiveType === 1) {
              this.form.effectiveTime = [formatTimeStamp(body.startTime), formatTimeStamp(body.endTime)];
            } else {
              this.form.day = body.day;
              this.form.hour = body.hour;
              this.form.minute = body.minute;
            }
          }
        });
      }
    },
    submit() {
      const formData = this.changeFormData(this.form);
      console.log('form');
      console.log(formData);
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let url = 'addCoupon';
          if (this.id) {
            url = 'updateCoupon';
          }
          const formData = this.changeFormData(this.form);

          console.log(formData);

          this.$post(url, formData, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$emit('refresh-list', true);
              this.$emit('close');
              this.$emit('update:visible', false);
            }
          });
        }
      });
    },
    changeFormData(formData) {
      formData = JSON.parse(JSON.stringify(this.form));
      formData.day = formData.day === undefined ? '' : Number(formData.day);
      formData.hour = formData.hour === undefined ? '' : Number(formData.hour);
      formData.minute = formData.minute === undefined ? '' : Number(formData.minute);
      if (formData.couponType === '2') {
        formData.amount = Number(formData.deduceNum);
      }

      formData.couponLimitCount = formData.couponLimitCount === undefined ? '' : Number(formData.couponLimitCount);
      delete formData.deduceNum;
      formData.limitStartTime = new Date(formData.receiveTime[0]).getTime();
      formData.limitEndTime = new Date(formData.receiveTime[1]).getTime();
      formData.startTime = new Date(formData.effectiveTime[0]).getTime();
      formData.endTime = new Date(formData.effectiveTime[1]).getTime();
      formData.availableCrowd = formData.availableCrowd.toString();
      formData.collarCrowd = formData.collarCrowd.toString();
      formData.isShow = formData.isShow.toString();
      if (formData.isShow.length === 0) {
        formData.isShow = '1';
      }
      this.courseSelectList.map(item => {
        item['id'] = null;
      });
      formData.couponId = this.id;
      formData.puGoodsShelfSelectVos = this.courseSelectList;
      formData.day = formData.day === null ? '' : formData.day;
      formData.hour = formData.hour === null ? '' : formData.hour;
      formData.minute = formData.minute === null ? '' : formData.minute;
      return formData;
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('refresh-list', true);
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 使用范围-全部课程
    scopeUseChange(val) {
      if (val === '1') {
        this.scopeStatus = true;
        this.courseSelectList = [];
      } else if (val !== '1' && this.scopeStatus === true) {
        this.courseSelectList = [];
        this.scopeStatus = false;
      }
    },
    // 选择课程弹窗
    initCourseDialog() {
      const data = {
        page: this.pagination.page,
        rows: this.pagination.limit,
        goodsShelfName: '',
        shelfStatus: 1
      };

      this.$post('getSetMealList', data).then(res => {
        this.courseData = res.body.data;
      });
    },
    // 选择课程提交
    submitCourse() {
      this.showCourse = false;
      // 在全部课程中筛选出已选中的课程
      // 全部课程 courseData 已选中的课程courseList

      this.courseSelectList = [];
      this.courseData.map(item => {
        this.courseList.map(valItem => {
          if (item.goodsShelfId === valItem) {
            this.courseSelectList.push({
              goodsShelfId: item.goodsShelfId,
              goodsShelfName: item.goodsShelfName
            });
          }
        });
      });
    },
    // 选择课程关闭
    closeCourseDialog() {
      this.showCourse = false;
    }
  }
};
</script>

<style lang='scss' scoped>
.amount-box{
  width: 50%;
  display: flex;
  .el-input,.el-input-number{
    padding: 0 10px;
    width: 80%;
  }

}
.el-select{
  width: 40%;
  display: inline-block;
  margin-right: 20px;
}
.couponType ::v-deep .el-form-item__content,.effectiveType ::v-deep .el-form-item__content{
  display: flex;
}
.yz-table-btnbox{
  text-align: left;
  margin-top: 10px;
}

::v-deep .el-transfer__buttons{
  width: 20%;
}
::v-deep .el-transfer__buttons .el-button{
    display: block;
    margin: 0;
    margin-bottom: 10px;
  }

::v-deep .el-transfer-panel{
  width: 39%;
  height: 500px;
}
::v-deep .el-transfer-panel__list.is-filterable{
  height: 400px;
}
.course-main{
  display: flex;
  padding: 20px;
  justify-content: center;
  // align-items: center;
}
.el-transfer{
  width: 800px;
}
.el-table{
  margin-top: 10px;
}
</style>
