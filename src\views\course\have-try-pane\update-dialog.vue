<template>
  <common-dialog
    width="70%"
    is-full
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form ref="form" class="form" size='mini' :model="form" label-width="120px" :rules="rules">
        <el-form-item label='试看名称' prop='tryName'>
          <el-input v-model="form.tryName" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>

        <el-form-item v-if="!freeId" label='试看课程' prop='courseId'>
          <remote-search-selects
            ref="remoteSearch"
            v-model="form.courseId"
            :props="{ apiName: 'courseSelect', value: 'courseId', label: 'courseName', query: 'courseName' }"
            :param="{ courseName: '' }"
            :default-option="null"
            @changeVal="handleSelectCourse"
          />
        </el-form-item>

        <el-form-item v-if="freeId != null" label='关联课时名称' prop='videoName'>
          <el-input v-model="videoName" disabled />
        </el-form-item>

        <el-form-item v-if="form.courseChannelCode == 'YZ' && !freeId" label='关联试看课时' prop='_courseTimeId'>
          <el-cascader
            v-model="form._courseTimeId"
            class="cascader"
            placeholder="试试搜索目录名称"
            :props="YzProps"
            filterable
            @change="handleYZSelection"
          />
        </el-form-item>

        <el-form-item v-if="form.courseChannelCode == 'KHW' && !freeId" label='关联试看课时' prop='_thirdVideId'>
          <el-cascader
            v-model="form._thirdVideId"
            class="cascader"
            placeholder="试试搜索目录名称"
            :props="khwProps"
            filterable
            @change="handleKHWSelection"
          />
        </el-form-item>

        <!-- 课时宝 -->
        <el-form-item v-if="form.courseChannelCode == 'KSB' && !freeId" label='关联试看课时' prop='_ksbCourseId'>
          <el-select v-model="form._ksbCourseId" placeholder="请选择" @change="handleKSBSelection">
            <el-option v-for="item in ksbCourseClassList" :key="item.id" :label="item.title" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item label='试看有效期' prop='time'>
          <el-date-picker
            v-model="form.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='顺序' prop='sort'>
          <el-input-number v-model="form.sort" class="yz-input-number" :min="0" :max="1000000000" :controls="false" />
        </el-form-item>

        <el-form-item label='是否启用' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>

  </common-dialog>
</template>
<script>
import { handleDateControl } from '@/utils';
import { ossUri } from '@/config/request';
import http from '@/utils/getFile';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    freeId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      videoName: '',
      show: false,
      tableLoading: false,
      courseDialogShow: false,
      // tableData: [],
      courseCatalog: [],
      currentSelectionCourseType: [],
      ksbCourseClassList: [],
      // pagination: {
      //   page: 1,
      //   total: 0,
      //   limit: 10
      // },
      form: {
        courseId: '',
        time: '',
        courseTimeName: '',
        _courseTimeId: '',
        _ksbCourseId: '', // 课师宝
        courseTimeId: '',
        courseChannelCode: '', // 视频渠道
        tryName: '',
        sort: undefined,
        status: '1',
        thirdVideId: '', // 第三方视频
        thirdInfo: '', // 第三方关联信息
        freeVideUrl: '', // khw的视频地址
        _thirdVideId: ''
        // thirdVideName: '' // 第三方视频名称
      },
      rules: {
        tryName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        courseId: [
          { required: true, message: '请选择课程', trigger: 'change' }
        ],
        _courseTimeId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        _ksbCourseId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        _thirdVideId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        time: [
          { required: true, message: '请选择试看有效期', trigger: 'change' }
        ]
      },
      khwMappingId: '',
      YzProps: {},
      khwCourse: {},
      khwProps: {}

    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  // created() {
  //   this.setKhwCascaderProps();
  //   this.setCascaderProps();
  // },
  methods: {
    setKhwCascaderProps() {
      const self = this;
      self.khwProps = {
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          console.log(level, 'level');
          console.log(node, 'node');
          if (level === 0) {
            const params = {
              courseId: self.khwMappingId
            };
            self.$post('getKHWSubjectsList', params).then(res => {
              const { fail, body } = res;
              if (!fail) {
                const nodes = body.map(item => ({
                  value: item,
                  label: item.subjectsName,
                  leaf: level >= 4,
                  ...item
                }));
                resolve(nodes);
              }
            });
          }

          if (level === 1) {
            const params = {
              examId: node.value.examId,
              subjectsId: node.value.subjectsId
            };
            self.$post('getkhwClassList', params).then(res => {
              const { fail, body } = res;
              if (!fail) {
                http.get(ossUri + body).then(json => {
                  console.log(json, 'res');
                  const nodes = json.body.list.map(item => ({
                    value: item.id,
                    label: item.name,
                    leaf: level >= 4
                  }));
                  resolve(nodes);
                });
                // console.log(body);
              }
            });
          }

          if (level === 2) {
            const params = {
              classId: node.value
            };
            self.$post('khwqueryClassInfo', params).then(res => {
              const { fail, body } = res;
              if (!fail) {
                http.get(ossUri + body).then(res => {
                  const { body } = res;
                  if (Object.prototype.toString.call(body) === '[object Object]') {
                    const obj = {};
                    // 先循环年份
                    Object.keys(body).forEach((key, index) => {
                      const teacher = body[key];
                      console.log(teacher, 'teacher');
                      obj[key] = [];

                      Object.keys(teacher).forEach(id => {
                        console.log(teacher[id], 'teacher[id]');
                        obj[key].push({
                          teaName: teacher[id][0].teaName,
                          teacherId: id,
                          list: teacher[id]
                        });
                      });
                    });

                    self.khwCourse[node.value] = obj;
                    const nodes = Object.keys(obj).map(key => ({
                      value: key,
                      label: key,
                      leaf: level >= 4
                    }));
                    resolve(nodes);

                    // 重装后的数据
                    // console.log(obj, '重装后的数据');
                  }
                });
              }
            });
          }

          if (level === 3) {
            const teacher = self.khwCourse[node.path[1]][node.value];
            const nodes = teacher.map(item => ({
              value: item.teacherId,
              label: item.teaName,
              leaf: level >= 4
            }));
            resolve(nodes);
            console.log(teacher, 'teacher');
          }

          if (level === 4) {
            const arr = self.khwCourse[node.path[1]][node.path[2]];
            let list = [];

            for (let i = 0; i < arr.length; i++) {
              if (arr[i].teacherId === node.value) {
                list = arr[i].list;
              }
            }

            const nodes = list.map(item => ({
              value: item,
              label: item.name,
              leaf: !item.child,
              child: item.child ? item.child : []
            }));
            resolve(nodes);
          }

          if (level > 4) {
            const nodes = node.data.child.map(item => ({
              value: item,
              label: item.name,
              leaf: !item.child,
              child: item.child ? item.child : []
            }));
            resolve(nodes);
          }
        }
      };
    },
    setCascaderProps() {
      const self = this;
      self.YzProps = {
        value: 'id',
        label: 'name',
        lazy: true,
        emitPath: false,
        async lazyLoad(node, resolve) {
          const { level } = node;

          if (level === 0) {
            const data = {
              page: 1,
              rows: 9999999,
              courseId: self.form.courseId
            };
            const { fail, body } = await self.$post('getCourseStage', data);
            if (!fail) {
              const nodes = [];
              if (Object.prototype.toString.call(body.data) === '[object Array]') {
                body.data.forEach(item => {
                  nodes.push({
                    name: item.stageName,
                    id: item.stageId,
                    leaf: level >= 1
                  });
                });
              }
              resolve(nodes);
            }
          } else if (level === 1) {
            const nodes = [];
            const data = {
              page: 1,
              rows: 9999999,
              stageId: node.value
            };
            const { fail, body } = await self.$post('getCourseTimeList', data);
            if (!fail) {
              body.data.forEach(item => {
                nodes.push({
                  name: item.courseTimeName,
                  id: item,
                  source: item,
                  leaf: level >= 1
                });
              });
              resolve(nodes);
            }
          }
        }
      };
    },
    handleKSBSelection(value) {
      const obj = this.ksbCourseClassList.find(item => {
        return item.id === value;
      });
      this.form.courseTimeName = obj.title;
      this.form.courseTimeId = value;
      this.form.thirdVideId = value;
    },
    handleYZSelection(note) {
      this.form.courseTimeName = note.courseTimeName;
      this.form.courseTimeId = note.courseTimeId;
    },
    handleKHWSelection(note) {
      const obj = note[note.length - 1];
      if (Object.prototype.toString.call(obj) === '[object Object]') {
        this.form.freeVideUrl = obj.videoInfo.hdUrl;
        this.form.thirdVideId = obj.videoInfo.id;
        this.form.courseTimeName = obj.name;
        this.form.thirdVideName = obj.videoInfo.name;
        this.form.thirdInfo = JSON.stringify(obj.videoInfo);
      }
    },
    handleSelectCourse({ value, label, source }) {
      this.form.courseChannelCode = '';

      setTimeout(() => {
        this.form.courseId = source.courseId;
        this.form.courseChannelCode = source.courseChannelCode;

        this.form.courseTimeId = '';
        this.form._courseTimeId = '';
        this.form.courseTimeName = '';
        this.form.thirdVideId = '';
        this.form.thirdInfo = '';
        this.form._thirdVideId = '';
        this.form._ksbCourseId = '';
        this.form.freeVideUrl = '';

        // 选择为课火网时候
        if (source.courseChannelCode && source.courseChannelCode === 'KHW') {
          this.khwMappingId = source.courseId;
          // this.getKHWCourseCatalog(source.courseId);
        }

        // 课师宝
        if (source.courseChannelCode && source.courseChannelCode === 'KSB') {
          this.getKSBCourseClassList();
        }
      }, 100);
    },
    getKHWCourseCatalog(value) {
      this.courseCatalog = [];
      this.$post('getKHWCourseCatalog', { courseId: value }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          const treeData = [];
          // 将课火网数据，转换符合树形组件数据
          body.forEach(course => {
            const courseObj = {
              name: course.threeClassifyName,
              id: course.threeClassifyId,
              children: []
            };
            // 阶段
            course.fourClassifyVOList.forEach(stage => {
              const stageObj = {
                name: stage.fourClassifyName,
                id: stage.fourClassifyId,
                children: []
              };
              // 章节
              stage.classVideoPackageListVOList.forEach(chapter => {
                const chapterObj = {
                  name: chapter.dictVideoPackageName,
                  id: chapter.videoPackageId,
                  children: []
                };
                // 课时
                chapter.classVideoInfoVOList.forEach(video => {
                  const videoObj = {
                    name: video.videoName,
                    id: video
                  };
                  chapterObj.children.push(videoObj);
                });
                stageObj.children.push(chapterObj);
              });
              courseObj.children.push(stageObj);
            });
            treeData.push(courseObj);
          });

          this.courseCatalog = treeData;
        }
      });
    },
    resetMappingId(value) {
      this.form.mappingId = null;
      this.form.courseName = null;
      this.form.mappingName = '';
    },
    selectionCourse(row) {
      this.form.mappingName = row.className;
      this.form.mappingId = row.classParentId;
      this.courseDialogShow = false;
    },
    getKSBCourseClassList() {
      const data = {
        courseId: this.form.courseId
      };
      this.$post('getKSBCourseClassList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.ksbCourseClassList = body.chapter_list;
        }
      });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addHaveTryVideo';
          const formData = JSON.parse(JSON.stringify(this.form));
          const date = handleDateControl(formData.time);
          let data = {};
          if (this.freeId) {
            apiKey = 'editHaveTryVideo';
            data = {
              freeId: this.freeId,
              tryName: formData.tryName,
              sort: formData.sort,
              status: formData.status,
              startTime: date[0],
              endTime: date[1]
            };
          } else {
            data = {
              ...formData,
              startTime: date[0],
              endTime: date[1]
            };
            delete data.time;
            delete data._courseTimeId;
            delete data._thirdVideId;
            delete data._ksbCourseId;
          }

          this.$post(apiKey, data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
              this.show = false;
            }
          });
        } else {
          return false;
        }
      });
    },
    open() {
      this.setKhwCascaderProps();
      this.setCascaderProps();
      if (this.freeId) {
        const data = {
          freeId: this.freeId
        };
        this.$post('getHaveTryInfo', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.courseChannelCode = body.courseChannelCode;
            this.form.courseId = body.courseId;
            this.form.courseName = body.courseName;
            this.form.courseTimeName = body.courseTimeName;
            this.form.sort = body.sort;
            this.form.status = body.status;
            this.form.thirdVideName = body.thirdVideName;
            this.form.tryName = body.tryName;
            this.form.time = [body.startTime, body.endTime];
            this.videoName = body.courseTimeName || body.thirdVideName;
          }
        });
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.setCascaderProps();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card {
    display: none;
  }
}

.padding-10 {
  margin: 0 10px;
}

.cascader {
  width: 100%;
}
</style>
