<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='类型' prop='mebFreeType'>
        <el-select v-model="form.mebFreeType" filterable clearable placeholder="请选择">
          <el-option
            v-for="item in $localDict['mebFreeType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='卡种类' prop='mebPayType'>
        <el-select v-model="form.mebPayType" filterable clearable placeholder="请选择">
          <el-option
            v-for="item in $localDict['mebPayType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='卡名称' prop='mebName'>
        <el-input v-model="form.mebName" placeholder='请输入' />
      </el-form-item>

      <el-form-item label='状态' prop='status'>
        <el-select v-model="form.status" filterable clearable placeholder="请选择">
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='上架状态' prop='upStatus'>
        <el-select v-model="form.upStatus" filterable clearable placeholder="请选择">
          <el-option label="上架" value="1" />
          <el-option label="下架" value="2" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" title="重置" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="handleDownload">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      :height="table_height"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="mebId" label="会员卡ID" align="center" />
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="mebFreeType" label="类型" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.mebFreeType | memberCardType }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="mebPayType" label="卡种类" align="center">
        <template slot-scope="scope">
          {{ scope.row.mebPayType | tansformCardSpecies }}
        </template>
      </el-table-column>
      <el-table-column prop="mebName" label="卡名称" align="center" />
      <el-table-column prop="limitMax" label="库存" align="center" width="200">
        <template slot-scope="scope">
          <div>
            <!-- :disabled="!scope.row.limitMax" -->
            <el-button
              size="mini"
              style="float:left;"
              type="danger"
              plain
              @click="open(2,'库存减少',scope.row)"
            >-</el-button>
            <span style="margin:0 10px">{{ scope.row.limitMax || 0 }}</span>
            <el-button
              size="mini"
              type="success"
              style="float:right;"
              plain
              @click="open(1,'库存增加',scope.row)"
            >+</el-button>
          </div>
          <!-- <span v-else style="margin:0 10px">无限制</span> -->
        </template>
      </el-table-column>
      <el-table-column prop="marketPrice" label="市场价" align="center" />
      <!-- <el-table-column prop="date" label="首购价" align="center" />
      <el-table-column prop="date" label="售价" align="center" /> -->
      <el-table-column prop="buyerCount" label="购买人数" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status==='1'?'success':'danger'">
            {{ scope.row.status | tansformStatus }}
          </el-tag>
          <!-- {{ scope.row.status | tansformStatus }} -->
        </template>
      </el-table-column>
      <el-table-column prop="upStatus" label="上架状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.upStatus | upStatus }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="210px">
        <template slot-scope="scope">
          <!-- <div class="yz-button-area"> -->

          <el-button
            size="mini"
            @click="updateStatus(scope.row,2)"
          >{{ scope.row.status==='1'?'禁用':'启用' }}</el-button>
          <el-button
            type="danger"
            size="mini"
            plain
            @click="updateStatus(scope.row,1)"
          >
            {{ scope.row.upStatus==='1'?'下架':'上架' }}
          </el-button>
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <!-- </div> -->
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <addCard :visible.sync="acVisible" :title="acTitle" :card-id="currentEditCardId" />

    <common-dialog
      :show-footer="true"
      width="300px"
      :title="stockTitle"
      :visible.sync='stockShow'
      @confirm="submit"
      @close='close'
    >
      <div class="dialog-main">
        <el-form
          ref="stockForm"
          class="form"
          size='mini'
          :model="stockForm"
          :rules="stockRules"
        >
          <el-form-item prop='num'>
            <input-number :value.sync="stockForm.num" />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>

  </div>
</template>
<script>
import { exportExcel } from '@/utils';
import { pagination, TABLE_HEIGHT } from '@/config/constant';
import addCard from './add-card';
export default {
  components: {
    addCard
  },
  filters: {
    upStatus(val) {
      if (!val) return '';
      const data = {
        '1': '上架',
        '2': '下架'
      };
      return data[val];
    }
  },
  data() {
    return {
      stockTitle: '',
      stockShow: false,
      stockForm: {
        num: undefined
      },
      stockRules: {
        num: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      table_height: TABLE_HEIGHT,
      acVisible: false,
      acTitle: '新增',
      tableLoading: false,
      currentEditCardId: null,
      form: {
        mebFreeType: '',
        mebPayType: '',
        mebName: '',
        upStatus: '',
        status: ''
      },
      tableData: [
        { id: '1', title: 'test卡' }
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        ...pagination
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    open(type, title, row) {
      this.stockShow = true;
      this.stockTitle = title;
      this.currentItem = { type, row };
    },
    submit() {
      this.$refs['stockForm'].validate((valid) => {
        if (valid) {
          let editNum = +this.stockForm.num;
          if (this.currentItem.type === 2) {
            editNum = -this.stockForm.num;
          }
          const data = {
            ruleId: this.currentItem.row.ruleId,
            editNum: editNum
          };
          this.$post('updateMebCardStock', data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
              this.stockShow = false;
            }
          });
        }
      });
    },
    close() {
      this.stockShow = false;
      this.$refs['stockForm'].resetFields();
    },
    handleDownload() {
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      exportExcel('exportCardVipList', data);
    },
    // type 1上架，2启停
    updateStatus(row, type) {
      const data = {
        'mebId': row.mebId,
        'mebFreeType': null,
        'mebPayType': null,
        'mebName': null,
        'status': null,
        'upStatus': null,
        'start': null,
        'summary': null,
        'remark': null
      };
      if (type === 1) {
        data.upStatus = row.upStatus === '1' ? '2' : '1';
      } else {
        data.status = row.status === '1' ? '2' : '1';
      }
      this.$post('updateCardStatus', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleEdit(row) {
      this.currentEditCardId = row.mebId;
      this.acTitle = '编辑';
      this.acVisible = true;
    },
    handleAdd() {
      this.acTitle = '新增';
      this.currentEditCardId = null;
      this.acVisible = true;
    },
    // 获取列表数据
    async getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      const { fail, body } = await this.$post('getCardList', data);
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
