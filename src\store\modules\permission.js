const state = {
  permissions: [], // 存储权限数据
  permissionsUpdateTime: 0, // 记录权限数据最后更新时间
};

// 添加请求锁，确保同一时间只有一个接口请求
let pendingRequest = null;

const mutations = {
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions;
    state.permissionsUpdateTime = Date.now();
  },
};

const actions = {
  // 检查权限
  checkPermission({ commit, state }, permissionCode) {
    return new Promise((resolve, reject) => {
      try {
        // 判断权限数据是否已过期（超过30秒）
        const now = Date.now();
        const isExpired = now - state.permissionsUpdateTime > 30 * 1000;

        // 如果权限数据不存在或已过期，则重新获取
        if (!state.permissions.length || isExpired) {
          // 如果已经有一个正在进行的请求，则复用这个请求
          if (pendingRequest) {
            pendingRequest
              .then((permissions) => {
                const hasPermission = Boolean(
                  permissions.find((item) => item === permissionCode)
                );
                resolve(hasPermission);
              })
              .catch((err) => {
                reject(err);
              });
            return;
          }

          // 创建新的请求
          pendingRequest = new Promise((resolveRequest, rejectRequest) => {
            this._vm
              .$post("evalPermission")
              .then((res) => {
                const { code, body } = res;
                if (code === "00" && body) {
                  commit("SET_PERMISSIONS", body);
                  resolveRequest(body);

                  // 检查是否有指定权限
                  const hasPermission = Boolean(
                    body.find((item) => item === permissionCode)
                  );
                  resolve(hasPermission);
                } else {
                  commit("SET_PERMISSIONS", []);
                  resolveRequest([]);
                  resolve(false);
                }
                // 请求完成后重置锁
                setTimeout(() => {
                  pendingRequest = null;
                }, 50);
              })
              .catch((error) => {
                commit("SET_PERMISSIONS", []);
                rejectRequest(error);
                reject(error);
                // 请求失败也要重置锁
                pendingRequest = null;
              });
          });
        } else {
          // 直接从现有权限数据中检查
          const hasPermission = Boolean(
            state.permissions.find((item) => item === permissionCode)
          );
          resolve(hasPermission);
        }
      } catch (error) {
        reject(error);
      }
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
