<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="读书计划" name="memberCard">
        <read-plan />
      </el-tab-pane>
      <el-tab-pane label="书本素材" name="course" lazy>
        <book-source />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import readPlan from './read-plan/index';
import bookSource from './book-source/index';
export default {
  components: {
    readPlan,
    bookSource
  },
  data() {
    return {
      activeName: 'memberCard'
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
