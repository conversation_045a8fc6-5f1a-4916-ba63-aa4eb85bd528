<template>
  <common-dialog
    :show-footer="true"
    width="700px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='学期名称' prop='semesterName'>
          <el-input
            v-model="form.semesterName"
            v-width="300"
            :disabled="enroNumToDisabled"
            maxlength="10"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='关联读书计划' prop='readPlanId'>
          <el-input
            v-model="form.readPlanName"
            v-width="300"
            disabled
          />
          <el-button
            :disabled="enroNumToDisabled"
            class="ml-15"
            type="primary"
            @click="bookDialogShow=true"
          >关联</el-button>
        </el-form-item>

        <el-form-item label='招募时间' prop='recruitTime'>
          <el-date-picker
            v-model="form.recruitTime"
            v-width="300"
            :picker-options="recruitOptions"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :disabled='isRecruitTimeShow || enroNumToDisabled'
          />
        </el-form-item>
        <el-form-item label='开学时间' prop='attendTime'>
          <el-date-picker
            v-model="form.attendTime"
            v-width="300"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            :picker-options="timeOptions"
            :disabled='isAttendTimeShow || schoolStartTimeDisabled'
          />
        </el-form-item>

        <el-form-item label='二维码图片' prop='wechatQRCodeFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

      </el-form>
    </div>

    <common-dialog
      title="关联读书计划"
      width="650px"
      :visible.sync='bookDialogShow'
      @open="initReadPlanDialog"
      @close='readPlanDialogShowClose'
    >
      <div class="dialog-main">
        <el-form
          ref='searchForm'
          size='mini'
          :model='searchForm'
          label-width='80px'
        >
          <el-form-item label="查询书本" prop="name">
            <el-input
              v-model="searchForm.name"
              v-width="300"
              placeholder="请输入书本名称"
            />
            <el-button
              class="ml-15"
              type="primary"
              icon='el-icon-search'
              @click="queryBooks"
            >查询</el-button>
          </el-form-item>

          <div>
            <el-table
              v-loading="tableLoading"
              border
              size="small"
              style="width: 100%"
              height="400px"
              header-cell-class-name='table-cell-header'
              :data="readPlanInfo"
            >
              <el-table-column prop="readPlanName" label="书本名称" align="center" />
              <el-table-column label="操作" align="center" width="150px">
                <template slot-scope="scope">
                  <div class="yz-button-area">
                    <el-button type="text" @click="selectionCourse(scope.row)">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="yz-table-pagination">
            <pagination
              :total='pagination.total'
              :page.sync="pagination.page"
              :limit.sync="pagination.limit"
              layout='prev, pager, next, sizes, total'
              @pagination="initReadPlanDialog"
            />
          </div>
        </el-form>
      </div>
    </common-dialog>
  </common-dialog>
</template>
<script>
import { handleDateControl } from '@/utils';
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    semesterId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const checkAttendTime = (rule, value, callback) => {
      const formData = JSON.parse(JSON.stringify(this.form));
      const recruitTime = handleDateControl(formData.recruitTime);
      formData.recruitEndTime = recruitTime[1];

      if (new Date(value).getTime() < new Date(formData.recruitEndTime).getTime()) {
        callback('开学时间不能在招募结束时间之前');
      } else {
        callback();
      }
    };
    return {
      readPlanInfo: [],
      show: false,
      tableLoading: false,
      bookDialogShow: false,
      semester: null, // 当前学期数据
      isAttendTimeShow: true,
      isRecruitTimeShow: true,
      timeLimit: {
        lastTermEndTime: null,
        nextEpisodeStartTime: null
      },
      recruitOptions: {
        disabledDate: (time) => {
          if (this.timeLimit.lastTermEndTime === null && this.timeLimit.nextEpisodeStartTime === null) {
            return false;
          }

          time.setHours(0);
          time.setMinutes(0);
          time.setSeconds(0);

          if (this.timeLimit.lastTermEndTime && this.timeLimit.nextEpisodeStartTime) {
            return new Date(this.timeLimit.lastTermEndTime).getTime() >= time.getTime() ||
              time.getTime() >= new Date(this.timeLimit.nextEpisodeStartTime).getTime();
          }

          if (this.timeLimit.lastTermEndTime) {
            return new Date(this.timeLimit.lastTermEndTime).getTime() >= time.getTime();
          }
        }
      },
      timeOptions: {
        disabledDate: (time) => {
          // 限制开课时间不能选择招募期日期内
          const arr = handleDateControl(this.form.recruitTime);
          return new Date(arr[1]).getTime() > time.getTime();
        }
      },
      searchForm: {
        name: '',
        status: '1'
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      readPlanName: '',
      form: {
        semesterName: '', //	学期名称
        attendTime: '', // 开课时间
        readPlanId: '', // 读书计划id
        recruitTime: '', // 招募时间
        readPlanName: '',
        wechatQRCodeFile: {
          isAdd: 0,
          fileUrl: ''
        },
        enroNum: '' // 报名人数
      },
      rules: {
        semesterName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        attendTime: [
          { required: true, message: '请选择日期' }, { validator: checkAttendTime, trigger: 'blur' }
        ],
        recruitTime: [
          { required: true, message: '请选择日期', trigger: 'blur' }
        ],
        readPlanName: [
          { required: true, message: '请输入', trigger: 'change' }
        ],
        readPlanId: [
          { required: true, message: '请关联读书计划', trigger: 'change' }
        ],
        'wechatQRCodeFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'change' }
        ]
      },
      fileList: []
    };
  },
  computed: {
    // 编辑，且有人报名， 禁止操作
    enroNumToDisabled() {
      return !!(this.semesterId && this.form.enroNum);
    },
    // 开学时间
    schoolStartTimeDisabled() {
      return this.enroNumToDisabled && new Date().getTime() >= new Date(this.form.attendTime).getTime();
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'form.readPlanName'(val) {
      if (val) {
        this.isRecruitTimeShow = false;
      }
    },
    'form.recruitTime'(val) {
      if (val) {
        this.isAttendTimeShow = false;
      }
    }
  },
  methods: {
    handleRemoveImg({ file, fileList }) {
      this.form.wechatQRCodeFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.wechatQRCodeFile.fileUrl = response;
      this.form.wechatQRCodeFile.isAdd = 1;
    },
    // 获取当前可招募时间的开始日期和结束日期
    getSemesterTimeLimit(id) {
      const isUpdate = this.semester !== null;
      const num = isUpdate ? this.semester.sortPeriodsNum : null;

      const params = {
        operating: isUpdate ? 'UPDATE' : 'ADD', // 操作类型 ADD, UPDATE
        readPlanId: id, // 读书计划id
        sortPeriodsNum: num // 排期数第几期
      };

      this.$post('getSemesterTimeLimit', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.timeLimit = body;
          }
        });
    },
    // 处理表单查询参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const [startTime, endTime] = handleDateControl(formData.recruitTime);
      formData.recruitStartTime = startTime;
      formData.recruitEndTime = endTime;
      delete formData.recruitTime;

      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    queryBooks() {
      this.pagination.page = 1;
      this.initReadPlanDialog();
    },
    // 关联读书计划弹窗
    initReadPlanDialog() {
      const data = {
        ...this.searchForm,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('findBookAllAllowList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.readPlanInfo = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 关联读书计划选择
    selectionCourse(row) {
      this.getSemesterTimeLimit(row.readPlanId);
      this.form.readPlanId = row.readPlanId;
      this.form.readPlanName = row.readPlanName;
      this.bookDialogShow = false;
    },
    // 初始化
    getBaseInfo() {
      const data = { semesterId: this.semesterId };
      this.$post('getStudentByIdInfo', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.semester = body;
          this.form.semesterName = body.semesterName;
          this.form.attendTime = body.attendTime;
          this.form.readPlanName = body.readPlanName;
          this.form.readPlanId = body.readPlanId;
          this.form.recruitTime = [body.recruitStartTime, body.recruitEndTime];
          this.form.wechatQRCodeFile.fileUrl = body.wechatQRCode;
          this.form.enroNum = body.enroNum; // 报名人数
          this.fileList.push({ url: ossUri + body.wechatQRCode });
          this.getSemesterTimeLimit(body.readPlanId);
        }
      });
    },
    open() {
      if (this.semesterId) {
        this.getBaseInfo();
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addSemester';
          const data = this.handleQueryParams();
          if (this.semesterId) {
            apiKey = 'updateSemester';
            data.semesterId = this.semesterId;
          }
          delete data.readPlanName;

          this.$post(apiKey, data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$emit('update:visible', false);
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
            }
          });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    readPlanDialogShowClose() {
      this.bookDialogShow = false;
      this.$refs['searchForm'].resetFields();
      this.pagination.page = 1;
      this.pagination.total = 0;
    }
  }
};
</script>
<style lang="scss" scoped>
.ml-15 {
  margin-left: 15px;
}
</style>
