<template>
  <div class="app-main">
    <div class="back">
      <span style="font-size: 12px">学员关卡详情</span>
      <i class="el-icon-close" @click="$router.go(-1)" />
    </div>

    <div class="yz-base-container">
      <!-- 表格 -->
      <el-table
        v-loading="loading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
      >
        <el-table-column
          prop="barrierSerialNum"
          label="关卡序号"
          align="center"
          width="70"
        />
        <el-table-column prop="barrierName" label="关卡名称" align="center" />
        <el-table-column prop="realName" label="关卡解锁模式" align="center">
          <template slot-scope="scope">
            {{ scope.row.levelMode == 1 ? "立即解锁" : "按日解锁" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="barrierContent"
          label="关卡内容"
          align="center"
        />
        <el-table-column prop="barrierRule" label="关卡规则" align="center" />

        <el-table-column prop="stdName" label="关卡指引" align="center">
          <template slot-scope="scope">
            <img
              v-if="scope.row.barrierGuide"
              :src="scope.row.barrierGuide"
              style="object-fit: contain; width: 70px; height: 50px"
            >
            <p v-else>无</p>
          </template>
        </el-table-column>
        <el-table-column
          prop="reservationTime"
          label="预约闯关时间"
          align="center"
        />
        <el-table-column label="是否完成闯关" align="center" width="100">
          <template slot-scope="scope">
            {{ scope.row.barrierSuccess == 1 ? "是" : "否" }}
          </template>
        </el-table-column>

        <el-table-column
          prop="completeTime"
          label="闯关完成时间"
          align="center"
        />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getStuBarrierList"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  created() {
    this.getStuBarrierList();
  },
  methods: {
    getStuBarrierList() {
      this.loading = true;
      const data = {
        trainCampId: this.$route.query.id,
        userId: this.$route.query.userId,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };

      this.$post('stuBarrierList', data).then((res) => {
        if (res.ok) {
          this.tableData = res.body.data;
          this.pagination.total = res.body.recordsTotal;
        }
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style>
.back {
  margin: 7px 15px 0 10px;
  color: #808c95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
}
</style>
