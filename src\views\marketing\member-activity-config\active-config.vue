<template>
  <common-dialog
    width="800px"
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="init"
    @confirm='submit'
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="140px"
        :rules="rules"
      >
        <el-form-item label='活动名称' prop='actName'>
          <el-input
            v-model="form.actName"
            maxlength="20"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='关联会员卡' prop='mebName'>
          <SearchSelect
            v-model="form.mebName"
            clearable
            :disabled="!isFormItemEdit"
            :options="mebCardList"
            :props="{label:'mebName',value:'mebId'}"
            @loadmore='loadmore'
            @search="search"
            @select="select"
            @open="open"
          />
        </el-form-item>

        <el-form-item label='背景配置' prop='backgroundImg'>
          <upload-file
            tip="图片格式为：jpg，png，不可大于1M"
            :max-limit="1"
            exts='jpg|png'
            :size='1'
            :file-list='bgFileList'
            :before-remove="beforeRemoveImg"
            @remove="({ file, fileList })=> handleRemoveImg({ file, fileList },'backgroundImg')"
            @success="({ response, file, fileList })=> uploadSuccess({ response, file, fileList },'backgroundImg')"
          />
        </el-form-item>

        <el-form-item label='卡面配置' prop='explainImg'>
          <upload-file
            tip="图片格式为：jpg，png，不可大于1M"
            :max-limit="1"
            exts='jpg|png'
            :size='1'
            :file-list='cardFileList'
            :before-remove="beforeRemoveImg"
            @remove="({ file, fileList })=> handleRemoveImg({ file, fileList },'explainImg')"
            @success="({ response, file, fileList })=> uploadSuccess({ response, file, fileList },'explainImg')"
          />
        </el-form-item>

        <el-form-item label="活动时间" prop="time">
          <el-date-picker
            v-model="form.time"
            clearable
            :picker-options="expireTimeOption"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='按钮文案' prop='buttonWord'>
          <el-input
            v-model="form.buttonWord"
            maxlength="10"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='按钮颜色' prop='buttonColor'>
          <el-input
            ref="colorInput"
            v-model="form.buttonColor"
            style="width: calc(100% - 35px)"
            show-word-limit
            placeholder="请输入"
            @change="setDemoColor"
          />
          <div class="color">
            <el-color-picker v-model="btnColor" @change="setBtnColor" />
          </div>
          <div class="form-tip">
            支持渐变颜色输入以下格式：linear-gradient(渐变的方向,渐变的颜色1,渐变的颜色2,渐变的颜色3,...)
            <p>列如：linear-gradient(90deg, #FF8C58, #F85203)</p>
          </div>
        </el-form-item>

        <el-form-item label='会员卡使用规则' prop='useRule'>
          <el-input
            v-model="form.useRule"
            type="textarea"
            show-word-limit
            rows="5"
            maxlength="500"
          />
        </el-form-item>

        <el-form-item label='分享标题' prop='shareTitle'>
          <el-input
            v-model="form.shareTitle"
            show-word-limit
            maxlength="26"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='分享描述' prop='shareDesc'>
          <el-input
            v-model="form.shareDesc"
            type="textarea"
            show-word-limit
            maxlength="36"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='分享图片' prop='shareImg'>
          <upload-file
            tip="图片格式为：jpg，png，不可大于1M"
            :max-limit="1"
            exts='jpg|png'
            :size='1'
            :file-list='shareFileList'
            @remove="({ file, fileList })=> handleRemoveImg({ file, fileList },'shareImg',true)"
            @success="({ response, file, fileList })=> uploadSuccess({ response, file, fileList },'shareImg')"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='是否默认活动' prop='defaultAct'>
          <el-radio-group v-model="form.defaultAct">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
import { handleDateControl } from '@/utils';
import { ossUri } from '@/config/request';
const actionChange = { required: true, trigger: 'change', message: '请选择' };
const actionblur = { required: true, trigger: 'blur', message: '请填写' };
const actionUpload = { required: true, trigger: 'change', message: '请上传图片' };
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      show: false,
      bgFileList: [],
      cardFileList: [],
      shareFileList: [],
      btnColor: '#FFA600',
      mebCardList: [], // 会员卡选项
      page: 1,
      limit: 10,
      total: 0,
      timer: null,
      buyNumber: 0, // 购买人数
      isFormItemEdit: true, // 某些表单项是否能编辑
      form: {
        actName: '',
        mebId: '',
        mebName: '',
        backgroundImg: '',
        explainImg: '',
        buttonColor: '',
        buttonWord: '',
        useRule: '',
        shareTitle: '',
        shareDesc: '',
        shareImg: '',
        status: 1,
        defaultAct: '0',
        time: []
      },
      rules: {
        mebName: [actionChange],
        actName: [actionblur],
        mebId: [actionChange],
        backgroundImg: [actionUpload],
        explainImg: [actionUpload],
        buttonColor: [{ required: true, trigger: 'change', message: '请选择颜色或者填写' }],
        time: [{ required: true, trigger: 'change', message: '请选择活动开始至结束时间' }],
        buttonWord: [actionblur],
        useRule: [actionblur],
        shareTitle: [actionblur],
        shareDesc: [actionblur],
        shareImg: [actionUpload],
        status: [actionChange]
      },
      fileList: [],
      dome: null,
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      this.getMebCardList();
      if (this.id) {
        this.getActPageInfo();
      }
    },
    // 获取会员卡活动页配置信息
    getActPageInfo() {
      const data = {
        mebDeployId: this.id
      };
      this.$post('getActPageInfo', data, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = body;
            const {
              actName,
              backgroundImg,
              explainImg,
              buttonColor,
              buttonWord,
              useRule,
              shareTitle,
              shareDesc,
              shareImg,
              status,
              mebId,
              mebName,
              buyTimes,
              defaultAct,
              actStartTime,
              actEndTime
            } = body;

            if (actStartTime && actEndTime) {
              this.$set(this.form, 'time', [actStartTime, actEndTime]);
            }

            this.form.actName = actName;
            this.form.backgroundImg = backgroundImg;
            this.form.explainImg = explainImg;
            this.form.buttonColor = buttonColor;
            this.form.buttonWord = buttonWord;
            this.form.useRule = useRule;
            this.form.shareTitle = shareTitle;
            this.form.shareDesc = shareDesc;
            this.form.shareImg = shareImg;
            this.form.status = status;
            this.form.mebId = mebId;
            this.form.mebName = mebName;
            this.form.defaultAct = defaultAct;

            this.bgFileList.push({ url: ossUri + backgroundImg });
            this.cardFileList.push({ url: ossUri + explainImg });
            this.shareFileList.push({ url: ossUri + shareImg });
            this.btnColor = buttonColor;
            this.buyNumber = buyTimes || 0;
            this.isFormItemEdit = !(this.buyNumber > 0);
            this.setDemoColor(buttonColor);
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form));
          const date = handleDateControl(formData.time);
          delete formData.time;
          const data = {
            ...formData,
            actStartTime: date[0],
            actEndTime: date[1]
          };
          let api = 'addActPageConfig';
          if (this.id) {
            api = 'editActPageConfig';
            data.mebDeployId = this.id;
          }

          this.$post(api, data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    setDemoColor(color) {
      const elem = this.$refs['colorInput'].$refs['input'];
      elem.style.background = '';
      elem.style.background = color;
    },
    setBtnColor(color) {
      this.form.buttonColor = color;
      this.setDemoColor(color);
    },
    close() {
      this.bgFileList = [];
      this.cardFileList = [];
      this.shareFileList = [];
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
    },
    beforeRemoveImg() {
      if (this.isFormItemEdit) {
        return true;
      } else {
        this.$message.error('该活动已用用户购买/领取，不可更改');
        return false;
      }
    },
    handleRemoveImg({ file, fileList }, key) {
      this.form[key] = '';
    },
    uploadSuccess({ response, file, fileList }, key) {
      this.form[key] = response;
    },
    loadmore() {
      if (this.mebCardList.length === this.total) {
        return;
      }
      this.page += 1;
      this.getMebCardList();
    },
    async search(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.page = 1;
        this.mebCardList = [];
        this.getMebCardList(value);
      }, 500);
    },
    open() {
      this.page = 1;
      this.mebCardList = [];
      this.getMebCardList();
    },
    select(value) {
      this.form.mebId = value.mebId;
    },
    getMebCardList(keyword = '') {
      const data = {
        mebName: keyword,
        // mebFreeType: 'pay', // 付费卡
        upStatus: 1,
        status: 1,
        start: (this.page - 1) * this.limit,
        length: this.limit
      };
      this.$post('getCardList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.mebCardList = this.mebCardList.concat(body.data);
          this.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-main{
  .form{
    .form-tip{
      font-size: 12px;
      color: #606266;
      margin-top: 7px;
    }
    .color {
      position: absolute;
      top:0;
      right: 0;
    }
  }
}
</style>
