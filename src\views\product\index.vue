<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='150px'
      @submit.native.prevent='search'
    >
      <el-form-item label='商品名称' prop='goodsShelfName'>
        <el-input v-model="form.goodsShelfName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label='商品id' prop='goodsShelfId'>
        <el-input v-model="form.goodsShelfId" placeholder="请输入" />
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" @click="addFactor">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="goodsShelfName" label="商品" align="center" width="120" />
      <el-table-column prop="goodsShelfId" label="商品id" align="center" width="70" />
      <el-table-column prop="material" label="素材管理" align="center">
        <template slot-scope="scope">
          <el-table
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            header-cell-class-name='table-cell-header'
            :data="scope.row.auditList"
          >
            <el-table-column prop="audit" label="个人系数" align="center" />
            <el-table-column prop="groupTeamAudit" label="助学主管团队系数" align="center" />
            <el-table-column prop="dpTeamAudit" label="助学校长团队系数" align="center" />
            <el-table-column prop="campusTeamAudit" label="大区团队系数" align="center" />
            <el-table-column prop="campusSubordinateTeamAudit" label="大区校长所辖助学校长系数" align="center" />
            <el-table-column prop="startTime" label="生效时间" align="center">
              <template slot-scope="scopeItem">{{ scopeItem.row.startTime }} ~ {{ scopeItem.row.endTime ? scopeItem.row.endTime : '至今' }}</template>
            </el-table-column>
            <el-table-column prop="createUserName" label="创建人" align="center" />
            <el-table-column prop="usingStatus" label="是否启用" align="center" />
            <el-table-column prop="isRisingStar" label="是否爆单" align="center">
              <template slot-scope="scopeItem">{{ scopeItem.row.isRisingStar==0?'禁用':'启用' }}</template>
            </el-table-column>
            <el-table-column prop="isRanking" label="是否排行" align="center">
              <template slot-scope="scopeItem">{{ scopeItem.row.isRanking==0?'禁用':'启用' }}</template>
            </el-table-column>
          </el-table>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" prop="enable" width="100">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="edit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 新增编辑 -->
    <add-factor-dialog :visible.sync="addFactorVisible" :rowData="currentEditRow" />
  </div>
</template>
<script>
import addFactorDialog from './add-factor-dialog';

export default {
  components: {
    addFactorDialog
  },
  data() {
    return {
      tableLoading: false,
      addFactorVisible: false,
      currentDsId: '',
      form: {
        goodsShelfName: '',
        goodsShelfId: ''
      },
      tableData: [],
      currentEditRow: null,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },

    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('querySJSocietyAudits', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    addFactor() {
      this.currentEditRow = null;
      this.addFactorVisible = true;
    },
    edit(row) {
      this.currentEditRow = row;
      this.addFactorVisible = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
</style>
