import axios from 'axios';
import { Message } from 'element-ui';
import qs from 'qs';
import store from '@/store';
import { getToken } from '@/utils/auth';
import { uri, timeout, contentType } from '@/config/request';

const service = axios.create({
  baseURL: uri, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout, // request timeout
  // headers['Content-Type'] = contentType.form;
  headers: {
    'Content-Type': contentType.form
  }
});

service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['X-Token'] = getToken();
    }
    // 在请求发出之前进行一些操作
    config.headers.sendTime = new Date().getTime();
    config.headers.title = '';
    config.headers.transferId = '';
    config.headers.transferSeq = '';
    config.headers.uri = config.baseURL + config.uri;
    if (config.uploadFile) {
      config.headers['Content-Type'] = contentType.file;
    }
    if (config.json) {
      config.headers['Content-Type'] = contentType.json;
    }
    if (config.method === 'post' && config.headers['Content-Type'] === contentType.form) {
      if (!config.bracketsArray) {
        config.data = qs.stringify(config.data);
      } else { // 数组去掉下标需要特殊处理
        config.data = qs.stringify(config.data, { arrayFormat: 'brackets' });
      }
    }
    return config;
  },
  error => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */
  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  res => {
    // 在这里对返回的数据进行处理
    if (res.status === 200) {
      const { config, data: { code }} = res;
      // 流数据
      if (Number(config?.isBinary) === 1) {
        return { body: res.data, fail: true, response: res };
      }
      if (code === '00') {
        return { ...res.data, fail: false };
      }

      if (code === 'E000034' && process.env.NODE_ENV === 'production') {
        // E000034登录超时
        top.location.href = '/toLogin.do';
      }
      Message({
        message: res.data.msg || res.msg || code || '未知错误',
        type: 'error'
      });
      return { ...res.data, fail: true };
    }
    console.log('返回网络请求状态码异常', res.status);
    return res;
  },
  error => {
    console.log('err' + error); // for debug
    if (!axios.isCancel(error)) {
      Message({
        message: error.message,
        type: 'error',
        duration: 5 * 1000
      });
    }
    return Promise.reject(error);
  }
);

service.CancelToken = axios.CancelToken;
service.isCancel = axios.isCancel;

export default service;
