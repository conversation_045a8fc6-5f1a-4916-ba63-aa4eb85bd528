<template>
  <div>
    <common-dialog
      show-footer
      width="60%"
      confirmText="保存"
      :title="row ? '编辑' : '新增'"
      :visible.sync="show"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size="mini"
          :model="form"
          label-width="150px"
          :rules="rules"
        >
          <el-form-item label="导航栏名称" prop="circleTabName">
            <el-input
              v-model="form.circleTabName"
              placeholder="请输入导航栏名称"
              maxlength="5"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="社团名称" prop="circleName">
            <el-input
              v-model="form.circleName"
              placeholder="请输入社团名称"
              maxlength="10"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="成员基数" prop="fictitiousMemberNum">
            <el-input-number
              v-model="form.fictitiousMemberNum"
              placeholder="请输入成员基数"
              :min="0"
              :max="1000000"
              :precision="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label="实际成员数量" prop="actualMemberNum">
            {{ form.actualMemberNum || 0 }}
          </el-form-item>

          <el-form-item label="创作基数" prop="fictitiousCreationNum">
            <el-input-number
              v-model="form.fictitiousCreationNum"
              placeholder="请输入创作基数"
              :min="0"
              :max="1000000"
              :precision="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label="实际创作数量" prop="actualCreationNum">
            {{ form.actualCreationNum || 0 }}
          </el-form-item>

          <el-form-item label="简介" prop="introduce">
            <el-input
              v-model="form.introduce"
              type="textarea"
              placeholder="请输入简介"
              rows="4"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="背景" prop="circleBackground">
            <upload-file
              :size="2"
              :max-limit="1"
              exts="jpeg|jpg|png|"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 375px * 320px，支持上传 png、jpg；图片大小限制 2M 内"
              :file-list="form.circleBackground"
              :on-error="onUploadErr"
              @remove="form.circleBackground = []"
              @success="onCircleBackgroundSucc"
            />
          </el-form-item>

          <el-form-item label="封面" prop="circleCover">
            <upload-file
              :size="2"
              :max-limit="1"
              exts="jpeg|jpg|png|gif|"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 228px * 228px，支持上传 png、jpg 和 gif；图片大小限制 2M 内"
              :file-list="form.circleCover"
              :on-error="onUploadErr"
              @remove="form.circleCover = []"
              @success="onCircleCoverSucc"
            />
          </el-form-item>

          <el-form-item label="成为达人二维码" prop="qrCode">
            <upload-file
              :max-limit="1"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 300px * 300px，比例 1:1 即可；图片大小限制 2M 内"
              :file-list="form.qrCode"
              :on-error="onUploadErr"
              @remove="form.qrCode = []"
              @success="onQrCodeSucc"
            />
          </el-form-item>

          <el-form-item label="是否在兴趣圈推荐" prop="ifRecommend">
            <el-radio-group v-model="form.ifRecommend">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- TODO: 排序 -->
          <el-form-item v-if="form.ifRecommend === 1" label="排序" prop="sort">
            <el-input-number
              v-model="form.sort"
              placeholder="请输入排序"
              :min="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label="状态" prop="ifShow">
            <el-radio-group v-model="form.ifShow">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      form: {
        circleTabName: undefined,
        circleName: undefined,
        fictitiousMemberNum: undefined,
        actualMemberNum: 0,
        fictitiousCreationNum: undefined,
        actualCreationNum: 0,
        introduce: undefined,
        circleBackground: [],
        circleCover: [],
        qrCode: [],
        ifRecommend: undefined,
        ifShow: undefined,
        sort: undefined
      },
      rules: {
        circleTabName: [
          { required: true, message: '请输入导航栏名称', trigger: 'blur' }
        ],
        circleName: [
          { required: true, message: '请输入社团名称', trigger: 'blur' }
        ],
        introduce: [{ required: true, message: '请输入简介', trigger: 'blur' }],
        circleBackground: [
          {
            required: true,
            type: 'array',
            message: '请上传背景图片',
            trigger: 'change'
          }
        ],
        circleCover: [
          {
            required: true,
            type: 'array',
            message: '请上传封面图片',
            trigger: 'change'
          }
        ],
        qrCode: [
          {
            required: true,
            type: 'array',
            message: '请上传成为达人二维码图片',
            trigger: 'change'
          }
        ],
        ifRecommend: [
          {
            required: true,
            message: '请选择是否在兴趣圈推荐',
            trigger: 'change'
          }
        ],
        ifShow: [{ required: true, message: '请选择状态', trigger: 'change' }],
        sort: [{ required: true, message: '请输入排序', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    // 图片上传失败
    onUploadErr(err, file, fileList) {
      this.$message.error('上传失败，请稍后再试');
      console.error(err, file, fileList);
    },
    // 背景图上传成功
    onCircleBackgroundSucc({ fileList }) {
      this.form.circleBackground = fileList;
      this.$refs.form.validateField('circleBackground');
    },
    // 封面图上传成功
    onCircleCoverSucc({ fileList }) {
      this.form.circleCover = fileList;
      this.$refs.form.validateField('circleCover');
    },
    // 成为达人二维码图上传成功
    onQrCodeSucc({ fileList }) {
      this.form.qrCode = fileList;
      this.$refs.form.validateField('qrCode');
    },
    // 弹窗打开时
    open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({ ...this.row }));
        this.form.circleBackground = this.row.circleBackground
          ? [
            {
              url: this.row.circleBackground,
              response: this.row.circleBackground
            }
          ]
          : [];
        this.form.circleCover = this.row.circleCover
          ? [{ url: this.row.circleCover, response: this.row.circleCover }]
          : [];
        this.form.qrCode = this.row.qrCode
          ? [{ url: this.row.qrCode, response: this.row.qrCode }]
          : [];
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row?.id,
              ...this.form,
              circleBackground: this.form.circleBackground?.[0]?.response,
              circleCover: this.form.circleCover?.[0]?.response,
              qrCode: this.form.qrCode?.[0]?.response,
              sort: this.form.ifRecommend === 1 ? this.form.sort : undefined
            };
            const url = this.row
              ? '/circleConfig/updateById'
              : '/circleConfig/add';
            const { code } = await this.$http({
              method: 'post',
              url,
              json: true,
              data: params
            });
            if (code !== '00') return;
            this.$message.success('保存成功');
            this.show = false;
            this.$emit('confirm');
            this.close();
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.warning('请检查填写项');
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;

  .el-input__inner {
    text-align: left;
    position: relative;
  }
}
</style>
