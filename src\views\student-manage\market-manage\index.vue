<template>
  <div>
    <open-packup>
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        class="yz-search-form"
        size="mini"
        :model="form"
        label-width="120px"
        @submit.native.prevent="search"
      >
        <el-form-item label="学业编码" prop="learnId">
          <el-input v-model="form.learnId" placeholder="请输入学业编码" />
        </el-form-item>

        <el-form-item label="远智编码" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label="学员姓名" prop="realName">
          <el-input v-model="form.realName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="学员手机" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='学员阶段' prop='stdStage'>
          <el-select v-model="form.stdStage" filterable placeholder="请选择">
            <el-option
              v-for="item in $dictJson['stdStage']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="优惠类型" prop="scholarship">
          <load-more-select
            :value="form.scholarship"
            :selectType="2"
            url="getMktActList"
            keyName="actName"
            selectVal="id"
            @input="getSchoValue"
          />
        </el-form-item>

        <el-form-item label="学期名称" prop="semesterName">
          <el-input v-model="form.semesterName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="当前情况" prop="challengeStatus">
          <el-select v-model="form.challengeStatus" filterable clearable placeholder="请选择">
            <!-- 1等待挑战；2挑战中；3挑战成功；4挑战失败 -->
            <el-option label="等待开始" value="1" />
            <el-option label="挑战中" value="2" />
            <el-option label="挑战成功" value="3" />
            <el-option label="挑战失败" value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="剩余机会" prop="remainChallengeCount">
          <el-select v-model="form.remainChallengeCount" filterable clearable placeholder="请选择">
            <el-option label="0次" value="0" />
            <el-option label="1次" value="1" />
            <el-option label="2次" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="学习状态" prop="learnStatus">
          <el-select v-model="form.learnStatus" filterable clearable placeholder="请选择">
            <el-option label="进行中" value="1" />
            <el-option label="通过" value="2" />
            <el-option label="失败" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="跑步状态" prop="runningStatus">
          <el-select v-model="form.runningStatus" filterable clearable placeholder="请选择">
            <el-option label="进行中" value="1" />
            <el-option label="通过" value="2" />
            <el-option label="失败" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="跟进人" prop="recruitName">
          <el-input v-model="form.recruitName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="学习剩余补卡" prop="patchCardCount">
          <el-select v-model="form.patchCardCount" filterable clearable placeholder="请选择">
            <el-option label="0次" value="0" />
            <el-option label="1次" value="1" />
            <el-option label="2次" value="2" />
            <el-option label="3次" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label="跑步剩余天数" prop="runningRemainDays">
          <el-select v-model="form.runningRemainDays" filterable clearable placeholder="请选择">
            <el-option label="0天" value="1" />
            <el-option label="低于5天" value="2" />
            <el-option label="低于10天" value="3" />
            <el-option label="低于30天" value="4" />
          </el-select>
        </el-form-item>

        <el-form-item label="跟进人手机号" prop="recruitMobile">
          <el-input v-model="form.recruitMobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="老师备注" prop="teachRemark">
          <el-input v-model="form.teachRemark" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="运营备注" prop="adminRemark">
          <el-input v-model="form.adminRemark" placeholder="请输入" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button type="primary" size="small" @click="handleReminderTask">批量提醒</el-button>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出表格</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="learnId" label="学业编码" align="center" width="160" />
      <el-table-column prop="yzCode" label="远智编码" align="center" width="100" />
      <el-table-column prop="stdStage" label="学员阶段" align="center" width="140" />
      <el-table-column width="100" prop="realName" label="学员姓名" align="center">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="handleRealName(scope.row)">
            {{ scope.row.realName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column width="150" prop="actName" label="活动名称" align="center">
        <template slot-scope="scope">
          <el-link type="primary" :underline="false" @click="handleSchoDetail(scope.row)">
            {{ scope.row.actName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column width="150" prop="sCSemesterList" label="已参加学期" align="center">
        <template slot-scope="scope">
          <el-link
            v-for="item in scope.row.sCSemesterList"
            :key="item.id"
            type="primary"
            :underline="false"
            @click="handleSemesterDetail(item)"
          >{{ item.semesterName }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="remainChallengeCount" label="剩余挑战机会" align="center" />
      <el-table-column prop="challengeStatus" label="挑战状态" align="center">
        <template slot-scope="scope">
          <div>
            {{ scope.row.challengeStatus | challengeStatus }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="learnStatus" label="学习状态" align="center">
        <template slot-scope="scope">
          <div>
            {{ scope.row.learnStatus | learnStatus }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="learnCount" label="学习打卡" align="center">
        <template slot-scope="scope">
          <div>
            {{ `${scope.row.learnCount}天` }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="lackCardCount" label="缺卡情况" align="center">
        <template slot-scope="scope">
          <div>
            {{ `${scope.row.lackCardCount}次` }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="patchCardCount" label="剩余补卡" align="center">
        <template slot-scope="scope">
          <div>
            {{ `${scope.row.patchCardCount}次` }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="runningStatus" label="跑步状态" align="center">
        <template slot-scope="scope">
          <div>
            {{ scope.row.runningStatus | runningStatus }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="runningCount" label="跑步打卡" align="center">
        <template slot-scope="scope">
          <div>
            {{ `${scope.row.runningCount}次` }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="runningRemainDays" label="剩余天数" align="center">
        <template slot-scope="scope">
          <div>
            {{ `${scope.row.runningRemainDays}天` }}
          </div>
        </template>
      </el-table-column>

      <el-table-column width="150" label="老师备注" align="center" prop="teachRemark">
        <template slot-scope="scope">
          <div class="yz-button-area">
            {{ scope.row.teachRemark }}<br>
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope.row, 'teachRemark')" />
          </div>
        </template>
      </el-table-column>
      <el-table-column width="150" label="运营备注" align="center" prop="adminRemark">
        <template slot-scope="scope">
          <div class="yz-button-area">
            {{ scope.row.adminRemark }}<br>
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope.row, 'adminRemark')" />
          </div>
        </template>
      </el-table-column>

      <el-table-column width="150" label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <!-- 提醒 赠送 剔除 达成 重置 -->
            <el-button v-if="scope.row.isRemind === '2'" type="text" @click="handleTask(scope.row)">提醒</el-button>
            <el-button v-else type="text" disabled>已提醒</el-button>
            <el-button type="text" @click="handleGive(scope.row)">赠送</el-button>
            <el-button type="text" @click="handleSetFinish(scope.row)">达成</el-button>
            <el-button type="text" @click="handleReset(scope.row)">重置</el-button>
            <el-button type="text" @click="handleFilt(scope.row)">剔除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 学员详情 -->
    <real-details :visible.sync="realVisible" :title="realTitle" :row="realData" />

    <!-- 活动详情 -->
    <scho-details :visible.sync="schoVisible" :title="schoTitle" :row="schoData" />

    <!-- 学期详情 -->
    <semester-details :visible.sync="semVisible" :title="semTitle" :row="semData" />

    <!-- 备注 弹窗 -->
    <remark-dialog
      :visible.sync="rdVisible"
      :remark="currentRemark"
      :remarkType="remarkType"
      :challengeId="challengeId"
    />

    <!-- 赠送 -->
    <el-dialog title="请输入赠送数量" :visible.sync="presentedVisible" width="30%">
      <div class="presented">
        <el-input-number v-model.number="presentedCount" :min="1" :max="100" />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="presentedVisible = false">取 消</el-button>
        <el-button type="primary" @click="handlePresented">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { exportExcel, getTextFromDict } from '@/utils';
import semesterDetails from './components/semester-details';
import realDetails from './components/real-details';
import schoDetails from './components/scho-details.vue';
import remarkDialog from './components/remark-dialog';
export default {
  components: {
    remarkDialog,
    semesterDetails,
    realDetails,
    schoDetails
  },
  filters: {
    challengeStatus(value) {
      if (!value) return;
      const data = {
        1: '等待开始',
        2: '挑战中',
        3: '挑战成功',
        4: '挑战失败'
      };
      return data[value];
    },
    learnStatus(value) {
      if (!value) return;
      const data = {
        1: '进行中',
        2: '通过',
        3: '失败'
      };
      return data[value];
    },
    runningStatus(value) {
      if (!value) return;
      const data = {
        1: '进行中',
        2: '通过',
        3: '失败'
      };
      return data[value];
    }
  },
  data() {
    return {
      challengeId: '',
      rdVisible: false,
      currentRemark: '',
      remarkType: '',
      tableData: [],
      tableLoading: false,
      selects: [],
      markActLoad: false,

      semVisible: false,
      semTitle: '',
      realData: {},
      realVisible: false,
      realTitle: '学员详情',
      semData: {},
      schoVisible: false,
      schoTitle: '活动详情',
      schoData: {},
      // 赠送
      presentedVisible: false,
      presentedCount: 5,
      presentedParmas: {
        id: '',
        userId: '',
        number: 0
      },
      // 表单
      form: {
        learnId: '', // 学业编码
        yzCode: '', // 远智编码
        realName: '', // 学员名称
        mobile: '', // 学员手机
        scholarship: '', // 优惠类型
        semesterName: '', // 学期名称
        challengeStatus: '', // 当前情况
        remainChallengeCount: '', // 剩余机会
        learnStatus: '', // 学习状态
        runningStatus: '', // 跑步状态
        recruitName: '', // 跟进人
        patchCardCount: '', // 学习剩余补卡
        runningRemainDays: '', // 跑步剩余天数
        recruitMobile: '', // 跟进人手机号
        adminRemark: '', // 运营备注
        teachRemark: '', // 老师备注
        stdStage: ''// 学员状态
      },

      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    presentedCount: {
      handler(val) {
        this.presentedParmas.number = val;
      },
      immediate: true
    }
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    format(valueKey, key) {
      return getTextFromDict(valueKey, key);
    },
    // 获取scholarship
    getSchoValue(val) {
      this.form.scholarship = val;
    },

    // 处理查询参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return { data, formData };
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const { data } = this.handleQueryParams();
      this.$post('getActClockList', data).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },

    // 批量选择
    handleSelectionChange(selects) {
      this.selects = selects;
    },

    // 批量提醒
    handleReminderTask() {
      const userIds = [];
      if (this.selects.length > 0) {
        this.selects.map((item) => {
          userIds.push(`${item.userId + '-' + item.id}`);
        });
        this.$post('getRemind', userIds, { json: true }).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
          this.getTableList();
        });
      } else {
        this.$message.error('请勾选数据');
      }
    },

    // 导出表格
    exportData() {
      const { data, formData } = this.handleQueryParams();
      if (this.checkData(formData)) {
        return this.$message({
          message: '请至少选择一项筛选条件后再导出数据',
          type: 'warning'
        });
      }
      exportExcel('getActClockZXlist', data);
    },

    // // 检查对象数据是否为空
    checkData(obj) {
      let flag = true;
      for (const key in obj) {
        if (obj[key] && obj[key].trim() !== '') {
          flag = false;
          return false;
        }
      }
      return flag;
    },

    // 数据查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },

    // 查看学员详情
    handleRealName(row) {
      const { userId, scholarship } = row;
      this.realData = { userId, scholarship };
      this.realVisible = true;
    },

    // 查看活动详情
    handleSchoDetail(row) {
      const { scholarship } = row;
      this.schoData = { scholarship };
      this.schoVisible = true;
    },

    // 查看学期详情
    handleSemesterDetail(row) {
      const { userId, challengeId, semesterId } = row;
      this.semData = { userId, challengeId, semesterId };
      this.semTitle = `学期详情(${row.semesterName})`;
      this.semVisible = true;
    },

    // 提醒
    handleTask(row) {
      const userIds = [`${row.userId + '-' + row.id}`];
      this.$post('getRemind', userIds, { json: true }).then((res) => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
        this.getTableList();
      });
    },
    // 赠送
    handleGive(row) {
      this.presentedVisible = true;
      const { id, userId } = row;
      this.presentedCount = 1;
      this.presentedParmas.id = id;
      this.presentedParmas.userId = userId;
    },
    // 赠送确定
    handlePresented() {
      const data = {
        number: this.presentedParmas.number.toString(),
        id: this.presentedParmas.id.toString(),
        userId: this.presentedParmas.userId.toString()
      };
      this.$post('getGiveNum', data).then((res) => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
          this.presentedVisible = false;
        }
        this.getTableList();
        this.presentedVisible = false;
      });
    },
    handleEdit(row, type) {
      console.log(row, type);
      if (type === 'adminRemark') {
        this.currentRemark = row.adminRemark;
        this.challengeId = row.id;
      } else if (type === 'teachRemark') {
        this.currentRemark = row.teachRemark;
        this.challengeId = row.id;
      }
      this.remarkType = type;
      this.rdVisible = true;
    },
    // 重置
    handleReset(row) {
      this.$confirm('是否要将学员本期奖学金的记录清空并重置加入, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          challengeId: row.id
        };
        this.$post('resetChallenge', data).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
          this.getTableList();
        });
      });
    },
    // 达成
    handleSetFinish(row) {
      this.$confirm('是否要将学员本期奖学金设为挑战成功, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = {
          challengeId: row.id
        };
        this.$post('setFinishChallenge', data).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
          this.getTableList();
        });
      });
    },
    // 剔除
    handleFilt(row) {
      this.$confirm('剔除后，本学期挑战失败，需要学生手动参加下一学期, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const { id, userId } = row;
        const data = {
          id,
          userId
        };
        this.$post('getEliminate', data).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
            this.presentedVisible = false;
          }
          this.getTableList();
          this.presentedVisible = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.presented {
  display: flex;
  justify-content: center;

  .presented-input {
    width: 100px;

    ::v-deep .el-input__inner {
      text-align: center;
    }
  }
}
</style>
