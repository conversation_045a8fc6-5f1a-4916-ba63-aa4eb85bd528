<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>
      <el-form-item label='供应渠道' prop='channelId'>
        <infinite-selects
          v-model="form.channelId"
          placeholder="请选择供应渠道"
          api-key="getSupplierSelects"
          key-name="channelName"
          value-name='channelId'
          :param="{sName:''}"
          clearable
        />
      </el-form-item>

      <el-form-item label="状态" prop="tradeType">
        <el-select v-model="form.tradeType" clearable placeholder="请选择状态">
          <el-option label="已购买" value="1" />
          <el-option label="已退款" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="购买时间" prop="time">
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='table-btnbox'>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="left">
            <!-- <span>结算总金额：{{ totalAmount }} &nbsp;元</span> -->
            <span>累计结算{{ tiredSettlementPrice }}元，累计退款{{ refundPrice }}元，累计净结算{{ settlementPrice }}元</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="align-right">
            <el-button type="primary" size="small" @click="scVisible=true">供应渠道管理</el-button>
            <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">EXCEL导出</el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      :height="table_height"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
    >
      <el-table-column label="供应渠道" align="center" prop="channelName" />
      <el-table-column label="商品名称" align="center" prop="goodsName" />
      <el-table-column label="状态" align="center" prop="tradeType">
        <template slot-scope="scope">
          {{ scope.row.tradeType | tradeType }}
        </template>
      </el-table-column>
      <el-table-column label="结算价" align="center" prop="settlementPrice" />
      <el-table-column label="操作日期" align="center" prop="createTime" />
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column label="购买人" align="center" prop="userName" />
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <supply-channel :visible.sync="scVisible" />

  </div>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
import { handleDateControl, exportExcel } from '@/utils';
import supplyChannel from './supply-channel';
export default {
  components: {
    supplyChannel
  },
  filters: {
    tradeType(val) {
      if (!val) return '';
      const data = {
        1: '已购买',
        2: '已退款'
      };
      return data[val];
    }
  },
  data() {
    return {
      tiredSettlementPrice: 0, // 累计结算
      refundPrice: 0, // 累计退费
      settlementPrice: 0, // 累计净结算
      tableLoading: false,
      form: {
        channelId: '',
        time: '',
        tradeType: '',
        yzCode: ''
      },
      table_height: TABLE_HEIGHT,
      scVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    exportData() {
      const { data } = this.handleQueryParams();
      exportExcel('exportSupplierReconciliation', data);
    },
    handleAdd() {
      this.udVisible = true;
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.createStartTime = date[0];
      formData.createEndTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return { data, formData };
    },
    async getTableList() {
      this.tableLoading = true;
      const { data, formData } = this.handleQueryParams();
      const { fail, body } = await this.$post('getSupplierBillList', data);
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
      // 请求结算总金额数据
      this.$post('getSupplierTotalAmount', { ...formData }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          if (body) {
            this.tiredSettlementPrice = body.tiredSettlementPrice || 0;// 累计结算
            this.refundPrice = body.refundPrice || 0;// 累计退费
            this.settlementPrice = body.settlementPrice || 0;// 累计净结算
          }
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container{
  .table-btnbox{
    margin:20px 0 10px 0;
    .left{
      color:red;
      line-height: 33px;
    }
    .align-right{
      text-align:right;
    }
  }
}
</style>
