<template>
  <common-dialog
    :show-footer="true"
    width="60%"
    title="新增/编辑勋章"
    :visible.sync="show"
    :confirmLoading="confirmLoading"
    @open="open"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-suffix="："
        size="small"
      >
        <!-- 勋章分类 -->
        <el-form-item label="勋章分类" prop="timeType">
          <el-select v-model="form.timeType" :disabled="isEdit" placeholder="请选择勋章分类" clearable>
            <el-option
              v-for="item in medalCategoryList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 勋章类型 -->
        <el-form-item label="勋章类型" prop="medalType">
          <el-select v-model="form.medalType" :disabled="isEdit" placeholder="请选择勋章类型" clearable>
            <el-option
              v-for="item in medalTypeList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 勋章名称 -->
        <el-form-item label="勋章名称" prop="name">
          <el-input
            v-model="form.name"
            maxlength="8"
            show-word-limit
            placeholder="请输入勋章名称"
          />
        </el-form-item>

        <!-- 勋章简介 -->
        <el-form-item label="勋章简介" prop="context">
          <el-input
            v-model="form.context"
            maxlength="20"
            show-word-limit
            placeholder="请输入勋章简介"
          />
        </el-form-item>

        <!-- 勋章图片 -->
        <el-form-item label="勋章图片" prop="imageUrl">
          <upload-file
            exts="jpg|png|jpeg"
            accept="image/*"
            :max-limit="1"
            :file-list="medalImage"
            @remove="medalImageRemove"
            @success="medalImageSuccess"
          />
        </el-form-item>

        <!-- 获取条件 -->
        <el-form-item v-if="form.medalType" label="获取条件" required>
          <!-- 跑步勋章 -->
          <run-medal
            v-if="form.medalType === 1"
            ref="runMedal"
            :run-data.sync="form.run"
            :disabled="isEdit"
            :picker-options="pickerOptions"
          />
          <!-- 读书勋章 -->
          <read-medal
            v-else-if="form.medalType === 2"
            ref="readMedal"
            :read-data.sync="form.read"
            :disabled="isEdit"
            :picker-options="pickerOptions"
          />
          <!-- 公益勋章 -->
          <welfare-medal
            v-else-if="form.medalType === 3"
            ref="welfareMedal"
            :welfare-data.sync="form.welfare"
            :disabled="isEdit"
          />
          <!-- 上进勋章 -->
          <sj-grade-medal
            v-else-if="form.medalType === 4"
            ref="sjGradeMedal"
            :sj-grade-type.sync="form.sjGrade.sjGrade"
            :disabled="isEdit"
          />
          <!-- 上课勋章 -->
          <course-medal
            v-else-if="form.medalType === 5"
            ref="courseMedal"
            :course-data.sync="form.course"
            :disabled="isEdit"
          />
          <!-- 练题勋章 -->
          <exercises-medal
            v-else-if="form.medalType === 6"
            ref="exerciseMedal"
            :exercises-data.sync="form.exercises"
            :disabled="isEdit"
          />
          <!-- 自考勋章 -->
          <self-study-medal
            v-else-if="form.medalType === 7"
            ref="selfStudyMedal"
            :self-study-type.sync="form.selfStudy.selfStudy"
            :disabled="isEdit"
          />
          <!-- 专属勋章 -->
          <span v-else-if="form.medalType === 8">由运营选择用户进行赠送，赠送后立马生效</span>
        </el-form-item>

        <!-- 触发场景 -->
        <el-form-item label="触发场景" prop="toastType">
          <el-select
            v-model="form.toastType"
            placeholder="请选择触发场景"
            clearable
          >
            <el-option
              v-for="item in triggerScenesList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- 勋章权重 -->
        <el-form-item label="勋章权重" prop="sort">
          <el-input-number
            v-model="form.sort"
            placeholder="请输入1-9999"
            :min="1"
            :max="9999"
            :controls="false"
            style="width: 100%;"
          />
        </el-form-item>

        <!-- 是否可领取 -->
        <el-form-item label="是否可领取" prop="gain">
          <el-radio-group v-model="form.gain">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
// import { ossUri } from '@/config/request';
import { medalCategoryList, medalTypeList, triggerScenesList } from '../../type';
import RunMedal from './run-medal.vue';
import ReadMedal from './read-medal.vue';
import WelfareMedal from './welfare-medal.vue';
import SjGradeMedal from './sj-grade-medal.vue';
import CourseMedal from './course-medal.vue';
import ExercisesMedal from './exercises-medal.vue';
import SelfStudyMedal from './self-study-medal.vue';
import { deepClone } from '@/utils/index';

export default {
  components: {
    RunMedal,
    ReadMedal,
    WelfareMedal,
    SjGradeMedal,
    CourseMedal,
    ExercisesMedal,
    SelfStudyMedal
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      confirmLoading: false,
      isEdit: false,
      form: {
        imageUrl: '',
        run: {
          first: undefined, // 首次跑步发帖
          distance: undefined, // 单次跑量公里数
          distanceTotal: undefined, // 累计跑量公里数
          dayTotal: undefined, // 累计跑步天
          dayTop: undefined, // 跑步天数排行榜TOP10日期
          sumTop: undefined // 月度跑量排行榜TOP10日期
        },
        read: {
          first: undefined, // 首次读书发帖
          readSum: undefined, // 累计书单本
          dayTotal: undefined, // 累计读书天
          dayTop: undefined, // 读书天数排行榜TOP10日期
          sumTop: undefined // 读书笔记排行榜TOP10日期
        },
        welfare: {
          first: undefined, // 首次公益发帖
          sum: undefined // 公益笔记条
        },
        sjGrade: {
          sjGrade: undefined // 上进勋章
        },
        course: {
          first: undefined, // 首次上课
          sum: undefined // 累计上课天
        },
        exercises: {
          first: undefined, // 首次上课
          sum: undefined // 累计上课天
        },
        selfStudy: {
          selfStudy: undefined // 上进勋章
        }
      },
      pickerOptions: {
        disabledDate(time) {
          // 禁用历史年月
          return time.getTime() < Date.now();
        }
      },
      medalImage: [],
      rules: {
        name: [{ required: true, message: '请输入勋章名称', trigger: 'blur' }],
        context: [{ required: true, message: '请输入勋章简介', trigger: 'blur' }],
        imageUrl: [{ required: true, message: '请上传勋章图片', trigger: 'change' }],
        timeType: [{ required: true, message: '请选择勋章分类', trigger: 'change' }],
        medalType: [{ required: true, message: '请选择勋章类型', trigger: 'change' }],
        conditionType: [{ required: true, message: '请选择获取条件', trigger: 'change' }],
        toastType: [{ required: true, message: '请选择触发场景', trigger: 'change' }],
        sort: [{ required: true, message: '请输入1-9999的权重值', trigger: 'blur', type: 'number' }],
        gain: [{ required: true, message: '请选择是否可领取', trigger: 'change' }]
      },
      medalCategoryList, // 勋章分类数据
      medalTypeList, // 勋章类型数据
      triggerScenesList, // 触发场景数据
      medalTypeToKeyName: {
        1: 'run',
        2: 'read',
        3: 'welfare',
        4: 'sjGrade',
        5: 'course',
        6: 'exercises',
        7: 'selfStudy'
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框时的操作
    open() {
      if (this.currentRow.medalTimeId) {
        this.isEdit = true;
        this.getDetail();
      }
    },

    // 获取勋章详情
    getDetail() {
      const form = JSON.parse(JSON.stringify(this.currentRow));
      this.form = form;
      this.medalImage = form.imageUrl ? [{ url: form.imageUrl }] : [];
    },
    // 勋章图片删除
    medalImageRemove({ file, fileList }) {
      this.form.imageUrl = '';
    },
    // 勋章图片上传成功
    medalImageSuccess({ response, file, fileList }) {
      this.form.imageUrl = response;
    },
    // 清洗获取条件数据
    cleanGainData(form) {
      // 获取当前勋章类型对应的key名
      const currentKey = this.medalTypeToKeyName[form.medalType];

      // 专属勋章不需要传递任何勋章相关对象
      if (form.medalType === 8) {
        Object.keys(this.medalTypeToKeyName).forEach(key => {
          delete form[this.medalTypeToKeyName[key]];
        });
      } else {
        // 非专属勋章，只保留当前勋章类型的对象，其他勋章对象删除
        Object.keys(this.medalTypeToKeyName).forEach(key => {
          const keyName = this.medalTypeToKeyName[key];
          if (keyName !== currentKey) {
            delete form[keyName];
          }
        });

        // 对于当前勋章对象，将未使用的字段设置为null
        if (form[currentKey]) {
          Object.keys(form[currentKey]).forEach(field => {
            if (form[currentKey][field] === undefined) {
              form[currentKey][field] = null;
            }
          });
        }
      }
    },

    // 提交表单
    submit() {
      // 先验证勋章获取条件组件
      let conditionValidation = { valid: true };

      if (this.form.medalType === 1 && this.$refs.runMedal) {
        conditionValidation = this.$refs.runMedal.validate();
      } else if (this.form.medalType === 2 && this.$refs.readMedal) {
        conditionValidation = this.$refs.readMedal.validate();
      } else if (this.form.medalType === 3 && this.$refs.welfareMedal) {
        conditionValidation = this.$refs.welfareMedal.validate();
      } else if (this.form.medalType === 4 && this.$refs.sjGradeMedal) {
        conditionValidation = this.$refs.sjGradeMedal.validate();
      } else if (this.form.medalType === 5 && this.$refs.courseMedal) {
        conditionValidation = this.$refs.courseMedal.validate();
      } else if (this.form.medalType === 6 && this.$refs.exerciseMedal) {
        conditionValidation = this.$refs.exerciseMedal.validate();
      } else if (this.form.medalType === 7 && this.$refs.selfStudyMedal) {
        conditionValidation = this.$refs.selfStudyMedal.validate();
      }

      if (!conditionValidation.valid) {
        this.$message.error(conditionValidation.message);
        return;
      }

      this.$refs.formModal.validate((valid) => {
        if (!valid) return;

        this.confirmLoading = true;

        const form = deepClone(this.form);
        this.cleanGainData(form);

        const apiKey = this.isEdit ? 'updatMedalTime' : 'addMedalTime';
        this.$post(apiKey, form, { json: true })
          .then((res) => {
            const { fail } = res;
            if (!fail) {
              this.show = false;
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
            }
          })
          .finally(() => {
            this.confirmLoading = false;
          });
      });
    },

    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>

.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;
    // margin: auto;
    // margin-bottom: auto;

    .el-radio {
      margin-right: 10px;
    }

    span {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}

</style>
