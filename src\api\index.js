import axios from '@/utils/axios';
import evaluation from './evaluation';

export const api = {
  login: '/loginByMobile.do', // 登录接口
  // 公共api
  getScholarship: '/baseinfo/scholarship.do', // 获取优惠类型
  // 招生
  getUnvs: '/baseinfo/sUnvs.do', // 还是获取院校接口
  getMajor2: '/baseinfo/sPfsnByOnScholarship.do', // 获取专业
  uploadFileOss: '/file/uploadFile.do',

  // 会员卡管理模块
  addVipCard: '/meb/addnewvipcard.do', // 新增会员卡
  getCardList: '/meb/getvipcardlist', // 会员卡列表信息
  editCard: '/meb/updateMeb', // 编辑会员卡
  getCardInfo: '/meb/getSingleManage', // 获取单个vip信息
  updateCardStatus: '/meb/editcardoperate', // 编辑状态，上架状态
  updateMebCardStock: '/meb/inventory', // 更新会员卡库存
  getVipList: '/meb/getMebSelectList', // 获取vip卡下拉数据

  // 课程管理模块->课程
  getCourseList: '/PuCourse/findAllPuCourse.do', // 获取课程列表
  getCourseSingle: '/PuCourse/findPuCourse.do', // 获取单个课程
  addCourse: '/PuCourse/insertPuCourse.do', // 新增课程
  editCourse: '/PuCourse/updatePuCourse.do', // 编辑课程
  getCourseStageList: '/PuCourseStage/findAllPuCourseStage.do', // 获取单个课程阶段列表
  addStage: '/PuCourseStage/insertPuCourseStage.do', // 新增阶段
  editStage: '/PuCourseStage/updatePuCourseStage.do', // 编辑阶段
  enableStage: '/PuCourseStage/updatePuCourseStageStatus.do', // 阶段启停
  getStageSingle: '/PuCourseStage/findPuCourseStage.do', // 获取单个阶段信息
  getKSBCourseList: '/PuCourse/getKSBCourseList.do', // 获取第三方课诗宝课程列表
  getQuestionBankList: '/PuCourseQuestionBank/getQuestionBankList.do', // 获取题库树形结构数据
  getExamSubjectByBankId: '/PuCourseQuestionBank/getExamSubjectByBankId.do', // 获取题库课程明细分页
  addOrUpdateCourseQuestionBank:
    '/PuCourseQuestionBank/addOrUpdateCourseQuestionBank.do', // 绑定课程的练题库
  getCourseQuestionBankVO: '/PuCourseQuestionBank/getCourseQuestionBankVO.do', // 获取课程编辑下，已绑定的课程题库
  getKHWExamTypeList: '/PuKHWCourse/getKHWExamTypeList', // 新课火网接口
  getKHWSubjectsList: '/PuKHWCourse/getSubjectsList', // 新课火网
  getkhwClassList: '/PuKHWCourse/getClassList',
  khwqueryClassInfo: '/PuKHWCourse/queryClassInfo', //
  getYiZhiCourseList: '/PuYIZHICourse/getPackageList', // 获取易智课程列表数据

  // 课程管理模块->课时
  getCourseTime: '/PuCourseTime/findAllPuCourseTime.do', // 获取课时列表
  addCourseTime: '/PuCourseTime/insertPuCourseTime.do', // 新增课时
  updateCourseTime: '/PuCourseTime/updatePuCourseTime.do', // 编辑课时
  getCourseTimeInfo: '/PuCourseTime/findPuCourseTime.do', // 获取单个课时信息
  editCourseTimeStage: '/PuCourseTime/updateStatusByIds.do', // 批量编辑课时状态
  courseSelect: '/PuCourse/findAllPuCourseAllow.do', // 课时下拉
  getTrainSelectCfList: '/newCourse/findNewCourseList', // 获取训练营分类下拉
  getCourseStage: '/PuCourseStage/findAllPuCourseStageAllow.do', // 获取某个课程的阶段
  getWayOfClassList: '/PuCourseTime/findAllSignDetail.do', // 查看上课情况
  getWayOfClassCount: '/PuCourseTime/findSignInfoCount.do', // 查看上课情况统计

  // 课程管理模块->频道号管理
  getChannelList: '/PuLiveChannel/findAllLiveChannel.do', // 获取频道号列表
  batchDeleteChannel: '/PuLiveChannel/deletes.do', // 批量删除频道号
  getChannelInfo: '/PuLiveChannel/findLiveChannel.do', // 获取单个频道号信息
  addChannel: '/PuLiveChannel/insertLiveChannel.do', // 新增频道号
  editChannel: '/PuLiveChannel/changePassword.do', // 编辑频道号
  getLiveChannelCategory: '/live/getLiveChannelCategory', // 获取保利直播分类数据

  // 试看管理
  getFreeVideoList: '/PuCourseTimeFree/findAllPuCourseFree.do', // 获取试看视频list
  getKHWCourseCatalog: '/PuCourse/getKHWClassTableByClassId.do', // 获取课火网所有课程，以及课程的目录
  getCourseTimeList: '/PuCourseTime/findAllPuCourseTimeAllow.do', // 获取阶段下的课时
  addHaveTryVideo: '/PuCourseTimeFree/insertPuCourseFree.do', // 新增试看视频
  editHaveTryVideo: '/PuCourseTimeFree/updatePuCourseFree.do', // 编辑试看配置
  getHaveTryInfo: '/PuCourseTimeFree/findPuCourseFree.do', // 获取单个试看配置信息
  batchUpdateHaveTryStatus: '/PuCourseTimeFree/updatePuCourseFreeByFreeIds.do', // 批量启用禁用
  getKSBCourseClassList: '/PuCourse/getKSBCourseInfo.do',

  // 推荐管理模块
  getBannerList: '/puBanner/findAllPuBanner.do', // 获取Banner 列表
  getBannerInfo: '/puBanner/findPuBanner.do', // 获取单个banner 信息
  addBanner: '/puBanner/insertPuBanner.do', // 新增Banner
  editBanner: '/puBanner/updatePuBanner.do', // 编辑 banner
  deleteBanner: '/puBanner/deletes.do', // 批量删除 Banner
  updateBannerStatus: '/puBanner/updateStatus.do', // 修改banner启停
  getSetMealList: '/puGoodsShelf/findSelectList.do', // 商品通用下拉菜单

  // 供应商管理
  getSupplierList: '/PuSupplier/findAllPuSupplier.do', // 获取供应商列表
  getSupplierInfo: '/PuSupplier/findPuSupplier.do', // 获取单个供应商信息
  getRolesList: '/auth/selectCodeList.do', // 获取角色下拉
  addSupplier: '/PuSupplier/addPuSupplier.do', // 添加供应商
  editSupplier: '/PuSupplier/updatePuSupplier.do', // 编辑供应商
  updateSupplierStatus: '/PuSupplier/updatePuSupplierStatus.do', // 修改供应商状态
  getSupplierTotalAmount: '/PuSupplierReconciliation/puSupplierPriceCount.do', // 获取市场价总额，结算总额

  // 二级分类管理
  getClassificationList: '/puGoodsType/findList', // 获取商品二级管理分类列表
  getClassificationInfo: '/puGoodsType/get', // 获取单个分类详情
  getSelectCfList: '/puGoodsType/findSelectList', // 获取分类下拉
  addClassification: '/puGoodsType/add', // 分类添加
  editClassification: '/puGoodsType/update', // 编辑分类
  updateCfStatus: '/puGoodsType/updateStatus', // 更新二级分类状态

  // 商品管理
  getCommodityLibraryList: '/puGoodsBase/findList',
  addCommodity: '/puGoodsBase/add', // 添加商品
  editCommodity: '/puGoodsBase/update', // 更改商品
  getCommodityInfo: '/puGoodsBase/get', // 获取商品详情
  getSupplierSelects: '/PuSupplier/findAllKeyValue.do', // 供应商下拉
  updateCommodityStatus: '/puGoodsBase/batchUpdateStatus', // 批量更新商品状态

  // 上架管理
  getPutCommodityList: '/puGoodsShelf/findList', // 获取上架商品列表
  addPutcommodity: '/puGoodsShelf/add', // 新增商品
  editPutcommodity: '/puGoodsShelf/update', // 编辑商品
  getGoodsList: '/puGoodsBase/findBaseList', // 获取商品列表
  getPutCommodityInfo: '/puGoodsShelf/get', // 获取单个商品详情信息
  updatePutCommodityStatus: '/puGoodsShelf/updateStatus', // 更新商品上架状态
  getGoodsBuyList: '/puGoodsShelf/findGoodsShelfBuyList.do', // 获取商品购买明细列表
  updateStock: ' /puGoodsShelf/updateStock', // 更新库存
  getAgreementList: ' /puGoodsShelf/getAgreementList', // 获取分期协议

  // 课程对账管理
  getGoodsBillList: '/PuGoodsShelfAccount/puGoodsShelfReconciliationList.do', // 获取课程对账管理列表
  editGoodsRemarks: '/PuGoodsShelfAccount/updateGoodsShelfOrderDetailRemark.do', // 修改账单备注
  getSetMealBillInfo: '/PuGoodsShelfAccount/puGoodsShelfReconciliationCount.do', // 获取套餐总账单信息

  // 分期对账管理
  getPuGoodsInstall: '/puGoodsInstallment/list', // 获取分期对账列表
  exportPuGoodsInstall: '/puGoodsInstallment/export', // 导出分期对账列表
  getOrderSummary: '/puGoodsInstallment/getOrderSummary', // 分期订单概览(总金额)
  getPermission: '/puGoodsInstallment/getPermission', // 获取用户权限节点
  updateGoodsRemark: '/puGoodsInstallment/updateRemark', // 修改分期订单备注
  subGoodsRefund: '/puGoodsInstallment/refund', // 申请首付退款
  getSubRefundDetail: '/puGoodsInstallment/detail', // 获取申请首付退款详情
  subGoodsApproval: '/puGoodsInstallment/refund/approval', // 通过申请
  subGoodsAbort: '/puGoodsInstallment/refund/abort', // 驳回申请
  getRefundDetail: '/puGoodsInstallment/getRefundDetail', // 操作记录

  // 供应商结算管理
  getSupplierBillList: '/PuSupplierReconciliation/supplierReconciliation.do', // 供应商对账列表

  // 会员卡对账管理
  getVipBillList: '/meb-us/getBillList.do',
  updateMebBillRemark: '/meb-us/doMebEdit.do', // 更新会员卡对账列表备注
  getMebCardBilltotal: '/meb-us/countAndSum', // 获取会员卡账单总金额，总购买人数

  // 读书计划对账管理
  getBookBillList: '/PuGoodsShelfAccount/puGoodsShelfReconciliationList.do', // 读书计划对账列表
  getBookBilltotal: '/PuGoodsShelfAccount/puGoodsShelfReconciliationCount.do', // 获取读书计划账单总金额，总购买人数
  //  updateMebBillRemark: '/meb-us/doMebEdit.do', // 更新会员卡对账列表备注
  bindDistributionOrder: '/distributionOrder/bindDistributionOrder', // 绑定订单
  findDistributionUser: '/userBaseInfo/findDistributionUser', // 分销人下拉
  findShelfSelectList: '/puGoodsShelf/findSelectList', // 缴费名称下拉

  // 用户关系管理
  getRelationList: '/meb-us/getRelationList', // 获取用户关系列表
  getMebUserList: '/meb-us/getMebUserList.do', // 获取会员卡的用户数据列表
  updateUserRelationsRemark: '/meb-us/doEdit', // 编辑备注
  getBoughtCourseList: '/PuGoodsShelfAccount/userGoodsShelfPayList.do', // 获取已购买课程的用户列表数据

  // 导出
  exportSignDetail: '/PuCourseTime/exportSignDetail.do', // 导出上课情况
  exportSetMeal: '/PuGoodsShelfAccount/exportGoodsShelfReconciliation', // 导出套餐对账
  exportCourseListExcel:
    '/PuGoodsShelfAccount/exportGoodsBaseReconciliation.do', // 导出商品明细
  exportUserGoodsShelfPayList:
    '/PuGoodsShelfAccount/exportUserGoodsShelfPayList.do', // 用户套餐购买明细导出
  exportSupplierReconciliation:
    '/PuSupplierReconciliation/exportSupplierReconciliation.do', // 供应商对账导出
  exportFindBaseList: '/puGoodsBase/exportFindBaseList.do', // 商品导出
  exportFindGoodsShelfList: '/puGoodsShelf/exportFindGoodsShelfList.do', // 上架商品导出
  exportCardVipList: '/meb-export/vip.do', // 导出会员卡数据
  exportUserRelationshipList: '/meb-export/relation.do', // 导出用户关系列表
  exportUserVipDetailList: '/meb-export/detail.do', // 导出用户购买会员卡明细列表
  exportVipCardBillList: '/meb-export/bill.do', // 导出会员卡

  // 课火网
  getKhwCourseType: '/PuCourse/getKHWClassDirectory.do', // 获取课火网课程类型
  getKhwCourseList: '/PuCourse/getKHWClassDirectoryList.do', // 获取课火网课程

  // 营销活动
  newAct: '/act/newAct.do', // 新增营销活动
  editAct: '/act/editAct.do', // 更新营销活动
  getActivityList: '/act/actList.do', // 获取活动列表
  getActivityInfo: '/act/reAct', // 获取活动信息
  getMebCardOrCommodity: '/act/items/', // 获取会员，或者商品接口，用于关联活动

  // 会员卡活动页面配置接口
  getActPageConfigList: '/act-deploy/getList',
  getPromoteLinkList: '/act-deploy/channelList', // 获取活动页面配置推广链接
  getActPageInfo: '/act-deploy/getDeploy', // 获取单个活动页配置信息
  addWxLink: '/act-deploy/createChannel', // 新增微信链接
  addActPageConfig: '/act-deploy/create', // 新增活动页面配置信息
  editActPageConfig: '/act-deploy/edit', // 编辑
  getActOptions: '/act-deploy/checkList', // 获取推广活动下拉

  // 上进大学网站分析模块
  getEventCountList: '/PuUserOperationStatistic/list.do', // 获取事件统计列表
  exportEventList: '/PuUserOperationStatistic/export.do', // 导出事件列表
  getEventTypeList: '/PuUserOperationStatistic/findAllPuCourseAllow.do', // 获取事件类型

  // 用户购买行为
  getUserGoodsBuy: '/operation/findUserGoodsBuy.do', // 获取用户购买行为数据

  // 课程评论模块
  getCourseCommentList: '/puComment/list.do', // 获取课程评论
  getCommentInfo: '/puComment/getCommentReply.do', // 获取该评论人对此课程的留言
  replyUser: '/puComment/adminReply.do', // 后台回复用户的留言信息
  updateCommentStatus: '/puComment/updateCommentStatus.do', // 修改评论状态
  setFeaturedReviews: '/puComment/updateCommentRecommend.do', // 将评论设置为精选评论
  exportCommentList: '/puComment/exportCommentList.do', // 导出评论
  getSensitiveVocabulary: '/sensitive/list.do', // 获取敏感词库
  addOrDelSensitive: '/sensitive/edit.do', // 新增或者删除敏感词

  // 计划管理-读书计划
  getReadPlanList: '/readPlan/list.do', // 读书计划列表查询
  addReadPlan: '/readPlan/add.do', // 新增读书计划
  updateReadPlan: '/readPlan/update.do', // 编辑读书计划
  batchUpdateStatus: '/readPlan/batchUpdateStatus.do', // 单个-批量更新状态
  findBookAllAllow: '/readPlan/findAllAllow.do', // 读书计划下拉框
  findBookAllAllowList: '/readPlan/findAllAllowList.do', // 读书计划列表
  getReadPlanInfo: '/readPlan/get.do', // 获取读书计划
  getClockClassDetailList: '/PlanClockDetail/getClockClassDetailList.do', // 获取读书计划该学期
  getDayClockList: '/PlanClockDetail/thatDayClockPerson.do', // 获取当前读书打卡的列表数据
  getReadCommentList: '/puComment/getReadCommentList.do', // 获取读书计划该学期的打卡评论
  getNeverClockPerson: '/PlanClockDetail/neverClockPerson', // 获取该学期从未打卡数
  bulkReminderClock: '/PlanClockDetail/clockRemind', // 批量提醒用户打卡

  // 计划管理-书本素材
  getBookSelect: '/planBook/getSelectList.do', // 书本基本列表-下拉框
  addPlanBook: '/planBook/add.do', // 新增书本
  editPlanBook: '/planBook/update.do', // 编辑书本
  getPlanBookList: '/planBook/getList.do', // 书本列表
  getPlanBook: '/planBook/get.do', // 获取书本
  findBookSoureAllow: '/planBook/updateStatus.do', // 单个-批量更新状态

  // 推荐人业绩
  getReferencesList: '/recommendPurchase/list.do', // 推荐人业绩列表
  referExportList: '/recommendPurchase/exportList.do', // 推荐人业绩导出

  // 学员管理
  getStudentList: '/PlanSemester/getList.do', // 学员管理
  addSemester: '/PlanSemester/add.do', // 新增读书计划学期
  updateSemester: '/PlanSemester/update.do', // 编辑读书计划学期
  getStudentByIdInfo: '/PlanSemester/get.do', // 单个学期查询
  // getpPuClockInfo: '/puClock/getList.do', // 打卡明细列表查询
  readExportList: '/readPlanEnro/exportList.do', // 报名明细导出
  getSignUpInfo: '/readPlanEnro/list.do', // 报名明细列表查询
  getSemesterTimeLimit: '/PlanSemester/getSemesterTimeLimit.do', // 获取新增或编辑学期招募开始日期
  getReadPlanEnro: '/readPlanEnro/userList', // 获取所有的用户报读的读书计划列表
  getuserReadClockList: '/readPlanClock/getUserClockList.do', // 获取用户读书打卡记录 （废弃）
  getReadGoodId: '/readPlan/getGoodsShelfId', // 获取读书计划id
  getUserClockDetails: '/PlanClockDetail/ClockConditionList', // 获取用户该学期打卡详情
  getActClockList: '/scholarshipChallenge/list', // 获取营销打卡管理列表
  getActClockZXlist: '/scholarshipChallenge/exportResult', // 导出营销打卡管理列表

  // 商品推荐管理
  getProductRec: '/puGoodsRecommend/findList', // 获取商品推荐列表
  addProductRec: '/puGoodsRecommend/add', // 新增推荐商品
  editProductRec: '/puGoodsRecommend/update', // 编辑推荐商品
  getGoodsSource: '/puGoodsRecommend/getGoodsSource', // 获取商品来源下拉数据
  getPositionCode: '/puGoodsRecommend/getPositionCode', // 获取商品推荐位置
  // 合并缴费订单管理
  getCombinedPayOrder: '/yzNewOrder/findYzNewOrderList', // 获取合并缴费订单列表
  exportYzNewOrder: '/yzNewOrder/exportYzNewOrder', // 获取合并缴费订单列表导出
  getChildPayDetail: '/yzNewOrder/findYzNewOrderDetailedList', // 获取子订单详情

  // 营销管理-优惠券
  getCouponList: '/puCoupon/list', // 获取优惠卷列表
  updateCouponState: '/puCoupon/updateCouponState', // 获取优惠卷状态
  getCouponDetail: '/puCoupon/get', // 获取优惠卷明细
  getCouponReceiveList: '/puCoupon/getCouponReceiveList', // 优惠卷领取记录接口
  addCoupon: '/puCoupon/addCoupon', // 新增优惠卷
  updateCoupon: '/puCoupon/updateCoupon', // 编辑优惠卷
  updateCouponStock: '/puCoupon/updateCouponStock', // 修改优惠卷库存
  scaleDetailed: '/orderCoupon/scaleDetailed', // 交易对账订单优惠券抵扣明细

  pieList: '/pieRoll/pieList', // 派卷子活动列表
  findBaseList: '/puCoupon/findBaseList', // 查询优惠卷下拉列表
  getReceiveDetailList: '/pieRoll/getReceiveDetailList', // 子活动下的领取详情
  getEmpNameList: '/recruiter/getEmpNameList', // 下拉老师列表
  updatePieRollChild: '/pieRoll/updatePieRollChild', // 派卷子活动编辑（状态）
  addPieRoll: '/pieRoll/add', // 创建派卷活动

  summaryByCouponId: '/puCouponStatistic/summaryByCouponId', // 获取单个优惠卷汇总统计数据
  listByCouponId: '/puCouponStatistic/listByCouponId', // 获取一个优惠卷统计数据列表分页
  listFigureByCouponId: '/puCouponStatistic/listFigureByCouponId', // 获取一个优惠卷统计数据列表图
  summaryCoupon: '/puCouponStatistic/summary', // 获取优惠卷汇总统计数据
  listCoupon: '/puCouponStatistic/list', // 获取优惠卷总统计数据列表图分页
  listFigureCoupon: '/puCouponStatistic/listFigure', // 获取优惠卷总统计数据列表图
  getTeacherList: '/sendCoupon/getTeacherList.do', // 获取老师信息
  selectDepartList: '/dep/selectList.do', // 获取部门信息
  getCouponRemindList: '/maturity/getCouponRemindList', // 获取优惠券过期提醒任务列表
  addCouponRemindTask: '/maturity/addCouponRemindInfo', // 新增优惠券过期提醒
  handleCouponReminEnable: '/maturity/updateCouponRemindInfo',

  // 单本读书计划
  addTeacher: '/ledRead/add', // 添加领读人
  editTeacher: '/ledRead/update', // 编辑领读人
  getLedReadInfo: '/ledRead/get',
  getledReadList: '/ledRead/findList', // 获取领读人列表
  ledReadUpdateStatus: '/ledRead/updateStatus', // 领读人状态更新
  getSingleBook: '/courseReadSingle/findList', // 获取单本读书计划
  getLedReadAllowList: '/ledRead/getAllowList', // 领读人下拉列表
  addSingleBook: '/courseReadSingle/addReadSingCourse', // 新增单本读书
  editSingleBook: '/courseReadSingle/updateReadSingCourse', // 编辑单本读书
  getSingleBookInfo: '/courseReadSingle/get', // 获取单本读书计划详情
  updateSingleBookStatus: '/newCourse/updateCourseStatus', // 修改课程状态
  updateSingleBookSort: '/newCourse/updateCourseSort',
  getSingleBookClassHour: '/classHour/findList', // 获取单本读书计划课时列表
  addSingleBookClassHour: '/classHour/addClassHour',
  editSingleBookClassHour: '/classHour/updateClassHour',
  getSingleBookClassHourInfo: '/classHour/get',
  getSingleBookDirectoryId: '/courseDirectory/getDirectoryIdByCourseId', // 获取课程目录id
  operationClassHourSort: '/classHour/operationClassHourSort', // 更新课时排序
  updateSingleBookClassStatus: '/classHour/updateStatus',

  // 赠送管理
  getGiveList: '/puMarketGive/getList', // 获取赠送列表数据
  getUserList: '/userBaseInfo/selectUserInfo', // 获取用户
  addMarketGive: '/puMarketGive/addMarketGive', // 添加送卷
  updateGiveRemark: '/puMarketGive/updateRemark', // 更新备注

  // 学霸卡
  getGoodShelfList: '/GoodShelfFree/list', // 获取赠送列表数据
  addGoodsFree: '/GoodShelfFree/addGoodsFree', // 新增关联课程
  deleteGoodsFree: '/GoodShelfFree/deleteGoodsFree', // 删除关联课程

  // 训练营
  getTrainCampList: '/TrainCamp/list', // 训练营列表
  addTrainCamp: '/TrainCamp/add', // 添加训练营
  editTrainCamp: '/TrainCamp/edit', // 编辑训练营
  trainCampStatus: '/TrainCamp/editStatus', // 训练营状态
  getTrainFindSingle: '/TrainCamp/findSingle', // 训练营查询单个
  getTrainHourList: '/TrainClassHour/list', // 训练营课时列表
  addTrainHourList: '/TrainClassHour/add', // 训练营课时添加
  editTrainHourList: '/TrainClassHour/edit', // 训练营课时编辑
  findClassHour: '/TrainClassHour/findClassHour', // 课时查询单个
  findClassExamPaper: '/TrainClassHour/findExamPaper', // 课时试卷列表查询

  getTrainLevelList: '/TrainLevel/list', // 关卡列表展示
  editTrainLevelContent: '/TrainClassHour/editLevelContent', // 关卡文案
  addTrainLevel: '/TrainLevel/add', // 关卡保存
  editTrainLevel: '/TrainLevel/edit', // 关卡编辑
  getTrainReviewList: '/TrainReview/list', // 点评设置查询
  addTrainReview: '/TrainReview/add', // 点评添加
  editTrainReview: '/TrainReview/edit', // 点评编辑
  getTrainReviewFindSingle: '/TrainReview/findSingle', // 查询单个点评
  editTrainingUrlAndLevel: '/TrainCamp/editTrainingUrlAndLevel', // 训练营编辑封面加周期

  getTrainUserSign: '/TrainUserSign/list', // 训练营学习人数列表
  trainUserSignFindSingle: '/TrainUserSign/findSingle', // 训练营学习个人具体数据

  getTrainUserRefundList: '/TrainCamp/getTrainUserRefundList', // 训练营自动退费展示列表

  // 分销管理
  distributionList: '/distribution/distributionList', // 分销商品列表
  addDistribution: '/distribution/addDistribution', // 添加分销商品
  findSingleDistribution: '/distribution/findSingleDistribution', // 单个分销商品
  findShelfGoodsList: '/distribution/findShelfGoodsList', // 分销上架商品查询
  getMemberCardList: '/distribution/getMemberCardList', // 分销上架会员卡查询
  editMaterial: '/distribution/editMaterial', // 素材管理
  distributionOrderList: '/distributionOrder/distributionOrderList', // 分销订单列表
  exportOrder: '/distributionOrder/exportOrder.do', // 订单导出
  batchMarkOrder: '/distributionOrder/batchMarkOrder', // 批量标记结算
  importSettleMoney: '/distributionOrder/importSettleMoney', // 导入已结算佣金
  distributionOrderSettle: '/distributionOrder/distributionOrderSettle', // 分销总额
  getGoodsShareMaterial: '/distributionMaterial/list.do', // 分销商品素材列表
  getGoodsShareMaterialDetails: '/distributionMaterial/get.do',
  addGoodsShareMaterial: '/distributionMaterial/add.do', //  添加分销商品素材
  editGoodsShareMaterial: '/distributionMaterial/update.do', // 编辑分销商品素材
  deleteGoodsShareMaterial: '/distributionMaterial/del.do', // 删除分销商品素材
  updateMaterialState: '/distributionMaterial/updateState.do', // 更改分销素材状态
  editMaterialSort: '/distributionMaterial/operationMaterialSort.do', // 修改素材排序

  distributionFindSingle: '/distributionStatistics/findSingle', // 查询单个商品统计
  distributionFindSingleList: '/distributionStatistics/findSingleList', // 查询单个商品统计详细列表
  distributionSingleChartList: '/distributionStatistics/findSingleListChart', // 查询单个商品统计详细图表
  distributionFindAll: '/distributionStatistics/findAll', // 全部商品统计
  distributionFindAllList: '/distributionStatistics/findAllList', // 全部商品统计列表
  distributionAllChartList: '/distributionStatistics/findAllListChart', // 全部商品统计列表图表

  shortLinkList: '/short/shortLinkList', // 短链接列表
  editShortLink: '/short/editShortLink', // 短链接编辑
  createShortLink: '/short/createShortLink', // 短链接新增

  // 上进打卡管理
  getProgressClockList: '/scholarshipChallenge/getZXlist', // 获取上进打卡管理列表
  getRemind: '/scholarshipChallenge/remind', // 提醒
  getGiveNum: '/scholarshipChallenge/giveNum', // 赠送
  getEliminate: '/scholarshipChallenge/eliminate', // 剔除
  resetChallenge: '/scholarshipChallenge/resetChallenge', // 重置
  setFinishChallenge: '/scholarshipChallenge/setFinishChallenge', // 达成
  getMktActList: '/scholarshipChallenge/getList', // 获取优惠类型搜索列表
  updateTeachRemark: '/scholarshipChallenge/updateTeachRemark', // 更新老师备注（邀约/推荐管理）
  updateAdminRemark: '/scholarshipChallenge/updateAdminRemark', // 更新运营备注（邀约/推荐管理）
  updateTeachRemark2: '/scholarshipChallenge/updateTeachRemark2', // 更新老师备注
  updateAdminRemark2: '/scholarshipChallenge/updateAdminRemark2', // 更新运营备注
  getCompleteMobile: '/userBaseInfo/selectMobileByUserId', // 获取不加密的手机号码

  setSuperPlusWeChatLink: '/studycard/updateStudycard.do', // 设置企微链接
  getSuperPlusWeChatLink: '/studycard/getStudycardUrl.do',
  getAllowanceStudycardUrl: '/studycard/getAllowanceStudycardUrl', // 设置企微链接
  updateAllowanceStudycard: '/studycard/updateAllowanceStudycard', // 保存企微链接

  performanceList: '/distributionPerformance/performanceList.do', // 分销人明细
  performanceExport: '/distributionPerformance/performanceExport.do', // 分销人明细导出
  getOperateUserList: '/distributionUser/getOperateUserList.do', // 官方运营人员列表
  updateOperateUserStatus: '/distributionUser/updateOperateUserStatus.do', // 官方运营人员列表启用禁用
  getOperateUserAddList: '/distributionUser/getOperateUserAddList.do', // 官方运营人员添加列表
  addOperateUser: '/distributionUser/addOperateUser.do', // 官方运营人员添加
  distributionOrderRemark: '/distributionOrder/distributionOrderRemark', // 分销订单添加备注
  querySJSocietyAudits: '/newAudit/querySJSocietyAudits.do', // 职业教育产品系数配置
  queryByGoodsShelfId: '/newAudit/queryByGoodsShelfId', // 职业教育产品系数详情
  saveOrUpdate: '/newAudit/saveOrUpdate.do', // 职业教育产品系数新增或修改（通过传id、auditId来区分，不传就是新增）
  findGoodsSelectList: '/distribution/findGoodsSelectList', // 分销商品下拉框
  queryPersonalOrderRecordsPage:
    '/monthlyPerformance/sjSociety/queryPersonalOrderRecordsPage.do', // 职业教育订单
  queryEmployeePerformancePage:
    '/monthlyPerformance/sjSociety/queryEmployeePerformancePage.do', // 职业教育月度绩效
  queryEmployeePerformanceDetails:
    '/monthlyPerformance/sjSociety/queryEmployeePerformanceDetails.do', // 助学老师的职业教育月度绩效明细
  queryEmployeePersonalPerformanceDetails:
    '/monthlyPerformance/sjSociety/queryEmployeePersonalPerformanceDetails.do', // 个人直招绩效详情
  queryEmployeeTeamPerformanceDetails:
    '/monthlyPerformance/sjSociety/queryEmployeeTeamPerformanceDetails.do', // 团队绩效详情
  queryOrderRecordsPage:
    '/monthlyPerformance/sjSociety/queryOrderRecordsPage.do', // 订单明细
  offsiteEdit: '/allStudent/offsiteEdit', // 异地附件保存

  campList: '/newTrainCamp/campList', // 训练营--训练营列表页
  addCamp: '/newTrainCamp/addCamp', // 训练营--新增训练营
  updateCamp: '/newTrainCamp/updateCamp', // 训练营--编辑训练营
  updateCampStatus: '/newTrainCamp/updateCampStatus', // 训练营--更改训练营状态
  barrierList: '/trainCampBarrier/barrierList', // 关卡--关卡分页列表
  updateBarrierStatus: '/trainCampBarrier/updateBarrierStatus', // 关卡--更改关卡状态
  addBarrier: '/trainCampBarrier/addBarrier', // 关卡--关卡新增
  updateBarrier: '/trainCampBarrier/updateBarrier', // 关卡--编辑关卡
  editBarrierSort: '/trainCampBarrier/editBarrierSort', // 关卡--编辑关卡顺序接口
  giftList: '/trainCampGift/giftList.do', // 礼物--通关礼物列表
  editStatus: '/trainCampGift/editStatus.do', // 礼物--礼物禁用、启用
  addGift: '/trainCampGift/addGift.do', // 礼物--礼物添加
  selGift: '/trainCampGift/selGift.do', // 礼物--礼物编辑前查询
  editGift: '/trainCampGift/editGift.do', // 礼物--礼物编辑

  checkedList: '/tcTargetStudent/checkedList.do', // 目标学员--查看目标学员列表
  targetStudent: '/targetStudent/list.do', // 目标学员--添加目标学员列表
  addStu: '/targetStudent/addStu.do', // 目标学员--添加选中
  cleanStu: '/targetStudent/cleanStu.do', // 目标学员--清除选中
  addAllStu: '/targetStudent/addAllStu.do', // 目标学员--添加全部
  cleanAllStu: '/targetStudent/cleanAllStu.do', // 目标学员--清除全部

  exportCheck: '/tcTargetStudent/checkExport.do', // 目标学员--导出选中
  exportAll: '/tcTargetStudent/allExport.do', // 目标学员--导出全部
  stuBarrierList: '/tcTargetStudent/stuBarrierList.do', // 目标学员--学员关卡详情列表

  // 广告模块
  getAdOrderList: '/puTempTrade/getOrderList.do', // 获取从投放广告里面购买的课程订单
  exportAdOrderList: '/puTempTrade/orderExport.do', // 导出广告订单列表
  getAdProductSelect: '/puTempTrade/getProductSelect.do', // 获取投放的广告商品下拉选择数据

  // 广告投放链接配置模块
  getAdConfigList: '/putInConf/findList', // 获取投放链接配置列表
  getAdTagList: '/putInConf/getTagList', // 获取投放链接配置列表
  addAdPutInConf: '/putInConf/addPutInConf', // 新增投放链接配置列表
  updateAdPutInConf: '/putInConf/updatePutInConf', // 编辑投放链接配置列表
  getHuoKeLinkSelect: '/putInConf/getQwContactList.do', // 获取广告获客链接下拉选择数据
  ...evaluation,

  // SCRM设置
  getScrmHolderList: '/assistConfig/getAssistConfigList.do', // 获取协助人设置列表
  exportScrmHolderList: '/assistConfig/exportAssistConfig.do', // 导出协助人

  // 五大时刻
  getMedaTimelList: '/bmsAdmin/medalTimeList.do', // 勋章列表
  addMedalTime: '/bmsAdmin/addMedalTime.do', // 勋章新增
  updatMedalTime: '/bmsAdmin/updateMedalTime.do', // 勋章修改
  getUserMedalTimeList: '/bmsAdmin/userMedalTimeList.do', // 用户勋章管理列表
  updateUserMedalTime: '/bmsAdmin/updateUserMedalTime.do', // 用户勋章状态调整
  getUserMedalTimeGiftList: '/bmsAdmin/userMedalTimeGiftList.do', //  用户勋章赠送列表
  updateUserMedalRemark: '/bmsAdmin/updateUserMedalRemark.do', // 用户勋章赠送备注
  getMedalTimeExportList: '/bmsAdmin/medalTimeExportList.do', // 是否可领取的勋章列表
  medalTimeExport: '/bmsAdmin/medalTimeExport.do', // 用户勋章赠送导入
  getGiveUseInfo: '/couponGiveRecord/getSummaryUsInfo.do', // 查询: 赠送用户信息
  // 新手任务
  getNewbieTaskConfigList: '/newbietask/config/list.do', // 新手任务-列表
  addNewbieTaskConfig: '/newbietask/config/add.do', // 新手任务-新增
  updateNewbieTaskConfig: '/newbietask/config/edit.do', // 新手任务-编辑
  addNewbieTask: '/newbietask/add.do', // 新手任务--配置新增
  updateNewbieTask: '/newbietask/edit.do', // 新手任务--配置修改
  sortEditNewbieTask: '/newbietask/config/sort/edit.do', // 新手任务-排序编辑
  newbieTaskLog: '/operate/common/list.do', // 新手任务--新手任务明细操作记录
  getNewbieTaskUserList: '/newbietask/user/list.do', // 新手任务--用户列表
  newbieTaskUserblack: '/newbietask/user/black.do', // 新手任务-用户拉黑
  newbieTaskUserdetail: '/newbietask/user/detail/list.do', // 新手任务--用户完成明细列表

  // 职业教育
  getEduStudentList: "/recruit/findZyjyStudents", // 学员列表
  updateEduStudentRemark: "/recruit/distributionOrderRemark", // 修改学员备注
  getEduUserMobile: "/recruit/selectMobileByUserId", // 获取学员手机号
  getEduUserList: "/recruit/selInvList", // 邀约人
  getEduDepartmentList: "/recruit/depList", // 邀约人部门
  getEduDistributionList: "/recruit/distributionList", // 商品列表

  // 工单管理
  addWorkOrder: "/workOrder/addWorkOrder", // 新增工单
  getWorkOrderDict: "/workOrder/dict", // 工单字典
  getStdIdList: "/workOrder/findByStdId", // 学业编码列表
  getyzCodeList: "/workOrder/findByYzCode", // 远智编码列表
  getHandlerList: "/employ/getEmpDpList", // 受理人列表
  getCreateEmpList: "/workOrder/createEmpList", // 已有发起人列表
  getReceiveEmpList: "/workOrder/receiveEmpList", // 已有受理人列表
  getConsultList: "/workOrder/consultList", // 咨询工单列表
  getComplaintList: "/workOrder/complaintList", // 投诉工单列表
  getRefundList: "/workOrder/refundList", // 退款工单列表
  getConsultDetail: "/workOrder/consultDetail", // 咨询工单详情
  getComplaintDetail: "/workOrder/complaintDetail", // 投诉工单详情
  editWorkOrderDetail: "/workOrder/editDetail", // 编辑工单详情
  transferWorkOrder: "/workOrder/transfer", // 转交工单
  transferBatchWorkOrder: "/workOrder/transferBatch", // 批量转交工单
  hangWorkOrder: "/workOrder/hang", // 挂起工单
  finishWorkOrder: "/workOrder/finish", // 完成工单
  refundWorkOrder: "/workOrder/refund", // 退款工单
  commentWorkOrder: "/workOrder/comment", // 回复工单
  isStudentExist: "/workOrder/isStudentExist", // 校验学员系统是否存在学生
  editWorkOrderDetail: "/workOrder/editDetail", // 编辑工单详情（包含学生手机号、是否回函等等）
  exportConsult: "/workOrder/exportConsult", // 导出咨询工单
  exportComplaint: "/workOrder/exportComplaint", // 导出投诉工单
  exportRefund: "/workOrder/exportRefund", // 导出退款工单
  deleteWorkOrder: "/workOrder/deleteWorkOrder", // 删除工单

  getEduStudentList: '/recruit/findZyjyStudents', // 学员列表
  updateEduStudentRemark: '/recruit/distributionOrderRemark', // 修改学员备注
  getEduUserMobile: '/recruit/selectMobileByUserId', // 获取学员手机号
  getEduUserList: '/recruit/selInvList', // 邀约人
  getEduDepartmentList: '/recruit/depList', // 邀约人部门
  getEduDistributionList: '/recruit/distributionList', // 商品列表

  // 礼品管理
  giftGivingOrderPage: '/giftGivingOrder/page', // 礼物发放订单
  getConsignorPage: '/giftGivingOrder/consignor/page', // 送礼人
  getConsigneePage: '/giftGivingOrder/consignee/page', // 收礼人
  addGivingOrder: '/giftGivingOrder/add', // 礼物发放新增订单
  updateGivingOrder: '/giftGivingOrder/update/{id}', // 礼物发放编辑订单
  getGivingOrderDetail: '/giftGivingOrder/get/{id}', // 礼物发放订单详情
  giftGivingOrderExport: '/giftGivingOrder/export', // 礼物发放订单导出
  giftGivingOrderConfig: '/giftGivingOrder/config', // 礼物发放订单富文本
  updateGiftGivingOrderConfig: '/giftGivingOrder/config/{id}', // 礼物发放订单更新富文本
  cancelGiftGivingOrder: '/giftGivingOrder/cancel/{id}', // 礼物发放订单取消
  updateGiftGivingOrderLogisticsNo: '/giftGivingOrder/update/{id}/logisticsNo', // 礼物发放订单物流单号
  getProductPage: '/product/page', // 商品下拉框
  getProductDetail: '/product/getById/{id}' // 商品详情

};

// const optionDefaultParams = { page: 1, rows: 10 };

export const getScholarship = () => axios.post(api.getScholarship, {});

export const request = (key, data, config) =>
  axios.post(api[key], data, config);
