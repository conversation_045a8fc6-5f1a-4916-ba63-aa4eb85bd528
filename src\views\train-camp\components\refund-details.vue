<template>
  <common-dialog
    is-full
    width="800px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='用户名称' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='退费状态' prop='refundState'>
          <el-select v-model="form.refundState" clearable>
            <el-option label="待退费" value="1" />
            <el-option label="退费成功" value="2" />
            <el-option label="退费失败" value="3" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <div class="dialog-main">
        <!-- 表格 -->
        <el-table
          v-loading="tableLoading"
          border
          size="small"
          style="width: 100%"
          header-cell-class-name='table-cell-header'
          :data="tableData"
        >
          <el-table-column prop="trainingName" label="训练营名称" align="center" />
          <el-table-column prop="userName" label="用户名称" align="center" />
          <el-table-column prop="orderNo" label="订单号" align="center" />
          <el-table-column prop="mobile" label="手机号" align="center" />
          <!-- <el-table-column label="推送状态" align="center" prop="isSend">
            <template slot-scope="scope">
              {{ scope.row.isSend === '1' ? '未推送' :'已推送' }}
            </template>
          </el-table-column> -->

          <el-table-column prop="payAmount" label="实缴金额" align="center" />
          <el-table-column prop="zmScale" label="智米抵扣" align="center" />
          <el-table-column prop="demurrageScale" label="滞留金抵扣" align="center" />
          <el-table-column prop="refundAmount" label="实缴退费金额" align="center" />
          <el-table-column prop="refundZm" label="智米退费金额" align="center" />
          <el-table-column prop="refundDemurrage" label="滞留金退费金额" align="center" />
          <el-table-column label="挑战成功时间" align="center" prop="challengeSuccessTime">
            <template slot-scope="scope">
              {{ scope.row.challengeSuccessTime | transformTimeStamp }}
            </template>
          </el-table-column>
          <el-table-column label="退费时间" align="center" prop="challengeSuccessTime">
            <template slot-scope="scope">
              <span v-if="scope.row.refundState !=='1' ">{{ scope.row.refundTime | transformTimeStamp }}</span>
            </template>
          </el-table-column>
          <el-table-column label="退费状态" align="center" prop="refundState">
            <template slot-scope="scope">
              {{ scope.row.refundState | refundStateChange }}
            </template>
          </el-table-column>
          <el-table-column label="退费失败原因" align="center" prop="refundRemark" />
        </el-table>
      </div>
    </div>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
  </common-dialog>
</template>

<script>
// import { handleDateControl } from '@/utils';
export default {
  components: {},
  filters: {
    refundStateChange(val) {
      if (!val) return '';
      const obj = {
        '1': '待退费',
        '2': '退费成功',
        '3': '退费失败'
      };
      return obj[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    trainingId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      title: '自动退费详情',
      tableLoading: false,
      isEditLock: false,
      lockStatus: '1',
      form: {
        userName: '',
        trainingName: '',
        refundState: '',
        orderNo: '',
        mobile: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      // const date = handleDateControl(formData.time);
      // formData.startTime = date[0];
      // formData.endTime = date[1];
      // delete formData.time;
      const data = {
        ...formData,
        trainingId: this.row.trainingId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      console.log(this.row);
      const data = this.handleQueryParams();
      this.$post('getTrainUserRefundList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    open() {
      console.log(this.trainingId, 'trainingId');
      this.getTableList();
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
