<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="100px"
      @submit.native.prevent="onSearch"
    >
      <el-form-item label="社团名称" prop="clubsActName">
        <el-input v-model="form.clubsActName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="远智编码" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="是否领队" prop="ifInitiator">
        <el-select v-model="form.ifInitiator" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="社团类型" prop="clubsActType">
        <el-select v-model="form.clubsActType" placeholder="请选择">
          <el-option
            v-for="item in DClubsActType"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="姓名" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入" clearable />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="onSearch('reset')"
        />
      </div>
    </el-form>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="loading"
      border
      size="small"
      class="table-container"
      :data="list"
    >
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <el-table-column prop="realName" label="姓名" align="center" />
      <el-table-column
        label="社团类型"
        align="center"
        :formatter="handleClubsActType"
      />
      <el-table-column prop="clubsActName" label="社团名称" align="center" />
      <el-table-column label="是否领队" align="center">
        <template #default="{ row }">
          <div>{{ row.ifInitiator == 1 ? "是" : "否" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="加入时间" align="center" />
      <!-- <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onTop(row)">
            {{ row.ifTop === 1 ? "取消置顶" : "置顶" }}
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="total"
        :page.sync="pager.pageNum"
        :limit.sync="pager.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script>
import { ClubsActType as DClubsActType } from '@/dict';
export default {
  data() {
    return {
      form: {
        clubsActName: '',
        yzCode: '',
        ifInitiator: '',
        clubsActType: '',
        realName: ''
      },
      DClubsActType,
      loading: true,
      list: [],
      pager: { pageNum: 1, pageSize: 10 },
      total: 0
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.loading = true;
        const params = { ...this.pager, ...this.form };
        const { code, body } = await this.$http({
          method: 'post',
          url: '/clubsActivityInfo/expertList',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.total = body.recordsTotal;
        this.list = body.data;
      } finally {
        this.loading = false;
      }
    },
    // 处理社团类型显示
    handleClubsActType(row) {
      return DClubsActType.find((v) => v.dictValue === row.clubsActType)
        ?.dictName;
    },
    // 搜索
    onSearch(type) {
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 置顶 | 取消置顶
    onTop(row) {
      const isToped = row.ifTop === 1;
      this.$confirm('', `您确定${isToped ? '取消置顶' : '置顶'}此达人？`, { type: 'warning', center: true }
      )
        .then(async(e) => {
          if (e !== 'confirm') return;
          this.loading = true;
          const { code } = await this.$http({
            method: 'post',
            url: `/clubsActivityInfo/topExpert`,
            data: {
              id: row.id,
              ifTop: isToped ? 0 : 1
            },
            json: true
          });
          this.loading = false;
          if (code !== '00') return;
          this.$message.success(`${isToped ? '取消置顶' : '置顶'}成功`);
          this.getData();
        })
        .catch(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0 30px;
}
</style>
