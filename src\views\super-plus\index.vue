<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='课程名称' prop='goodsShelfName'>
        <el-input v-model="form.goodsShelfName" placeholder="请输入" />
      </el-form-item>

      <!-- <el-form-item label='下拉' prop='demo2'>
        <el-select v-model="form.demo2" clearable>
          <el-option label="测试" value="1" />
        </el-select>
      </el-form-item>

      <el-form-item label='时间控件' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item> -->

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" @click="SetUpTeacher = true">设置补贴课程老师企微</el-button>
      <el-button type="primary" size="small" @click="slVisibel = true">设置老师企微</el-button>
      <el-button
        type="success"
        size="small"
        @click="adVisibel = true"
      >添加</el-button>
      <el-button type="danger" size="small" icon="el-icon-turn-off" plain @click="updateStatus(2)">批量移除</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="goodsShelfName" label="课程名称" align="center" />
      <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="menuMove(scope.row)">移除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <add-dialog :visible.sync="adVisibel" />
    <set-link-dialog :visible.sync="slVisibel" />
    <set-link-dialogs :visible.sync="SetUpTeacher" />

  </div>
</template>
<script>
import addDialog from './add-dialog';
import setLinkDialog from './set-link-dialog';
import setLinkDialogs from './set-link-dialogs';
export default {
  components: {
    addDialog,
    setLinkDialog,
    setLinkDialogs
  },
  data() {
    return {
      tableLoading: false,
      form: {
        goodsShelfName: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      adVisibel: false,
      slVisibel: false,
      SetUpTeacher: false
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getGoodShelfList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    // 单个移除
    menuMove(item) {
      this.$post('deleteGoodsFree', [{ id: item.id }], { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },

    // 批量移除
    updateStatus(status) {
      if (this.selects?.length > 0) {
        const data = [];
        this.selects.map(item => {
          data.push({
            id: item.id + ''
          });
        });
        this.$post('deleteGoodsFree', data, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
          this.getTableList();
        });
      } else {
        this.$message.error('请勾选数据');
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
}
</style>
