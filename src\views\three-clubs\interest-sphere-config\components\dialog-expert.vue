<template>
  <common-dialog class="common-dialog" width="60%" title="配置达人" :visible.sync="show" @close="close">
    <!-- 表单 -->
    <el-form
      ref="form"
      size="mini"
      label-width="100px"
      class="yz-search-form"
      :model="form"
    >
      <el-form-item label="远智编码" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="是否达人" prop="ifExpert">
        <el-select v-model="form.ifExpert" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model.number="form.mobile" maxlength="11" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="跟进人姓名" prop="empName">
        <el-select
          v-model="form.empName"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="请输入"
          :remote-method="empNameRemoteMethod"
          :loading="empNameLoading"
        >
          <el-option v-for="item in empNameOpts" :key="item.empId" :label="item.empName" :value="item.empName" />
        </el-select>
      </el-form-item>

      <!-- <div class="search-reset-box m-r-10">
      </div> -->

    </el-form>

    <div v-loading="tableLoading">

      <!-- 按钮区 -->
      <div class="m-t-10 m-l-10 m-b-10">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click="search('reset')">重置</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-circle-plus-outline" @click="onAddExpert">添加为达人</el-button>
        <el-button size="mini" type="danger" plain icon="el-icon-remove-outline" @click="onRemoveExpert">移除达人</el-button>
      </div>

      <div class="m-l-10 m-r-10">
        <!-- 表格 -->
        <el-table
          ref="table"
          border
          size="small"
          style="width: 100%;"
          header-cell-class-name="table-cell-header"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50" />

          <el-table-column prop="ifExpert" label="是否达人" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.ifExpert == 1" size="mini"> 是</el-tag>
              <el-tag v-else size="mini" type="info"> 否 </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="realName" label="姓名" align="center" />
          <el-table-column prop="yzCode" label="远智编码" align="center" />
          <el-table-column prop="empName" label="跟进人" align="center" />
        </el-table>
      </div>
      <!-- 分页区 -->
      <div class="yz-table-pagination m-b-10">
        <pagination
          :total='pager.total'
          :page.sync="pager.pageNum"
          :limit.sync="pager.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
/** 达人配置类型： 1: 添加为达人 2: 移除达人 */
const CHANGE_TYPE = {
  ADD: 1,
  REMOVE: 2
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      form: {
        yzCode: '',
        mobile: '',
        empName: '',
        ifExpert: ''
      },
      tableLoading: false,
      tableData: [],
      empNameLoading: false,
      empNameOpts: [],
      selectionList: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form, configId: this.row.id };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/circleConfig/getCircleExpert',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    search(type) {
      if (type === 'reset') {
        this.$refs.form.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // 选中用户
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    // 获取跟进人
    async empNameRemoteMethod(query) {
      if (!query) return;
      try {
        this.empNameLoading = true;
        const { code, body } = await this.$http({
          method: 'get',
          url: '/employ/getEmpList.do',
          params: { sName: query, page: 1, rows: 7 }
        });
        if (code !== '00') return;
        this.empNameOpts = body.data ?? [];
      } finally {
        this.empNameLoading = false;
      }
    },
    // 操作成员状态 新增|删除 达人
    async handleClubActMember(type) {
      if (this.tableLoading) return;
      try {
        this.tableLoading = true;
        const params = {
          configId: this.row.id,
          type,
          userIdList: this.selectionList.map(s => s.userId),
          learnIdList: this.selectionList.map(s => s.learnId)
        };
        const { code } = await this.$http({
          method: 'post',
          url: '/circleConfig/changeCircleExpert',
          data: params,
          json: true
        });
        if (code !== '00') {
          this.tableLoading = false;
          return;
        }
        this.$message.success(type === CHANGE_TYPE.ADD ? '添加成功' : '移除成功');
        this.getData();
      } catch (error) {
        console.log(error);
        this.tableLoading = false;
      }
    },
    // btn 添加为达人
    async onAddExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加为达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(CHANGE_TYPE.ADD);
        }).catch(() => { });
    },
    // btn 移除达人
    onRemoveExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(CHANGE_TYPE.REMOVE);
        }).catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.m-r-10 {
  margin-right: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}

.yz-search-form{
  padding-right: 0;
  @extend .m-t-10;
}

</style>
