<template>
  <common-dialog
    is-full
    width="600px"
    title="分类管理"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="success" size="small" @click="add">新增</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        height="calc(100vh - 175px)"
        :data="tableData"
      >
        <el-table-column prop="goodsTypeName" label="分类" align="center" />
        <el-table-column prop="status" label="是否启用" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1? 'success':'danger' ">
              {{ scope.row.status === 1 ? '启用':'禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="test" label="编辑" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="edit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <common-dialog
      :show-footer="true"
      width="600px"
      title="新增/编辑"
      :visible.sync='childDialog'
      @open="childOpen"
      @confirm="submit"
      @close='childClose'
    >
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label='分类名称' prop='goodsTypeName'>
            <el-input
              v-model="form.goodsTypeName"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label='是否启用' prop='status'>
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="2">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>

  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      childDialog: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      form: {
        goodsTypeName: '',
        status: ''
      },
      rules: {
        goodsTypeName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      currentEditRow: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    add() {
      this.currentEditRow = null;
      this.childDialog = true;
    },
    edit(row) {
      this.currentEditRow = row;
      this.childDialog = true;
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        goodsTypeLevel: 2,
        pGoodsTypeId: 3,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getClassificationList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    open() {
      this.getTableList();
    },
    childOpen() {
      if (this.currentEditRow) {
        this.getTypeInfo();
      }
    },
    // 编辑回显表单
    getTypeInfo() {
      const params = {
        goodsTypeId: this.currentEditRow.goodsTypeId
      };
      this.$post('getClassificationInfo', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.goodsTypeName = body.goodsTypeName;
            this.form.status = body.status;
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addClassification';
          const params = {
            goodsTypeLevel: '2',
            pGoodsTypeId: '3',
            sort: 1,
            ...this.form
          };

          if (this.currentEditRow) {
            apiKey = 'editClassification';
            params.goodsTypeId = this.currentEditRow.goodsTypeId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.childDialog = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.getTableList();
              }
            });
        }
      });
    },
    childClose() {
      this.childDialog = false;
      this.$refs.form.resetFields();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
