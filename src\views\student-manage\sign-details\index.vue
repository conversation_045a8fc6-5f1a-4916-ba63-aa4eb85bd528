<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='学业编码' prop='learnId'>
        <el-input v-model="form.learnId" placeholder="请输入学业编码" />
      </el-form-item>
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>
      <el-form-item label='学员姓名' prop='userName'>
        <el-input v-model="form.userName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='学员手机号' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="learnId" label="学业编码" align="center" width="120" />
      <el-table-column prop="yzCode" label="远智编码" align="center" width="120" />
      <el-table-column prop="userName" label="学员姓名" align="center" width="150px" />
      <el-table-column prop="mobile" label="手机号码" align="center" width="150px" />
      <el-table-column label="读书计划" align="center">
        <template slot-scope="scope">
          <el-table
            border
            size="small"
            style="width: 100%"
            header-cell-class-name='table-header'
            :data="scope.row.readPlanEnroBaseVOS"
          >
            <el-table-column prop="readPlanName" label="读书计划" align="center" />
            <el-table-column prop="semesterName" label="学期名称" align="center" />
            <el-table-column prop="enroTime" label="报名时间" align="center" />
            <el-table-column prop="userName" label="学习明细" align="center" width="200px">
              <template slot-scope="item">
                <el-button
                  class="padding-0"
                  type="text"
                  @click="seeDetails(item.row, scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>

        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <learning :visible.sync="visible" :row="row" :title="title" />

  </div>
</template>
<script>
import learning from '../learning-progress/index';
export default {
  components: {
    learning
  },
  filters: {
    platform(value) {
      if (!value) return;
      const dict = window.parent.dictJson;
      if (dict['platform']) {
        const data = dict['platform'].find(item => {
          return item.dictValue === value;
        });
        return data.dictName;
      }
      return value;
    }
  },
  data() {
    return {
      tableLoading: false,
      timer: null,
      form: {
        userName: '',
        mobile: '',
        learnId: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      visible: false,
      platform: [],
      row: null,
      title: ''
    };
  },
  mounted() {
    this.platform = this.$localDict['platform'];
    this.getTableList();
  },
  methods: {
    // 查看明细
    seeDetails(row, parentRow) {
      this.title = parentRow.userName + ' ' + row.readPlanName + '' + row.semesterName + ' ' + '打卡情况';
      this.row = row;
      this.visible = true;
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getReadPlanEnro', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          window.scrollTo(0, 0);
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  margin-bottom: 10px;
}

.padding-0 {
  padding: 0;
}

::v-deep .table-header {
  background: #ffffff;
}

</style>
