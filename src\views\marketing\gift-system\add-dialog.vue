<template>
  <common-dialog
    :show-footer="true"
    width="650px"
    title="新增"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label='赠送类型' prop='marketGiveType'>
          <el-select
            v-model="form.marketGiveType"
            filterable
            placeholder="请选择"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item in $localDict['giveType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="form.marketGiveType === 'coupon'"
          label='选择优惠卷'
          prop='mappingId'
        >
          <yz-select
            v-model="form.mappingId"
            url="/puCoupon/findBaseList.do"
            method="post"
            :props="{
              label: 'couponName',
              value: 'couponId',
              query: 'couponName'
            }"
            @change="handleMappingChange"
          />
        </el-form-item>

        <el-form-item
          v-if="form.marketGiveType === 'goods'"
          label='选择课程'
          prop='mappingId'
        >
          <remote-search-selects
            v-model="form.mappingId"
            :props="{
              apiName: 'getSetMealList',
              value: 'goodsShelfId',
              label: 'goodsShelfName',
              query: 'goodsShelfName'
            }"
            :param="{
              goodsShelfName:'',
              shelfStatus: 1
            }"
            @changeVal="handleMappingChange"
          />

        </el-form-item>

        <el-form-item
          v-if="form.marketGiveType === 'vip'"
          label='选择会员卡'
          prop='mappingId'
        >
          <yz-select
            v-slot="slotProps"
            v-model="form.mappingId"
            url="/meb/getMebSelectList"
            method="post"
            :props="{
              label: 'mebName',
              value: 'mebId',
              query: 'mebName'
            }"
            :data="{
              mebFreeType: 'free',
              status: 1
            }"
            @change="handleMappingChange"
          >
            <el-option
              v-for="item in slotProps.option"
              :key="item.mebId"
              :value="item.mebId"
              :label="item.mebName"
            >
              <span style="float: left">{{ item.mebName }}</span>
              <span class="card">{{ mebType[item.mebPayType] }}&nbsp;&nbsp;[ID: {{ item.mebId }}]</span>
            </el-option>
          </yz-select>
        </el-form-item>

        <el-form-item label='选择用户' prop='userIds'>
          <el-button type="primary" plain @click="userDialog = true">添加用户</el-button>
          <span class="ml-10">已选择 {{ selectedList.length }} 个用户</span>
        </el-form-item>

        <el-form-item label='备注' prop='remark'>
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
            rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

      </el-form>
    </div>

    <common-dialog
      :show-footer="true"
      width="1200px"
      title="选择用户"
      :visible.sync='userDialog'
      cancelText="返 回"
      @open="userDialogOpen"
      @confirm="userDialog = false"
      @close='userDialog = false'
    >
      <div class="dialog-main">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form
              ref='searchForm'
              :inline="true"
              size='mini'
              :model='userForm'
              label-width='60px'
              label-position="left"
              @submit.native.prevent='searchUser'
            >
              <el-form-item label='姓名' prop='realName'>
                <el-input v-model="userForm.realName" v-width="120" placeholder="请输入" />
              </el-form-item>

              <el-form-item label='手机号' prop='mobile'>
                <el-input v-model="userForm.mobile" v-width="120" placeholder="请输入" />
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  icon='el-icon-search'
                  native-type="submit"
                  size="mini"
                >搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click='search' />
              </el-form-item>
            </el-form>
            <div class="title">待选列表</div>
            <el-table
              v-loading="tableLoading"
              border
              size="small"
              style="width: 100%"
              :height="400"
              header-cell-class-name='table-cell-header'
              :data="waitingList"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="userName" label="用户名" align="center" />
              <el-table-column prop="mobile" label="手机号" align="center" />
              <el-table-column label="操作" align="center" width="150px">
                <template slot-scope="scope">
                  <div class="yz-button-area">
                    <el-button type="text" @click="handleAdd(scope.row, scope.$index)">添加</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
          <el-col :span="12">
            <div class="right-head">
              <el-button
                type="danger"
                size="mini"
                @click="batchDelete"
              >批量移除</el-button>
            </div>
            <div class="title">已选列表</div>
            <el-table
              border
              size="small"
              style="width: 100%"
              :height="400"
              header-cell-class-name='table-cell-header'
              :data="selectedList"
              @selection-change="handleSelected"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="userName" label="用户名" align="center" />
              <el-table-column prop="mobile" label="手机号" align="center" />
              <el-table-column label="操作" align="center" width="150px">
                <template slot-scope="scope">
                  <div class="yz-button-area">
                    <el-button type="text" @click="handleRemove(scope.row, scope.$index)">移除</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
    </common-dialog>
  </common-dialog>
</template>

<script>
import { checkInput } from '@/common/vali';
export default {
  filters: {
    mebType(val) {
      if (!val) return;
      const data = {
        year: '年卡',
        season: '季卡',
        mouth: '月卡',
        week: '周卡'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const checkUsers = (rule, value, callback) => {
      if (this.form.userIds.length <= 0) {
        return callback(new Error('请选择添加用户'));
      } else {
        callback();
      }
    };
    return {
      show: false,
      userDialog: false,
      tableLoading: false,
      form: {
        userIds: [],
        mappingId: '',
        mappingName: '',
        marketGiveType: 'vip',
        remark: ''
      },
      userForm: {
        realName: '',
        mobile: ''
      },
      rules: {
        userIds: [
          { required: true, validator: checkUsers, trigger: 'change' }
        ],
        marketGiveType: [
          { required: true, trigger: 'change', message: '请选择' }
        ],
        mappingId: [
          { required: true, trigger: 'change', message: '请选择' }
        ],
        actName: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ]
      },
      waitingList: [],
      selectedList: [],
      rightTableSelects: [],
      mebType: {
        year: '年卡',
        season: '季卡',
        mouth: '月卡',
        week: '周卡'
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleTypeChange() {
      this.form.mappingName = '';
      this.form.mappingId = '';
    },
    search() {
      this.$refs['searchForm'].resetFields();
    },
    // 单个删除
    handleRemove(row, index) {
      this.selectedList.splice(index, 1);
    },
    // 批量移除
    batchDelete() {
      if (this.rightTableSelects.length > 0) {
        const rowIndexs = [];
        this.rightTableSelects.forEach(row => {
          this.selectedList.find((item, index) => {
            if (row.userId === item.userId) {
              rowIndexs.push(index);
            }
          });
          rowIndexs.forEach(e => {
            this.selectedList.splice(e, 1);
          });
        });
      } else {
        this.$message.error('请选择数据后进行操作');
      }
    },
    handleSelected(selects) {
      this.rightTableSelects = selects;
    },
    handleAdd(row, index) {
      // eslint-disable-next-line no-unused-vars
      let isAdd = false; // 是否已经添加
      for (let i = 0; i < this.selectedList.length; i++) {
        const item = this.selectedList[i];
        if (item.userId === row.userId) {
          isAdd = true;
          break;
        }
      }

      if (!isAdd) {
        const data = JSON.parse(JSON.stringify(row));
        this.selectedList.push(data);
        this.waitingList.splice(index, 1);
      } else {
        this.$message.error('你已经添加此用户！！！');
      }
    },
    searchUser() {
      this.waitingList = [];
      const data = {
        ...this.userForm
      };
      this.$post('getUserList', data)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            if (body) {
              this.waitingList.push(body);
            }
            console.log(body);
          }
        });
    },
    handleMappingChange({ value, label, source }) {
      // this.form.mappingId = value;
      this.form.mappingName = label;
    },
    open() {},
    // 用户弹窗打卡回调
    userDialogOpen() {},
    submit() {
      this.form.userIds = this.selectedList.map(item => item.userId);
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$confirm('确认赠送？一经赠送将无法撤销，请谨慎操作', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const params = {
              ...this.form
            };

            this.$post('addMarketGive', params, { json: true })
              .then(res => {
                const { fail } = res;
                if (!fail) {
                  this.$message({
                    message: '操作成功',
                    type: 'success'
                  });
                  this.$emit('refresh');
                  this.show = false;
                }
              });
          });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.right-head {
  text-align: right;
  height: 47px;
}
.title {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.card {
  float: right;
  color: #8492a6;
  font-size: 13px;
}

</style>
