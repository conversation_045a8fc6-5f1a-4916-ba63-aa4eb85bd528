<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    :title="row?'编辑':'新建'+ '短链'"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          label-width="120px"
          :rules="rules"
        >
          <el-form-item label='原链接' prop='originalUrl'>
            <el-input
              v-model="form.originalUrl"
              :disabled='isEdit'
            />
          </el-form-item>
          <el-form-item label='短链名称' prop='title'>
            <el-input
              v-model="form.title"
            />
          </el-form-item>
          <el-form-item label='是否启用' prop='state'>
            <el-radio-group v-model="form.state">
              <el-radio label="1">启用</el-radio>
              <el-radio label="2">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div></common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      isEdit: false,
      form: {
        title: '',
        originalUrl: '',
        state: '1'
      },
      rules: {
        originalUrl: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        title: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      console.log(this.row);
      if (this.row) {
        this.form.originalUrl = this.row.originalUrl;
        this.form.title = this.row.title;
        this.form.state = this.row.state;
        this.isEdit = true;
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'createShortLink';
          const params = {
            ...this.form,
            appId: '555'
          };

          if (this.row) {
            apiKey = 'editShortLink';
            params.courseId = this.row.courseId;
            params['id'] = this.row.id + '';
          }

          this.$post(apiKey, params)
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$parent.getTableList();
              }
            });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
