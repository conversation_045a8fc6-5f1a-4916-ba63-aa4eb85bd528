<!-- 测评配置-测评分享 -->
<template>
  <el-dialog :visible.sync="visible" top="-40px" append-to-body width="48%" :close-on-click-modal="false" @open="openInit" @close="closeBtn">
    <div class="eval_share">
      <h4 class="eval_share-h4"><span class="eval_share-span">*</span> 分享奖励启用状态：</h4>
      <div class="eval_share-div">
        <div>
          <el-radio v-model="enableFlag" label="1">启用，当天首次分享奖励智米数</el-radio>
          <el-input-number v-model="zhimiCount" :min="0" :max="999" :precision="0" :controls="false" placeholder="请输入1~999数字" />
        </div>
        <el-radio v-model="enableFlag" label="0">禁用</el-radio>
      </div>
      <h4>变更记录</h4>
      <el-table v-loading="optionLoading" border size="small" :data="optionData" header-cell-class-name="table-cell-header" height="340">
        <el-table-column prop="createEmp" label="操作人" align="center" />
        <el-table-column prop="createTime" label="操作时间" align="center" />
        <el-table-column prop="remark" label="变更信息" align="center" />
      </el-table>
    </div>
    <div slot="footer" class="eval_share-footer">
      <el-button :size="'mini'" @click="closeBtn">取 消</el-button>
      <el-button type="primary" :size="'mini'" @click="submitBtn">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCutDay } from '@/utils';

export default {
  props: { visible: { type: Boolean, default: false }},
  data() {
    return {
      rows: {},
      zhimiCount: 0,
      enableFlag: '0',
      optionLoading: false,
      optionData: []
    };
  },
  methods: {
    // 获取邀约上限变更记录
    openInit() {
      this.optionData = [];
      this.optionLoading = true;
      this.$post('selectSharePrize').then((res) => {
        const { code, body } = res;
        if (code === '00') {
          this.optionData = body?.map((item) => {
            item.createTime = getCutDay(item.createTime);
            return item;
          });
          this.rows = body[0] || {};
          this.zhimiCount = this.rows.zhimiCount;
          this.enableFlag = this.rows.enableFlag;
          console.log('获取邀约上限变更记录-data', this.optionData);
        }
        this.optionLoading = false;
      }).catch((err) => {
        this.optionLoading = false;
        console.log('获取邀约上限变更记录-err', err);
      });
    },
    // 修改提交
    submitBtn() {
      if (this.enableFlag === '0' && this.rows.enableFlag === '0') {
        this.$message({ message: '启用状态未发生变更', type: 'error' });
        return;
      }
      if (this.enableFlag === '1' && !this.zhimiCount) {
        this.$message({ message: '请输入1~999数字！', type: 'error' });
        return;
      }
      const params = { enableFlag: this.enableFlag, zhimiCount: this.zhimiCount };
      // 如果是启用，智米数发生更改，则可提交，否则不可
      if (this.enableFlag === '1' && this.rows.zhimiCount === this.zhimiCount) {
        this.$message({ message: '请输入修改的数据！', type: 'error' });
        return;
      }
      // 如果是禁用，去掉智米数
      if (this.enableFlag === '0') {
        delete params.zhimiCount;
      }
      this.$post('updateSharePrize', params, { json: true })
        .then((res) => {
          const bools = res?.code === '00';
          if (bools) this.openInit();
          this.$message({
            message: `修改${bools ? '成功' : '失败'}！`,
            type: bools ? 'success' : 'error'
          });
          this.closeBtn();
        })
        .catch((err) => {
          this.$message({ message: '修改失败！', type: 'error' });
          console.log('获取邀约上限变更记录-err', err);
        });
    },
    closeBtn() {
      this.rows = {};
      this.zhimiCount = 0;
      this.enableFlag = '0';
      this.$emit('on-close');
    }
  }
};
</script>

<style lang="scss">
.eval_share {
  .eval_share-h4 {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    p {
      font-weight: 550;
    }
    .eval_share-span {
      margin: 0 4px;
      color: red;
      font-size: 18px;
    }
    .el-input {
      width: 80%;
    }
  }
  .eval_share-div {
    margin-left: 40px;
  }
}
.eval_share-footer {
  text-align: center;
}
</style>
