@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./table.scss";
@import "./button.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a {
  color: #1890ff;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}
ul,
ol,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

p {
  padding: 0;
  margin: 0;
}

.tac {
  text-align: center;
}

.tar {
  text-align: right;
}

.p20 {
  padding: 20px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px;
}

.ml10 {
  margin-left: 10px;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.break-all {
  word-break: break-all;
}

// main-container global css
.app-container {
  padding: 20px;
}

.yz-base-container {
  background: #fff;
  border-radius: 4px;
  padding: 10px;
  margin: 6px;
  box-shadow: 0 0 20px rgba($color: #000000, $alpha: 0.1);
}

// global dialog main css
.dialog-main {
  padding: 20px;
}

.yz-input-number {
  width: 100%;

  .el-input__inner {
    text-align: left;
  }
}
.yuan {
  position: relative;
  .el-input__inner {
    padding-right: 30px;
  }
}
.yuan::after {
  content: "元";
  position: absolute;
  right: 8px;
  top: 0;
  color: #c0c4cc;
  font-size: 12px;
}
.day {
  position: relative;
  .el-input__inner {
    padding-right: 30px;
  }
}
.day::after {
  content: "天";
  position: absolute;
  right: 8px;
  top: 0;
  color: #c0c4cc;
  font-size: 12px;
}
.people {
  position: relative;
  .el-input__inner {
    padding-right: 30px;
  }
}
.people::after {
  content: "人";
  position: absolute;
  right: 8px;
  top: 0;
  color: #c0c4cc;
  font-size: 12px;
}
.minute {
  position: relative;
  .el-input__inner {
    padding-right: 30px;
  }
}
.minute::after {
  content: "分钟";
  position: absolute;
  right: 8px;
  top: 0;
  color: #c0c4cc;
  font-size: 12px;
}

.el-popconfirm__main {
  margin-bottom: 10px;
  justify-content: center;
}

.el-popconfirm__action {
  text-align: center;
}

// 权限控制元素的淡入淡出效果
.auth-fade-enter-active,
.auth-fade-leave-active {
  transition: opacity 0.5s ease-in-out;
}
.auth-fade-enter,
.auth-fade-leave-to {
  opacity: 0;
}
