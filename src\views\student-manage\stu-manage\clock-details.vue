<template>
  <common-dialog
    width="1200px"
    title="打卡明细"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <div class="el-tabs el-tabs--top el-tabs--border-card">
        <div class="el-tabs__header is-top">
          <div class="el-tabs__nav-wrap is-top">
            <div
              class="el-tabs__item is-top"
              :class="{'is-active': tabName === 0}"
              @click="handleTabClick(0)"
            >已打卡</div>
            <div
              class="el-tabs__item is-top"
              :class="{'is-active': tabName === 1}"
              @click="handleTabClick(1)"
            >未打卡</div>
            <div
              class="el-tabs__item is-top"
              :class="{'is-active': tabName === 2}"
              @click="handleTabClick(2)"
            >从未打卡</div>
            <div
              class="el-tabs__item is-top"
              :class="{'is-active': tabName === 3}"
              @click="handleTabClick(3)"
            >发布心得数</div>
          </div>
        </div>
        <div class="el-tabs__content">
          <div v-if="tabName === 0" class="el-tab-pane">
            <el-table
              ref="table"
              :key="0"
              v-loading="tableLoading"
              border
              size="small"
              :data="tableData"
              :height="500"
              style="width: 100%"
              header-cell-class-name='table-cell-header'
            >
              <el-table-column prop="realName" label="学员姓名" align="center" />
              <el-table-column prop="nickName" label="昵称" align="center" />
              <el-table-column prop="mobile" label="手机号" align="center" />
              <el-table-column prop="clockTime" label="打卡时间" align="center" />
            </el-table>
          </div>
          <div v-if="tabName === 1" class="el-tab-pane">
            <el-table
              ref="table"
              :key="1"
              v-loading="tableLoading"
              border
              size="small"
              :data="tableData"
              :height="500"
              style="width: 100%"
              header-cell-class-name='table-cell-header'
            >
              <el-table-column prop="realName" label="学员姓名" align="center" />
              <el-table-column prop="nickName" label="昵称" align="center" />
              <el-table-column prop="mobile" label="手机号" align="center" />
            </el-table>
          </div>
          <div v-if="tabName === 2" class="el-tab-pane">
            <el-table
              ref="table"
              :key="2"
              v-loading="tableLoading"
              border
              size="small"
              :data="tableData"
              :height="500"
              style="width: 100%"
              header-cell-class-name='table-cell-header'
            >
              <el-table-column prop="realName" label="学员姓名" align="center" />
              <el-table-column prop="nickName" label="昵称" align="center" />
              <el-table-column prop="mobile" label="手机号" align="center" />
            </el-table>
          </div>
          <div v-if="tabName === 3" class="el-tab-pane">
            <el-table
              ref="table"
              :key="3"
              v-loading="tableLoading"
              border
              size="small"
              :data="tableData"
              :height="500"
              style="width: 100%"
              header-cell-class-name='table-cell-header'
            >
              <el-table-column prop="commentUserName" label="学员姓名" align="center" />
              <el-table-column prop="nickname" label="昵称" align="center" />
              <el-table-column prop="commentMobile" label="手机号" align="center" />
              <el-table-column prop="commentContent" label="留言内容" width="300">
                <template slot-scope="scope">
                  <div class="comment"> {{ scope.row.commentContent }}</div>
                  <div class="imgs">
                    <el-image
                      v-for="(imgUrl, index) in scope.row.imgs"
                      :key="index"
                      class="comment-img"
                      :src="imgUrl"
                      :preview-src-list="scope.row.imgs"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="commentTime" label="留言时间" align="center" />
              <el-table-column prop="status" label="是否显示" align="center">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="scope.row.status === '1'? 'success':'danger'">
                    {{ scope.row.status === '1' ? "是" : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="recommend" label="是否推荐" align="center">
                <template slot-scope="scope">
                  <el-tag size="mini" :type="scope.row.recommend === '2'? 'success':'danger'">
                    {{ scope.row.recommend === '2' ? '是': '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="250">
                <template slot-scope="scope">
                  <el-button size="mini" type="primary" @click="openReplyDialog(scope.row)">回复</el-button>
                  <el-button size="mini" type="danger" @click="updateStatus(scope.row)">
                    {{ scope.row.status === '1' ? '屏蔽' : '展示' }}
                  </el-button>
                  <el-button
                    size="mini"
                    type="warning"
                    @click="setFeaturedReviews(scope.row)"
                  >
                    <!--由于后台原因展示用文字判断先-->
                    {{ scope.row.recommend === '2' ? '取消精选':'设置精选' }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 分页区 -->
        <div class="yz-table-pagination">
          <pagination
            :total='pagination.total'
            :page.sync="pagination.page"
            :limit.sync="pagination.limit"
            @pagination="handleGetTableList"
          />
        </div>
      </div>

      <!-- 留言回复 -->
      <replyMessage
        :visible.sync="rMVisible"
        :comment-id="commentId"
        :comment-user-id="commentUserId"
        @refresh="getReadCommentList"
      />

    </div>
  </common-dialog>
</template>

<script>
import replyMessage from '../../course-reviews/reply-message';
import { ossUri } from '@/config/request';
export default {
  components: {
    replyMessage
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tabIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      show: false,
      tabName: 0,
      tableData: [],
      tableLoading: false,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      semesterId: null,
      arrangeId: null,
      rMVisible: false,
      commentId: null,
      commentUserId: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    tabIndex(val) {
      this.tabName = val;
    }
  },
  methods: {
    setFeaturedReviews(rowData) {
      const data = {
        commentId: rowData.commentId
      };
      this.$post('setFeaturedReviews', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getReadCommentList();
        }
      });
    },
    updateStatus(rowData) {
      const data = {
        commentId: rowData.commentId
      };
      this.$post('updateCommentStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getReadCommentList();
        }
      });
    },
    // 打开回复弹窗
    openReplyDialog(rowData) {
      this.commentId = rowData.commentId;
      this.commentUserId = rowData.commentUserId;
      this.rMVisible = true;
    },
    open() {
      this.tabName = this.tabIndex;
      this.handleSwitch();
    },
    handleGetTableList() {
      if (this.tabName === 0) {
        this.getClockList(1);
        return;
      }

      if (this.tabName === 1) {
        this.getClockList(2);
        return;
      }

      if (this.tabName === 2) {
        this.getNeverClockPerson();
      }

      if (this.tabName === 3) {
        this.getReadCommentList();
      }
    },
    handleSwitch() {
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.handleGetTableList();
    },
    getNeverClockPerson() {
      this.tableLoading = true;
      const params = {
        semesterId: this.semesterId,
        arrangeId: this.arrangeId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('getNeverClockPerson', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    // 获取发布心得数
    getReadCommentList() {
      this.tableLoading = true;
      const params = {
        semesterId: this.semesterId,
        arrangeId: this.arrangeId,
        commentMobile: null, // 手机号码
        commentUserName: null, // 姓名
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('getReadCommentList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.data.forEach(item => {
              if (typeof item.pictureUrl === 'string' && item.pictureUrl !== '') {
                item.imgs = item.pictureUrl.split(',');
                item.imgs = item.imgs.map(item => {
                  return ossUri + item;
                });
              } else {
                item.imgs = [];
              }
            });
            this.tableData = body.data;
            this.tableLoading = false;
            this.pagination.total = body.recordsTotal;
          }
        });
    },
    getClockList(type) {
      this.tableLoading = true;
      const params = {
        semesterId: this.semesterId,
        clockType: type, // 1、代表已打卡，2、代表未打卡
        arrangeId: this.arrangeId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getDayClockList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    handleTabClick(name) {
      this.tabName = name;
      this.handleSwitch();
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.el-tabs--border-card {
  box-shadow: none;
}

.yz-table-pagination {
  margin-bottom: 15px;
}

.comment-img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  border: 1px solid #ccc;
  padding: 2px;

  ::v-deep .el-image__error {
    font-size: 12px;
    text-align: center;
  }

}

</style>
