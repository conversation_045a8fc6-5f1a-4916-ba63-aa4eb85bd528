<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      @submit.native.prevent='onSearch'
    >
      <el-form-item label="价格" prop="amount">
        <el-input
          v-model="form.amount"
          clearable
          placeholder="请输入价格"
          @input="handleLimitInput"
        />
      </el-form-item>

      <el-form-item label="投放渠道" prop="putInChannel">
        <el-select v-model="form.putInChannel" clearable>
          <el-option
            v-for="item in putInChannel"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="商品" prop="productId">
        <el-select
          v-model="form.productId"
          filterable
          clearable
          placeholder="请选择商品"
        >
          <el-option
            v-for="item in productOptions"
            :key="item.productId"
            :label="item.productName"
            :value="item.productId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="对应的获客链接" prop="contactType">
        <el-select
          v-model="form.contactType"
          filterable
          clearable
          placeholder="请选择对应的获客链接"
        >
          <el-option
            v-for="item in huoKeLinkOptions"
            :key="item.contactType"
            :label="item.contactName"
            :value="item.contactType"
          />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="onSearch(0)" />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox mt10" style="margintop: 10px">
      <el-button
        type="primary"
        size="small"
        @click="handleAdd"
      >新增商品</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column
        prop="putInChannel"
        label="投放渠道"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.putInChannel | putInChannel }}
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column
        prop="productTitle"
        label="页面标题(对外显示)"
        align="center"
      />
      <el-table-column prop="productBannerImg" label="头图" align="center" width="125">
        <template slot-scope="scope">
          <el-image
            :src="scope.row.headerUrl"
            :preview-src-list="[scope.row.headerUrl]"
            style="height: 60px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="detailUrlList" label="详情页图片" align="center" width="160">
        <template slot-scope="scope">
          <el-image
            v-for="(imgUrl, index) in scope.row.detailUrlList"
            :key="index"
            :src="imgUrl"
            :preview-src-list="scope.row.detailUrlList"
            fit="contain"
            style="height: 60px"
          />
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="价格" align="center" width="80">
        <template slot-scope="scope">
          {{ typeof scope.row.amount == 'number' ? scope.row.amount + "元" : '/' }}
        </template>
      </el-table-column>
      <el-table-column label="对应的获客链接" align="center">
        <template slot-scope="scope">
          {{ scope.row.contactName ? scope.row.contactName : '/' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="workWechatTagVOS"
        label="对应标签"
        align="center"
        width="120"
      >
        <template slot-scope="scope">
          <template v-if="scope.row.workWechatTagVOS.length">
            <el-tag
              v-for="(item, index) in scope.row.workWechatTagVOS"
              :key="index"
              type="warning"
              size="mini"
              style="margin-right: 5px"
            >{{ item.workWechatTagName }}</el-tag>
          </template>
          <template v-else>/</template>
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="最新修改时间" align="center">
        <template slot-scope="scope">
          {{
            scope.row.updateTime | transformTimeStamp("YYYY-MM-DD HH:mm:ss", "/")
          }}
        </template>
      </el-table-column>
      <el-table-column prop="updateUser" label="最新修改人" align="center" width="90">
        <template slot-scope="scope">{{ scope.row.updateUser || '/' }}</template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{
            scope.row.createTime
              | transformTimeStamp("YYYY-MM-DD HH:mm:ss", "/")
          }}
        </template>
      </el-table-column>
      <el-table-column prop="createUser" label="创建人" align="center" width="90" />
      <el-table-column label="操作" align="center" width="170">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleEdit(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="copyUrl(scope.row, $event)"
          >复制链接</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="loadNextPage"
      />
    </div>

    <add-edit
      :visible.sync="aeVisible"
      :huoKeLinkOptions="huoKeLinkOptions"
      :row="currentEditRow"
    />
  </div>
</template>

<script>
import clipboard from '@/utils/clipboard';
import addEdit from './add-edit.vue';
import { ossUri } from '@/config/request';
import { arrToEnum } from '@/utils';
import { putInChannel } from './type';
const putInChannelEnum = arrToEnum(putInChannel, 'dictValue', 'dictName');
export default {
  filters: {
    putInChannel(val) {
      return putInChannelEnum[val] || '/';
    }
  },
  components: {
    addEdit
  },
  data() {
    return {
      putInChannel: putInChannel,
      aeVisible: false, // 编辑弹框
      currentEditRow: {},
      // 表格筛选
      form: {
        amount: null,
        putInChannel: null,
        productId: null,
        contactType: null
      },
      productOptions: [], // 商品
      huoKeLinkOptions: [], // 获客链接
      tableLoading: false, // 表格是否加载
      pagination: { page: 1, total: 0, limit: 10 }, // 页码参数
      tableData: [],
      tagList: [] // 标签列表
    };
  },
  provide() {
    return {
      tagList: () => this.tagList
    };
  },
  mounted() {
    this.getProductOptions();
    this.getHuoKeLinkOptions();
    this.loadNextPage();
    this.getTagList();
  },
  methods: {
    // 限制只能输入数字和小数点
    handleLimitInput(value) {
      // this.form.amount = value.replace(/[^\d.]/g, '');

      // 只允许输入数字和小数点
      this.form.amount = value.replace(/[^\d.]/g, '');

      // 限制小数点后最多两位
      const parts = this.form.amount.split('.');
      if (parts.length > 1) {
        parts[1] = parts[1].slice(0, 2); // 限制小数点后最多两位
        this.form.amount = parts.join('.');
      }
    },
    // 复制链接
    copyUrl(row, event) {
      const regOrigin = row.putInChannel === 1 ? '92' : '93'; // 92 (微信投放), 93(字节投放)
      const productId = row.productId;
      // 正式环境地址(这里复制的链接是正式环境的, 至于测试环境, 需要自己在url上修改拼接)
      const url = `https://zm.yzou.cn/active/placeCourse?productId=${productId}&regOrigin=${regOrigin}`;
      clipboard(url, event);
    },
    // 新增
    handleAdd() {
      this.currentEditRow = null;
      this.aeVisible = true;
    },
    // 编辑
    handleEdit(row) {
      this.currentEditRow = row;
      this.aeVisible = true;
    },
    // 搜索
    onSearch(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.loadNextPage();
      }
    },
    // 加载下一页
    loadNextPage() {
      this.tableLoading = true;
      const params = this.getQueryParams();

      this.$post('getAdConfigList', params)
        .then((res) => {
          const { fail, body } = res;
          if (!fail) {
            body.data.forEach(item => {
              item.headerUrl = ossUri + item.productBannerImg;
              item.detailUrlList = item.productDetailImgList.map(item => {
                return ossUri + item;
              });
            });
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 处理列表请求参数
    getQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));

      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    // 获取商品
    getProductOptions() {
      const params = { page: 1, rows: 99999, sName: '' };
      this.$post('getAdProductSelect', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.productOptions = body.data;
        }
      });
    },
    // 获取获客链接
    getHuoKeLinkOptions() {
      const params = { page: 1, rows: 99999, sName: '' };
      this.$post('getHuoKeLinkSelect', params).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.huoKeLinkOptions = body.data;
        }
      });
    },
    // 获取标签列表
    getTagList() {
      this.$post('getAdTagList').then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tagList = body.tagGroup.map(item => {
            item.id = item.groupId;
            item.name = item.groupName;
            return item;
          });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss"></style>
