<template>
  <common-dialog
    :title="row ? '编辑' : '新增'"
    :visible.sync="show"
    @close="show = false"
    @confirm="onSubmit"
  >
    <el-form
      ref="dialogForm"
      :width="1000"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="key-tag-dialog-form"
      size="mini"
    >
      <el-form-item label="关键标签总称" prop="tagName">
        <el-input
          v-model="form.tagName"
          placeholder="请输入关键标签总称"
          maxlength="30"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="app排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          placeholder="请输入app排序"
          :min="0"
          :controls="false"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="选择标签" prop="selectedTags">
        <el-input
          v-model="filterText"
          placeholder="输入关键字进行过滤"
          clearable
          @change="handleFilterChange"
        />
        <el-tree
          ref="tagTree"
          accordion
          highlight-current
          :data="tagTreeData"
          class="key-tag-tree"
          node-key="id"
          :empty-text="treeText"
          :filter-node-method="filterNode"
          @node-click="handleNodeClick"
        />
      </el-form-item>
      <el-form-item
        v-if="form.selectedTags.length > 0"
        :label="`已选标签(${form.selectedTags.length}/${maxTagCount})`"
      >
        <div class="selected-tags">
          <el-tag
            v-for="tag in form.selectedTags"
            :key="tag.tagInfoId"
            closable
            type="warning"
            size="small"
            style="margin: 2px"
            @close="handleTagClose(tag)"
          >
            {{ tag.tagInfoName }}
          </el-tag>
        </div>
      </el-form-item>
      <el-form-item label="引导文案" prop="guideCopywriting">
        <el-input
          v-model="form.guideCopywriting"
          placeholder="请输入"
          maxlength="30"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="跳转链接" prop="route">
        <el-input v-model="form.route" placeholder="请输入" clearable />
      </el-form-item>
      <div style="text-align: center; margin-top: 20px">
        <el-button
          type="primary"
          size="small"
          @click="onSubmit"
        >保存</el-button>
      </div>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  name: 'DialogEdit',
  props: {
    title: { type: String, default: '新增' },
    visible: { type: Boolean, required: true },
    row: { type: Object, default: () => null }
  },
  data() {
    return {
      maxTagCount: 20, // 最大标签数量
      form: {
        tagName: '',
        sort: 0,
        selectedTags: [],
        guideCopywriting: '',
        route: ''
      },
      treeText: '加载中..',
      tagTreeData: [],
      filterText: '',
      rules: {
        tagName: [
          { required: true, message: '请输入关键标签总称', trigger: 'blur' }
        ],
        sort: [{ required: true, message: '请输入app排序', trigger: 'blur' }],
        selectedTags: [
          { required: true, message: '请选择标签', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set() {
        this.$emit('close');
      }
    }
  },
  watch: {
    row: {
      handler(val) {
        if (val) {
          this.form = JSON.parse(
            JSON.stringify({
              ...val,
              selectedTags: (val.fansTagTanmaRelations || []).map((v) => ({
                tagInfoId: v.tanmaTagId,
                tagInfoName: v.tmTagName
              }))
            })
          );
        }
      },
      immediate: true,
      deep: true
    },
    show(val) {
      if (!val) {
        this.$refs?.dialogForm?.resetFields();
        this.form.selectedTags = [];
        this.filterText = '';
      } else {
        this.getTagsList();
      }
    }
  },
  methods: {
    // 标签关闭
    handleTagClose(tag) {
      this.form.selectedTags = this.form.selectedTags.filter(
        (v) => v.tagInfoId !== tag.tagInfoId
      );
    },
    // 标签选择改变
    handleNodeClick(data) {
      if (!data.tagInfoId) return;
      // 已选标签中存在，则移除
      const newArr = this.form.selectedTags.filter(
        (v) => v.tagInfoId !== data.tagInfoId
      );
      // 已选标签数量未达到上限，则添加
      if (this.form.selectedTags.length >= this.maxTagCount) {
        this.$message.error(
          `已超过${this.maxTagCount}个标签，请先取消已选标签，再重新添加标签`
        );
        return;
      }
      this.form.selectedTags = [...newArr, data];
    },
    // 过滤内容改变
    handleFilterChange(val) {
      setTimeout(() => {
        this.$refs.tagTree.filter(val);
      }, 0);
    },
    // 过滤标签数据函数
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 循环处理标签数据
    loops(data) {
      data.map((item) => {
        item.label = item.tagCategoryName;
        item.id = item.tagCategoryId;
        item.children = item.childrens;
        delete item.childrens;
        if (!item?.children?.length && item.nodes?.length) {
          // 最后一级
          item.children = item.nodes
            .filter((v) => v.tagInfoId)
            .map((v) => ({
              ...v,
              id: '' + v.tagCategoryId + v.tagInfoId,
              label: v.tagInfoName
            }));
          delete item.nodes;
        } else {
          return this.loops(item.children);
        }
        return item;
      });
      return data;
    },
    // 获取标签列表
    getTagsList() {
      if (this.tagTreeData.length) return;
      this.$post('queryTreeForSelect', {}, { json: 'application/json' })
        .then((res) => {
          this.tagTreeData = this.loops(res?.body || []);
        })
        .catch(() => {
          this.tagTreeData = [];
        })
        .finally(() => {
          this.treeText = '暂无数据';
        });
    },
    // 提交
    onSubmit() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.handleRequestSubmit(this.form);
        }
      });
    },
    // 处理请求
    handleRequestSubmit(form) {
      // 准备接口参数
      const params = {
        tagName: form.tagName,
        guideCopywriting: form.guideCopywriting,
        route: form.route,
        sort: form.sort || 0,
        tmTagIds: form.selectedTags.map((tag) => tag.tagInfoId)
      };

      // 判断是新增还是编辑
      const isEdit = !!this.row && !!this.row.id;
      const apiUrl = isEdit ? '/teacher/fans/tagConfig/edit' : '/teacher/fans/tagConfig/add';
      const actionText = isEdit ? '编辑' : '新增';

      // 如果是编辑，添加id参数
      if (isEdit) {
        params.id = this.row.id;
      }

      // 调用接口
      this.$http
        .post(apiUrl, params, {
          headers: { 'Content-Type': 'application/json' }
        })
        .then((res) => {
          const { code, msg } = res;
          if (code === '00') {
            this.$message.success(`${actionText}成功`);
            this.$emit('refresh'); // 通知父组件
            this.show = false;
          } else {
            this.$message.error(msg || `${actionText}失败`);
          }
        })
        .catch((error) => {
          console.error(`${actionText}失败:`, error);
          this.$message.error(`${actionText}失败，请稍后重试`);
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.key-tag-dialog-form {
  padding: 20px;

  .key-tag-tree {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    padding: 10px;
    margin-bottom: 8px;
  }

  .selected-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
  }

  ::v-deep .el-input-number {
    .el-input__inner {
      text-align: left;
    }
  }
}
</style>
