<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="学期管理" name="stuMenage">
        <stu-manage />
      </el-tab-pane>
      <el-tab-pane label="学员打卡明细" name="course" lazy>
        <sign-details />
      </el-tab-pane>
      <el-tab-pane label="奖学金打卡管理" name="market" lazy>
        <market-manage />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import stuManage from './stu-manage/index';
import signDetails from './sign-details/index';
import marketManage from './market-manage/index.vue';
export default {
  components: {
    stuManage,
    signDetails,
    marketManage
  },
  data() {
    return {
      activeName: 'stuMenage'
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
