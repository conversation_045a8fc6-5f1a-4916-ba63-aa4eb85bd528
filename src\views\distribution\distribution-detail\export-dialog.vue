<template>
  <common-dialog
    title="佣金结算导出"
    :visible.sync='show'
    :showFooter="true"
    width="400px"
    @confirm="confirm"
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='form'
        :model='form'
        label-width='120px'
        :rules="rules"
      >
        <el-form-item label='选择结算月份' prop='time'>
          <el-date-picker
            v-model="form.time"
            type="month"
            placeholder="选择日期"
            value-format="yyyy-MM"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import FileSaver from 'file-saver';
import axios from 'axios';
import { api } from '@/api';
import { downUri } from '@/config/request';
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        time: ''
      },
      rules: {
        time: [
          { required: true, message: '请选择', trigger: 'blur' }
        ]
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { time } = this.form;
          const splitArr = time.split('-');
          const url = downUri + api['performanceExport'];
          const data = new FormData();
          data.append('year', splitArr[0]);
          data.append('month', splitArr[1]);
          const config = {
            method: 'POST',
            responseType: 'blob',
            data: data
          };
          axios(url, config).then(res => {
            FileSaver.saveAs(res.data, `上进学社${splitArr[0]}年${splitArr[1]}月分销佣金.xlsx`); // 利用file-saver保存文件,注意：第一个参数是blod二进制
            this.close();
          }).catch(error => {
            console.log(error);
          });
        }
      });
    },
    open() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
