<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    :title="isEdit ? '编辑' : '新增' + '分销商品'"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >

        <el-form-item label='分类' prop='goodTypeName'>
          <el-select
            v-model="form.distributionGoodType"
            clearable
            placeholder="请选择商品类型"
            :disabled="isEdit"
          >
            <el-option label="课程" value="course" />
            <el-option label="读书计划" value="readPlan" />
            <el-option label="训练营" value="training" />
            <el-option label="会员卡" value="member" />
            <el-option label="学霸卡" value="studycard" />
          </el-select>
          <el-select
            v-show="form.distributionGoodType !== 'member'"
            v-model="form.goodTypeName"
            filterable
            remote
            reserve-keyword
            placeholder="请输入上架的商品名称"
            :disabled="isEdit"
            :remote-method="remoteMethod"
            :loading="loading"
            @visible-change="visibleChange"
            @change="setMappingName"
          >
            <el-option
              v-for="item in options"
              :key="item.goodsShelfId"
              :label="item.goodsShelfName"
              :value="item.goodsShelfName"
            />
          </el-select>
          <!-- 已上架的会员卡 -->
          <el-select
            v-show="form.distributionGoodType === 'member'"
            v-model="form.goodTypeName"
            filterable
            remote
            reserve-keyword
            placeholder="请输入上架的会员卡名称"
            :disabled="isEdit"
            :remote-method="remoteMethod"
            :loading="loading"
            @visible-change="visibleChange"
            @change="setVipCardName"
          >
            <el-option
              v-for="item in options"
              :key="item.mebId"
              :label="item.mebName"
              :value="item.mebName"
            />
          </el-select>

        </el-form-item>

        <el-form-item v-if="form.distributionGoodType !== 'member'" label='商品售价' prop='sellPrice'>
          <el-input
            v-model="form.sellPrice"
            disabled
          />
        </el-form-item>

        <el-form-item v-if="form.distributionGoodType !== 'member'" label='会员价' prop='memPrice'>
          <el-input
            v-model="form.memPrice"
            disabled
          />
        </el-form-item>

        <el-form-item label='佣金' prop='generalProportion'>
          <div class="commission">
            普通用户佣金比例
            <el-input-number v-model="form.generalProportion" size="mini" :min="0" :max="100" label="描述文字" width="50px" /> %
          </div>
          <div class="commission">
            会员用户佣金比例
            <el-input-number v-model="form.memProportion" size="mini" :min="0" :max="100" label="描述文字" /> %
          </div>

        </el-form-item>

        <!-- <el-form-item label='结算周期' prop='cycleType'>
          <el-radio-group v-model="form.cycleType">
            <el-radio label="2">默认结算周期</el-radio>
            <el-radio label="1">自定义结算周期</el-radio>
          </el-radio-group>
          <div v-if="form.cycleType === '1'" class="time-box">间隔第<el-input v-model="form.cycleMonth" class="timeIpt" />个月15日结算</div>
        </el-form-item> -->

        <el-form-item label='是否分佣' prop='divideStatus'>
          <el-radio-group v-model="form.divideStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='是否启用' prop='enable'>
          <el-radio-group v-model="form.enable">
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const commissList = (rule, value, callback) => {
      const generalProportion = this.form.generalProportion;
      const memProportion = this.form.memProportion;
      if (generalProportion === undefined || memProportion === undefined) {
        callback(new Error('请输入佣金比例'));
      } else if (generalProportion > 100 || generalProportion < 0 || memProportion > 100 || memProportion < 0) {
        callback(new Error('请输入正确的佣金比例'));
      } else {
        callback();
      }
    };
    // const cycleTypeCheck = (rule, value, callback) => {
    //   if (this.form.cycleType === '2') {
    //     callback();
    //   } else if (this.form.cycleType === '1') {
    //     if (this.form.cycleMonth === undefined || this.form.cycleMonth === '') {
    //       callback('请输入');
    //     } else if (this.form.cycleMonth < 0) {
    //       callback('结算周期必须大于或等于0');
    //     } else {
    //       callback();
    //     }
    //   }
    // };
    return {
      show: false,
      isEdit: false,
      loading: false,
      selectName: '',
      options: [],
      queryParam: {},
      goodTypeTempName: '',
      form: {
        distributionId: '', // 分销订单ID
        distributionNumber: '', // 分销商品编码
        distributionGoodType: 'course', // 分销商品类型
        distributionGoodId: '', // 分销商品ID
        distributionGoodName: '课程', // 分销商品名称
        distributionDetailId: '', // 分销详情id
        goodTypeName: '', // 商品类型名称回显
        goodsShelfId: null, // 上架商品id
        // cycleType: '2', // 结算周期
        // cycleMonth: 2, // 结算周期月
        memPrice: null,
        sellPrice: null,
        generalProportion: undefined, // 普通佣金比例
        memProportion: undefined, // 会员佣金比例
        enable: '1', // 是否启用
        divideStatus: 1 // 是否分佣
      },
      rules: {
        generalProportion: [
          { required: true, validator: commissList, trigger: 'change' }
        ],
        // cycleType: [
        //   { required: true, validator: cycleTypeCheck, trigger: 'change' }
        // ],
        goodTypeName: [
          { required: true, message: '请选择', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'form.distributionGoodType'(val) {
      const data = {
        'readPlan': '读书计划',
        'course': '课程',
        // 'readSingle': '单本读书',
        'training': '训练营',
        'studycard': '学霸卡',
        'member': '会员卡'
      };
      this.form.distributionGoodName = data[val];
      this.form.goodsShelfId = '';

      this.options = [];
      this.form.goodTypeName = '';
      this.form.sellPrice = '';
      this.form.memPrice = '';
      if (this.goodTypeTempName) {
        this.form.goodTypeName = this.goodTypeTempName;
      }
      this.getOptions();
    }
  },
  mounted() {},
  methods: {
    open() {
      if (this.row) {
        this.isEdit = true;
        this.findSingleDistribution();
      } else {
        this.getOptions();
      }
    },
    // 单个分销商品
    findSingleDistribution() {
      this.$post('findSingleDistribution', { distributionNumber: this.row.distributionNumber })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.distributionId = body.distributionId;
            this.form.distributionNumber = body.distributionNumber;
            if (body.distributionDetailId) {
              this.form.distributionDetailId = body.distributionDetailId;
            }
            this.form.distributionGoodId = body.distributionGoodId;
            // 上架商品名称回显
            this.form.goodTypeName = this.goodTypeTempName = body.distributionGoodName;
            this.form.distributionGoodType = body.distributionGoodType;
            this.form.sellPrice = body.sellPrice;
            this.form.memPrice = body.memPrice;
            // this.form.cycleType = body.cycleType;
            // this.form.cycleMonth = body.cycleMonth;
            this.form.generalProportion = body.generalProportion;
            this.form.memProportion = body.memProportion;
            this.form.enable = body.enable;
            this.form.divideStatus = body.divideStatus;

            // 下拉回显
            this.form.goodsShelfId = body.distributionGoodId;
            this.options = {
              goodsShelfId: body.distributionGoodId,
              goodsShelfName: body.distributionGoodName
            };
          }
        });
    },
    // 下拉选中
    setMappingName(value) {
      let obj = {};
      obj = this.options.find((item) => {
        return item.goodsShelfName === value;
      });
      // console.log(obj, 'obj');
      this.form.distributionGoodId = obj.goodsShelfId;
      this.form.distributionGoodName = obj.goodsShelfName;
      this.form.sellPrice = obj.marketPrice;
      this.form.memPrice = obj.vipPrice;
    },
    // 会员卡下拉选中
    setVipCardName(value) {
      let obj = {};
      obj = this.options.find((item) => {
        return item.mebName === value;
      });
      this.form.distributionGoodId = obj.mebId;
      this.form.distributionGoodName = obj.mebName;
    },
    // 下拉框出现/隐藏
    visibleChange(status) {
      if (!status) {
        this.resetGetOptions();
      } else {
        this.getOptions();
      }
    },

    resetGetOptions() {
      this.options = [];
      // this.getOptions();
    },
    getOptions() {
      this.queryParam = {
        tradeCode: this.form.distributionGoodType,
        goodsShelfName: this.selectName || ''
      };
      let url = 'findShelfGoodsList';

      // 会员卡
      if (this.form.distributionGoodType === 'member') {
        url = 'getMemberCardList';
        this.queryParam = {
          upStatus: '1',
          mebName: this.selectName || ''
        };
      }
      this.$post(url, this.queryParam).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.loading = false;
          this.options = body;
        }
      });
    },
    // 搜索上架商品
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        this.selectName = query;
        this.getOptions();
      } else {
        this.options = [];
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const apiKey = 'addDistribution';
          const params = {
            ...this.form
          };
          // params.cycleMonth = +params.cycleMonth;
          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                setTimeout(() => {
                  this.$parent.getTableList();
                }, 500);
              }
            });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.el-select{
  width: 48%;
  margin-right: 2%;
}
.el-select:nth-child(2){
  margin-right: 0px;
}
.commission .el-input--mini{
  width: 49%;
  margin-left: 10px;
}
.commission{
  margin-bottom: 10px;
}
.time-box{
  margin-left: 31%;
}
.timeIpt{
  width: 15%;
  height: 20px;
  margin: 0 10px;
}
</style>
