<template>
  <common-dialog
    title="添加官方运营人员"
    width="1000px"
    :showFooter="true"
    :visible.sync='show'
    @confirm="confirm"
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='100px'
        @submit.native.prevent='search'
      >
        <el-form-item label='姓名' prop='empName'>
          <el-input v-model="form.empName" type="text" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label='部门' prop='dpId'>
          <SearchSelect
            v-model="form.dpId"
            clearable
            :options="selectDepartList"
            :props="{label:'dpName',value:'dpId'}"
            @loadmore='loadmore'
            @search="searchSelect"
            @select="select"
            @open="openSelect"
          />
        </el-form-item>
        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" type="text" placeholder="请输入手机号" clearable />
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <span style="opacity: 0;">占位</span>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" />
        <el-table-column prop="empName" label="助学老师" align="center" />
        <el-table-column prop="dpName" label="所在部门" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        empName: '',
        dpId: '',
        mobile: ''
      },
      tableLoading: false,
      tableData: [],
      checkList: [],
      selectDepartList: [],
      page: 1,
      limit: 10,
      total: 0,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getSerQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.getSerQueryParams();
      this.$post('getOperateUserAddList', params, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          if (body) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          } else {
            this.tableData = [];
          }
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelectionChange(selections) {
      this.checkList = selections;
    },
    loadmore() {
      if (this.selectDepartList.length === this.total) {
        return;
      }
      this.page += 1;
      this.getSelectList();
    },
    // 搜索
    async searchSelect(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.page = 1;
        this.selectDepartList = [];
        this.getSelectList(value);
      }, 500);
    },
    // 选中部门
    select(value) {
      if (value === undefined) { return; }
    },
    openSelect() {
      this.page = 1;
      this.selectDepartList = [];
      this.getSelectList();
    },
    getSelectList(keyword = '') {
      const data = {
        page: (this.page - 1) * this.limit,
        length: this.limit
      };
      data['sName'] = keyword;

      this.$post('selectDepartList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.selectDepartList = this.selectDepartList.concat(body.data);
          this.total = body.recordsTotal;
        }
      });
    },
    confirm() {
      if (this.checkList.length === 0) {
        this.$message({
          message: '请选择',
          type: 'warning'
        });
        return;
      }
      const list = this.checkList.map(item => {
        return item.empId;
      });
      const params = { empIds: list };
      this.$post('addOperateUser', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$emit('getTableList');
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      });
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
</style>
