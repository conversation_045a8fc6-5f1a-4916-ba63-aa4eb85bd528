import store from "../store";

/**
 * 权限检查插件
 * 将权限检查方法注册为全局方法
 */
export default {
  install(Vue) {
    /**
     * 检查权限
     * @param {string} permissionCode - 权限编码
     * @returns {Promise<boolean>} - 是否有权限
     */
    Vue.prototype.$checkPermission = function (permissionCode) {
      return store.dispatch("permission/checkPermission", permissionCode);
    };

    /**
     * 批量检查权限
     * @param {string[]} permissionCodes - 权限编码数组
     * @returns {Promise<Object>} - 权限结果对象，键为权限编码，值为是否有权限
     */
    Vue.prototype.$batchCheckPermissions = async function (permissionCodes) {
      if (!Array.isArray(permissionCodes) || permissionCodes.length === 0) {
        return {};
      }

      // 使用Promise.all并发检查所有权限
      const permissionPromises = permissionCodes.map((code) =>
        this.$checkPermission(code).then((result) => ({ code, result }))
      );

      const results = {};
      const checkedPermissions = await Promise.all(permissionPromises);

      // 将结果转换为对象格式
      checkedPermissions.forEach((item) => {
        results[item.code] = item.result;
      });

      return results;
    };
  },
};
