// let url = '';
// if (process.env.NODE_ENV === 'development') { // 开发环境直接启动代理 不需要路径
//   url = '';
// } else if (process.env.RUN_ENV === 'production') { // 判断打包测试环境还是正式环境
//   url = process.env.RUN_ENV === 'production' ? 'http://new.yzou.cn/' : 'http://bms2.yzwill.cn';
// }

// const url = process.env.RUN_ENV === 'production' ? 'http://new.yzou.cn/' : 's';
const url = '';

export const uri = url;
// "proxy": "http://new.yzou.cn/", http://*************
// export const downloadUri = url;

export const downUri = window.location.protocol + process.env.VUE_APP_DOWN_URL_API;

// export const downUri = top.location.origin.includes('localhost') ? 'http://bms.yzwill.cn' : top.location.origin;

export const ossUri = process.env.VUE_APP_OSS_URL;
export const zmcUrl = process.env.VUE_APP_ZMC_URL;

// export const ossUri = process.env.VUE_APP_RUN_ENV === 'production'
//   ? 'http://yzims.oss-cn-shenzhen.aliyuncs.com/' : 'http://yzimstest.oss-cn-shenzhen.aliyuncs.com/';
// ossUri = process.env.PRE_ENV === 'pre' ? 'http://yzimstest.oss-cn-shenzhen.aliyuncs.com/' :  ossUri; // 预发布环境

export const timeout = 120000;

export const contentType = {
  form: 'application/x-www-form-urlencoded',
  json: 'application/json',
  file: 'multipart/form-data'
};
