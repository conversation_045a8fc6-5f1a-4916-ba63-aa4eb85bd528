<template>
  <div>
    <common-dialog
      show-footer
      width="60%"
      :title="row ? '编辑' : '新增'"
      :visible.sync="show"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form ref="form" class="form" size="mini" :model="form" label-width="150px" :rules="rules">

          <el-form-item label="社团类型" prop="clubsActType">
            <el-select v-model="form.clubsActType" placeholder="请选择社团类型" :disabled="row">
              <el-option
                v-for="item in dictClubTypes"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="组建类型" prop="clubsActBuildType">
            <el-select v-model="form.clubsActBuildType" placeholder="请选择组建类型">
              <el-option
                v-for="item in dictBuildTypes"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="社团序号" prop="sort">
            <el-input-number
              v-model="form.sort"
              placeholder="请输入社团序号"
              :controls="false"
              :min="0"
              :max="10000"
              :precision="0"
            />
          </el-form-item>

          <el-form-item label="加入是否需要审核" prop="ifAudit">
            <el-radio-group v-model="form.ifAudit">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="领队" prop="initiatorUserName">
            <el-select
              v-model="form.initiatorUserName"
              filterable
              remote
              reserve-keyword
              placeholder="请输入远智编号或手机号码"
              :remote-method="yzCodeRemoteMethod"
              :loading="yzCodeLoading"
              @change="yzCodeChange"
            >
              <el-option v-for="item in yzCodeOptions" :key="item.userId" :label="item.userName" :value="item" />
            </el-select>
          </el-form-item>

          <el-form-item label="角标显示" prop="subscriptShow">
            <el-input v-model="form.subscriptShow" placeholder="请输入角标显示" maxlength="4" show-word-limit />
          </el-form-item>

          <el-form-item label="社团名称" prop="clubsActName">
            <el-input
              v-model="form.clubsActName"
              placeholder="请输入社团名称"
              maxlength="10"
              show-word-limit
              @input="handleClubsActNameInput"
            />
          </el-form-item>

          <el-form-item label="累计跑量基数/累计读书基数" prop="clubsActBaseNum">
            <el-input-number
              v-model="form.clubsActBaseNum"
              placeholder="请输入累计跑量基数"
              :controls="false"
              :min="0"
              :max="1000000"
              :precision="0"
            />
          </el-form-item>

          <el-form-item label="简介" prop="clubsActIntroduce">
            <el-input
              v-model="form.clubsActIntroduce"
              type="textarea"
              placeholder="请输入简介"
              rows="4"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="封面" prop="clubsActCover">
            <upload-file
              :size="2"
              :max-limit="1"
              exts="jpeg|jpg|png|gif|"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 228px * 228px，支持上传 png、jpg 和 gif；图片大小限制 2M 内"
              :file-list="form.clubsActCover"
              @remove="form.clubsActCover = []"
              @success="onClubsActCoverSucc"
            />
          </el-form-item>

          <el-form-item label="照片墙" prop="photoUrls">
            <upload-file
              multiple
              accept="image/png,image/jpeg,image/gif"
              tip="最多可支持上传 20 张，按顺序依次展示；建议图片尺寸宽高比 1:1； 图片大小限制 2M 内"
              :max-limit="20"
              :file-list="form.photoUrls"
              :on-error="onUploadErr"
              @remove="onPhotoUrlsRemove"
              @success="onPhotoUrlsSucc"
            />
          </el-form-item>

          <el-form-item v-if="form.ifAudit === 1" label="加入社团二维码" prop="clubsActQrCode">
            <upload-file
              :max-limit="1"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 300px * 300px，比例 1:1 即可；图片大小限制 2M 内"
              :file-list="form.clubsActQrCode"
              :on-error="onUploadErr"
              @remove="form.clubsActQrCode = []"
              @success="onClubsActQrCodeSucc"
            />
          </el-form-item>

          <el-form-item label="加入达人二维码" prop="clubsActExpertQrCode">
            <upload-file
              :max-limit="1"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 300px * 300px，比例 1:1 即可；图片大小限制 2M 内"
              :file-list="form.clubsActExpertQrCode"
              :on-error="onUploadErr"
              @remove="form.clubsActExpertQrCode = []"
              @success="onClubsActExpertQrCodeSucc"
            />
          </el-form-item>

          <el-form-item label="状态" prop="ifShow">
            <el-radio-group v-model="form.ifShow">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>

      </div>
    </common-dialog>
  </div>
</template>

<script>
import { ClubsActType, ClubsActBuildType } from '@/dict';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      yzCodeLoading: false,
      yzCodeOptions: [],
      dictClubTypes: ClubsActType,
      dictBuildTypes: ClubsActBuildType,
      form: {
        ifAudit: 1,
        ifShow: 0,
        clubsActBaseNum: undefined,
        clubsActCover: [],
        photoUrls: [],
        clubsActQrCode: [],
        clubsActExpertQrCode: []
      },
      rules: {
        clubsActType: [{ required: true, message: '请选择社团类型', trigger: 'change' }],
        clubsActBuildType: [{ required: true, message: '请选择组建类型', trigger: 'change' }],
        sort: [{ required: true, message: '请输入社团序号', trigger: 'blur' }],
        ifAudit: [{ required: true, message: '请选择加入是否需要审核' }],
        initiatorUserName: [{ required: true, message: '请选择领队', trigger: 'blur' }],
        subscriptShow: [
          { required: true, message: '请输入角标显示', trigger: 'blur' },
          { min: 1, max: 4, message: '长度在 1 到 4 个字符', trigger: 'blur' }
        ],
        clubsActName: [{ required: true, message: '请输入社团名称', trigger: 'blur' }],
        clubsActIntroduce: [{ required: true, message: '请输入简介', trigger: 'blur' }],
        clubsActCover: [{ required: true, type: 'array', message: '请上传封面', trigger: 'change' }],
        clubsActQrCode: [{ required: true, type: 'array', message: '请上传加入社团二维码', trigger: 'change' }],
        clubsActExpertQrCode: [{ required: true, type: 'array', message: '请上传加入达人二维码', trigger: 'change' }],
        ifShow: [{ required: true, message: '请选择状态', trigger: 'change' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 社团名称输入限制：允许文字、英文、数字
    handleClubsActNameInput(value) {
      const regex = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/;
      if (!regex.test(value)) {
        this.form.clubsActName = value.slice(0, -1);
      }
    },
    // 图片上传失败
    onUploadErr(err, file, fileList) {
      this.$message.error('上传失败，请稍后再试');
      console.error(err, file, fileList);
    },
    // 封面上传成功
    onClubsActCoverSucc({ fileList }) {
      this.form.clubsActCover = fileList;
    },
    // 照片墙上传成功
    onPhotoUrlsSucc({ fileList }) {
      this.form.photoUrls = fileList;
    },
    // 照片墙删除成功
    onPhotoUrlsRemove({ fileList }) {
      this.form.photoUrls = fileList;
    },
    // 加入社团二维码上传成功
    onClubsActQrCodeSucc({ fileList }) {
      this.form.clubsActQrCode = fileList;
    },
    // 加入达人二维码上传成功
    onClubsActExpertQrCodeSucc({ fileList }) {
      this.form.clubsActExpertQrCode = fileList;
    },
    // 弹窗打开时，如果是编辑则赋值回显
    async open() {
      if (this.row) {
        try {
          this.loading = true;
          const { code, body } = await this.$http({
            method: 'get',
            url: `/clubsActivityInfo/getById/${this.row.id}`
          });
          if (code !== '00') return;
          this.form = {
            ...body,
            // 封面回显
            clubsActCover: body.clubsActCover ? [{ url: body.clubsActCover, response: body.clubsActCover }] : [],
            // 照片墙回显
            photoUrls: body.photoUrls ? body.photoUrls.map(f => ({ url: f, response: f })) : [],
            // 加入社团二维码回显
            clubsActQrCode: body.clubsActQrCode ? [{ url: body.clubsActQrCode, response: body.clubsActQrCode }] : [],
            // 加入达人二维码回显
            clubsActExpertQrCode: body.clubsActExpertQrCode ? [{ url: body.clubsActExpertQrCode, response: body.clubsActExpertQrCode }] : []
          };
        } finally {
          this.loading = false;
        }
      }
    },
    // 输入编号或手机号码 获取用户
    async yzCodeRemoteMethod(query) {
      if (!query) return;
      try {
        this.yzCodeLoading = true;
        const { code, body } = await this.$http({
          method: 'get',
          url: `/clubsActivityInfo/getUserByKey/${query}`
        });
        if (code !== '00') return;
        this.yzCodeOptions = body ?? [];
      } finally {
        this.yzCodeLoading = false;
      }
    },
    // 领队输入编号或手机号码选中后，赋值操作
    yzCodeChange(e) {
      this.form.learnId = e.learnId;
      this.form.initiatorUserId = e.userId;
      this.form.initiatorUserName = e.userName;
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              ...this.form,
              clubsActCover: this.form.clubsActCover?.[0]?.response,
              clubsActQrCode: this.form.ifAudit === 1 ? this.form.clubsActQrCode?.[0]?.response : '',
              clubsActExpertQrCode: this.form.clubsActExpertQrCode?.[0]?.response,
              photoUrls: this.form.photoUrls?.map(f => f.response)
            };
            const url = this.row ? '/clubsActivityInfo/updateById' : '/clubsActivityInfo/add';
            const { code } = await this.$http({
              method: 'post',
              url,
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.show = false;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } catch (error) {
            console.log(error);
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.warning('请检查填写项');
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.el-input-number {
  width: 100%;

  ::v-deep .el-input__inner {
    text-align: left;
  }
}
</style>
