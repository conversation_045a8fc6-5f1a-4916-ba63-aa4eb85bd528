<template>
  <common-dialog
    :show-footer="true"
    width="1300px"
    :title="type === 'edit' ? '编辑训练营' : '新增训练营'"
    :visible.sync="show"
    @confirm="confirm"
    @close="show = false"
  >
    <el-form
      ref="searchForm"
      size="small"
      :model="form"
      label-width="180px"
      :rules="rules"
      @submit.native.prevent="search"
    >
      <el-form-item label="训练营类型：" required style="margin: 18px 0 0 0">
        <el-col :span="4">
          <el-form-item prop="trainCampLearnType">
            <el-select
              v-model="form.trainCampLearnType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in $dictJson['recruitType']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="line" :span="1">-</el-col>
        <el-col :span="4">
          <el-form-item prop="trainCampType">
            <el-select
              v-model="form.trainCampType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in $dictJson['trainCampType']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item label="训练营名称：" prop="trainCampName">
        <el-col :span="9">
          <el-input
            v-model="form.trainCampName"
            placeholder="请输入"
            show-word-limit
            maxlength="10"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="活动天数：" prop="activityDays">
        <el-col :span="9">
          <el-input-number
            v-model="form.activityDays"
            :min="1"
            :max="200"
            style="width: 100%"
            placeholder="请输入"
          />
        </el-col>
      </el-form-item>

      <el-form-item label="开始日期：" prop="enrollStartTime">
        <el-col :span="9">
          <el-date-picker
            v-model="form.enrollStartTime"
            :picker-options="pickerOptionsStar"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
          />
        </el-col>
      </el-form-item>

      <el-form-item label="结束日期：" prop="enrollEndTime">
        <el-col :span="9">
          <el-date-picker
            v-model="form.enrollEndTime"
            :picker-options="pickerOptionsEnd"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期"
          />
        </el-col>
      </el-form-item>

      <el-form-item prop="trainCampPoster" label="训练营海报：">
        <el-col :span="9">
          <el-upload
            class="avatar-uploader"
            :action="action"
            :on-success="
              (response, file, fileList) =>
                uploadSuccess(response, file, fileList, 1)
            "
            :show-file-list="false"
            accept="image/*"
            :before-upload="(file) => beforeAvatarUpload(file, 750, 1448)"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <p>支持图片格式：jpg, bmp, png，图片尺寸750*1448，大小10M以内</p>
        </el-col>
      </el-form-item>
      <el-form-item label="训练营背景图：">
        <div class="uploader-css" style="width: 80%">
          <p>头部图</p>
          <p>中间图</p>
          <p>底部图</p>
        </div>
        <div class="uploader-css">
          <el-form-item prop="trainCampBackgroundHeader">
            <el-upload
              class="avatar-uploader"
              :action="action"
              :data="fieldData"
              :show-file-list="false"
              accept="image/*"
              :on-success="
                (response, file, fileList) =>
                  uploadSuccess(response, file, fileList, 2)
              "
              :before-upload="(file) => beforeAvatarUpload(file, 750, 240)"
            >
              <img v-if="imageUrl2" :src="imageUrl2" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </el-form-item>
          <el-form-item prop="trainCampBackgroundCenter">
            <el-upload
              class="avatar-uploader"
              :action="action"
              :data="fieldData"
              :show-file-list="false"
              accept="image/*"
              :on-success="
                (response, file, fileList) =>
                  uploadSuccess(response, file, fileList, 3)
              "
              :before-upload="(file) => beforeAvatarUpload(file, 750, 1300)"
            >
              <img v-if="imageUrl3" :src="imageUrl3" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </el-form-item>
          <el-form-item prop="trainCampBackgroundFooter">
            <el-upload
              class="avatar-uploader"
              :action="action"
              :data="fieldData"
              :show-file-list="false"
              accept="image/*"
              :on-success="
                (response, file, fileList) =>
                  uploadSuccess(response, file, fileList, 4)
              "
              :before-upload="(file) => beforeAvatarUpload(file, 750, 420)"
            >
              <img v-if="imageUrl4" :src="imageUrl4" class="avatar">
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
          </el-form-item>
        </div>
        <a class="txt" @click="imgShow = true">样图</a>
        <div class="uploader-css">
          <div class="uploader-text">
            <p>支持图片格式：jpg, bmp, png，图片尺寸750*240，大小 10M 以内</p>
          </div>
          <div class="uploader-text">
            <p>支持图片格式：jpg, bmp, png，图片尺寸750*1300，大小 10M 以内</p>
          </div>
          <div class="uploader-text">
            <p>支持图片格式：jpg, bmp, png，图片尺寸750*420，大小 10M 以内</p>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="关卡解锁模式：" prop="levelMode">
        <el-col :span="9">
          <el-select v-model="form.levelMode" clearable placeholder="请选择">
            <el-option label="立即解锁" value="1" />
            <el-option label="按日解锁" value="2" />
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item label="每日闯关提醒时间：" prop="barrierTipTime">
        <!-- <el-col :span="10">
          <el-time-picker
            v-model="form.barrierTipTime"
            placeholder="任意时间点"
            format="HH:mm"
            value-format="HH:mm"
            :picker-options="{
              selectableRange: '5:00:00 - 23:00:00'
            }"
          />
        </el-col> -->
        <el-radio-group v-model="form.barrierTipTime">
          <el-radio label="08:00">8:00</el-radio>
          <el-radio label="12:00">12:00</el-radio>
          <el-radio label="20:00">20:00</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="入营推送消息：" prop="joinPushContent">
        <el-col :span="9">
          <el-input
            v-model.trim="form.joinPushContent"
            type="textarea"
            placeholder="请输入内容"
            maxlength="20"
            show-word-limit
          />
        </el-col>
      </el-form-item>
      <el-form-item>
        <p>重点注意：</p>
        <p>1、可用"[NAME]"代替学员姓名；</p>
        <p>2、消息仅支持展示前20个字，请提炼重点内容。</p>
        <p>3、不允许换行，否则影响展示效果。</p>
      </el-form-item>

      <el-form-item label="是否配置联系老师：" prop="ifConfigTeacher">
        <el-radio-group v-model="form.ifConfigTeacher">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>

      <div v-if="form.ifConfigTeacher == 1">
        <el-form-item label="联系老师配置：" prop="teacherConfigType">
          <el-radio-group v-model="form.teacherConfigType">
            <el-radio :label="1">对应跟进人企微</el-radio>
            <el-radio :label="2">固定企微</el-radio>
          </el-radio-group>
          <div v-if="form.teacherConfigType === 1" style="color: #aaa">
            若用户当前无跟进人，将展示以下老师信息
          </div>
        </el-form-item>
        <el-form-item label="老师姓名：" prop="teacherName">
          <el-col :span="9">
            <el-input
              v-model="form.teacherName"
              placeholder="请输入"
              maxlength="8"
              show-word-limit
            />
          </el-col>
        </el-form-item>

        <el-form-item label="职务：" prop="teacherJob">
          <el-col :span="9">
            <el-input
              v-model="form.teacherJob"
              placeholder="请输入"
              maxlength="15"
              show-word-limit
            />
          </el-col>
        </el-form-item>

        <el-form-item label="手机号：" prop="teacherMobile">
          <el-col :span="9">
            <el-input
              v-model="form.teacherMobile"
              placeholder="请输入"
              maxlength="11"
              show-word-limit
            />
          </el-col>
        </el-form-item>

        <el-form-item prop="qwQrCode" label="企微二维码：">
          <el-upload
            class="avatar-uploader"
            :action="action"
            :on-success="
              (response, file, fileList) =>
                uploadSuccess(response, file, fileList, 5)
            "
            :show-file-list="false"
            accept="image/*"
            :before-upload="(file) => beforeAvatarUpload(file, 'ratio', 1, 2)"
          >
            <img v-if="imageUrl5" :src="imageUrl5" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <p>支持图片格式：jpg, bmp, png，长宽比 1:1，大小 2M 以内</p>
        </el-form-item>
      </div>
    </el-form>

    <!-- 样图显示 -->
    <div v-show="imgShow" class="demo-img">
      <i class="el-icon-close" @click="imgShow = false" />
      <img
        src="https://yzims.oss-cn-shenzhen.aliyuncs.com/zkxly.png"
        style="width: 380px; height: 700px"
      >
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { validate } from '@/utils/validate';

const rules = {
  trainCampLearnType: [
    { required: true, message: '请输入训练营学业类型', trigger: 'blur' }
  ],
  trainCampType: [
    { required: true, message: '请输入训练营类型', trigger: 'change' }
  ],
  trainCampName: [
    { required: true, message: '请输入活动名称', trigger: 'blur' }
  ],
  activityDays: [
    { required: true, message: '请输入活动天数', trigger: 'blur' }
  ],
  enrollStartTime: [
    { required: true, message: '请输入活动开始时间', trigger: 'blur' }
  ],
  enrollEndTime: [
    { required: true, message: '请输入活动结束时间', trigger: 'blur' }
  ],
  levelMode: [
    { required: true, message: '请输入关卡解锁模式', trigger: 'change' }
  ],
  barrierTipTime: [
    {
      required: true,
      message: '请输入每日闯关提醒时间',
      trigger: 'blur'
    }
  ],
  trainCampPoster: [
    { required: true, message: '上传训练营海报图', trigger: 'change' }
  ],
  trainCampBackgroundHeader: [
    {
      required: true,
      message: '请上传训练营背景头部图',
      trigger: 'change'
    }
  ],
  trainCampBackgroundCenter: [
    {
      required: true,
      message: '请上传训练营背景中间图',
      trigger: 'change'
    }
  ],
  trainCampBackgroundFooter: [
    {
      required: true,
      message: '请上传训练营背景底部图',
      trigger: 'change'
    }
  ],
  joinPushContent: [{ required: true, message: '请输入入营推送消息' }],
  ifConfigTeacher: [{ required: true, message: '请选择是否配置联系老师' }],
  teacherConfigType: [{ required: true, message: '请选择联系老师配置' }],
  teacherName: [{ required: true, message: '请输入老师姓名' }],
  teacherJob: [{ required: true, message: '请输入职务' }],
  teacherMobile: [
    { required: true, message: '请输入手机号' },
    // 验证合法手机号码
    {
      validator: (rule, value, callback) => {
        if (!validate('mobile', value)) {
          callback(new Error('请输入正确的手机号'));
        } else callback();
      },
      trigger: 'blur'
    }
  ],
  qwQrCode: [
    { required: true, message: '请上传企微二维码', trigger: 'change' }
  ]
};

const initForm = {
  trainCampLearnType: '',
  trainCampType: '',
  trainCampName: '',
  activityDays: undefined,
  enrollStartTime: '',
  enrollEndTime: '',
  levelMode: '',
  barrierTipTime: '20:00',
  trainCampPoster: '',
  trainCampBackgroundHeader: '',
  trainCampBackgroundCenter: '',
  trainCampBackgroundFooter: '',
  joinPushContent: '',
  ifConfigTeacher: 0,
  teacherConfigType: 1,
  teacherName: '',
  teacherJob: '',
  teacherMobile: '',
  qwQrCode: ''
};

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    editId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      action: '/file/uploadFile.do',
      rules,
      imgShow: false,
      form: JSON.parse(JSON.stringify(initForm)),
      imageUrl: '',
      imageUrl2: '',
      imageUrl3: '',
      imageUrl4: '',
      imageUrl5: '',
      value1: '',
      fieldData: {
        fileSrc: 'trainCamp'
      },
      imgURL: '',
      imgName: '',
      pickerOptionsStar: {
        disabledDate(time) {
          return (
            time.getTime() <
            new Date(new Date().setHours(0, 0, 0, 0)).getTime() - 1
          );
        }
      },
      pickerOptionsEnd: {
        disabledDate(time) {
          return time.getTime() < new Date();
        }
      }
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit('update:visible', v);
      }
    }
  },
  watch: {
    show(v) {
      if (v && this.type === 'edit') {
        this.getCampDetail();
      } else {
        this.$nextTick(() => {
          this.imageUrl = '';
          this.imageUrl2 = '';
          this.imageUrl3 = '';
          this.imageUrl4 = '';
          this.imageUrl5 = '';
          this.form = JSON.parse(JSON.stringify(initForm));
        });
      }
    }
  },
  methods: {
    confirm() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.postCamp();
        } else {
          this.$message.warning('请检查填写项');
        }
      });
    },
    postCamp() {
      const data = {
        ...this.form,
        trainCampLearnType: Number(this.form.trainCampLearnType)
      };
      this.$post(this.type === 'add' ? 'addCamp' : 'updateCamp', data, {
        json: true
      }).then((res) => {
        if (res.ok) {
          this.$message.success(this.type === 'add' ? '添加成功' : '编辑成功');
          this.show = false;
          this.$emit('fresh');
          this.$refs['searchForm'].resetFields();
        }
      });
    },
    beforeAvatarUpload(file, width, height, size = 10) {
      const isJPG = [
        'image/bmp',
        'image/png',
        'image/jpg',
        'image/jpeg'
      ].includes(file.type);
      if (!isJPG) {
        this.$message.error('请按格式要求上传图片！');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < size;
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 10M！');
        return false;
      }
      const isSize = new Promise(function(resolve, reject) {
        const URL = window.URL || window.webkitURL;
        const img = new Image();
        img.onload = function() {
          // 限制图片比例
          if (width === 'ratio') {
            img.width / img.height === height
              ? resolve()
              : reject('图片长宽比不规范，请按格式要求上传图片！');
          } else {
            img.width === width && img.height === height
              ? resolve()
              : reject('图片尺寸不规范，请按格式要求上传图片！');
          }
        };
        img.src = URL.createObjectURL(file);
      }).then(
        () => {
          return file;
        },
        (e) => {
          this.$message.error(e);
          return Promise.reject();
        }
      );
      return isJPG && isLt2M && isSize;
    },
    // 图片上传成功
    uploadSuccess(response, file, fileList, num) {
      if (num === 1) {
        this.imageUrl = URL.createObjectURL(file.raw);
        this.form.trainCampPoster = response.body;
        this.$refs.searchForm.validateField('trainCampPoster');
      } else if (num === 2) {
        this.imageUrl2 = URL.createObjectURL(file.raw);
        this.form.trainCampBackgroundHeader = response.body;
        this.$refs.searchForm.validateField('trainCampBackgroundHeader');
      } else if (num === 3) {
        this.imageUrl3 = URL.createObjectURL(file.raw);
        this.form.trainCampBackgroundCenter = response.body;
        this.$refs.searchForm.validateField('trainCampBackgroundCenter');
      } else if (num === 4) {
        this.imageUrl4 = URL.createObjectURL(file.raw);
        this.form.trainCampBackgroundFooter = response.body;
        this.$refs.searchForm.validateField('trainCampBackgroundFooter');
      } else {
        this.imageUrl5 = URL.createObjectURL(file.raw);
        this.form.qwQrCode = response.body;
        this.$refs.searchForm.validateField('qwQrCode');
      }
    },
    getCampDetail() {
      this.$http({
        method: 'get',
        url: `/newTrainCamp/campDetail/${this.editId}`
      }).then((res) => {
        if (res.ok) {
          this.imgURL = ossUri;
          res.body.trainCampLearnType = String(res.body.trainCampLearnType);
          res.body.trainCampType = String(res.body.trainCampType);
          res.body.levelMode = String(res.body.levelMode);
          this.imageUrl = res.body.trainCampPoster;
          this.imageUrl2 = res.body.trainCampBackgroundHeader;
          this.imageUrl3 = res.body.trainCampBackgroundCenter;
          this.imageUrl4 = res.body.trainCampBackgroundFooter;
          this.imageUrl5 = res.body.qwQrCode;
          this.form = res.body;
          this.form.teacherConfigType = res.body.teacherConfigType || undefined;// 存量数据字段为 0 需转换为 undefined
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.line {
  text-align: center;
}
.uploader-css {
  p {
    margin-right: 213px;
  }
  display: flex;
  .uploader-text {
    margin-right: -170px;
    p {
      width: 210px;
      line-height: 1.5;
    }
  }
}
::v-deep .el-form-item__content {
  position: relative;
}
.txt {
  position: absolute;
  left: -90px;
  top: 120px;
}
.demo-img {
  position: absolute;
  right: 0;
  top: 0;
  i {
    font-size: 30px;
    position: absolute;
    right: 0px;
    top: 0;
  }
}
.avatar-uploader ::v-deep {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin-right: 75px;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
