<template>
  <div class="yz-base-container horses_push">
    <!-- 表单区 -->
    <el-form
      size="mini"
      :model="querys"
      label-width="120px"
      class="yz-search-form"
      @submit.native.prevent="getTableList"
    >
      <el-form-item label="推送标签">
        <div
          class="horses_push-tree"
          :style="{ color: querys.tagId ? '#000' : '' }"
          @click="handleRow(0)"
        >
          {{ querys.tagCategoryName }}
        </div>
      </el-form-item>
      <el-form-item label="启用状态">
        <el-select
          v-model="querys.isEnable"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click.stop="refreshBtn"
        />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="horses_push-btnbox">
      <div class="yz-pdesc">
        <h4>备注:</h4>
        <p>针对同时满足以下条件的用户，每天10:00将触发探马消息提醒</p>
        <ul>
          <li>任务id=启用状态</li>
          <li>用户标签=推送标签</li>
          <li>用户标签权重>=推送权重值</li>
        </ul>
      </div>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleRow(1)"
      >新建推送标签</el-button>
    </div>
    <!-- 表格区 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column label="任务Id" prop="id" align="center" />
      <el-table-column
        label="推送标签类型"
        prop="tagLargeCategoryName"
        align="center"
      />
      <el-table-column label="推送标签" prop="tagName" align="center" />
      <el-table-column label="推送权重值" prop="sort" align="center" />
      <el-table-column
        label="推送用户总数（次日0点更新）"
        prop="totalPushCount"
        align="center"
      >
        <template slot-scope="scope">
          <div class="horses_push-aline" @click="openDetailsBtn(scope.row)">
            {{ scope.row.totalPushCount }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="已推送用户（次日0点更新）"
        prop="pushCount"
        align="center"
      />
      <el-table-column
        label="待推送用户（次日0点更新）"
        prop="noPushCount"
        align="center"
      />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <!-- <el-table-column label="app引导文案" prop="jumpText" align="center" />
      <el-table-column label="app跳转链接" prop="jumpUrl" align="center" />
      <el-table-column label="app排序" prop="dataSort" align="center" /> -->
      <el-table-column label="创建人" prop="createUser" align="center" />
      <el-table-column label="启用状态" prop="isEnable" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.isEnable ? "启用" : "禁用" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="small"
            @click="handleRow(2, scope.row)"
          >编辑</el-button>
          <el-button
            :type="scope.row.isEnable ? 'info' : 'success'"
            size="small"
            @click="onEnableBtn(scope.row)"
          >
            {{ scope.row.isEnable ? "禁用" : "启用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.rows"
        @pagination="getTableList"
      />
    </div>
    <!-- 新建用户推送标签 -->
    <common-dialog
      :showFooter="true"
      :visible.sync="pushVisible"
      @close="closePushBtn"
      @confirm="submitPushBtn"
    >
      <el-form
        ref="pushForm"
        class="horses_push-popup"
        size="mini"
        label-width="120px"
        :model="pushTags"
        :rules="pushTagRule"
      >
        <el-form-item label="推送标签：" prop="title">
          <el-input
            v-model="pushTags.title"
            maxlength="30"
            show-word-limit
            placeholder="请输入标签查询"
            :disabled="queryType === 2"
            @change="titleChange"
          />
        </el-form-item>
        <div v-if="queryType !== 2" class="popup-tree">
          <el-tree
            ref="selTree"
            accordion
            :highlight-current="true"
            :data="pushTagData"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @current-change="getCheckedNodes"
          />
        </div>
        <template v-if="queryType">
          <el-form-item label="推送权重值：" prop="sort">
            <el-input-number
              v-model="pushTags.sort"
              :controls="false"
              :min="0"
              :disabled="queryType === 2"
              show-word-limit
              placeholder="请输入正整数"
            />
          </el-form-item>
          <div v-if="queryType !== 2" class="popup-desc">
            备注: <span>【推送权重值】请谨慎填写，创建后无法修改</span>
          </div>
          <!-- <el-form-item label="app引导文案：" prop="jumpText">
            <el-input v-model="pushTags.jumpText" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="app跳转链接：" prop="jumpUrl">
            <el-input v-model="pushTags.jumpUrl" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="app排序：" prop="dataSort">
            <el-input-number
              v-model="pushTags.dataSort"
              :controls="false"
              :min="0"
              placeholder="数字越大的排前面"
            />
          </el-form-item> -->
        </template>
      </el-form>
    </common-dialog>
    <!-- 用户推送明细 -->
    <common-dialog
      class="horses_push-details"
      is-full
      title="用户推送明细"
      :visible.sync="detailsVisible"
      @close="closeDetailsBtn"
      @open="getDetailsList"
    >
      <el-form
        ref="detailsForm"
        class="yz-search-form details-search"
        size="mini"
        label-width="120px"
        :model="details"
      >
        <el-form-item label="真实姓名：">
          <el-input v-model="details.realName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="远智编号：">
          <el-input v-model="details.yzCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="任务id">
          <el-input v-model="details.taskId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="手机号码：">
          <el-input v-model="details.mobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否已推送">
          <el-select
            v-model="details.isPush"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="跟进人姓名">
          <el-input v-model="details.followName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否报读">
          <el-select
            v-model="details.isRead"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否缴费">
          <el-select
            v-model="details.isPay"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <div class="details-btn">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-download"
          @click="exportBtn"
        >导出名单</el-button>
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          @click="getDetailsList"
        >搜索</el-button>
      </div>
      <el-table
        v-loading="detailsLoading"
        border
        size="small"
        header-cell-class-name="table-cell-header"
        :data="detailsData"
      >
        <el-table-column label="任务Id" prop="taskId" align="center" />
        <el-table-column label="推送标签" prop="tagName" align="center" />
        <el-table-column label="推送权重值" prop="tagSort" align="center" />
        <el-table-column label="用户信息" align="center">
          <template slot-scope="scope">
            <p>远智编号：{{ scope.row.yzCode || "" }}</p>
            <p>真实姓名：{{ scope.row.realName || "" }}</p>
            <p>手机号码：{{ scope.row.mobile || "" }}</p>
          </template>
        </el-table-column>
        <el-table-column label="跟进人姓名" prop="followName" align="center" />
        <el-table-column label="是否报读" prop="isRead" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.isRead ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否缴费" prop="isPay" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.isPay ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="对应标签的权重"
          prop="userTagSort"
          align="center"
        />
        <el-table-column label="是否已推送" prop="isPush" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.isPush ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="推送日期" prop="pushTime" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="detailsPagin.total"
          :page.sync="detailsPagin.page"
          :limit.sync="detailsPagin.rows"
          @pagination="getDetailsList"
        />
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { exportExcel, getCutDay } from '@/utils';

export default {
  components: {},
  data() {
    return {
      queryType: 1,
      querysClose: 1,
      querys: {
        tagCategoryName: '请输入推送标签',
        tagId: '',
        isEnable: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        rows: 10
      },
      // 新建推送标签
      pushVisible: false,
      pushTags: {
        title: '',
        sort: '',
        tagId: '',
        tagCategoryId: '',
        tagCategoryCode: '',
        jumpText: '',
        jumpUrl: '',
        dataSort: undefined
      },
      pushTagData: [],
      defaultProps: {
        children: 'childrens',
        label: 'tagZdyName'
      },
      pushTagRule: {
        title: [{ required: true, message: '请选择', trigger: 'change' }],
        sort: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (value === '' || value === undefined) {
                callback(new Error('请输入0-10000正整数'));
                return;
              }
              if (value > 10000) {
                callback(new Error('请正确输入0-10000范围内的正整数'));
                return;
              }
              if (!/^[1-9]\d*$/.test(Number(value || 0))) {
                callback(new Error('请正确输入正整数'));
                return;
              }
              callback();
            }
          }
        ]
      },
      // 明细
      details: {
        taskId: '.000'
      },
      detailsPagin: {
        page: 1,
        total: 0,
        rows: 10
      },
      detailsVisible: false,
      detailsLoading: false,
      detailsData: []
    };
  },
  created() {
    this.getTableList();
    this.getTagsList();
  },
  // 方法集合
  methods: {
    // 重置
    refreshBtn() {
      this.querysClose = 1;
      this.querys = {
        tagCategoryName: '请输入推送标签',
        tagId: '',
        isEnable: ''
      };
    },
    // 新建推送设置
    handleRow(type, row) {
      this.queryType = type;

      // 编辑
      if (type === 2) {
        this.pushTags = {
          id: row.id,
          title: row.tagName,
          sort: row.sort,
          tagId: row.tagId,
          tagCategoryId: row.tagCategoryId,
          tagCategoryCode: row.tagCategoryCode,
          jumpText: row.jumpText,
          jumpUrl: row.jumpUrl,
          dataSort: row.dataSort || undefined
        };
      }
      this.pushVisible = true;
    },
    // 启用禁用
    onEnableBtn(row) {
      const { id, tagId, isEnable } = row;
      const test = isEnable
        ? '推送标签禁用后，标签对应用户数不再更新，每日10：00不触发探马推送'
        : '推送标签启用后，将于每日10：00，针对待推送人群进行推送,（次日生效）';
      this.$confirm(test, `确定【${isEnable ? '禁用' : '启用'}】当前标签？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'none'
      })
        .then(() => {
          this.$post('tanmaUpdateIsEnable', {
            id,
            tagId,
            isEnable: isEnable ? 0 : 1
          })
            .then((res) => {
              if (res?.code === '00') this.getTableList();
            })
            .catch(() => {});
        })
        .catch((err) => {
          console.log('启用禁用-err', err);
        });
    },
    // 模糊查询
    titleChange(title) {
      setTimeout(() => {
        this.$refs.selTree.filter(title);
      }, 0);
    },
    // 过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.tagZdyName?.indexOf(value) !== -1;
    },
    // 获取当前选中的标签值
    getCheckedNodes(data) {
      if (!data.tagInfoId) {
        this.$message({ message: '该标签不存在tagInfoId', type: 'warning' });
        this.pushTags = {
          title: '',
          sort: '',
          tagId: '',
          tagCategoryId: '',
          tagCategoryCode: ''
        };
        return;
      }
      let tagLargeCategoryName = '';
      let tagCategoryCode = data?.tagCategoryCode?.slice(0, 2);
      if (tagCategoryCode === 'A1') {
        const news = data?.tagCategoryCode?.slice(0, 3);
        tagCategoryCode = news === 'A10' ? 'A10' : 'A1';
      }
      switch (tagCategoryCode) {
        case 'A0':
          tagLargeCategoryName = '个人标签';
          break;
        case 'A1':
          tagLargeCategoryName = '基础标签';
          break;
        case 'A2':
          tagLargeCategoryName = '营销标签';
          break;
        case 'A3':
          tagLargeCategoryName = '上进学社';
          break;
        case 'A4':
          tagLargeCategoryName = '社区标签';
          break;
        case 'A5':
          tagLargeCategoryName = '行为标签';
          break;
        case 'A6':
          tagLargeCategoryName = '成教学情标签';
          break;
        case 'A7':
          tagLargeCategoryName = '国开学情标签';
          break;
        case 'A8':
          tagLargeCategoryName = '自考学情标签';
          break;
        case 'A9':
          tagLargeCategoryName = '兴趣标签';
          break;
        case 'A10':
          tagLargeCategoryName = '第三方标签';
          break;
        default:
          tagLargeCategoryName = '未知标签';
          break;
      }
      this.pushTags = {
        ...data,
        tagLargeCategoryName,
        title: data.tagZdyName,
        tagId: data.tagInfoId
      };
    },
    // 获取标签列表
    getTagsList() {
      this.$post('queryTreeForSelect', {}, { json: 'application/json' })
        .then((res) => {
          this.pushTagData = this.loops(res?.body || []);
        })
        .catch(() => {
          this.pushTagData = [];
        });
    },
    loops(data) {
      data.map((item) => {
        item.tagZdyName = item.tagCategoryName;
        if (!item.childrens?.length && item.nodes?.length) {
          item.nodes.map((chie) => {
            chie.tagZdyName = chie.tagInfoName;
          });
          item.childrens = item.nodes;
        } else {
          return this.loops(item.childrens);
        }
        return item;
      });
      return data;
    },
    // 提交推送设置
    submitPushBtn() {
      this.$refs['pushForm'].validate(async(valid) => {
        if (valid) {
          if (this.queryType === 1) {
            // 新增
            await this.$post('addTanmaConfig', this.pushTags, {
              json: 'application/json'
            });
            this.querysClose = 0;
            this.getTableList();
          } else if (this.queryType === 2) {
            // 编辑
            await this.$post('tanmaUpdateIsEnable', this.pushTags);
            this.querysClose = 0;
            this.getTableList();
          } else {
            // 查询
            this.querys.tagCategoryName = this.pushTags.title;
            this.querys.tagId = this.pushTags.tagId;
            this.querysClose = 0;
          }
          this.closePushBtn();
        }
      });
    },
    // 关闭推送设置
    closePushBtn() {
      if (this.querysClose) {
        this.querys.tagId = '';
        this.querys.tagCategoryName = '请输入推送标签';
      }
      this.pushVisible = false;
      this.pushTags = {
        title: '',
        sort: '',
        tagId: '',
        tagCategoryId: '',
        tagCategoryCode: '',
        jumpText: '',
        jumpUrl: '',
        dataSort: undefined
      };
      this.$refs['pushForm'].resetFields();
    },
    // 获取探马推送消息列表
    getTableList() {
      this.tableLoading = true;
      const obs = { ...this.querys, ...this.pagination };
      this.$post('queryByPage', obs, { json: 'application/json' })
        .then((res) => {
          const { code, body } = res;
          if (code === '00' && body?.data) {
            body.data.map((item) => {
              item.totalPushCount =
                Number(item.noPushCount) + Number(item.pushCount);
              // console.log('获取探马推送消息列表', item, item.pushCount);
              item.isEnable = Number(item?.isEnable || 0);
              item.createTime = item.createTime && getCutDay(item.createTime);
              return item;
            });
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
          this.tableLoading = false;
        })
        .catch(() => {
          this.tableData = [];
          this.tableLoading = false;
        });
    },
    // 打开明细记录弹窗
    openDetailsBtn(row) {
      console.log('打开明细记录弹窗', row);
      this.details.taskId = row.id;
      this.detailsVisible = true;
    },
    // 获取探马推送消息列表-明细
    getDetailsList() {
      this.detailsData = [];
      this.detailsLoading = true;
      const params = { ...this.details, ...this.detailsPagin };
      console.log('明细', params);
      this.$post('getPushUserDetail', params, { json: 'application/json' })
        .then((res) => {
          const { code, body } = res;
          // console.log('获取探马推送消息列表-明细', res);
          if (code === '00' && body?.data) {
            body.data.map((item) => {
              item.pushTime = item.pushTime && getCutDay(item.pushTime);
              item.isRead = Number(item?.isRead || 0);
              item.isPay = Number(item?.isPay || 0);
              item.isPush = Number(item?.isPush || 0);
            });
            this.detailsData = body.data;
            this.detailsPagin.total = body.recordsTotal;
          }
          this.detailsLoading = false;
        })
        .catch(() => {
          this.detailsLoading = false;
        });
    },
    // 导出名单-用户明细
    exportBtn() {
      exportExcel('tanmaExport', { taskId: this.details.taskId });
    },
    // 关闭明细记录弹窗
    closeDetailsBtn() {
      this.details = {};
      this.detailsVisible = false;
    }
  }
};
</script>

<style lang="scss">
.horses_push {
  &-tree {
    width: 100%;
    padding: 0 15px;
    height: 28px;
    line-height: 28px;
    appearance: none;
    -webkit-appearance: none;
    background-color: #fff;
    background-image: none;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: #c0c4cc;
    display: inline-block;
    outline: 0;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    cursor: pointer;
  }
  &-btnbox {
    margin: 4px 0 18px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    text-align: left;
    .yz-pdesc {
      margin-left: 3%;
      font-size: 14px;
      line-height: 24px;
      h4 {
        margin: 0;
        color: #606266;
      }
      p {
        color: #606266;
      }
      ul {
        margin-left: 40px;
        color: rgb(255, 22, 22);
        li {
          list-style: disc;
        }
      }
    }
    .el-button--small {
      height: 30px;
    }
  }
  &-aline {
    color: #8bbef1;
    font-size: 15px;
  }
  &-aline:hover {
    color: #1a87f3;
    cursor: pointer;
    text-decoration: underline;
  }
  // 新建推送
  &-popup {
    margin: 20px;
    .el-input-number--mini {
      width: 100%;
      .el-input__inner {
        text-align: left !important;
      }
    }
    .popup-tree {
      padding: 20px;
      margin: 0 0 20px 80px;
      max-height: 320px;
      overflow: hidden;
      overflow-y: scroll;
      border: 1px solid #eef2f7;
    }
    .popup-desc {
      margin: 0 0 15px 24px;
      font-style: italic;
      span {
        color: rgb(255, 22, 22);
      }
    }
  }
  // 明细
  &-details {
    .yz-common-dialog__container {
      padding: 20px;
    }
    .details-search {
      margin: 20px 20px 0;
    }
    .details-btn {
      margin-bottom: 20px;
      text-align: right;
    }
  }
}
</style>
