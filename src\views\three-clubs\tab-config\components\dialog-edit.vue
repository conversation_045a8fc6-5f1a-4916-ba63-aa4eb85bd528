<template>
  <div>
    <common-dialog
      show-footer
      width="60%"
      title="编辑"
      confirmText="保存"
      :visible.sync="show"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form
          ref="form"
          class="form"
          label-suffix=":"
          size="mini"
          :model="form"
          label-width="150px"
          :rules="rules"
        >
          <el-form-item label="类型" prop="type">{{ ClubTypeName }}</el-form-item>

          <el-form-item label="展示名称" prop="clubsName">
            <el-input v-model="form.clubsName" maxlength="5" show-word-limit />
          </el-form-item>

          <el-form-item :label="ClubTypeName + '成员基数'" prop="fictitiousMember">
            <el-input-number v-model="form.fictitiousMember" :controls="false" :max="1000000" :min="0" :precision="0" />
          </el-form-item>

          <el-form-item label="实际人员数量" prop="actualMember">
            {{ form.actualMember }}
          </el-form-item>

          <el-form-item :label="getClubTypeBaseLabel(form.clubsType)" prop="fictitiousBaseNum">
            <el-input-number
              v-model="form.fictitiousBaseNum"
              :controls="false"
              :max="1000000"
              :min="0"
              :precision="0"
            />
          </el-form-item>

          <el-form-item :label="getActualBaseLabel(form.clubsType)" prop="actualBaseNum">
            {{ form.actualBaseNum }}
          </el-form-item>

          <el-form-item label="简介" prop="clubsIntroduce">
            <el-input v-model="form.clubsIntroduce" type="textarea" :rows="3" maxlength="100" show-word-limit />
          </el-form-item>

          <el-form-item label="封面" prop="clubsCover">
            <upload-file
              :size="2"
              :max-limit="1"
              exts="jpeg|jpg|png|gif"
              accept="image/png,image/jpeg,image/gif"
              :file-list="form.clubsCover"
              tip="建议尺寸 228px * 228px，支持上传 png、jpg 和 gif；图片大小限制 2M 内"
              @remove="form.clubsCover = []"
              @success="onClubsCoverSucc"
            />
          </el-form-item>
          <el-form-item v-if="isPublic" label="成为志愿者二维码" prop="clubsQrCode">
            <upload-file
              :size="2"
              :max-limit="1"
              exts="jpeg|jpg|png|gif|"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 640px * 888px；图片大小限制 2M 内"
              :file-list="form.clubsQrCode"
              @remove="form.clubsQrCode = []"
              @success="onQrCodeSuccess"
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { ClubType, ClubTypeMap } from '@/dict';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      form: {
        clubsName: '',
        clubsType: '',
        actualMember: undefined,
        fictitiousMember: undefined,
        actualBaseNum: undefined,
        fictitiousBaseNum: undefined,
        clubsIntroduce: '',
        clubsCover: [],
        clubsQrCode: []
      },
      rules: {
        clubsName: [
          { required: true, message: '请输入展示名称', trigger: 'blur' },
          { min: 1, max: 5, message: '长度在 1 到 5 个字符', trigger: 'blur' }
        ],
        fictitiousMember: [{ required: true, message: '请输入成员基数', trigger: 'blur' }],
        fictitiousBaseNum: [{ required: true, message: '请输入基数', trigger: 'blur' }],
        clubsIntroduce: [{ required: true, message: '请输入简介', trigger: 'blur' }],
        clubsCover: [{ required: true, type: 'array', message: '请上传封面', trigger: 'change' }],
        clubsQrCode: [{ required: true, type: 'array', message: '请上传成为志愿者二维码', trigger: 'change' }]
      }
    };
  },
  computed: {
    // 获取类型名称
    ClubTypeName() {
      return ClubType.find(v => v.dictValue === this.form?.clubsType)?.dictName ?? '';
    },
    // 是否公益
    isPublic() {
      return this.form?.clubsType === ClubTypeMap.public;
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 封面上传成功
    onClubsCoverSucc({ fileList }) {
      this.form.clubsCover = fileList;
      this.$refs.form.validateField('clubsCover');
    },
    // 成为志愿者二维码上传成功
    onQrCodeSuccess({ fileList }) {
      this.form.clubsQrCode = fileList;
      this.$refs.form.validateField('clubsQrCode');
    },
    // 获取类型基数名称
    getClubTypeBaseLabel(type) {
      switch (type * 1) {
        case ClubTypeMap.run:
          return '跑步帖子基数';
        case ClubTypeMap.read:
          return '读书基数';
        case ClubTypeMap.public:
          return '场次基数';
        default:
          return '基数';
      }
    },
    // 获取基数对应实际数量名称
    getActualBaseLabel(type) {
      switch (type * 1) {
        case ClubTypeMap.run:
          return '实际帖子数量';
        case ClubTypeMap.read:
          return '实际读书数量';
        case ClubTypeMap.public:
          return '实际场次数量';
        default:
          return '实际数量';
      }
    },
    // 弹窗打开时
    async open() {
      if (this.row) {
        try {
          this.loading = true;
          const { code, body } = await this.$http({
            method: 'get',
            url: `/clubsInfo/getById/${this.row.id}`
          });
          if (code !== '00') return;
          this.form = {
            ...body,
            clubsCover: body.clubsCover ? [{ url: body.clubsCover, response: body.clubsCover }] : [],
            clubsQrCode: body.clubsQrCode ? [{ url: body.clubsQrCode, response: body.clubsQrCode }] : []
          };
        } finally {
          this.loading = false;
        }
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row.id,
              clubsName: this.form.clubsName,
              fictitiousMember: this.form.fictitiousMember,
              fictitiousBaseNum: this.form.fictitiousBaseNum,
              clubsIntroduce: this.form.clubsIntroduce,
              clubsCover: this.form.clubsCover[0].response
            };
            if (this.form.clubsQrCode?.length) {
              params.clubsQrCode = this.form.clubsQrCode[0].response; // 社团二维码
            }
            const { code } = await this.$http({
              method: 'post',
              url: '/clubsInfo/updateById',
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.show = false;
            this.$emit('confirm');
            this.$message.success('操作成功');
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number {
  .el-input__inner {
    text-align: left;
  }

  width: 100%;
}
</style>
