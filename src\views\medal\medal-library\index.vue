<template>
  <div class="yz-base-container">
    <!-- 搜索表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      label-suffix=":"
      @submit.native.prevent="search"
    >
      <!-- 搜索项 -->
      <el-form-item label="勋章Id" prop="medalTimeId">
        <el-input v-model="form.medalTimeId" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="勋章分类" prop="timeType">
        <el-select v-model="form.timeType" clearable>
          <el-option
            v-for="item in medalCategoryList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="勋章类型" prop="medalType">
        <el-select v-model="form.medalType" clearable>
          <el-option
            v-for="item in medalTypeList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="触发场景" prop="toastType">
        <el-select v-model="form.toastType" clearable>
          <el-option
            v-for="item in triggerScenesList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否可领取" prop="gain">
        <el-select v-model="form.gain" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="勋章名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入" />
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          :disabled="tableLoading"
        >
          搜索
        </el-button>
        <el-button
          :disabled="tableLoading"
          icon="el-icon-refresh"
          size="mini"
          @click="search(0)"
        />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <div class="right">
        <el-button type="primary" size="small" @click="handleAdd">
          新建勋章
        </el-button>
      </div>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column prop="medalTimeId" label="勋章Id" align="center" />
      <el-table-column prop="timeType" label="勋章分类" align="center">
        <template slot-scope="{ row }">
          {{ row.timeType | medalCategoryEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="medalType" label="勋章类型" align="center">
        <template slot-scope="{ row }">
          {{ row.medalType | medalTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="勋章名称" align="center" />
      <el-table-column prop="context" label="勋章描述" align="center" />
      <el-table-column prop="toastType" label="触发场景" align="center">
        <template slot-scope="{ row }">
          {{ row.toastType | triggerScenesEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="勋章权重" align="center" />
      <el-table-column prop="createTime" label="勋章创建时间" align="center" />
      <el-table-column prop="gainUserSum" label="勋章获得用户数" align="center" />
      <el-table-column prop="useUserSum" label="当前佩戴人数" align="center" />
      <el-table-column prop="gain" label="是否可领取" align="center">
        <template slot-scope="{ row }">
          {{ row.gain === 1 ? "是" : "否" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="80">
        <template slot-scope="{ row }">
          <el-button size="mini" type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 新增/编辑弹框 -->
    <add-edit
      :visible.sync="addEditVisible"
      :currentRow="currentRow"
      @refresh="getTableList"
    />
  </div>
</template>

<script>
import AddEdit from './components/add-edit.vue'; // 引入新增/编辑组件
import { medalCategoryList, medalTypeList, triggerScenesList } from './../type';
import { arrToEnum } from '@/utils';

const medalCategoryEnum = arrToEnum(medalCategoryList, 'value', 'name');
const medalTypeEnum = arrToEnum(medalTypeList, 'value', 'name');
const triggerScenesEnum = arrToEnum(triggerScenesList, 'value', 'name');

export default {
  components: {
    AddEdit
  },
  filters: {
    medalTypeEnum(val) {
      return medalTypeEnum[val] || '/';
    },
    medalCategoryEnum(val) {
      return medalCategoryEnum[val] || '/';
    },
    triggerScenesEnum(val) {
      return triggerScenesEnum[val] || '/';
    }
  },
  data() {
    return {
      medalCategoryList, // 勋章分类
      medalTypeList, // 勋章类型
      triggerScenesList, // 触发场景
      tableLoading: false,
      addEditVisible: false, // 控制新增/编辑弹框显示
      currentRow: {}, // 当前编辑行数据
      form: {
        medalTimeId: '',
        timeType: '',
        medalType: '',
        toastType: '',
        gain: '',
        name: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 打开新增弹框
    handleAdd() {
      this.currentRow = {};
      this.addEditVisible = true;
    },
    // 打开编辑弹框
    handleEdit(row) {
      this.currentRow = { ...row };
      this.addEditVisible = true;
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const data = this.handleQueryParams();
      this.$post('getMedaTimelList', data, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body?.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>
