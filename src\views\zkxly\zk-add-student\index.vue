<template>
  <div class="app-main">
    <div class="back">
      <span style="font-size: 12px">添加学员</span>
      <i class="el-icon-close" @click="$router.go(-1)" />
    </div>

    <div class="yz-base-container">
      <!-- 表单 -->
      <el-form
        ref="searchForm"
        class="yz-search-form"
        size="mini"
        :model="form"
        label-width="120px"
        @submit.native.prevent="search"
      >
        <el-form-item label="学业编码：" prop="learnId">
          <el-input v-model="form.learnId" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item label="远智编码：" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="招生类型:" prop="recruitType">
          <el-select
            v-model="form.recruitType"
            clearable
            placeholder="请选择"
            disabled
          >
            <el-option
              v-for="item in $dictJson['recruitType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="报考层次：" prop="pfsnLevel">
          <el-select v-model="form.pfsnLevel" clearable placeholder="请选择">
            <el-option
              v-for="item in $dictJson['pfsnLevel']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年级：" prop="grade">
          <el-select
            v-model="form.grade"
            clearable
            placeholder="请选择"
            filterable
          >
            <el-option
              v-for="item in $dictJson['grade']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="院校：" prop="unvsId">
          <el-select
            v-model="form.unvsId"
            clearable
            filterable
            remote
            placeholder="请输入关键词"
            :loading="unvSLoading"
            :filter-method="getUnvSList"
            @change="getMajorList"
          >
            <el-option
              v-for="item in unvSList"
              :key="item.unvs_id"
              :label="item.unvs_name"
              :value="item.unvs_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="专业：" prop="pfsnId">
          <el-select
            v-model="form.pfsnId"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="item in majorList"
              :key="item.pfsnId"
              :label="item.pfsnName"
              :value="item.pfsnId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="自考考期：" prop="examinationId">
          <el-select
            v-model="form.examinationId"
            clearable
            filterable
            remote
            placeholder="请输入关键词"
            :loading="examLoading"
            :filter-method="getExaminationList"
          >
            <el-option
              v-for="item in examList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学员姓名：" prop="stdName">
          <el-input v-model="form.stdName" placeholder="请输入" clearable />
        </el-form-item>

        <el-form-item label="证件号码：" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="手机号：" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="是否目标学员：" prop="isChecked">
          <el-select v-model="form.isChecked" clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="缴费时间起：" prop="startPayTime">
          <el-date-picker
            v-model="form.startPayTime"
            type="date"
            placeholder="选择日期时间"
            clearable
          />
        </el-form-item>
        <el-form-item label="缴费时间止：" prop="endPayTime">
          <el-date-picker
            v-model="form.endPayTime"
            type="date"
            placeholder="选择日期时间"
            :picker-options="pickerEndDate"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="学员阶段："
          prop="stdStages"
          style="width: 100vw; padding-right: 120px"
        >
          <el-checkbox-group v-model="form.stdStages">
            <el-checkbox label="1">意向学员</el-checkbox>
            <el-checkbox label="2">考前辅导</el-checkbox>
            <el-checkbox label="3">考前确认</el-checkbox>
            <el-checkbox label="4">入学考试</el-checkbox>
            <el-checkbox label="5">已录取</el-checkbox>
            <el-checkbox label="6">注册学员</el-checkbox>
            <el-checkbox label="7">在读学员</el-checkbox>
            <el-checkbox label="8">毕业学员</el-checkbox>
            <el-checkbox label="9">留级学员</el-checkbox>
            <el-checkbox label="10">退学学员</el-checkbox>
            <el-checkbox label="11">其他</el-checkbox>
            <el-checkbox label="12">待录取</el-checkbox>
            <el-checkbox label="13">待注册</el-checkbox>
            <el-checkbox label="14">休学学员</el-checkbox>
            <el-checkbox label="15">服务终止</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <div class="search-reset-box">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox">
        <el-popconfirm
          title="是否确认添加选中?"
          cancel-button-type="button"
          style="margin-right: 10px"
          @confirm="addSelected()"
        >
          <el-button
            slot="reference"
            type="primary"
            size="small"
          >添加选中</el-button>
        </el-popconfirm>
        <el-popconfirm
          title="是否确认清除选中?"
          cancel-button-type="button"
          style="margin-right: 10px"
          @confirm="clearSelected()"
        >
          <el-button
            slot="reference"
            type="primary"
            size="small"
          >清除选中</el-button>
        </el-popconfirm>
        <el-popconfirm
          title="是否确认添加全部?"
          cancel-button-type="button"
          style="margin-right: 10px"
          @confirm="addBoth()"
        >
          <el-button
            slot="reference"
            type="primary"
            size="small"
          >添加全部</el-button>
        </el-popconfirm>
        <el-popconfirm
          title="是否确认清除全部?"
          cancel-button-type="button"
          style="margin-right: 10px"
          @confirm="clearBoth()"
        >
          <el-button
            slot="reference"
            type="primary"
            size="small"
          >清除全部</el-button>
        </el-popconfirm>
        <el-button
          type="primary"
          size="small"
          @click="bidVisibel = true"
        >目标学员导入</el-button>
      </div>
      <!-- 批量导入 -->
      <batch-import-dialog :visible.sync="bidVisibel" @refresh="getTableList" />

      <!-- 表格 -->
      <el-table
        ref="multipleTable"
        v-loading="loading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection" />
        <el-table-column prop="isChecked" label="是否目标学员" align="center" />
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="stdName" label="学员姓名" align="center" />
        <el-table-column prop="tutorName" label="班主任" align="center" />
        <el-table-column prop="grade" label="年级" align="center" />
        <el-table-column prop="recruitType" label="招生类型" align="center">
          <template slot-scope="scope">{{
            scope.row.recruitType | recruitTypeMethod
          }}</template>
        </el-table-column>
        <el-table-column prop="pfsnName" label="院校专业" align="center" />
        <el-table-column prop="stdStage" label="学员阶段" align="center">
          <template slot-scope="scope">{{
            scope.row.stdStage | stdStageMethod
          }}</template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import batchImportDialog from './batch-import-dialog';
import moment from 'moment';
import Axios from 'axios';

const CancelToken = Axios.CancelToken;

export default {
  components: { batchImportDialog },
  data() {
    return {
      form: {
        learnId: '',
        yzCode: '',
        stdName: '',
        idCard: '',
        mobile: '',
        grade: '',
        unvsId: '',
        pfsnId: '',
        pfsnLevel: '',
        isChecked: '',
        recruitType: this.$route.query.trainCampLearnType,
        examinationId: '',
        stdStages: [],
        startPayTime: '',
        endPayTime: '',
        mappingId: this.$route.query.id
      },
      loading: false,
      tableData: [],
      controller: null,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      levelShoe: false,
      trainingCampaShow: false,
      checkList: [],
      bidVisibel: false,
      multipleSelection: [],
      examList: [],
      examLoading: false,
      majorList: [],
      unvSList: [],
      unvSLoading: false,
      pickerEndDate: {
        disabledDate: (time) => {
          const beginDateVal = this.form.startPayTime;
          if (beginDateVal) {
            return (
              time.getTime() <
              new Date(beginDateVal).getTime() + 1 * 24 * 60 * 60 * 1000
            );
          }
        }
      }
    };
  },
  created() {
    this.getTableList();
  },
  methods: {

    // 获取院校数据
    getUnvSList(sName) {
      if (!sName) return;
      this.unvSLoading = true;
      this.$http({
        method: 'get',
        url: '/bdUniversity/findAllKeyValue.do',
        params: { page: 1, rows: 7, sName }
      })
        .then((res) => {
          if (res.ok) {
            this.unvSList = res.body.data;
          }
        })
        .finally(() => {
          this.unvSLoading = false;
        });
    },
    // 获取专业数据
    getMajorList(sName) {
      this.form.pfsnId = '';
      this.majorList = [];
      if (!sName) return;
      this.$http({
        method: 'get',
        url: '/baseinfo/sPfsn.do',
        params: { page: 1, rows: 500, sName }
      })
        .then((res) => {
          if (res.ok) {
            this.majorList = res.body.data;
          }
        });
    },
    // 获取自考考期数据
    getExaminationList(key) {
      if (!key) return;
      this.examLoading = true;
      this.$http({
        method: 'post',
        url: '/zkExamination/findAllEffectiveExaminationList',
        params: {
          start: 0,
          length: 7,
          examinationName: key
        }
      })
        .then((res) => {
          if (res.ok) {
            this.examList = res.body.data;
          }
        })
        .finally(() => {
          this.examLoading = false;
        });
    },
    // 获取列表数据
    async getTableList() {
      try {
        this.controller?.cancel('已取消上次一次未完成的请求');
        this.loading = true;
        const data = {
          ...this.form,
          startPayTime: this.form.startPayTime
            ? moment(this.form.startPayTime).format('YYYY-MM-DD HH:mm:ss')
            : '',
          endPayTime: this.form.endPayTime
            ? moment(this.form.endPayTime).format('YYYY-MM-DD') +
              ' ' +
              '23:59:59'
            : '',
          code: 'TC_CODE',
          start: (this.pagination.page - 1) * this.pagination.limit,
          length: this.pagination.limit
        };
        this.controller = CancelToken.source();
        const res = await this.$post('targetStudent', data, {
          json: true,
          cancelToken: this.controller.token
        });
        if (res.ok) {
          res.body.data.forEach((item) => {
            item.isChecked =
              this.$route.query.id === item.mappingId ? '是' : '否';
          });
          this.tableData = res.body.data;
          this.pagination.total = res.body.recordsTotal;
        }
        this.loading = false;
      } catch (error) {
        if (!Axios.isCancel(error)) {
          this.controller = null;
          this.loading = false;
        }
      }
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      }
      this.pagination.page = 1;
      this.getTableList();
    },
    // 添加选中
    addSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择列表数据');
        return false;
      }
      const data = [];
      this.multipleSelection.forEach((item) => {
        const obj = {
          userId: item.userId,
          learnId: item.learnId,
          stdId: item.stdId,
          mappingId: this.$route.query.id,
          code: 'TC_CODE',
          dataSource: '2',
          mappingType: this.$route.query.trainCampType
        };
        data.push(obj);
      });
      this.$post('addStu', data, { json: true }).then((res) => {
        if (res.ok) {
          this.$message.success('添加成功');
          this.$refs.multipleTable.clearSelection();
          this.getTableList();
        }
      });
    },
    // 清除选中
    clearSelected() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选择列表数据');
        return false;
      }
      const userId = [];
      this.multipleSelection.forEach((item) => {
        userId.push(item.userId);
      });
      const data = {
        userIds: userId,
        mappingId: this.$route.query.id,
        code: 'TC_CODE'
      };
      this.$post('cleanStu', data, { json: true }).then((res) => {
        if (res.ok) {
          this.$message.success('清除成功');
          this.$refs.multipleTable.clearSelection();
          this.getTableList();
        }
      });
    },
    // 添加全部
    addBoth() {
      const data = {
        ...this.form,
        code: 'TC_CODE',
        mappingId: this.$route.query.id
      };
      this.$post('addAllStu', data, { json: true }).then((res) => {
        if (res.ok) {
          this.$message.success('添加全部成功');
          this.$refs.multipleTable.clearSelection();
          this.getTableList();
        }
      });
    },
    // 清除全部
    clearBoth() {
      const data = {
        ...this.form,
        code: 'TC_CODE',
        mappingId: this.$route.query.id,
        mappingType: this.$route.query.trainCampType
      };
      this.$post('cleanAllStu', data, { json: true }).then((res) => {
        if (res.ok) {
          this.$message.success('清除全部成功');
          this.$refs.multipleTable.clearSelection();
          this.getTableList();
        }
      });
    },
    selectionChange(selection) {
      this.multipleSelection = selection;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-checkbox {
  margin-right: 23px;
}
.back {
  margin: 7px 15px 0 10px;
  color: #808c95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
}
</style>
>
