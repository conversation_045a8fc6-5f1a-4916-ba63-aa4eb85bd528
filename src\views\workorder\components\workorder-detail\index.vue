<template>
  <common-dialog
    :visible.sync="visible"
    title="工单详情"
    width="1200px"
    :show-footer="false"
    @close="handleClose"
  >
    <div v-loading="loading" class="flex">
      <!-- 左侧工单详情，添加独立滚动区域 -->
      <div class="p-5 flex-1 overflow-auto h-170 scroll-y">
        <!-- 工单基本信息 -->
        <div class="mb-5">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex">
              <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                >工单类型：</span
              >
              <span class="flex-1">{{ workOrderData.orderType }}</span>
            </div>
            <div class="flex">
              <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                >工单标题：</span
              >
              <span class="flex-1">{{ workOrderData.title }}</span>
            </div>
            <div class="flex">
              <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                >工单优先级：</span
              >
              <span
                class="flex-1"
                :class="getPriorityClass(workOrderData.priority)"
              >
                {{ getPriorityText(workOrderData.priority) }}
              </span>
            </div>
            <template v-if="orderType() === '1'">
              <div class="flex">
                <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                  >咨询分类：</span
                >
                <span class="flex-1"
                  >{{ workOrderData.consultType1 }} -
                  {{ workOrderData.consultType2 }}</span
                >
              </div>
              <div class="flex col-span-2">
                <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                  >工单内容：</span
                >
                <span class="flex-1 whitespace-pre-wrap leading-normal">{{
                  workOrderData.content
                }}</span>
              </div></template
            >
            <template v-if="orderType() === '2'">
              <div class="flex">
                <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                  >投诉来源：</span
                >
                <span class="flex-1">{{
                  workOrderData.category1DictIdText +
                  " - " +
                  workOrderData.category2DictIdText
                }}</span>
              </div>
              <div class="flex">
                <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                  >投诉原因：</span
                >
                <span class="flex-1">{{ workOrderData.reasonDictIdText }}</span>
              </div>
              <div class="flex col-span-2">
                <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                  >投诉内容：</span
                >
                <span class="flex-1 whitespace-pre-wrap leading-normal">{{
                  workOrderData.content
                }}</span>
              </div>
            </template>
            <div class="flex col-span-2">
              <span class="w-25 text-right pr-2.5 text-gray-500 flex-shrink-0"
                >受理人：</span
              >
              <span class="flex-1">{{ workOrderData.receiveEmpName }}</span>
            </div>
          </div>
        </div>

        <!-- 附件展示 -->
        <div
          v-if="workOrderData.fileList && workOrderData.fileList.length > 0"
          class="mb-5 p-4 rounded"
        >
          <div class="text-sm font-medium mb-2.5 text-gray-600">附件</div>
          <div class="flex flex-wrap gap-4">
            <div
              v-for="(file, index) in workOrderData.fileList"
              :key="file.id || index"
              class="w-25 cursor-pointer"
              @click="handleFileClick(file)"
            >
              <!-- <div v-if="isVideoFile(file.name)" class="relative w-25 h-25 rounded overflow-hidden border border-gray-200">
                <video
                  :src="file.localPath || file.url"
                  class="w-full h-full object-cover"
                  controls
                  muted
                ></video>
              </div> -->
              <!-- 图片文件 -->
              <div
                v-if="isImageFile(file.name)"
                class="relative w-25 h-25 rounded overflow-hidden border border-gray-200"
              >
                <el-image
                  :src="file.url"
                  fit="cover"
                  class="w-full h-full object-cover"
                >
                  <div
                    slot="error"
                    class="w-full h-full flex justify-center items-center bg-gray-100 text-gray-400"
                  >
                    <i class="el-icon-picture-outline" />
                  </div>
                </el-image>
                <div
                  class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center opacity-0 hover:opacity-100 transition-opacity"
                >
                  <i
                    class="el-icon-view text-white text-xl mx-2 cursor-pointer hover:transform hover:scale-110"
                    @click.stop="previewImage(file)"
                  />
                  <i
                    class="el-icon-download text-white text-xl mx-2 cursor-pointer hover:transform hover:scale-110"
                    @click.stop="downloadFile(file)"
                  />
                </div>
              </div>
              <!-- 非图片文件 -->
              <div
                v-else
                class="relative w-25 h-25 flex justify-center items-center bg-gray-100 rounded border border-gray-200"
              >
                <i
                  :class="getFileIcon(file.name)"
                  class="text-4xl text-gray-400"
                />
                <div
                  class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center opacity-0 hover:opacity-100 transition-opacity"
                >
                  <i
                    class="el-icon-download text-white text-xl cursor-pointer hover:transform hover:scale-110"
                    @click.stop="downloadFile(file)"
                  />
                </div>
              </div>
              <div
                class="mt-1 text-xs text-gray-600 text-center truncate"
                :title="file.name"
              >
                {{ file.name }}
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div
          v-if="!finished && hasOperatePermission"
          class="flex justify-center gap-5 my-5"
        >
          <el-button
            type="primary"
            @click="handleAction('transfer')"
            :disabled="workOrderData.isRefund"
            >转交</el-button
          >

          <el-popover v-model="suspendVisible" placement="top" width="240">
            <p class="text-center mb-3">确定要挂起该工单吗？</p>
            <div style="text-align: right; margin: 0">
              <el-button size="mini" @click="suspendVisible = false"
                >取消</el-button
              >
              <el-button type="primary" size="mini" @click="handleSuspend"
                >确定</el-button
              >
            </div>
            <el-button
              slot="reference"
              type="warning"
              :disabled="workOrderData.status === '3' || workOrderData.isRefund"
              >挂起</el-button
            >
          </el-popover>

          <el-button
            type="danger"
            :disabled="workOrderData.isRefund"
            @click="handleAction('refund')"
            >退费</el-button
          >
          <el-button
            type="success"
            :disabled="finished"
            @click="handleAction('finish')"
            >完结</el-button
          >
        </div>

        <!-- 回复表单 -->
        <div v-if="!finished && hasOperatePermission" class="p-5 rounded my-5">
          <el-form
            ref="replyForm"
            :model="replyForm"
            :rules="replyRules"
            label-width="80px"
          >
            <el-form-item label="回复内容" prop="comment">
              <el-input
                v-model="replyForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入回复内容"
                :maxlength="500"
                show-word-limit
              />
              <upload-attachment v-model="replyForm.filesList" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitReply">回复</el-button>
            </el-form-item>
          </el-form>
        </div>
        <history-list :list="workOrderData.logs" />
      </div>

      <!-- 右侧个人信息，添加独立滚动区域 -->
      <div
        class="w-100 h-170 scroll-y border-l border-gray-200 p-4 overflow-auto"
      >
        <div class="text-base font-medium mb-4">• 个人信息</div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">工单发起人：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.createEmpIdText }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">学员姓名：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.stuName }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">手机号码：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.stuPhone }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">远智编码：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.stuYzCode }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">学业编码：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.stuXyCode }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">招生类型：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.stuEnrollType }}
          </div>
        </div>

        <div class="mb-4 flex">
          <div class="w-40 flex-shrink-0 text-right pr-3 mb-1">院校：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.universityName }}
          </div>
        </div>
        <div class="mb-4 flex">
          <div class="w-40 flex-shrink-0 text-right pr-3 mb-1">专业：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.majorName }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">来电号码：</div>
          <div class="flex-1">
            <el-input
              v-model="userInfo.stuCallPhone"
              placeholder="请输入来电号码"
              suffix-icon="el-icon-check"
              size="mini"
              :disabled="finished"
              @blur="saveUserInfo"
              @keyup.enter.native="saveUserInfo"
            />
          </div>
        </div>
        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">缴费时间：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.payTime }}
          </div>
        </div>
        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">通过科目数：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.passCourseCount }}
          </div>
        </div>
        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">
            学员累计上课时长：
          </div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.studyDuration }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">跟进老师：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.followEmpName }}
          </div>
        </div>

        <div class="mb-4 flex items-center">
          <div class="w-40 flex-shrink-0 text-right pr-3">跟进老师部门：</div>
          <div class="text-gray-700 flex-1">
            {{ userInfo.followEmpDepart }}
          </div>
        </div>
        <template v-if="orderType() === '2'">
          <div class="mb-4 flex items-center">
            <div class="w-40 flex-shrink-0 text-right pr-3">是否回函：</div>
            <div class="flex-1">
              <common-select
                v-model="userInfo.needReply"
                type="isOrNot_2"
                placeholder="请选择"
                size="mini"
                :disabled="finished"
                @change="saveUserInfo"
              />
            </div>
          </div>

          <div class="mb-4 flex items-center">
            <div class="w-40 flex-shrink-0 text-right pr-3">是否解决：</div>
            <div class="flex-1">
              <common-select
                v-model="userInfo.isRevoke"
                type="isOrNot_2"
                placeholder="请选择"
                size="mini"
                :disabled="finished"
                @change="saveUserInfo"
              />
            </div>
          </div>
          <div class="mb-4 flex items-center">
            <div class="w-40 flex-shrink-0 text-right pr-3">
              前端是否有服务：
            </div>
            <div class="flex-1">
              <common-select
                v-model="userInfo.hasServiceService"
                type="isOrNot_2"
                placeholder="请选择"
                size="mini"
                :disabled="finished"
                @change="saveUserInfo"
              />
            </div>
          </div>

          <div class="mb-4 flex items-center">
            <div class="w-40 flex-shrink-0 text-right pr-3">
              后端是否有服务：
            </div>
            <div class="flex-1">
              <common-select
                v-model="userInfo.hasBackendService"
                type="isOrNot_2"
                placeholder="请选择"
                size="mini"
                :disabled="finished"
                @change="saveUserInfo"
              />
            </div></div
        ></template>
      </div>
    </div>

    <!-- 图片预览 -->
    <media-viewer
      v-if="previewVisible"
      :url-list="previewUrlList"
      :initial-index="previewIndex"
      :z-index="2000"
      @close="previewVisible = false"
    />

    <!-- 转交弹窗 -->
    <transfer-dialog
      :visible.sync="transferDialogVisible"
      :selected-work-orders="[workOrderData]"
      @success="handleTransferSuccess"
    />

    <!-- 退费弹窗 -->
    <refund-dialog
      :visible.sync="refundDialogVisible"
      :workOrderId="workOrderId"
      :workOrderType="workOrderType"
      @success="handleRefundSuccess"
    />

    <!-- 完结工单弹窗 -->
    <finish-dialog
      :visible.sync="finishDialogVisible"
      :workOrderId="workOrderId"
      @success="handleFinishSuccess"
    />
  </common-dialog>
</template>

<script>
import { imageData } from "@/components/formTools/MediaViewer/utils";
import { request } from "@/api";

export default {
  name: "WorkorderDetail",
  components: {
    CommonDialog: () => import("@/components/common-dialog"),
    UploadAttachment: () => import("../upload-attachment"),
    MediaViewer: () => import("@/components/formTools/MediaViewer"),
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
    HistoryList: () => import("./workorder-history"),
    TransferDialog: () => import("../transfer-dialog"),
    RefundDialog: () => import("./refund-dialog"),
    FinishDialog: () => import("./finish-dialog"),
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // 工单ID
    workOrderId: {
      type: [Number, String],
      default: "",
    },
    // 工单类型
    workOrderType: {
      type: [Number, String],
      default: "",
    },
  },
  inject: ["orderType"],
  data() {
    return {
      hasOperatePermission: false, // 是否有操作权限
      loading: false,
      workOrderData: {},
      attachmentList: [],
      userInfo: {
        createEmpIdText: "",
        stuName: "",
        stuPhone: "",
        stuYzCode: "",
        stuXyCode: "",
        stuEnrollType: "",
        universityName: "",
        majorName: "",
        stuCallPhone: "",
        payTime: "",
        passCourseCount: "",
        studyDuration: "",
        followEmpName: "",
        followEmpDepart: "",
        needReply: "",
        isRevoke: "",
        hasServiceService: "",
        hasBackendService: "",
      },
      replyForm: {
        comment: "",
        filesList: [],
      },
      replyRules: {
        comment: [
          { required: true, message: "请输入回复内容", trigger: "blur" },
        ],
      },
      previewVisible: false,
      previewUrlList: [],
      previewIndex: 0,
      statusText: "",
      transferDialogVisible: false,
      suspendVisible: false,
      refundDialogVisible: false, // 退费弹窗显示状态
      finishDialogVisible: false, // 完结工单弹窗显示状态
    };
  },
  computed: {
    // 工单状态是否已完结
    finished() {
      return this.workOrderData.status === "1";
    },
  },
  watch: {
    visible(val) {
      if (val && this.workOrderId) {
        this.fetchWorkOrderDetail();
      }
    },
  },
  async created() {
    // 检查是否有操作权限，如果有operateAll权限，则必定有操作权限
    this.hasOperatePermission =
      (await this.$checkPermission("workOrder:operateAll")) ||
      (await this.$checkPermission("workOrder:operate"));
  },
  methods: {
    formatSeconds(seconds) {
      if (!seconds) {
        return "-";
      }
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      // 格式化小时和分钟，不足两位时前面补0
      const formattedHours = hours.toString().padStart(2, "0");
      const formattedMinutes = minutes.toString().padStart(2, "0");
      return `${formattedHours} 小时 ${formattedMinutes} 分钟`;
    },
    // 获取工单详情数据
    fetchWorkOrderDetail() {
      this.loading = true;

      // 根据工单类型选择不同的接口
      const apiName =
        this.orderType() === "1" ? "getConsultDetail" : "getComplaintDetail";

      request(apiName, { id: this.workOrderId }, { json: true })
        .then((res) => {
          if (res.ok) {
            this.handleWorkOrderData(res.body);
          } else {
            this.$message.error(res.msg || "获取工单详情失败");
          }
        })
        .catch((err) => {
          console.error("获取工单详情失败", err);
          this.$message.error("获取工单详情失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理工单数据
    handleWorkOrderData(data) {
      // 如果没有数据，直接返回
      if (!data) {
        this.$message.error("未获取到工单详情数据");
        return;
      }

      if (this.orderType() === "1") {
        // 咨询类工单
        this.workOrderData = {
          id: data.consultId,
          orderNo: data.consultNo,
          orderType: data.orderTypeText,
          title: data.title,
          priority: data.priority === 2 ? "urgent" : "normal",
          status: String(data.consultStatus), // 1已完结，2受理中，3已挂起，4已退费
          consultType1: data.category1DictIdText,
          consultType2: data.category2DictIdText,
          content: data.content,
          fileList: data.filesList || [],
          logs: data.logs || [],
          isRefund: String(data.consultStatus) === "4", // 是否已退费，默认为false
          receiveEmpName: data.receiveEmpName,
        };
      } else {
        // 投诉类工单
        this.workOrderData = {
          id: data.complaintId,
          orderNo: data.complaintNo,
          orderType: data.orderTypeText,
          title: data.title,
          priority: data.priority === 2 ? "urgent" : "normal",
          status: String(data.complaintStatus || data.status), // 1已完结，2受理中，3已挂起，4已退费
          category1DictIdText: data.category1DictIdText,
          category2DictIdText: data.category2DictIdText,
          reasonDictIdText: data.reasonDictIdText,
          content: data.content,
          fileList: data.filesList || [],
          logs: data.logs || [],
          isRefund: String(data.complaintStatus) === "4", // 是否已退费，默认为false
          receiveEmpName: data.receiveEmpName,
        };
      }

      // 初始化用户信息
      this.initUserInfo(data);

      // 添加工单状态说明
      this.statusText = this.getStatusText(this.workOrderData.status);
    },

    // 初始化用户信息
    initUserInfo(data) {
      this.userInfo = {
        createEmpIdText: data.createEmpIdText,
        stuName: data.stuName,
        stuPhone: data.stuPhone,
        stuYzCode: data.stuYzCode,
        stuXyCode: data.stuXyCode,
        stuEnrollType: data.stuEnrollType,
        universityName: data.universityName,
        majorName: data.majorName,
        stuCallPhone: data.stuCallPhone,
        payTime: data.payTime ? this.formatDate(data.payTime) : "",
        passCourseCount:
          data.passCourseCount === null ? "-" : data.passCourseCount,
        studyDuration: data.studyDuration
          ? this.formatSeconds(data.studyDuration)
          : "-",
        followEmpName: data.followEmpName,
        followEmpDepart: data.followEmpDepart,
        needReply: data.needReply,
        isRevoke: data.isRevoke,
        hasServiceService: data.hasServiceService,
        hasBackendService: data.hasBackendService,
      };
    },

    // 格式化日期
    formatDate(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
    },

    // 格式化秒数为小时
    // formatSeconds(seconds) {
    //   if (!seconds) return '0小时';
    //   const hours = Math.floor(seconds / 3600);
    //   return `${hours}小时`;
    // },

    // 从URL中提取文件名
    getFileNameFromUrl(url) {
      if (!url) return "未知文件";
      const parts = url.split("/");
      return parts[parts.length - 1];
    },

    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();

      if (["jpg", "jpeg", "png", "gif", "bmp"].includes(extension)) {
        return "el-icon-picture-outline";
      } else if (["doc", "docx"].includes(extension)) {
        return "fa fa-file-word-o";
      } else if (["xls", "xlsx"].includes(extension)) {
        return "fa fa-file-excel-o";
      } else if (["ppt", "pptx"].includes(extension)) {
        return "fa fa-file-powerpoint-o";
      } else if (extension === "pdf") {
        return "fa fa-file-pdf-o";
      } else if (["zip", "rar", "7z"].includes(extension)) {
        return "fa fa-file-archive-o";
      } else if (["mp4", "avi", "mov", "wmv", "mkv"].includes(extension)) {
        return "fa fa-file-video-o";
      } else {
        return "el-icon-document";
      }
    },

    // 判断文件是否为图片
    isImageFile(fileName) {
      if (!fileName) return false;
      const extension = fileName.split(".").pop().toLowerCase();
      return imageData.includes(extension);
    },

    // 判断文件是否为视频
    isVideoFile(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();
      return ["mp4", "avi", "mov", "wmv", "mkv"].includes(extension);
    },

    // 处理文件点击
    handleFileClick(file) {
      if (!this.isImageFile(file.name)) {
        // 如果是非图片文件，尝试下载
        this.downloadFile(file);
      }
      // 图片文件点击不做任何操作，通过专门的预览按钮预览
    },

    // 下载文件
    downloadFile(file) {
      console.log("下载文件:", file);

      // 判断是否为图片文件
      if (this.isImageFile(file.name)) {
        this.downloadImage(file);
      } else {
        this.downloadNormalFile(file);
      }
    },

    // 下载图片文件
    downloadImage(file) {
      // 创建一个canvas元素
      const image = new Image();
      image.crossOrigin = "Anonymous"; // 解决跨域问题

      image.onload = () => {
        // 创建canvas
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;

        // 绘制图片到canvas
        const ctx = canvas.getContext("2d");
        ctx.drawImage(image, 0, 0);

        // 将canvas转换为blob
        canvas.toBlob((blob) => {
          // 创建下载链接
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = file.name;
          link.style.display = "none";

          // 添加到body并点击
          document.body.appendChild(link);
          link.click();

          // 清理
          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 100);
        });
      };

      image.onerror = () => {
        // 图片加载失败，使用备用方法
        this.fallbackDownload(file);
      };

      // 设置图片源
      image.src = file.url;
    },

    // 下载普通文件
    downloadNormalFile(file) {
      // 创建一个隐藏的a标签
      const link = document.createElement("a");
      link.href = file.url;
      link.download = file.name;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 备用下载方法
    fallbackDownload(file) {
      try {
        // 使用iframe方式下载
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.src = file.url;
        document.body.appendChild(iframe);

        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);

        this.$message({
          message: `${file.name} 开始下载，如果没有自动下载，请右键图片选择"另存为"`,
          type: "info",
          duration: 5000,
        });
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error('下载失败，请右键点击图片选择"图片另存为"');
      }
    },

    // 预览图片
    previewImage(file) {
      // 获取所有图片文件
      const imageFiles = this.workOrderData.fileList.filter((item) =>
        this.isImageFile(item.name)
      );

      // 获取所有图片URL
      this.previewUrlList = imageFiles.map((item) => item.url);

      // 找到当前点击图片的索引
      this.previewIndex = imageFiles.findIndex((item) => item.id === file.id);
      if (this.previewIndex === -1) this.previewIndex = 0;

      // 显示预览
      this.previewVisible = true;
    },

    // 获取优先级文本
    getPriorityText(priority) {
      if (priority === "urgent") return "紧急";
      return "一般";
    },

    // 获取优先级样式类
    getPriorityClass(priority) {
      if (priority === "urgent") return "text-red-500 font-medium";
      return "text-blue-500";
    },

    // 处理操作按钮点击
    handleAction(action) {
      switch (action) {
        case "transfer":
          this.transferDialogVisible = true;
          break;
        case "finish":
          if (!this.finished) {
            this.finishDialogVisible = true;
          } else {
            this.$message.warning("该工单已完结");
          }
          break;
        case "refund":
          if (!this.workOrderData.isRefund) {
            this.refundDialogVisible = true;
          } else {
            this.$message.warning("该工单已完成退费操作");
          }
          break;
        case "suspend":
          this.suspendVisible = true;
          break;
      }
    },

    // 提交回复
    submitReply() {
      this.$refs.replyForm.validate((valid) => {
        if (valid) {
          request(
            "commentWorkOrder",
            {
              orderId: this.workOrderId,
              type: this.orderType(),
              comment: this.replyForm.comment, // 回复内容
              files: this.replyForm.filesList, // 文件
            },
            { json: true }
          )
            .then((res) => {
              if (res.ok) {
                this.$message.success("回复提交成功");
                // 清空回复表单
                this.replyForm.comment = "";
                this.replyForm.filesList = [];
                // 重新获取工单详情
                this.fetchWorkOrderDetail();
              } else {
                this.$message.error(res.msg || "回复提交失败");
              }
            })
            .catch((err) => {
              console.error("回复提交失败", err);
              this.$message.error("回复提交失败");
            });
        }
      });
    },

    // 关闭弹窗
    handleClose() {
      this.workOrderData = {};
      this.attachmentList = [];
      this.userInfo = {};
      this.replyForm.comment = "";
      this.replyForm.filesList = [];
      this.previewVisible = false;
      this.previewUrlList = [];
      this.statusText = "";
      this.$emit("update:visible", false);
    },

    // 保存用户信息
    saveUserInfo() {
      request(
        "editWorkOrderDetail",
        {
          orderId: this.workOrderId,
          type: this.orderType(),
          ...this.userInfo,
        },
        { json: true }
      ).then((res) => {
        if (res.ok) {
          this.$message.success("用户信息已保存");
        } else {
          this.$message.error(res.msg || "用户信息保存失败");
        }
      });
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case "1":
          return "已完结";
        case "2":
          return "受理中";
        case "3":
          return "已挂起";
        default:
          return "未知状态";
      }
    },

    // 处理转交成功
    handleTransferSuccess() {
      // 关闭当前详情弹窗
      this.$emit("update:visible", false);
      // 通知父组件刷新列表
      this.$emit("refresh");
    },

    // 处理挂起确认
    handleSuspend() {
      console.log("确认挂起工单:", this.workOrderId);
      // 调用挂起工单的接口
      request(
        "hangWorkOrder",
        {
          orderId: this.workOrderId,
          type: this.orderType(),
        },
        { json: true }
      )
        .then((res) => {
          if (res.ok) {
            this.$message.success("工单已成功挂起");
            // 关闭当前详情弹窗
            this.$emit("update:visible", false);
            // 通知父组件刷新列表
            this.$emit("refresh");
          } else {
            this.$message.error(res.msg || "挂起工单失败");
          }
        })
        .catch((err) => {
          console.error("挂起工单失败", err);
          this.$message.error("挂起工单失败");
        });
      this.suspendVisible = false;
    },

    // 处理退费成功
    handleRefundSuccess() {
      this.$message.success("退费操作已完成");
      // 重新获取工单详情
      this.fetchWorkOrderDetail();
      this.$emit("refresh"); // 通知父组件刷新列表
    },

    // 处理完结成功
    handleFinishSuccess() {
      // 关闭当前详情弹窗
      this.$emit("update:visible", false);
      // 通知父组件刷新列表
      this.$emit("refresh");
    },
  },
};
</script>

<style scoped></style>
