<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    title="素材管理"
    confirmText='保存'
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        :rules="rules"
      >

        <el-form-item label='素材管理' prop="materialUrlListFile">
          <upload-file
            :max-limit="3"
            :file-list='fileList'
            @remove="handleDetailsRemoveImg"
            @success="detailsUploadSuccess"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      fileList: [], // 素材回显
      form: {
        distributionNumber: '',
        materialUrlListFile: [] // 素材文件集合
      },
      rules: {
        'trainingUrlFile.fileUrl': [
          { required: true, message: '请上传', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      if (this.row.distributionNumber) {
        this.form.distributionNumber = this.row.distributionNumber;
      }
      if (this.row.material) {
        // 处理图片回显
        const list = this.row.material.split(',');
        if (list.length !== 0) {
          list.map(item => {
            this.fileList.push({ url: ossUri + item });
            this.form.materialUrlListFile.push({ fileUrl: item, isAdd: 0 });
          });
        }
      }
    },
    // 素材
    handleDetailsRemoveImg({ file, fileList }) {
      let index = 0;
      for (let i = 0; i < this.form.materialUrlListFile.length; i++) {
        const item = this.form.materialUrlListFile[i];
        if ((item.fileUrl) === file.response) {
          index = i;
          break;
        }
      }

      this.form.materialUrlListFile.splice(index, 1);
    },
    detailsUploadSuccess({ response, file, fileList }) {
      this.form.materialUrlListFile.push({ fileUrl: file.response, isAdd: 1 });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$post('editMaterial', this.form, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
              this.close();
            }
          });
          this.$parent.getTableList();
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.dialog-main{
padding: 50px;

  span{
    margin-left: 10px;
  }
  p{
    margin-top: 20px;
    line-height: 20px;
  }
}

</style>
