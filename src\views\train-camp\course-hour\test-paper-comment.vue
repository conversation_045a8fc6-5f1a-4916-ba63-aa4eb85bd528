<template>
  <common-dialog
    :show-footer="true"
    is-full
    width="800px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="success" size="small" @click="addComment">新增</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="scoreMin" label="分值区间" align="center">
          <template slot-scope="scope">
            {{ scope.row.scoreMin }} - {{ scope.row.scoreMax }}
          </template>
        </el-table-column>
        <el-table-column prop="reviewContent" label="点评内容" align="center" />
        <el-table-column prop="reviewUserName" label="点评人" align="center" />
        <el-table-column prop="enable" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.enable === 1? 'success':'danger'">
              {{ scope.row.enable === 1 ? '启用':'禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" prop="tradeType">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="updateStatus(scope.row)">{{ scope.row.enable === 2?'启用':'禁用' }}</el-button>
              <el-button type="text" @click="editComment(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <add-comment :visible.sync="acVisible" :row="currentEditRow" :trainingId='trainingId' :paperId="paperId" @getTableList='getTableList' />
  </common-dialog>
</template>

<script>
import addComment from './add-comment';
export default {
  components: {
    addComment
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    trainingId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      acVisible: false,
      currentEditRow: null,
      paperId: '',
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
  },
  methods: {
    // 新增训练营
    addComment() {
      this.paperId = this.row.paperId;
      this.currentEditRow = null;
      this.acVisible = true;
    },
    editComment(row) {
      this.paperId = this.row.paperId;
      this.currentEditRow = row;
      this.acVisible = true;
    },
    // 获取列表数据
    async getTableList() {
      this.tableLoading = true;
      const data = {
        paperId: this.row.paperId,
        trainingId: this.trainingId
      };
      const { fail, body } = await this.$post('getTrainReviewList', data);
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body;
      }
    },
    // 启用禁用
    updateStatus(row) {
      row.enable = row.enable === 1 ? 2 : 1;
      this.$post('editTrainReview', row, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
      this.getTableList();
    },
    open() {
      this.getTableList();
    },
    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
