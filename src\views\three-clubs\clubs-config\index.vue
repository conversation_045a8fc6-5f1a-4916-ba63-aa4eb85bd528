<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='onSearch'
    >

      <el-form-item label='社团类型' prop='clubsActType'>
        <el-select v-model="form.clubsActType" placeholder="请选择社团类型">
          <el-option v-for="item in dictType" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label='社团名称' prop='clubsActName'>
        <el-input v-model="form.clubsActName" placeholder="请输入社团名称" />
      </el-form-item>

      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>

      <el-form-item label='组建类型' prop='clubsActBuildType'>
        <el-select v-model="form.clubsActBuildType" placeholder="请选择组建类型">
          <el-option
            v-for="item in dictBuildType"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label='状态' prop='ifShow'>
        <el-select v-model="form.ifShow" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch("reset")' />
      </div>
    </el-form>

    <div class="table-tools">
      <el-button type="primary" size="mini" @click="onEdit()">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="sort" label="社团序号" align="center" />
      <el-table-column prop="clubsActType" label="社团类型" align="center" :formatter="handleClubsActType" />
      <el-table-column prop="clubsActBuildType" label="组建类型" align="center" :formatter="handleActBuildType" />
      <el-table-column prop="initiatorUserName" label="领队" align="center" />
      <el-table-column prop="clubsActExpertNum" label="达人" align="center" />
      <el-table-column prop="clubsActName" label="社团名称" align="center" />
      <el-table-column prop="ifAudit" label="是否要审核" align="center" :formatter="handleIfAudit" />
      <el-table-column prop="clubsActJoinPerson" label="社团人数" align="center" />
      <el-table-column prop="subscriptShow" label="角标显示" align="center" />
      <el-table-column prop="clubsActIntroduce" label="简介" align="center" />
      <el-table-column prop="ifShow" label="状态" align="center" :formatter="handleIfShow" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" plain @click="onEdit(row)">编辑</el-button>
          <el-button type="text" size="small" @click="onUserConfig(row)">配置人员</el-button>
          <el-button type="text" size="small" @click="onImportUser(row)">导入人员</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div>

    <!-- 新增|编辑弹框 -->
    <dialog-operation :visible.sync="operationVisible" :row="editRow" @confirm="editRow ? getData() : onSearch()" />

    <!-- 配置人员弹框 -->
    <dialog-user :visible.sync="userVisible" :row="editRow" @close="getData" />

    <!-- 导入人员弹框 -->
    <dialog-import-user :visible.sync="importUserVisible" :row="editRow" @refresh="getData" />
  </div>
</template>

<script>
import DialogOperation from './components/dialog-operation.vue';
import DialogUser from './components/dialog-user.vue';
import DialogImportUser from './components/dialog-import-user.vue';
import { ClubsActType, ClubsActBuildType } from '@/dict';

export default {
  components: { DialogOperation, DialogUser, DialogImportUser },
  data() {
    return {
      dictType: ClubsActType,
      dictBuildType: ClubsActBuildType,
      tableLoading: true,
      operationVisible: false,
      userVisible: false,
      importUserVisible: false,
      editRow: {},
      tableData: [],
      form: {
        clubsActType: '',
        clubsActName: '',
        clubsActBuildType: '',
        yzCode: ''
      },
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/clubsActivityInfo/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 社团类型名称显示
    handleClubsActType(row) {
      return this.dictType.find(item => item.dictValue === row.clubsActType)?.dictName;
    },
    // 组建类型名称显示
    handleActBuildType(row) {
      return this.dictBuildType.find(item => item.dictValue === row.clubsActBuildType)?.dictName;
    },
    // 是否要审核名称显示
    handleIfAudit(row) {
      return row.ifAudit === 1 ? '是' : '否';
    },
    // 是否禁用名称显示
    handleIfShow(row) {
      return row.ifShow === 1 ? '启用' : '禁用';
    },
    // btn 搜索 | 重置
    onSearch(type) {
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 编辑 | 新增
    onEdit(row) {
      this.editRow = row;
      this.operationVisible = true;
    },
    // btn 用户配置
    onUserConfig(row) {
      this.editRow = row;
      this.userVisible = true;
    },
    // btn 导入人员
    onImportUser(row) {
      this.editRow = row;
      this.importUserVisible = true;
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}

.recommend-input {
  padding: 20px;
}
</style>
