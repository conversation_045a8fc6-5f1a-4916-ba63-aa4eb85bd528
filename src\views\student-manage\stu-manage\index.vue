<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='学期名称' prop='semesterName'>
        <el-input v-model="form.semesterName" placeholder='请输入名称' />
      </el-form-item>
      <el-form-item label='读书计划' prop='readPlanId'>
        <infinite-selects
          v-model="form.readPlanId"
          placeholder="请选择计划"
          api-key="findBookAllAllow"
          key-name="readPlanName"
          value-name='readPlanId'
          :param="{name:''}"
          clearable
        />
      </el-form-item>

      <el-form-item label="读书计划类型" prop="readPlanType">
        <el-select v-model="form.readPlanType" clearable placeholder="请选择状态">
          <el-option label="上进学社" value="puSociety" />
          <el-option label="营销活动" value="mktAct" />
        </el-select>
      </el-form-item>

      <el-form-item label="招募时间" prop="recruitTime">
        <el-date-picker
          v-model="form.recruitTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item label="打卡时间" prop="attendTime">
        <el-date-picker
          v-model="form.attendTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 表格 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
    >
      <el-table-column label="学期名称" align="center" prop="semesterName">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="openLearningDetails(scope.row)"
          >{{ scope.row.semesterName }} </el-link>
        </template>
      </el-table-column>
      <el-table-column label="读书计划" align="center" prop="readPlanName" />
      <el-table-column prop="readPlanType" label="读书计划类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.readPlanType | readPlanChannel }}
        </template>
      </el-table-column>
      <el-table-column label="招募日期" align="center" prop="recruitStartTime" :formatter="hasRecruitTime" />
      <el-table-column label="打卡日期" align="center" prop="attendTime" :formatter="hasAttendTimeTime" />
      <el-table-column label="报名人数" align="center" prop="enroNum">
        <template slot-scope="scope">
          <el-link type="success" @click="signUpDetails(scope.row)">{{ scope.row.enroNum }} </el-link>
        </template>
      </el-table-column>
      <el-table-column label="挑战成功人数" align="center" prop="successNum" />
      <el-table-column label="批量提醒" align="center">
        <template slot="header">
          <el-tooltip class="item" effect="dark" content="提醒当天未打卡的用户且当天只能提醒一次" placement="top">
            <div><span>批量提醒</span><i class="el-icon-question question" /></div>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button
              type="text"
              :disabled="scope.row.isRemind === '1'"
              @click="handleReminderTask(scope.row)"
            >
              批量提醒
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <update-dialog :visible.sync="udVisible" :title="udTitle" :semester-id="semesterId" />
    <sign-details-dialog :visible.sync="sddVisible" :user-id="currentSignUserID" />

    <!-- 学习明细 -->
    <learning-details :visible.sync="ldVisible" :row="currentRow" />

  </div>
</template>
<script>
import { handleDateControl, exportExcel } from '@/utils';
import updateDialog from './update-dialog';
import signDetailsDialog from './sign-details-dialog';
import learningDetails from './learning-details';
export default {
  components: {
    updateDialog,
    signDetailsDialog,
    learningDetails
  },
  filters: {
    tradeType(val) {
      if (!val) return '';
      const data = {
        1: '已购买',
        2: '已退款'
      };
      return data[val];
    },
    readPlanChannel(val) {
      if (!val) return '';
      const data = {
        'puSociety': '上进学社',
        'mktAct': '营销活动'
      };
      return data[val];
    }
  },
  data() {
    return {
      udVisible: false,
      udTitle: '新增',
      courseId: null,
      semesterId: null,
      tiredSettlementPrice: 0, // 累计结算
      refundPrice: 0, // 累计退费
      settlementPrice: 0, // 累计净结算
      tableLoading: false,
      currentSignUserID: null,
      sddVisible: false,
      form: {
        semesterName: '',
        readPlanId: '',
        readPlanType: '',
        // readPlanName: '',
        attendTime: '',
        recruitTime: '',
        channelId: ''
      },
      scVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      ldVisible: false,
      currentRow: {}
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    openLearningDetails(row) {
      this.currentRow = row;
      this.ldVisible = true;
    },
    handleReminderTask(row) {
      this.$confirm('是否提醒今天未打卡的用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          semesterId: row.semesterId,
          attendTime: row.attendTime
        };
        this.$post('bulkReminderClock', params)
          .then(res => {
            const { fail } = res;
            if (!fail) {
              this.getTableList();
              this.$message({
                message: '操作成功',
                type: 'success'
              });
            }
          });
      });
    },
    hasRecruitTime(row, column) {
      // 将"2020-5-30 00:00:00" 转化为2020.5.30
      const startTime = row.recruitStartTime.substring(0, 10).replace(/-/g, '.');
      const endTime = row.recruitEndTime.substring(0, 10).replace(/-/g, '.');
      return startTime + ' - ' + endTime;
    },
    hasAttendTimeTime(row, column) {
      const startTime = row.attendTime.substring(0, 10).replace(/-/g, '.');
      const endTime = row.attendEndTime.substring(0, 10).replace(/-/g, '.');
      return startTime + ' - ' + endTime;
    },

    exportData() {
      const { data } = this.handleQueryParams();
      exportExcel('exportSupplierReconciliation', data);
    },
    handleAdd() {
      this.semesterId = '';
      this.udVisible = true;
    },
    handleEdit(row) {
      this.udVisible = true;
      this.udTitle = '编辑';
      this.semesterId = row.semesterId;
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const recruitTime = handleDateControl(formData.recruitTime);
      formData.recruitStartTime = recruitTime[0];
      formData.recruitEndTime = recruitTime[1];
      delete formData.recruitTime;

      const attendTime = handleDateControl(formData.attendTime);
      formData.attendTime = attendTime[0];
      formData.attendEndTime = attendTime[1];
      // delete formData.attendTime;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return { data, formData };
    },
    async getTableList() {
      this.tableLoading = true;
      const { data } = this.handleQueryParams();
      const { fail, body } = await this.$post('getStudentList', data);
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },

    // 报名明细
    signUpDetails(row) {
      this.currentSignUserID = row.semesterId + '';
      this.sddVisible = true;
    }
  }
};
</script>

<style lang="scss">
.question {
  margin-left: 5px;
  color: #20a0ff;
  cursor: pointer;
}
</style>
