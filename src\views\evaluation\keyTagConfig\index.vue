<template>
  <div class="key-tag-config-container">
    <!-- 查询表单 -->
    <el-form
      size="mini"
      :model="searchForm"
      label-width="120px"
      class="yz-search-form"
      @submit.native.prevent="getTableList"
    >
      <el-form-item label="关键标签总称">
        <el-input v-model="searchForm.tagName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="创建人">
        <el-input v-model="searchForm.creator" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="searchForm.createTime"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="searchForm.status" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          :disabled="tableLoading"
          @click="getTableList"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          :disabled="tableLoading"
          @click="resetSearch"
        />
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="header-bar">
      <el-button
        type="primary"
        icon="el-icon-plus"
        size="small"
        @click="openDialog(null)"
      >新增</el-button>
    </div>
    <!-- 数据表格 -->
    <el-table
      v-loading="tableLoading"
      :data="tableData"
      border
      stripe
      class="key-tag-table"
    >
      <el-table-column prop="sort" label="app排序" align="center" width="90" />
      <el-table-column prop="tagName" label="关键标签总称" align="center" />
      <el-table-column label="已选标签" align="center">
        <template slot-scope="scope">
          <div class="selected-tags">
            <el-tag
              v-for="(tag, idx) in (
                scope.row.fansTagTanmaRelations || []
              ).slice(0, 3)"
              :key="idx"
              type="warning"
              size="mini"
              class="tag-item"
            >
              {{ tag.tmTagName || "未知标签" }}
            </el-tag>
            <el-popover
              v-if="(scope.row.fansTagTanmaRelations || []).length > 3"
              width="300"
              placement="bottom"
              title="已选标签"
              trigger="click"
              popper-class="all-tags-popover"
            >
              <div class="selected-tags">
                <el-tag
                  v-for="(tag, idx) in scope.row.fansTagTanmaRelations || []"
                  :key="idx"
                  type="warning"
                  size="mini"
                  style="margin: 2px; display: block"
                >
                  {{ tag.tmTagName || "未知标签" }}
                </el-tag>
              </div>
              <span slot="reference" class="more-link">更多&gt;&gt;</span>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="tagUserNum"
        label="用户总数"
        align="center"
        width="100"
      />
      <el-table-column
        prop="guideCopywriting"
        label="列表页引导文案"
        align="center"
      />
      <el-table-column prop="route" label="列表页跳转链接" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column prop="createUser" label="创建人" align="center" />
      <el-table-column prop="updateTime" label="最后修改时间" align="center" />
      <el-table-column prop="updateUser" label="最后修改人" align="center" />
      <el-table-column prop="ifAllow" label="状态" align="center" width="80">
        <template slot-scope="scope">
          <span
            :style="{ color: scope.row.ifAllow === 1 ? '#333' : '#ff4d4f' }"
          >
            {{ scope.row.ifAllow === 1 ? "启用" : "禁用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <span class="table-action" @click="openDialog(scope.row)">编辑</span>
          <span class="table-action" @click="toggleEnable(scope.row)">
            {{ scope.row.ifAllow === 1 ? "禁用" : "启用" }}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pagination-bar">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page.sync="pagination.page"
        @current-change="getTableList"
      />
    </div>

    <!-- 新建/编辑弹窗 -->
    <DialogEdit
      :visible="dialogVisible"
      :row="dialogForm"
      @refresh="getTableList"
      @close="dialogVisible = false"
    />
  </div>
</template>

<script>
import DialogEdit from './components/DialogEdit.vue';
export default {
  name: 'KeyTagConfig',
  components: { DialogEdit },
  data() {
    return {
      searchForm: {
        tagName: '',
        creator: '',
        createTime: [],
        status: undefined
      },
      tableData: [],
      tableLoading: false,
      pagination: {
        page: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      dialogForm: null
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      this.tableLoading = true;

      // 处理搜索条件
      const searchParams = {
        ...this.searchForm,
        pageNum: (this.pagination.page - 1) * this.pagination.pageSize, // 偏移量数量
        pageSize: this.pagination.pageSize
      };

      // 处理时间范围
      if (this.searchForm?.createTime?.length === 2) {
        searchParams.startTime = this.searchForm.createTime[0] + ' 00:00:00';
        searchParams.endTime = this.searchForm.createTime[1] + ' 23:59:59';
        delete searchParams.createTime;
      }

      // 调用接口
      this.$http
        .post('/teacher/fans/tagConfig/list', searchParams, {
          headers: { 'Content-Type': 'application/json' }
        })
        .then((res) => {
          const { code, body } = res;
          if (code === '00') {
            // 处理表格数据，映射字段名
            this.tableData = body.data;

            // 更新分页信息
            this.pagination.total = body.recordsTotal || 0;
          }
        })
        .catch((error) => {
          console.error('获取表格数据失败:', error);
          this.$message.error('获取数据失败，请稍后重试');
          this.tableData = [];
          this.pagination.total = 0;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    resetSearch() {
      this.searchForm = { tagName: '', creator: '', createTime: [] };
      this.getTableList();
    },
    openDialog(row) {
      this.dialogForm = { ...row };
      this.dialogVisible = true;
    },
    handleDialogSubmit(form) {
      // 关闭对话框并刷新列表
      this.dialogVisible = false;
      this.getTableList();
    },
    // 启用 禁用
    toggleEnable(row) {
      const currentStatus = row.ifAllow;
      const newStatus = currentStatus === 1 ? 0 : 1;
      const actionText = newStatus === 1 ? '启用' : '禁用';

      // 二次弹窗确认
      this.$confirm(`确定要${actionText}该标签吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 调用状态修改接口
          this.$http
            .post(
              '/teacher/fans/tagConfig/status',
              {
                id: row.id,
                status: newStatus
              },
              {
                headers: { 'Content-Type': 'application/json' }
              }
            )
            .then((res) => {
              const { code, msg } = res;
              if (code === '00') {
                this.$message.success(`${actionText}成功`);
                this.getTableList(); // 刷新列表
              } else {
                this.$message.error(msg || `${actionText}失败`);
              }
            })
            .catch((error) => {
              console.error('状态修改失败:', error);
              this.$message.error(`${actionText}失败，请稍后重试`);
            });
        })
        .catch(() => {
          // 用户取消操作
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.key-tag-config-container {
  background: #fff;
  padding: 24px;
  min-height: 100vh;
  .header-bar {
    text-align: right;
    margin: 30px 0 10px;
    .title {
      font-size: 20px;
      font-weight: bold;
    }
  }
  .key-tag-table {
    margin-bottom: 20px;
    .selected-tags {
      .more-link {
        color: #409eff;
        cursor: pointer;
        font-size: 12px;
        margin-left: 4px;
        display: inline;
      }
    }
    .table-action {
      color: #409eff;
      cursor: pointer;
      margin: 0 6px;
    }
  }
  .pagination-bar {
    text-align: right;
    margin-top: 10px;
  }
}

.all-tags-popover {
  min-width: 220px;
  max-width: 350px;
  padding: 10px 8px 6px 8px;
}
.selected-tags {
  display: flex;
  flex-wrap: wrap;
}
.tag-item {
  margin-bottom: 3px;
  display: inline-block;
  margin-right: 100%;
}
</style>
