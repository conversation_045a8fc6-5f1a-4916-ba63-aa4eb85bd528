export const REG = {
  idCard: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  mobile: /^1[3456789]\d{9}$/,
  email: /^\w+@[a-z0-9]+\.[a-z]+$/i,
  zipCode: /^[1-9][0-9]{5}$/,
  emoji: /\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]/g,
  space: /\s+/g, // 空格
  numAndEnglish: /^[A-Za-z0-9]+$/, // 数字和字符
  positiveInteger: /^[+]{0,1}(\d+)$/, // 正整数(包括0)
  positiveInteger2: /^[1-9]\d*$/, // 正整数(不包括0)
  url: /^((https?|ftp|file):\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/
};

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  const valid_map = ['admin', 'editor'];
  return valid_map.indexOf(str.trim()) >= 0;
}

/**
 * 公共的正则校验方法
 * @param {String} key 正则的名字 对应上面的REG
 * @param {String} val 值
 */
export function validate(key, val) {
  const reg = REG[key];
  if (!reg.test(val)) {
    return false;
  }
  return true;
}
