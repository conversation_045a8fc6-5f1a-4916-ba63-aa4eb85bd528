<template>
  <el-select
    v-model="val"
    class="yz-infinite-select"
    clearable
    filterable
    remote
    :disabled="disabled"
    :popper-class="popperClass"
    :remote-method="search"
    :placeholder="placeholder"
    @change="onChange"
    @visible-change="vChange"
  >
    <el-option v-for="item in options" :key="item[valName]" :value="item[valName]" :label="getLabel(item)" />
  </el-select>
</template>

<script>
export default {
  name: 'InfiniteSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    options: {
      type: Array,
      default: () => []
    },
    valName: { // 值的名字
      type: String,
      default: '',
      required: true
    },
    label: {
      type: String | Function,
      default: '' || (() => {})
    },
    customPopperClass: { // 自定义的下拉选择器类名
      type: String,
      default: ''
    },
    visibleChange: { // 下拉框的显示隐藏事件
      type: Object | Function,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      val: ''
    };
  },
  computed: {
    popperClass() {
      return this.customPopperClass || `scroll-popper${Date.now()}`;
    }
  },
  watch: {
    value(v) {
      this.val = v;
    },
    loading(val) {
      this.initArrow();
    }
  },
  mounted() {
    this.val = this.value;
    this.initArrow();
    this.initScroll();
  },
  methods: {
    initScroll() {
      const scrollPopper = document.querySelector(`.${this.popperClass} .el-scrollbar__wrap`);
      scrollPopper.onscroll = (e) => {
        const cha = scrollPopper.scrollHeight - scrollPopper.scrollTop;
        if ((cha - scrollPopper.clientHeight) < 1) {
          this.$emit('loadMore');
        }
      };
    },
    initArrow() {
      const current = document.querySelector('.yz-infinite-select');
      const icon = current.querySelector('.el-input__suffix-inner .el-input__icon');
      const arrow = 'el-select__caret el-input__icon el-icon-arrow-up';
      const loading = 'el-select__caret el-input__icon el-icon-loading';
      icon.className = this.loading ? loading : arrow;
    },
    getLabel(item) {
      if (typeof this.label === 'string') {
        return item[this.label];
      }
      return this.label(item);
    },
    search(val) {
      this.$emit('search', val);
    },
    onChange(val) {
      this.$emit('input', val);
      this.$emit('change', val);
    },
    vChange(show) {
      if (this.visibleChange) {
        this.visibleChange(show);
        return;
      }
      if (show) {
        this.search();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
  .yz-infinite-select{

  }
</style>
