<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='训练营名称' prop='trainingName'>
          <el-input
            v-model="form.trainingName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label='训练周期' prop='trainingDay'>
          <el-input
            v-model="form.trainingDay"
            :disabled="isEdit"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='及格分数' prop='passScore'>
          <el-input-number
            v-model="form.passScore"
            :min="1"
            :max="1000000000"
            :controls="false"
            :disabled="isEdit"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='挑战成功是否自动退费' prop='autoRefund' required>
          <el-radio-group v-model="form.autoRefund" :disabled="isEdit">
            <el-radio :label="0">不自动退费</el-radio>
            <el-radio :label="1">自动退费</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='训练营图片' prop='coverPicFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='训练营欢迎页' prop='trainingUrlFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='coverfileList'
            @remove="handleCoverRemoveImg"
            @success="coverUploadSuccess"
          />
        </el-form-item>
        <el-form-item label='进群二维码' prop="codeUrlFile.fileUrl">
          <upload-file
            :max-limit="1"
            :file-list='qrCode'
            @remove="handleQrCodeRemoveImg"
            @success="qrCodeUploadSuccess"
          />
        </el-form-item>

        <el-form-item label='服务有效期' prop='serviceValidDay'>
          <el-input-number
            v-model="form.serviceValidDay"
            v-width="150"
            :min="1"
            :max="1000000000"
            class="yz-input-number day padding-left"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='enable'>
          <el-radio-group v-model="form.enable">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="考证须知">
          <u-editor :width="500" :height="600" @ready="editorReady" />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { validate } from '@/utils/validate';
import { positiveInteger } from '@/common/vali';
import UEditor from '@/components/UEditor';
export default {
  components: {
    UEditor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const validateFileList = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请上传图片'));
      } else {
        callback();
      }
    };
    const validateTrainingDay = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'));
      } else if (!validate('positiveInteger2', value)) {
        callback(new Error('请输入正整数'));
      } else {
        callback();
      }
    };
    return {
      show: false,
      fileList: [],
      coverfileList: [],
      qrCode: [],
      isEdit: false,
      form: {
        serviceValidDay: undefined,
        trainingName: '',
        trainingDay: '',
        passScore: undefined,
        enable: '1',
        autoRefund: 0,
        // 训练营封面图
        trainingUrlFile: {
          fileUrl: '',
          isAdd: 0
        },
        coverPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 群二维码
        codeUrlFile: {
          fileUrl: '',
          isAdd: 0
        },
        detailsPicFileList: [],
        examInstructions: ''
      },
      rules: {
        trainingName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        passScore: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        trainingDay: [
          { required: true, validator: validateTrainingDay, trigger: 'change' }
        ],
        'coverPicFile.fileUrl': [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        detailsPicFileList: [
          { required: true, validator: validateFileList, trigger: 'change' }
        ],
        serviceValidDay: [
          { required: true, validator: positiveInteger, trigger: 'change' }
        ]
      },
      editor: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    editorReady(editor) {
      this.editor = editor;
    },
    open() {
      if (this.row) {
        this.getcourseInfo();
        // 已报名不可编辑
        this.isEdit = this.row.trainTotal > 0;
      }
    },
    getcourseInfo() {
      const params = {
        trainingId: this.row.trainingId
      };
      this.$post('getTrainFindSingle', params, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.trainingName = body.trainingName;
            this.form.trainingDay = body.trainingDay;
            this.form.serviceValidDay = body.serviceValidDay;
            this.form.enable = body.enable;
            this.form.passScore = body.passScore;
            this.form.autoRefund = body.autoRefund;
            this.form.examInstructions = body.examInstructions;

            let timer = setInterval(() => {
              if (this.editor) {
                clearInterval(timer);
                timer = null;
                this.editor.setContent(body.examInstructions || '');
              }
            }, 300);

            // 处理图片回显
            if (body.codeUrl) {
              this.qrCode.push({ url: ossUri + body.codeUrl });
              this.form.codeUrlFile.fileUrl = body.codeUrl;
            }
            if (body.coverPic) {
              this.fileList.push({ url: ossUri + body.coverPic });
              this.form.coverPicFile.fileUrl = body.coverPic;
            }
            if (body.trainingUrl) {
              this.coverfileList.push({ url: ossUri + body.trainingUrl });
              this.form.trainingUrlFile.fileUrl = body.trainingUrl;
            }
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addTrainCamp';
          const params = {
            ...this.form,
            'coverPic': this.form.coverPicFile.fileUrl,
            'codeUrl': this.form.codeUrlFile.fileUrl
          };

          params.examInstructions = this.editor.getContent();

          if (this.row) {
            apiKey = 'editTrainCamp';
            params.trainingId = this.row.trainingId;
            params.courseId = this.row.courseId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$parent.getTableList();
              }
            });
        }
      });
    },

    // 训练营封面图
    handleCoverRemoveImg({ file, fileList }) {
      this.form.trainingUrlFile.fileUrl = '';
    },
    coverUploadSuccess({ response, file, fileList }) {
      this.form.trainingUrlFile.fileUrl = response;
      this.form.trainingUrlFile.isAdd = 1;
    },
    handleRemoveImg({ file, fileList }) {
      this.form.coverPicFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.coverPicFile.fileUrl = response;
      this.form.coverPicFile.isAdd = 1;
    },
    handleQrCodeRemoveImg() {
      this.form.codeUrlFile.fileUrl = '';
    },
    // 二维码进群
    qrCodeUploadSuccess({ response, file, fileList }) {
      this.form.codeUrlFile.fileUrl = response;
      this.form.codeUrlFile.isAdd = 1;
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
