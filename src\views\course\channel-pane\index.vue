<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='频道号' prop='channelId'>
        <el-input v-model="form.channelId" placeholder="请输入数字，限制输入10位" :maxlength="10" @input="handleLimitInput" />
      </el-form-item>

      <el-form-item label='频道名称' prop='channelName'>
        <el-input v-model="form.channelName" placeholder="请输入频道名称" />
      </el-form-item>

      <el-form-item label='课程名称' prop='courseName'>
        <el-input v-model="form.courseName" placeholder="请输入课程名称" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="adVisible=true">新增</el-button>
      <el-button type="danger" size="small" plain icon="el-icon-delete" @click="batchDelete">批量删除</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      :height="table_height"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="channelId" label="频道号" align="center" />
      <el-table-column prop="channelPassword" label="频道密码" align="center" />
      <el-table-column prop="channelName" label="频道名称" align="center" />
      <el-table-column prop="courseName" label="关联课程" align="center" />
      <el-table-column prop="date" label="详情" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <add-dialog :visible.sync="adVisible" />
    <edit-dialog :visible.sync="edVisible" :lc-id="currentModifyLcId" />
  </div>
</template>
<script>

import { pagination, TABLE_HEIGHT } from '@/config/constant';
import addDialog from './add-dialog';
import editDialog from './edit-dialog';
export default {
  components: {
    addDialog,
    editDialog
  },
  data() {
    return {
      table_height: TABLE_HEIGHT,
      adVisible: false,
      edVisible: false,
      tableLoading: false,
      udTitle: '新增',
      currentModifyLcId: null,
      form: {
        channelId: undefined,
        channelName: '',
        courseName: ''
      },
      selection: [],
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10,
        ...pagination
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    handleLimitInput(value) {
      this.form.channelId = value.replace(/[^\d.]/g, '');
    },
    handleEdit(row) {
      this.currentModifyLcId = row.lcId;
      this.edVisible = true;
    },
    batchDelete() {
      if (this.selection.length > 0) {
        let ids = '';
        this.selection.forEach(e => {
          ids += e.lcId + ',';
        });
        ids = ids.slice(0, ids.length - 1);
        const data = {
          lcIds: ids
        };
        this.$post('batchDeleteChannel', data).then(res => {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        });
      } else {
        this.$message.error('请勾选数据！');
      }
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = {
        courseSystem: 7, // 直播类型 7->上进大学
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getChannelList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  padding-top:20px;
}
</style>
