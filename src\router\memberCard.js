import Layout from '@/layout';

/** 会员卡管理路由模块  */
export default [
  {
    path: '/memberCard',
    name: 'memberCard',
    redirect: '/memberCard/index',
    component: Layout,
    meta: {
      title: '会员卡管理',
      icon: 'el-icon-user'
    },
    children: [
      {
        path: 'index',
        name: 'memberCard.index',
        component: () => import('@/views/member-card-manage/index'),
        meta: {
          title: '会员卡管理',
          breadcrumb: false
        }
      }
    ]
  }
];
