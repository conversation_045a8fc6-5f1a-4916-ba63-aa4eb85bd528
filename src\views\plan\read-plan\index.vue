<template>
  <div>
    <div>
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='名称' prop='readPlanName'>
          <el-input v-model.trim="form.readPlanName" placeholder='请输入名称' />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" clearable placeholder="请选择状态">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="读书计划类型" prop="readPlanType">
          <el-select v-model="form.readPlanType" clearable placeholder="请选择状态">
            <el-option label="上进学社" value="puSociety" />
            <el-option label="营销活动" value="mktAct" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="success" size="small" icon="el-icon-open" plain @click="updateStatus(1)">批量启用</el-button>
        <el-button type="danger" size="small" icon="el-icon-turn-off" plain @click="updateStatus(2)">批量禁用</el-button>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="readPlanName" label="计划名称" align="center" />
        <el-table-column prop="readPlanType" label="读书计划类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.readPlanType | readPlanChannel }}
          </template>
        </el-table-column>
        <el-table-column prop="applyNum" label="参与人数" align="center" />
        <el-table-column prop="updateTime" label="最后更新时间" align="center" />
        <el-table-column prop="updateUserName" label="最后更新者" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status==1" type="success">已启用</el-tag>
            <el-tag v-else type="danger">已禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="editStatus(scope.row)">{{ scope.row.status==1?'禁用':"启用" }}</el-button>
              <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

      <!-- 弹窗 -->
      <update-dialog
        :id='currentEditId'
        :title="updateDialogTitle"
        :visible.sync="udVisible"
        @refresh-list="getTableList"
      />
    </div>
  </div>
</template>

<script>
import updateDialog from './update-dialog';
export default {
  components: {
    updateDialog
  },
  filters: {
    readPlanChannel(val) {
      if (!val) return '';
      const data = {
        'puSociety': '上进学社',
        'mktAct': '营销活动'
      };
      return data[val];
    }
  },
  data() {
    return {
      stockTitle: '',
      stockShow: false,
      stockForm: {
        num: undefined
      },
      stockRules: {
        num: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      form: {
        readPlanName: '',
        readPlanType: '',
        status: ''
      },
      selects: [],
      // 弹窗
      udVisible: false,
      updateDialogTitle: '新增',
      tableLoading: false,
      currentEditId: null,
      table_height: 'TABLE_HEIGHT',
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 获取列表数据
    getTableList() {
      const formData = JSON.parse(JSON.stringify(this.form));
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...formData
      };
      this.$post('getReadPlanList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    open(type, title, row) {
      this.stockShow = true;
      this.stockTitle = title;
      this.currentItem = { type, row };
    },
    submit() {
      this.$refs['stockForm'].validate((valid) => {
        if (valid) {
          let editNum = +this.stockForm.num;
          if (this.currentItem.type === 2) {
            editNum = -this.stockForm.num;
          }
          const data = {
            ruleId: this.currentItem.row.ruleId,
            editNum: editNum
          };
          this.$post('updateMebCardStock', data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
              this.stockShow = false;
            }
          });
        }
      });
    },
    close() {
      this.stockShow = false;
      this.$refs['stockForm'].resetFields();
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    handleAdd() {
      this.currentEditId = null;
      this.updateDialogTitle = '新增';
      this.udVisible = true;
    },
    handleEdit(row) {
      this.currentEditId = row.readPlanId;
      this.updateDialogTitle = '编辑';
      this.udVisible = true;
    },
    // 启用禁用
    editStatus(row) {
      row.status = row.status + '';
      const data = {
        status: row.status === '1' ? '2' : '1',
        idStr: row.readPlanId
      };
      this.$post('batchUpdateStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
        this.getTableList();
      });
    },
    // 批量启用/禁用
    updateStatus(status) {
      if (this.selects.length > 0) {
        const ids = this.selects.map(item => {
          return item.readPlanId;
        });
        const data = {
          idStr: ids.join(),
          status: status
        };
        this.$post('batchUpdateStatus', data).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
          this.getTableList();
        });
      } else {
        this.$message.error('请勾选数据');
      }
    }
  }

};
</script>

<style lang = "scss" scoped>
.yz-base-container{
  .table-btnbox{
    margin:20px 0 10px 0;
    .left{
      color:red;
      line-height: 33px;
    }
    .align-right{
      text-align:right;
    }
  }
  .el-form {
    margin-bottom: 10px;
  }
}
</style>
