<template>
  <el-radio-group v-model="conditionType" :disabled="disabled" class="vertical-radio-group" @change="handleConditionTypeChange">
    <div class="radio-item">
      <el-radio :label="1">首次读书发帖</el-radio>
    </div>

    <div class="radio-item">
      <el-radio :label="2">累计书单≥</el-radio>
      <el-input-number
        v-if="conditionType === 2"
        v-model="readData.readSum"
        :disabled="disabled"
        :precision="0"
        :min="1"
        :controls="false"
        placeholder="请输入累计书单数"
        style="width: 180px;"
      />
      <span v-if="conditionType === 2">本</span>
    </div>

    <div class="radio-item">
      <el-radio :label="3">累计读书≥</el-radio>
      <el-input-number
        v-if="conditionType === 3"
        v-model="readData.dayTotal"
        :disabled="disabled"
        :precision="0"
        :min="1"
        :controls="false"
        placeholder="请输入累计读书数"
        style="width: 180px;"
      />
      <span v-if="conditionType === 3">天</span>
    </div>

    <div class="radio-item">
      <el-radio :label="4">读书天数排行榜TOP10</el-radio>
      <el-date-picker
        v-if="conditionType === 4"
        v-model="readData.dayTop"
        :disabled="disabled"
        type="month"
        placeholder="请选择年月"
        format="yyyy年MM月"
        value-format="yyyy-MM"
        style="width: 180px;"
        :picker-options="pickerOptions"
      />
    </div>

    <div class="radio-item">
      <el-radio :label="5">读书笔记排行榜TOP10</el-radio>
      <el-date-picker
        v-if="conditionType === 5"
        v-model="readData.sumTop"
        :disabled="disabled"
        type="month"
        placeholder="请选择年月"
        format="yyyy年MM月"
        value-format="yyyy-MM"
        style="width: 180px;"
        :picker-options="pickerOptions"
      />
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'ReadMedal',
  props: {
    readData: {
      type: Object,
      default: () => ({
        first: undefined, // 首次读书发帖
        readSum: undefined, // 累计书单本
        dayTotal: undefined, // 累计读书天
        dayTop: undefined, // 读书天数排行榜TOP10日期
        sumTop: undefined // 读书笔记排行榜TOP10日期
      })
    },
    disabled: {
      type: Boolean,
      default: false
    },
    pickerOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      conditionType: null
    };
  },
  watch: {
    readData: {
      handler(val) {
      },
      deep: true
    }
  },
  mounted() {
    // 根据readData中的值判断当前选择的条件类型
    if (this.readData.first) {
      this.conditionType = 1;
    } else if (this.readData.readSum) {
      this.conditionType = 2;
    } else if (this.readData.dayTotal) {
      this.conditionType = 3;
    } else if (this.readData.dayTop) {
      this.conditionType = 4;
    } else if (this.readData.sumTop) {
      this.conditionType = 5;
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.conditionType) {
        return { valid: false, message: '请选择获取条件' };
      } else if (this.conditionType === 2 && !this.readData.readSum) {
        return { valid: false, message: '请输入累计书单数' };
      } else if (this.conditionType === 3 && !this.readData.dayTotal) {
        return { valid: false, message: '请输入累计读书数' };
      } else if (this.conditionType === 4 && !this.readData.dayTop) {
        return { valid: false, message: '请选择读书天数排行榜年月' };
      } else if (this.conditionType === 5 && !this.readData.sumTop) {
        return { valid: false, message: '请选择读书笔记排行榜年月' };
      }
      return { valid: true };
    },
    // 处理条件类型变化
    handleConditionTypeChange(val) {
      // 重置所有属性为undefined
      Object.keys(this.readData).forEach(key => {
        this.readData[key] = undefined;
      });

      // 如果选择了首次读书发帖，设置first为true
      if (val === 1) {
        this.readData.first = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }

    span {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}
</style>
