<template>
  <common-dialog class="common-dialog" width="80%" title="活动推荐列表" :visible.sync="show" @close="close">
    <!-- 表单 -->
    <el-form
      ref="form"
      size="mini"
      label-width="100px"
      class="yz-search-form"
      :model="form"
    >
      <el-form-item label="活动名称" prop="actName">
        <el-input v-model="form.actName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="城市" prop="actCity">
        <el-input v-model="form.actCity" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="活动地点" prop="actAddress">
        <el-input v-model="form.actAddress" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="报名时间起" prop="enrollStartTime">
        <el-date-picker
          v-model="form.enrollStartTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="报名时间止" prop="enrollEndTime">
        <el-date-picker
          v-model="form.enrollEndTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="活动时间起" prop="actStartTime">
        <el-date-picker
          v-model="form.actStartTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="活动时间止" prop="actEndTime">
        <el-date-picker
          v-model="form.actEndTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="活动费用" prop="actFee">
        <el-input v-model="form.actFee" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="是否置顶" prop="actTop">
        <el-select v-model="form.actTop" placeholder="请选择" clearable>
          <el-option label="置顶" :value="1" />
          <el-option label="未置顶" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="是否启用" prop="actStatus">
        <el-select v-model="form.actStatus" placeholder="请选择" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="创建人" prop="actCreateUser">
        <el-input v-model.number="form.actCreateUser" placeholder="请输入" clearable />
      </el-form-item>

    </el-form>

    <div v-loading="tableLoading">

      <!-- 按钮区 -->
      <div class="m-t-10 m-l-10 m-b-10">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click="search('reset')">重置</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-circle-plus-outline" @click="onAddExpert">添加选中</el-button>
        <el-button size="mini" type="danger" plain icon="el-icon-remove-outline" @click="onRemoveExpert">移除选中</el-button>
      </div>

      <div class="m-l-10 m-r-10">
        <!-- 表格 -->
        <el-table
          ref="table"
          border
          size="small"
          style="width: 100%;"
          header-cell-class-name="table-cell-header"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column label="是否选中" align="center" width="70">
            <template #default="scope">
              <el-tag v-if="scope.row.ifSelected == 1" size="mini"> 是</el-tag>
              <el-tag v-else size="mini" type="info"> 否 </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="actName" label="活动名称" align="center" />
          <el-table-column prop="actCity" label="城市" align="center" />
          <el-table-column prop="actAddress" label="地点" align="center" />
          <el-table-column prop="actFee" label="费用" align="center" width="135" />
          <el-table-column label="报名时间" align="center" width="255">
            <template #default="scope">
              {{ scope.row.enrollStartTime }}~{{ scope.row.enrollEndTime }}
            </template>
          </el-table-column>
          <el-table-column label="活动时间" align="center" width="255">
            <template #default="scope">
              {{ scope.row.actStartTime }}~{{ scope.row.actEndTime }}
            </template>
          </el-table-column>
          <el-table-column prop="actCreateUser" label="创建人" align="center" />
          <el-table-column label="置顶状态" align="center" width="70">
            <template #default="scope">
              {{ scope.row.actTop == 1 ? "置顶" : "未置顶" }}
            </template>
          </el-table-column>
          <el-table-column label="是否启用" align="center" width="70">
            <template #default="scope">
              {{ scope.row.actStatus == 1 ? "启用" : "禁用" }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页区 -->
      <div class="yz-table-pagination m-b-10">
        <pagination
          :total='pager.total'
          :page.sync="pager.pageNum"
          :limit.sync="pager.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>

import { DMarkTaskType, DIrrigationDitch } from '@/dict';
/** 操作类型: 1: 添加选中 0: 清除选中 */
const OPERATE_TYPE = {
  ADD: 1,
  REMOVE: 0
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      DMarkTaskType,
      DIrrigationDitch,
      show: false,
      form: {
        configId: undefined, // 圈子配置ID
        actName: undefined, // 活动名称
        actCity: undefined, // 城市
        actAddress: undefined, // 活动地点
        enrollStartTime: undefined, // 报名时间起
        enrollEndTime: undefined, // 报名时间止
        actStartTime: undefined, // 活动时间起
        actEndTime: undefined, // 活动时间止
        actFee: undefined, // 活动费用
        actTop: undefined, // 是否置顶 1：置顶 0：不置顶
        actStatus: undefined, // 是否启用（1 启用 2禁用）
        actCreateUser: undefined // 创建人
      },
      tableLoading: false,
      tableData: [],
      selectionList: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
        if (val) {
          this.search();
        }
      },
      immediate: true
    }
  },
  methods: {
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    async getData() {
      try {
        this.tableLoading = true;
        const params = {
          ...this.pager,
          ...this.form,
          configId: this.row.id
        };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/circleConfig/activityRecommendList',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    search(type) {
      if (type === 'reset') {
        this.$refs.form.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    handleMarkTaskType(row) {
      return this.DMarkTaskType.find(v => v.dictValue === Number(row.markTaskType))?.dictName;
    },
    handleIrrigationDitch(row) {
      return this.DIrrigationDitch.find(v => v.dictValue === Number(row.irrigationDitch))?.dictName ?? '缺失';
    },
    // 选中用户
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    // 操作成员状态 新增|删除 达人
    async handleClubActMember(type) {
      if (this.tableLoading) return;
      try {
        this.tableLoading = true;
        const params = {
          configId: this.row.id,
          businessType: 2, // 业务类型，1:习惯 2:活动 3:话题
          operateType: type,
          mappingList: this.selectionList.map(s => s.id)
        };
        const { code } = await this.$http({
          method: 'post',
          url: '/circleConfig/addOrClearSelected',
          data: params,
          json: true
        });
        if (code !== '00') {
          this.tableLoading = false;
          return;
        }
        this.$message.success(type === OPERATE_TYPE.ADD ? '添加成功' : '移除成功');
        this.getData();
      } catch (error) {
        console.log(error);
        this.tableLoading = false;
      }
    },
    // btn 添加
    async onAddExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加为选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.ADD);
        }).catch(() => { });
    },
    // btn 移除
    onRemoveExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.REMOVE);
        }).catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.m-r-10 {
  margin-right: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}

.yz-search-form{
  padding-right: 0;
  @extend .m-t-10;
}

::v-deep .el-table__header-wrapper .el-checkbox{
display:none
}
</style>
