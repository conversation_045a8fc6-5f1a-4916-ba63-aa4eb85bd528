<template>
  <div>
    <common-dialog
      show-footer
      width="600px"
      :title="row ? '编辑' : '新增'"
      :visible.sync="show"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form ref="form" class="form" size="mini" :model="form" label-width="100px" :rules="rules">

          <el-form-item label="模块" prop="moduleType">
            <el-select v-model="form.moduleType" placeholder="请选择模块">
              <el-option v-for="item in tagOpts" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>

          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" maxlength="20" show-word-limit />
          </el-form-item>

          <el-form-item label="路由" prop="route">
            <el-input v-model="form.route" placeholder="请输入路由" />
            <div style="color:pink;">说明：帖子详情、活动页、话题详情分别是三种类型路由</div>
          </el-form-item>

          <el-form-item v-if="form.moduleType === tagOpts[0].dictValue" label="热度基数" prop="fictitiousBaseNum">
            <el-input-number v-model="form.fictitiousBaseNum" :controls="false" :max="1000000" :min="0" :precision="0" />
            <div style="color:pink;">说明：自定义输入整数，上限 1000000</div>
          </el-form-item>

          <el-form-item label="状态" prop="ifAllow">
            <el-radio-group v-model="form.ifAllow">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { DModuleType } from '@/dict';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      tagOpts: DModuleType,
      form: {
        moduleType: '',
        title: '',
        route: '',
        fictitiousBaseNum: undefined,
        ifAllow: 1
      },
      rules: {
        moduleType: [{ required: true, message: '请选择模块', trigger: 'change' }],
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { max: 20, message: '长度不能超过 20 个字符', trigger: 'blur' }],
        route: [{ required: true, message: '请输入路由', trigger: 'blur' }],
        fictitiousBaseNum: [{ required: true, message: '请输入热度基数', trigger: 'blur' }],
        ifAllow: [{ required: true, message: '请选择状态' }]
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    async open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({ ...this.row }));
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row?.id,
              ...this.form
            };
            const url = this.row ? '/moduleRecommendConfig/updateById' : '/moduleRecommendConfig/add';
            const { code } = await this.$http({
              method: 'post',
              url,
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.show = false;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.el-input-number{
 ::v-deep .el-input__inner {
    text-align: left;
  }

  width: 100%;
}
</style>
