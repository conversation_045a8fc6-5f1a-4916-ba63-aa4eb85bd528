<template>
  <div class="yz-base-container">
    <open-packup>
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='供应渠道' prop='supplyChannel'>
          <infinite-selects
            v-model="form.supplyChannel"
            placeholder="请选择供应渠道"
            api-key="getSupplierSelects"
            key-name="channelName"
            value-name='channelId'
            :param="{sName:''}"
            clearable
          />
        </el-form-item>

        <el-form-item label='商品名称' prop='goodsShelfName'>
          <el-input v-model="form.goodsShelfName" placeholder="请输入商品名称" />
        </el-form-item>

        <el-form-item label='手机号' prop='commentMobile'>
          <el-input v-model="form.commentMobile" placeholder="手机号" />
        </el-form-item>

        <el-form-item label='评论人' prop='commentUserName'>
          <el-input v-model="form.commentUserName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label='是否显示' prop='status'>
          <el-select
            v-model="form.status"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="显示" value="1" />
            <el-option label="屏蔽" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label='推荐状态' prop='recommend'>
          <el-select
            v-model="form.recommend"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="推荐" value="2" />
            <el-option label="不推荐" value="1" />
          </el-select>
        </el-form-item>

        <el-form-item label='评分' prop='score'>
          <el-select
            v-model="form.score"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="一星" value="1" />
            <el-option label="二星" value="2" />
            <el-option label="三星" value="3" />
            <el-option label="四星" value="4" />
            <el-option label="五星" value="5" />
          </el-select>
        </el-form-item>

        <el-form-item label='发布渠道' prop='type'>
          <el-select
            v-model="form.type"
            filterable
            clearable
            placeholder="请选择"
          >
            <el-option label="商品" value="course" />
            <el-option label="读书计划" value="readPlan" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" @click="sVisible=true">敏感词库</el-button>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @cell-dblclick="viewFullText"
    >

      <el-table-column prop="type" label="类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.type | type }}
        </template>
      </el-table-column>
      <el-table-column width="150" prop="channelName" label="供应渠道" align="center" />
      <el-table-column prop="goodsShelfName" label="商品名称" align="center" />
      <el-table-column width="100" prop="yzCode" label="远智编码" align="center" />
      <el-table-column width="150" prop="commentUserName" label="评论人" align="center" />
      <el-table-column width="120" prop="commentMobile" label="评论人手机号" align="center">
        <!--        <template slot-scope="scope">-->
        <!--          <span @click="showMobile(scope.row)">-->
        <!--            <span>{{ scope.row.commentMobile | hidePhone(6,scope.row.showMobile) }}</span>-->
        <!--          </span>-->
        <!--        </template>-->
        <template slot-scope="scope">
          <div>{{ scope.row.commentMobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.commentUserId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column width="200" prop="score" label="评分" align="center">
        <template slot-scope="scope">
          <el-rate
            v-model="scope.row.score"
            disabled
            text-color="#ff9900"
          />
        </template>
      </el-table-column>
      <el-table-column width="300" prop="commentContent" label="留言内容" align="center">
        <template slot-scope="scope">
          <div class="comment" title="双击查看更多内容">
            {{ scope.row.commentContent }}
          </div>
        </template>
      </el-table-column>
      <el-table-column width="100" prop="status" label="是否显示" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.status === '显示'? 'success':'danger'">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column width="100" prop="recommend" label="是否推荐" align="center">
        <template slot-scope="scope">
          <el-tag size="mini" :type="scope.row.recommend === '是'? 'success':'danger'">
            {{ scope.row.recommend }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column width="250" label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="openReplyDialog(scope.row)">回复</el-button>
          <el-button size="mini" type="danger" @click="updateStatus(scope.row)">
            {{ scope.row.status === '显示'?'屏蔽':'展示' }}
          </el-button>
          <el-button
            size="mini"
            type="warning"
            @click="setFeaturedReviews(scope.row)"
          >
            <!--由于后台原因展示用文字判断先-->
            {{ scope.row.recommend === '是'?'取消精选':'设置精选' }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 留言回复 -->
    <replyMessage
      :visible.sync="rMVisible"
      :comment-id="commentId"
      :comment-user-id="commentUserId"
      @refresh="getTableList"
    />
    <!-- 敏感词库 -->
    <sensitiveVocabulary :visible.sync="sVisible" />

    <el-dialog width="600px" title="留言内容" :visible.sync="dialogCommentVisible">
      <div class="dialog-comment">
        {{ fullText }}
      </div>
    </el-dialog>

  </div>
</template>

<script>
import replyMessage from './reply-message';
import sensitiveVocabulary from './sensitive-vocabulary';
import { exportExcel } from '@/utils';
import LookMobile from '@/mixins/LookMobile';

export default {
  components: {
    replyMessage,
    sensitiveVocabulary
  },
  filters: {
    type(val) {
      if (!val) return '';
      const data = {
        course: '课程',
        readPlan: '读书计划'
      };
      return data[val];
    }
  },
  mixins: [LookMobile],
  data() {
    return {
      commentId: undefined,
      commentUserId: undefined,
      tableLoading: false,
      rMVisible: false,
      sVisible: false,
      tableData: [],
      dialogCommentVisible: false,
      fullText: '',
      // 表单
      form: {
        supplyChannel: '',
        goodsShelfName: '',
        commentMobile: '',
        commentUserName: '',
        status: '',
        recommend: '',
        score: '',
        type: '',
        yzCode: ''
      },
      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    showMobile(row) {
      row.showMobile = !row.showMobile;
    },
    viewFullText(row, column, cell, event) {
      if (column.property === 'commentContent') {
        this.fullText = row.commentContent;
        this.dialogCommentVisible = true;
      }
    },
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportCommentList', data);
    },
    updateStatus(rowData) {
      const data = {
        commentId: rowData.commentId
      };
      this.$post('updateCommentStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    setFeaturedReviews(rowData) {
      const data = {
        commentId: rowData.commentId
      };
      this.$post('setFeaturedReviews', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    // 打开回复弹窗
    openReplyDialog(rowData) {
      this.commentId = rowData.commentId;
      this.commentUserId = rowData.commentUserId;
      this.rMVisible = true;
    },
    // 处理查询参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getCourseCommentList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          body.data.forEach(item => {
            item.showMobile = false;
            item.score = Number(item.score);
          });
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 数据查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }

};
</script>

<style lang="scss" scoped>
.comment {
  width: 100%;
  overflow: hidden;
  display: -webkit-box;   //将对象作为弹性伸缩盒子模型显示  *必须结合的属性*
  -webkit-box-orient: vertical;   //设置伸缩盒对象的子元素的排列方式  *必须结合的属性*
  -webkit-line-clamp: 5;   //用来限制在一个块元素中显示的文本的行数
  word-break: break-all;   //让浏览器实现在任意位置的换行 *break-all为允许在单词内换行*
}

.dialog-comment {
  line-height: 26px;
}

</style>
