<template>
  <common-dialog
    :show-footer="true"
    is-full
    width="600px"
    title="优惠券详情"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <!-- <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >

        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label='用户姓名' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form> -->

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="couponName" label="优惠券" align="center" />
        <el-table-column prop="couponAmount" label="优惠券面额" align="center">
          <template slot-scope="scope">
            {{ scope.row.couponAmount / 100 }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="领取时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.createTime | transformTimeStamp }}
          </template>
        </el-table-column>
        <el-table-column prop="usageTime" label="使用时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.usageTime | transformTimeStamp }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      form: {
        mobile: '',
        userName: ''
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.getTableList();
      }
    },
    open() {
      this.getTableList();
    },
    getTableList() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        userName: this.row.userName,
        mobile: this.row.mobile
      };
      console.log(data);
      this.$post('scaleDetailed', { orderDetailedNo: this.row.orderDetailNo }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body;
        }
      });
    },
    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
