<template>
  <div>
    <open-packup>
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='用户姓名' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>

        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder='请输入手机号' />
        </el-form-item>

        <el-form-item label='卡名称' prop='mebName'>
          <el-input v-model="form.mebName" placeholder='请输入卡名称' />
        </el-form-item>

        <el-form-item label='订单渠道' prop='buySource'>
          <el-select
            v-model="form.buySource"
            clearable
            placeholder="请选择订单渠道"
          >
            <el-option label="微信" value="WECHAT" />
            <el-option label="APP" value="APP" />
          </el-select>
        </el-form-item>

        <el-form-item label='卡类型' prop='mebFreeType'>
          <el-select v-model="form.mebFreeType" clearable placeholder="请选择卡类型">
            <el-option
              v-for="item in $localDict['mebFreeType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='卡种类' prop='mebPayType'>
          <el-select v-model="form.mebPayType" clearable placeholder="请选择卡种类">
            <el-option
              v-for="item in $localDict['mebPayType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='单据号' prop='orderNo'>
          <el-input v-model="form.orderNo" placeholder='请输入单据号' />
        </el-form-item>

        <el-form-item label='第三方单号' prop='thirdPradNo'>
          <el-input v-model="form.thirdPradNo" placeholder='请输入第三方单号' />
        </el-form-item>

        <el-form-item label='收费方式' prop='payType'>
          <el-select v-model="form.payType" clearable placeholder="请选择收费方式">
            <el-option
              v-for="item in $dictJson['paymentType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="缴费起止时间" prop="time">
          <el-date-picker
            v-model="form.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='应付金额区间' prop='amountsPay'>
          <el-input-number
            v-model="form.startAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最低"
          />
          -
          <el-input-number
            v-model="form.endAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最高"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <div class="left">
        会员卡累计付费{{ totalAmount }}元，累计付费{{ totalSum }}人
      </div>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">EXCEL导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
    >
      <el-table-column label="订单渠道" align="center" prop="buySource">
        <template slot-scope="scope">
          {{ scope.row.buySource | buySource }}
        </template>
      </el-table-column>
      <el-table-column label="卡类型" align="center" prop="mebFreeType">
        <template slot-scope="scope">
          {{ scope.row.mebFreeType | memberCardType }}
        </template>
      </el-table-column>
      <el-table-column label="卡种类" align="center" prop="mebPayType">
        <template slot-scope="scope">
          {{ scope.row.mebPayType | tansformCardSpecies }}
        </template>
      </el-table-column>
      <el-table-column label="卡名称" align="center" prop="mebName" />
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="mobile" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <!-- 应缴金额=商品价格，实缴金额=现金金额 -->
      <el-table-column label="应缴金额" align="center" prop="amount" />
      <el-table-column label="实缴金额" align="center" prop="normalPrice" />
      <el-table-column label="智米抵扣" align="center" prop="zmDeduction" />
      <el-table-column label="滞留抵扣" align="center" prop="zlDeduction" />
      <el-table-column label="缴费日期" align="center" prop="payDate" width="150" />
      <el-table-column label="单据号" align="center" prop="orderNo" />
      <el-table-column label="第三方单据号" align="center" prop="thirdPradNo" />
      <el-table-column label="收款方式" align="center" prop="payType">
        <template slot-scope="scope">
          {{ scope.row.payType | tansformPayMethod }}
        </template>
      </el-table-column>
      <el-table-column label="订单绑定" align="center" prop="payStatus">
        <template slot-scope="scope">
          <el-button type="text" @click="orderBind(scope.row)">绑定</el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否审核" align="center" prop="review">
        <template slot-scope="scope">
          {{ scope.row.review | tansformApprovalStatus }}
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="reviewName" />
      <el-table-column label="备注" align="center" prop="remark">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope.row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <remark-dialog
      :id="currentId"
      :visible.sync="rdVisible"
      :remark="currentRemark"
    />

    <!-- 订单绑定 -->
    <order-bind :visible.sync="obVisible" :row="currentRow" orderIdentify="member" />
  </div>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
import { handleDateControl, exportExcel } from '@/utils';
import remarkDialog from './remark-dialog';
import orderBind from '../components/order-bind.vue';
import LookMobile from '@/mixins/LookMobile';
export default {
  components: {
    remarkDialog,
    orderBind
  },
  filters: {
    // 订单渠道
    buySource(val) {
      if (!val) return '';
      const data = {
        WECHAT: '微信',
        APP: 'APP'
      };
      return data[val];
    }
  },
  mixins: [LookMobile],
  data() {
    return {
      obVisible: false,
      currentId: null,
      currentRemark: null,
      currentRow: null,
      totalAmount: 0, // 总金额
      totalSum: 0, // 总购买数
      form: {
        userName: '',
        mobile: '',
        mebFreeType: '',
        mebPayType: '',
        thirdPradNo: '',
        payStratDate: '',
        payEndDate: '',
        orderNo: '',
        payType: '',
        buySource: '',
        mebName: '',
        startAmount: undefined,
        endAmount: undefined,
        time: null,
        yzCode: ''
      },
      tableLoading: false,
      table_height: TABLE_HEIGHT,
      rdVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 获取总金额，总购买人数
    getMebCardBilltotal() {
      const data = this.handleQueryParams();
      this.$post('getMebCardBilltotal', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.totalAmount = body.totalAmount || 0;
          this.totalSum = body.totalSum || 0;
        }
      });
    },
    exportData() {
      const data = this.handleQueryParams();
      console.log(data);
      exportExcel('exportVipCardBillList', data);
    },
    handleEdit(row) {
      this.currentId = row.logId;
      this.currentRemark = row.remark;
      this.rdVisible = true;
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.payStratDate = date[0];
      formData.payEndDate = date[1];
      delete formData.time;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...formData
      };
      return data;
    },
    async getTableList() {
      this.tableLoading = true;
      this.getMebCardBilltotal();

      const data = this.handleQueryParams();
      const { fail, body } = await this.$post('getVipBillList', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
        this.form.startAmount = undefined;
        this.form.endAmount = undefined;
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    orderBind(row) {
      this.currentRow = row;
      this.obVisible = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  .left{
    float: left;
    color:red;
  }
}
</style>
