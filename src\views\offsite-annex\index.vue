<template>
  <!-- <common-dialog :visible.sync="show" width="60%"> -->
  <div>
    <div class="annex">
      <div class="offsite">
        <div v-for="(item,index) in annexList" :key="index" style="padding: 10px 0">
          <el-radio v-model="radio" :label="index">{{ item.materialName }}</el-radio>
          <div v-if="radio == index">
            <p class="offsite_test">{{ item.materialDesc }}</p>
            <div class="offsite-foundation">
              <div v-for="(items, indexs) in item.thrAnnexList" :key="indexs" class="offsite-upload">
                <p style="text-align: center; line-height: 100px;;width:300px">{{ items.materialName }}<span v-if="items.isRequire==1" style="color:red;font-size: 20px;"> *</span></p>

                <el-upload
                  class="avatar-uploader"
                  :action="action"
                  :show-file-list="false"
                  :on-success="handleUploadSuccess.bind(null,{'index':indexs,'data':items})"
                  :before-upload="beforeAvatarUpload"
                  :data="fieldData"
                  :name="field"
                  :file-list="fileList"
                  :on-change="handleChange"
                >
                  <i class="el-icon-plus avatar-uploader-icon" />
                </el-upload>
                <img v-if="items.imageUrl || items.annexUrl" :src="items.imageUrl?items.imageUrl:imgURL+items.annexUrl" class="avatar imgShow" @click="handlePictureCardPreview(items.annexUrl?items.annexUrl:items.imageUrl)">

                <div class="lookImg" @click="handlePictureCardPreview(items.annexPreviewUrl)">
                  <p>点击查看示例图</p>
                  <img :src="imgURL+items.annexPreviewUrl" class="offsite-img">
                </div>
                <el-dialog :visible.sync="dialogVisible" top="8vh">
                  <img width="50%" :src="dialogImageUrl" alt="">
                </el-dialog>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="btn">
      <el-button type="primary" :disabled='isDisabled' @click="deb()">保存</el-button>
      <!-- <el-button @click="deb()">取消</el-button> -->
    </div>
  </div>
  <!-- </common-dialog> -->
</template>

<script>
import { ossUri } from '@/config/request';
let time;
export default {
  data() {
    return {
      imageUrl: '',
      form: {
        pass: '',
        checkPass: '',
        age: ''
      },
      dialogVisible: false,
      radio: 0,
      annexList: [],
      imgURL: '',
      action: '/file/uploadFileToTemp.do',
      fieldData: {
        fileName: ''
      },
      field: 'fileData',
      fileList: [],
      offsiteInfoList: '',
      learnId: '',
      annexId: '',
      ruleCode: '',
      stdId: '',
      show: true,
      dialogImageUrl: '',
      annexInfoList: '',
      okBtnType: false,
      isDisabled: false
    };
  },
  watch: {
    radio(newVal, oldVal) {
      if (newVal) {
        this.isDisabled = false;
      }
    }
  },
  created() {
    this.learnId = this.$route.query.learnId;
    this.ruleCode = this.$route.query.ruleCode;
    this.stdId = this.$route.query.stdId;
    this.annexId = this.$route.query.annexId;

    if (this.learnId) {
      this.getAnnex();
    }
  },
  methods: {
    getAnnex() {
      const data = {
        learnId: this.learnId,
        ruleCode: this.ruleCode
      };
      this.$http({ method: 'get', url: '/stuRemoteData/getAnnexList', params: data }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.annexList = body;
          this.imgURL = ossUri;
          this.annexList.forEach((item, index) => {
            item.thrAnnexList.forEach((items, indexs) => {
              if (items.annexUrl !== null) {
                this.radio = index;
              }
            });
          });
        }
      });
    },

    handleUploadSuccess(obj, res, file) {
      this.okBtnType = true;
      this.annexList.forEach((item, index) => {
        var imgList = item.thrAnnexList;
        if (index === this.radio) {
          item.thrAnnexList.forEach((item, index) => {
            var objIndex = obj.index;
            if (item.id === obj.data.id) {
              if (objIndex === index) {
                obj.data.annexUrl = file.name;
                obj.data.learnId = this.learnId;
                obj.data.annexType = obj.data.materialType;
                obj.data.subAnnexType = obj.data.materialType;
                obj.data.annexName = obj.data.materialName;
                imgList[index].imageUrl = URL.createObjectURL(file.raw);
              }
            } else {
              item.learnId = this.learnId;
              item.annexType = item.materialType;
              item.subAnnexType = item.materialType;
              item.annexName = item.materialName;
            }
          });
          var data = JSON.parse(JSON.stringify(item.thrAnnexList));
          data.forEach(key => {
            // delete key.isRequire;
            delete key.materialDesc;
            delete key.materialName;
            delete key.materialType;
            delete key.type;
            delete key.thrAnnexList;
            delete key.annexPreviewUrl;
            delete key.imageUrl;
            delete key.pId;
            delete key.id;
          });
          this.offsiteInfoList = data;
        }
      });
    },
    beforeAvatarUpload(file) {
      console.log(file.type, 'type');
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJPG) {
        this.$message.error('上传头像图片只能是 JPG和PNG 格式!');
      }
      this.fieldData.fileName = file.name;
      return isJPG;
    },
    handleChange(file, fileList) {
      this.isDisabled = false;
      this.fileList = fileList;
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = this.imgURL + file;
      this.dialogVisible = true;
    },
    handleRemove(file) {
      this.annexList.forEach(item => {
        item.thrAnnexList.forEach((items, index) => {
          if (file.id === items.id) {
            items.imageUrl = URL.revokeObjectURL(file.imageUrl);
          }
        });
      });
    },
    deb: function() {
      const that = this;
      if (time) {
        clearTimeout(time);
      }
      time = setTimeout(function() {
        that.num++;
        that.onSubmit();
        time = undefined;
      }, 1000);
    },
    onSubmit() {
      const that = this;
      if (that.radio === '') {
        that.$message.error('请选中需要上传的选项');
      }
      var type = '';
      that.annexList.forEach((item, index) => {
        if (that.radio === index) {
          const newlist = item.thrAnnexList.filter((item, i, arr) => {
            return item.isRequire === 1;
          });
          type = newlist.every(function(elem, index, arr) {
            if (elem.imageUrl || elem.annexUrl) {
              return true;
            } else {
              return false;
            }
          });
          // types = item.thrAnnexList.every(function(elem, index, arr) {
          //   if (elem.annexUrl) {
          //     return true;
          //   } else {
          //     return false;
          //   }
          // });
          // item.thrAnnexList.forEach(i => {
          //   if (i.isRequire === 1 && i.annexUrl) {
          //     requireType = true;
          //   }
          // });
        }
      });
      if (type) {
        that.offsiteInfoList.forEach(item => {
          if (item.annexUrl) {
            item.isRequire = '1';
          }
        });
        const formData = new FormData();
        var data = JSON.stringify(that.offsiteInfoList);
        formData.append('learnId', that.learnId);
        formData.append('offsiteInfos', data);
        formData.append('stdId', that.stdId);
        formData.append('annexId', that.annexId);
        if (this.okBtnType) {
          this.isDisabled = true;
          that.$post('offsiteEdit', formData, { uploadFile: true }).then(res => {
            if (res.ok) {
              that.$message.success('保存成功');
            }
          });
        } else {
          that.$message.warning('未更新上传图片，确认是否重新上传，如不上传请关闭弹窗');
        }
        // } else {
        //   that.$message.error('请上传选中选项的图片');
        // }
      } else {
        that.$message.error('请上传选中选项的图片');
      }
    },
    cal() {
      window.close();
      console.log(window);
    }
  }
};
</script>
<style lang="scss" scoped>
.annex {
      // width: 47%;
      // height:800px;
      margin: 2% 0 2% 15%;
  .offsite{
   .offsite-foundation{
    // display: flex;
    margin-bottom: 30px;
    .offsite-upload{
      display: flex;
      margin-bottom: 10px;
    }
    .offsite-img{
    border-radius: 0.05rem;
    height: 100px;
    -o-object-fit: cover;
    object-fit: cover;
    width: 150px;
    }
    }
    .offsite_test{
      margin:20px 0;
    }
  }
}
  .btn{
    float: right;
    margin: 0 20px 20px 0;
  }
   ::v-deep .el-radio__label{
    font-size: 18px;
  }
  ::v-deep .el-dialog__body{
    text-align: center;
  }
  .lookImg{
  position: relative;
  p{
    width: 150px;
  position:absolute;
  line-height: 100px;
  text-align: center;
  }
}
</style>

<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-right: 20px;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
.imgShow{
  margin:0 40px 0 20px;
}

</style>
