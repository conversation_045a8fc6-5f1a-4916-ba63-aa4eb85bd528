<template>
  <div class='yz-base-container'>

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item label='推荐位置' prop='positionCode'>
        <el-select
          v-model="form.positionCode"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="(item,index) in positionCode"
            :key="index"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <!--      <el-form-item label='商品来源' prop='goodsSource'>-->
      <!--        <el-select-->
      <!--          v-model="form.goodsSource"-->
      <!--          filterable-->
      <!--          clearable-->
      <!--          placeholder="请选择"-->
      <!--        >-->
      <!--          <el-option-->
      <!--            v-for="(value,key) in goodsSource"-->
      <!--            :key="key"-->
      <!--            :label="value"-->
      <!--            :value="key"-->
      <!--          />-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->

      <el-form-item label='推荐商品名' prop='goodsShelfName'>
        <el-input v-model="form.goodsShelfName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='是否启用' prop='status'>
        <el-select
          v-model="form.status"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="add"
      >新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="positionCode" label="推荐位置" align="center">
        <template slot-scope="scope">
          {{ format(scope.row.positionCode, 'positionCode') }}
        </template>
      </el-table-column>
      <el-table-column prop="recommendType" label="推荐类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.recommendType | recommendType }}
        </template>
      </el-table-column>
      <el-table-column prop="goodsShelfName" label="推荐内容(商品/链接)" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.recommendType === 1">{{ scope.row.goodsShelfName }}</span>
          <span v-if="scope.row.recommendType === 2">{{ scope.row.url }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="推荐排序" align="center" />
      <el-table-column prop="status" label="是否启用" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 1? 'success':'danger' ">
            {{ scope.row.status === 1 ? '启用':'禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <update-dialog
      :id="editId"
      :visible.sync="udVisible"
      :title="udTitle"
    />

  </div>
</template>

<script>
import updateDialog from './update-dialog';
import { getTextFromDict } from '@/utils';
export default {
  components: {
    updateDialog
  },
  filters: {
    recommendType(val) {
      if (!val) return;
      const data = {
        1: '商品',
        2: '推荐链接'
      };
      return data[val];
    }
  },
  data() {
    return {
      udVisible: false,
      udTitle: '新增',
      tableLoading: false,
      form: {
        positionCode: '',
        goodsSource: '', // 业务调整，不需要了
        goodsShelfName: '',
        status: ''
      },
      tableData: [],
      editId: null,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      positionCode: [], // 推荐位置下拉
      goodsSource: [] // 商品来源
    };
  },
  mounted() {
    this.positionCode = this.$dictJson['positionCode'];
    // this.getGoodsSource();
    this.getTableList();
  },
  methods: {
    // // 获取商品来源
    // getGoodsSource() {
    //   this.$post('getGoodsSource')
    //     .then(res => {
    //       const { fail, body } = res;
    //       if (!fail) {
    //         this.goodsSource = body;
    //       }
    //     });
    // },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    format(valueKey, key) {
      return getTextFromDict(valueKey, key);
    },
    // 新增
    add() {
      this.editId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    // 编辑
    edit(row) {
      this.editId = row.goodsRecommendId;
      this.udTitle = '编辑';
      this.udVisible = true;
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getProductRec', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  margin-top:16px;
}

.table-btnbox {
  margin:20px 0 10px 0;
  .left{
    line-height: 33px;
  }
  .align-right{
    text-align:right;
  }
}

</style>
