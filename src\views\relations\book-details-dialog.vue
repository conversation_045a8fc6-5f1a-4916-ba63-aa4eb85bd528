<template>
  <common-dialog is-full title="购买读书计划明细" :visible.sync='show' @open="init" @close='close'>
    <div class="dialog-main">
      <open-packup>
        <el-form
          ref='searchForm'
          class='yz-search-form'
          size='mini'
          :model='form'
          label-width='120px'
          @submit.native.prevent='search'
        >
          <el-form-item label='远智编码' prop='yzCode'>
            <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
          </el-form-item>

          <el-form-item label='用户姓名' prop='userName'>
            <el-input v-model="form.userName" />
          </el-form-item>

          <el-form-item label='手机号码' prop='mobile'>
            <el-input v-model="form.mobile" />
          </el-form-item>

          <el-form-item label='订单来源' prop='orderChannel'>
            <el-select
              v-model="form.orderChannel"
              clearable
              placeholder="请选择订单来源"
            >
              <el-option label="零一裂变" value="lingYi" />
              <el-option label="远智" value="YZ" />
            </el-select>
          </el-form-item>

          <el-form-item label='最近售卖渠道' prop='appType'>
            <el-select v-model="form.appType" placeholder="请选择">
              <el-option
                v-for="item in $localDict['platform']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label='跟进人' prop='follower'>
            <el-input v-model="form.follower" />
          </el-form-item>

          <el-form-item label='最近购买时间' prop='time'>
            <el-date-picker
              v-model="form.time"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>

          <el-form-item label='会员卡名称' prop='mebName'>
            <el-input v-model="form.mebName" />
          </el-form-item>

          <el-form-item label='推荐人' prop='referrerName'>
            <el-input v-model="form.referrerName" />
          </el-form-item>

          <el-form-item label='招生老师' prop='requireName'>
            <el-input v-model="form.requireName" />
          </el-form-item>

          <el-form-item label='推广活动' prop='_actName'>
            <el-select v-model="form._actName" placeholder="请选择" @change="getChannelOptions">
              <el-option
                v-for="item in actOptions"
                :key="item.mebDeployId"
                :label="item.actName"
                :value="item.mebDeployId"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-if="form.actName!=''" label='推广渠道' prop='channelName'>
            <el-select v-model="form.channelName" placeholder="请选择">
              <el-option
                v-for="item in channelOptions"
                :key="item.channelName"
                :label="item.channelName"
                :value="item.channelName"
              />
            </el-select>
          </el-form-item>

          <div class="search-reset-box">
            <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
          </div>

        </el-form>
      </open-packup>

      <!-- 按钮区 -->
      <!-- <div class='yz-table-btnbox'>
        <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">EXCEL导出</el-button>
      </div> -->

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 290px)"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="userName" label="用户" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center">
          <template slot-scope="scope">
            <span @click="showMobile(scope.row)">
              <span>{{ scope.row.mobile | hidePhone(6,scope.row.showMobile) }}</span>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="goodsShelfName" label="读书计划名称" align="center" />
        <el-table-column label="订单来源" align="center" prop="orderChannel">
          <template slot-scope="scope">
            {{ scope.row.orderChannel | orderSource }}
          </template>
        </el-table-column>
        <el-table-column prop="appType" label="售卖渠道" align="center">
          <template slot-scope="scope">
            {{ scope.row.appType | appType }}
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="购买时间" align="center" />
        <el-table-column prop="recommendUserName" label="推荐人" align="center" />
        <el-table-column prop="empName" label="招生老师" align="center" />
        <el-table-column prop="empDpName" label="招生老师部门" align="center" />
        <el-table-column prop="followName" label="跟进人" align="center" />
        <el-table-column prop="followDpName" label="跟进人部门" align="center" />

      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>
<script>
import { handleDateControl, exportExcel } from '@/utils';
import openPackup from '@/components/open-packup';
export default {
  components: {
    openPackup
  },
  filters: {
    appType(val) {
      if (!val) return '';
      const data = {
        WECHAT: '微信',
        APP: 'APP'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      form: {
        userName: '',
        mobile: '',
        mebName: '',
        payStratDate: '',
        appType: '',
        referrerName: '',
        requireName: '',
        follower: '',
        time: '',
        actName: '',
        _actName: '',
        channelName: '',
        tradeCode: 'readPlan', // 交易编码 课程：course，读书计划：readPlan
        orderChannel: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      actOptions: [],
      channelOptions: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    showMobile(row) {
      row.showMobile = !row.showMobile;
    },
    // 获取推广活动
    getActOptions() {
      this.$post('getActOptions').then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.actOptions = body;
        }
      });
    },
    // 获取推广渠道
    getChannelOptions(val) {
      this.form.channelName = '';
      const item = this.actOptions.find(item => {
        return item.mebDeployId === val;
      });
      this.form.actName = item.actName;
      const data = {
        mappingId: item.mebDeployId
      };
      this.$post('getPromoteLinkList', data, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.channelOptions = body;
          }
        });
    },
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportUserGoodsShelfPayList', data);
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.payStratDate = date[0];
      formData.payEndDate = date[1];
      delete formData.time;
      delete formData._actName;
      const data = {
        ...formData,
        userId: this.userId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    init() {
      this.getTableList();
      this.getActOptions();
    },
    async getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();

      const { fail, body } = await this.$post('getBoughtCourseList', data);
      if (!fail) {
        body.data.forEach(item => {
          item.showMobile = false;
        });
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.form.actName = '';
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$refs['searchForm'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
