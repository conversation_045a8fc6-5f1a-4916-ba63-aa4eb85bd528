<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="优惠券管理" name="couponManage">
        <couponManage />
      </el-tab-pane>
      <el-tab-pane label="赠送管理" name="giftSystem">
        <giftSystem />
      </el-tab-pane>
      <el-tab-pane label="微信群邀约管理" name="memberCard">
        <wxInvite />
      </el-tab-pane>
      <el-tab-pane label="会员卡活动管理" name="course" lazy>
        <mebActConfig />
      </el-tab-pane>

    </el-tabs>
  </div>
</template>
<script>
import mebActConfig from './member-activity-config/index';
import couponManage from './coupon-manage/index';
import wxInvite from './wx-invite/index';
import giftSystem from './gift-system/index';
export default {
  components: {
    couponManage,
    mebActConfig,
    wxInvite,
    giftSystem
  },
  data() {
    return {
      activeName: 'couponManage'
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
