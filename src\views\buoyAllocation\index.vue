<template>
  <div class="yz-base-container">
    <!-- 表单区 -->
    <el-form
      size="mini"
      :model="querys"
      label-width="120px"
      class="yz-search-form"
      style="margin-top: 20px;"
      @submit.native.prevent="serachBtn"
    >
      <el-row>
        <el-form-item label="浮标id：">
          <el-input-number v-model="querys.id" class="es-input" :min="0" placeholder="请输入" :controls="false" :precision="0" />
        </el-form-item>
        <el-form-item label="启用状态：">
          <el-select v-model="querys.isAllow" filterable clearable placeholder="请选择">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="展示用户：">
          <el-select v-model="querys.userGroup" filterable clearable placeholder="请选择">
            <el-option label="实时用户群" value="0" />
            <el-option label="标签分群" value="1" />
          </el-select>
        </el-form-item>
      </el-row>
      <el-row style="margin-top: 10px;">
        <el-form-item label="上架时间起：">
          <el-date-picker v-model="querys.startTime" type="datetime" placeholder="选择时间" />
        </el-form-item>
        <el-form-item label="上架时间止：">
          <el-date-picker v-model="querys.endTime" type="datetime" placeholder="选择时间" />
        </el-form-item>
      </el-row>
      <div class="search-reset-box">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click.stop="refreshBtn" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox table-btn">
      <el-button type="success" size="small" icon="el-icon-plus" @click="addupdateBtn">新增</el-button>
    </div>
    <!-- 表格区 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column label="浮标id" prop="id" align="center" width="70" />
      <el-table-column label="展示权重" prop="weight" align="center" width="70" />
      <el-table-column label="图片" align="center" prop="floatImg">
        <template slot-scope="scope">
          <img class="answer-img" :src="scope.row.floatImgSrc" alt="">
        </template>
      </el-table-column>
      <el-table-column label="上架时间" align="center">
        <template slot-scope="scope">
          <div>起：{{ scope.row.startTime }}</div>
          <div>止：{{ scope.row.endTime }}</div>
        </template>
      </el-table-column>
      <el-table-column label="展示用户" prop="userShowText" align="center" />
      <el-table-column label="跳转地址" prop="jumpText" align="center" />
      <el-table-column label="是否启用" prop="allowText" align="center" />
      <el-table-column label="最近操作人" prop="updateUser" align="center" />
      <el-table-column label="操作时间" prop="updateTime" align="center" />
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button v-if="scope.row.isAllow == 0" type="success" size="small" @click="seeDetails(scope.row)">启用</el-button>
          <el-button v-else type="info" size="small" @click="seeDetails(scope.row)">禁用</el-button>
          <el-button type="primary" size="small" @click="addupdateBtn(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.size" @pagination="getTableList" />
    </div>
    <!-- 编辑弹窗 -->
    <added-buoy :visible="showBuoy" :rows="rows" @on-close="closeUpdateBtn" />
  </div>
</template>

<script>
import addedBuoy from './added-buoy.vue';
import { ossUri } from '@/config/request';
import { getCutDay } from '@/utils';

export default {
  components: { addedBuoy },
  props: {},
  data() {
    return {
      querys: {
        id: undefined,
        isAllow: '',
        userGroup: '',
        startTime: '',
        endTime: ''
      },
      rows: {},
      showBuoy: false,
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        size: 10
      },
      buoyParams: {}
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    serachBtn() {
      this.pagination.page = 1;
      this.getTableList();
    },
    refreshBtn() {
      this.querys = {
        id: undefined,
        isAllow: '',
        userGroup: '',
        startTime: '',
        endTime: ''
      };
    },
    // 打开-新增修改
    addupdateBtn(item) {
      this.showBuoy = true;
      if (item.id) this.rows = item;
    },
    // 关闭-新增修改
    closeUpdateBtn() {
      this.rows = {};
      this.showBuoy = false;
      this.getTableList();
    },
    // 禁用-启用
    seeDetails(item) {
      const blos = Boolean(item.isAllow === 0);// 禁用
      const text = blos ? '启用' : '禁用';
      this.tableLoading = true;
      this.$post('updateFloatConfig', { id: item.id, isAllow: blos ? 1 : 0 }, { json: true })
        .then((res) => {
          console.log('禁用-启用-res', res);
          if (res.code === '00') {
            this.$message({ message: `${text}成功`, type: 'success' });
            this.getTableList();
          } else {
            this.$message.error(`${text}失败`);
          }
          this.tableLoading = false;
        }).catch(() => {
          this.$message.error(`${text}失败`);
          this.tableLoading = false;
        });
    },
    // 接口获取配置列表
    getTableList() {
      this.tableLoading = true;
      const { startTime, endTime } = this.querys;
      const obs = {
        start: this.pagination.page,
        length: this.pagination.size,
        ...this.querys,
        startTime: startTime && getCutDay(startTime),
        endTime: endTime && getCutDay(endTime)
      };
      for (const key in obs) {
        if (obs[key] === '') delete obs[key];
      }
      this.$post('selectConfigList', obs).then((res) => {
        const { code, body } = res;
        console.log('接口获取配置列表', body);
        if (code === '00') {
          body.data?.map(item => {
            if (item.floatImg) item.floatImgSrc = ossUri + item.floatImg;
            item.userShowText = Number(item.userGroup) ? '标签分群' : '实时用户群';
            item.jumpText = Number(item.jumpAddress) ? '跳转小程序' : '跳转弹窗';
            item.isAllow = Number(item.isAllow) || 0;
            item.allowText = item.isAllow === 1 ? '启用' : '禁用';
          });
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
        this.tableLoading = false;
      }).catch(() => {
        this.tableData = [];
        this.tableLoading = false;
      });
    }
  }
};
</script>

<style lang="scss">
.es-input {
  width: 100%;
  .el-input__inner {
    text-align: left !important;
  }
}
</style>

