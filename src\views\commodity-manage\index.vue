<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">

      <el-tab-pane label="商品库管理" name="storehouse">
        <storehouse />
      </el-tab-pane>

      <el-tab-pane label="上架管理" name="putCommodity" lazy>
        <put-commodity />
      </el-tab-pane>

    </el-tabs>
  </div>
</template>
<script>
import storehouse from './storehouse';
import putCommodity from './put-commodity';
export default {
  components: {
    storehouse,
    putCommodity
  },
  data() {
    return {
      activeName: 'storehouse',
      a: ''
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
