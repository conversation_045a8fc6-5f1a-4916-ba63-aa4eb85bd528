<template>
  <common-dialog
    show-footer
    :title="title"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='课程名称' prop='courseName'>
          <!-- <infinite-selects
            v-model="form.courseName"
            api-key="courseSelect"
            key-name="courseName"
            value-name='courseId'
            :default-option="courseDefaultOption"
            :param="{courseName: ''}"
            @change="handleCourseSelect"
          /> -->
          <remote-search-selects
            ref="remoteSearch"
            v-model="form.courseName"
            :props="{apiName: 'courseSelect', value: 'courseId', label: 'courseName', query: 'courseName'}"
            :param="{courseName:''}"
            :default-option="courseDefaultOption"
            @change="handleCourseSelect"
          />
        </el-form-item>

        <!-- [待]当有配置阶段时显示，为必填 -->
        <el-form-item label='阶段名称' prop='stageId'>
          <el-select v-model="form.stageId" v-loadmore="loadMoreStage" placeholder="请选择">
            <el-option
              v-for="item in afterStageOption"
              :key="item.stageId"
              :label="item.stageName"
              :value="item.stageId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='课时名称' prop='courseTimeName'>
          <el-input
            v-model="form.courseTimeName"
            placeholder="请输入"
            maxlength="25"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='上课方式' prop='courseType'>
          <el-select v-model="form.courseType" placeholder="请选择" @change="courseTypeChange">
            <el-option label="直播" :value="0" />
            <el-option label="录播" :value="1" />
            <el-option label="硬盘推流" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="上课时间" prop="time">
          <el-date-picker
            v-model="form.time"
            :picker-options="expireTimeOption"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <!-- 当选择硬盘推流必填，显示 -->
        <el-form-item v-if="form.courseType == 2" label='视频VIDS' prop='liveVids'>
          <el-input v-model="form.liveVids" />
        </el-form-item>
        <!-- 编辑的时候, 并且是直播或者硬盘推流 才显示保利威回放vids -->
        <el-form-item v-if="courseTimeId && (form.courseType == 0 || form.courseType == 2)" label='保利威回放VIDS' prop='hfVids'>
          <el-input v-model="form.hfVids" />
        </el-form-item>

        <el-form-item label='腾讯云回放链接' prop='txyVideoUrl' :rules="txyVideoUrlRule">
          <el-input v-if="form.courseType === 1" v-model="form.txyVideoUrl" placeholder='请输入腾讯云回放链接' />
          <el-input v-else v-model="form.txyVideoUrl" disabled />
        </el-form-item>

        <el-form-item label='是否允许回放' prop='allowHf'>
          <el-radio-group v-model="form.allowHf">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='回放有效时间' prop='hfTime'>
          <input-number v-model="form.hfTime" :length='10'>
            <span slot="suffix">h</span>
          </input-number>
        </el-form-item>

        <el-form-item label='排序' prop='sort'>
          <input-number v-model="form.sort" :length='10' />
        </el-form-item>

        <!-- <el-form-item label='是否允许试看' prop='trySee'>
          <el-radio-group v-model="form.trySee">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item> -->

        <el-form-item label='互动形式' prop='interactiveType'>
          <el-radio-group v-model="form.interactiveType">
            <el-radio label="2">留言</el-radio>
            <el-radio label="1">聊天室</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='是否启用' prop='allow'>
          <el-radio-group v-model="form.allow">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
import { handleDateControl } from '@/utils';

export default {

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    // 课时id
    courseTimeId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      courseDefaultOption: null,
      stageDefaultOption: null,
      show: false,
      stageOption: [],
      stageOptionPage: 1,
      form: {
        time: '',
        courseName: '',
        courseTimeName: '',
        stageId: '',
        courseType: '',
        startTime: '',
        endTime: '',
        // trySee: 0,
        allow: 1,
        interactiveType: '2',
        liveVids: '',
        hfVids: '',
        hfTime: undefined,
        allowHf: 0,
        sort: undefined,
        txyVideoUrl: ''
      },
      // playbackVidsRules: { required: true, message: '硬盘推流需要上传视频vid', trigger: 'blur' },
      rules: {
        courseName: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        courseTimeName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        stageId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        courseType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        time: [
          { required: true, message: '请选择时间', trigger: 'blur' }
        ],
        liveVids: [
          { required: true, message: '硬盘推流需要上传视频vid', trigger: 'blur' }
          // { required: true, message: '请输入', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        allowHf: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        // trySee: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ],
        interactiveType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        allow: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      expireTimeOption: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 24 * 60 * 60 * 1000;
        }
      }
    };
  },
  computed: {
    afterStageOption() {
      let arr = this.stageOption;
      if (this.stageDefaultOption) {
        arr = this.stageOption.filter(item => {
          if (item['stageName'] !== this.stageDefaultOption['stageName'] && item['stageId'] !== this.stageDefaultOption['stageId']) {
            return true;
          }
        });
        arr.push(this.stageDefaultOption);
      }
      return arr;
    },
    txyVideoUrlRule() {
      return [{ required: this.form.courseType === 1, message: '请输入腾讯云回放链接', trigger: 'blur' }];
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
    // 'form.courseType'(val) {
    //   const rule = val === 2 ? [this.playbackVidsRules] : [];
    //   this.rules.liveVids = [].concat(rule);
    // }
  },
  methods: {
    init() {
      if (this.courseTimeId) {
        const data = {
          courseTimeId: this.courseTimeId
        };
        this.$post('getCourseTimeInfo', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.courseName = body.courseId;

            // 请求阶段下拉
            this.getStageOption();
            this.courseDefaultOption = {
              courseName: body.courseName,
              courseId: body.courseId
            };
            // this.$refs['remoteSearch'].resetGetOptions();

            this.stageDefaultOption = {
              stageId: body.stageId,
              stageName: body.stageName
            };

            this.form.courseTimeName = body.courseTimeName;
            this.form.stageId = body.stageId;
            this.form.courseType = body.courseType;
            this.form.time = [body.startTime, body.endTime];
            // this.form.trySee = body.trySee;
            this.form.sort = body.sort;
            this.form.allow = body.allow;
            this.form.interactiveType = body.interactiveType;
            this.form.liveVids = body.liveVids;
            this.form.hfVids = body.hfVids;
            this.form.hfTime = body.hfTime;
            this.form.allowHf = body.allowHf;
            this.form.txyVideoUrl = body.txyVideoUrl;
          }
        });
      }
    },
    handleCourseSelect() {
      this.stageOptionPage = 1;
      this.stageDefaultOption = null;// 切换要清除 不然编辑状态数据有问题
      this.stageOption = [];
      this.form.stageId = '';
      this.getStageOption();
    },
    async getStageOption() {
      if (this.form.courseName !== null && this.form.courseName !== '') {
        const data = {
          page: this.stageOptionPage,
          rows: 10,
          courseId: this.form.courseName
        };
        const { fail, body } = await this.$post('getCourseStage', data);
        if (!fail) {
          if (body.data.length > 0) {
            const ops = this.stageOption.concat(body.data);
            this.stageOption = [].concat(ops);
          } else {
            this.stageOptionPage -= 1;
          }
        }
      }
    },
    loadMoreStage() {
      this.stageOptionPage += 1;
      this.getStageOption();
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addCourseTime';
          const formData = JSON.parse(JSON.stringify(this.form));
          const date = handleDateControl(formData.time);
          formData.startTime = date[0];
          formData.endTime = date[1];
          delete formData.time;
          const data = {
            ...formData
          };
          // 编辑
          if (this.courseTimeId) {
            apiKey = 'updateCourseTime';
            data.courseTimeId = this.courseTimeId;
          }
          this.$post(apiKey, data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      // this.$refs['form'].resetFields();
      // this.form.time = undefined;
      // this.stageOption = [];
      Object.assign(this.$data, this.$options.data());// 重置data
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    courseTypeChange(val) {
      if (val !== 1) {
        this.$refs.form.clearValidate('txyVideoUrl');
        this.form.txyVideoUrl = '';
      }
    }
  }
};
</script>
