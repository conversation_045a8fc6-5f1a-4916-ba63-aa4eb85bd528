<template>
  <div>
    <el-upload
      :class="{hide:hideUpload}"
      v-bind="$attrs"
      :action="action"
      :list-type="listType"
      :before-upload="beforeUpload"
      :on-preview="handlePictureCardPreview"
      :on-success="uploadSuccess"
      :on-remove="handleRemoveImg"
      :on-exceed="handleExceed"
      :limit="maxLimit"
      :file-list="fileList"
      :before-remove="beforeRemove"
      v-on="$listeners"
    >
      <slot name="default">
        <i class="el-icon-plus" />
      </slot>

      <div v-if="tip || $slots.tip" slot="tip">
        <slot name="tip">
          <div v-if="tip" class="el-upload__tip">{{ tip }}</div>
        </slot>
      </div>

    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: 'UploadFile',
  props: {
    action: {
      type: String,
      default: '/file/webuploader.do'
    },
    listType: {
      type: String,
      default: 'picture-card'
    },
    fileList: {
      type: Array,
      default: null
    },
    // 最多上传几张
    maxLimit: {
      type: Number,
      default: null
    },
    // slot 提示说明文字
    tip: {
      type: String,
      default: null
    },
    // 允许上传的文件后缀
    exts: {
      type: String,
      default: ''
    },
    // 文件大小单位为 M
    size: {
      type: Number,
      default: 0
    },
    imgWidth: {
      type: Number,
      default: 0
    },
    imgHeight: {
      type: Number,
      default: 0
    },
    beforeRemove: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      hideUpload: false,
      dialogVisible: false,
      dialogImageUrl: ''
    };
  },
  watch: {
    fileList: {
      immediate: true,
      handler() {
        if (this.fileList.length >= this.maxLimit) {
          this.hideUpload = true;
        }
      }
    }
  },
  methods: {
    beforeUpload(file) {
      const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const specifyFormat = this.exts.split('|');
      const isFitSize = file.size / 1024 / 1024 < this.size; // 是否符合规定大小
      const isFormat = specifyFormat.includes(fileType); // 是否符合规定文件格式

      // 格式限制
      if (this.exts && !isFormat) {
        this.$message.error(`请上传格式为 ${specifyFormat.join('，')}的文件`);
        return false;
      }

      // 文件大小限制
      if (this.size !== 0 && !isFitSize) {
        this.$message.error(`文件大小不能超过 ${this.size} M`);
        return false;
      }

      // 尺寸限制：宽高、单高、单宽、无限制
      const all = this.imgWidth && this.imgHeight;
      const singeH = this.imgWidth === 0 && this.imgHeight;
      const singeW = this.imgWidth && this.imgHeight === 0;
      if (all || singeH || singeW) {
        const dime = new Promise((resolve, reject) => {
          const nImg = new Image();
          const _URL = window.URL || window.webkitURL;
          nImg.onload = () => {
            console.log('nImg-----', nImg.width, nImg.height);
            let valid = false;
            if (all) {
              valid = nImg.width === this.imgWidth && nImg.height === this.imgHeight;
            } else if (singeH) {
              valid = nImg.height === this.imgHeight && this.imgWidth === 0;
            } else if (singeW) {
              valid = nImg.width === this.imgWidth && this.imgHeight === 0;
            }
            valid ? resolve(1) : reject(0);
          };
          nImg.src = _URL.createObjectURL(file);
        }).then(
          () => { return file; },
          () => {
            const test = all ? `上传图片尺寸只能是：${this.imgWidth}*${this.imgHeight}px` : singeH ? `上传图片尺寸固定高度：${this.imgHeight}px` : singeW ? `上传图片尺寸固定宽度：${this.imgWidth}px` : '上传图片尺寸错误';
            this.$message.error(test);
            return Promise.reject();
          });
        return dime;
      }
    },
    handleExceed() {
      this.$message.error('最多只能上传' + this.maxLimit + '张图片');
    },
    handleRemoveImg(file, fileList) {
      if (fileList.length < this.maxLimit) {
        this.hideUpload = false;
      }
      this.$emit('remove', { file, fileList });
    },
    uploadSuccess(response, file, fileList) {
      if (fileList.length >= this.maxLimit) {
        this.hideUpload = true;
      }
      this.$emit('success', { response, file, fileList });
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
      this.$emit('preview', file);
    }
  }
};
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card{
    display: none;
  }
}
::v-deep .el-upload-list__item-thumbnail{
  object-fit: cover;
}

::v-deep .el-upload--picture-card {
  border:1px solid #c0ccda;
}

</style>
