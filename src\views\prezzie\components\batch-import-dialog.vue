<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="导入送礼名单"
    :visible.sync="show"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form ref="searchForm" size="mini" :model="form" label-width="120px">
        <el-form-item label="模板：">
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label="选择文件：">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击选择文件</em>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>

      <!-- 导入失败提示区域 -->
      <div v-if="importError" class="import-error-section">
        <div class="error-message">
          <i
            class="el-icon-warning"
            style="color: #f56c6c; margin-right: 8px"
          />
          导入表格有误，请核对修改后再重新导入
        </div>
        <el-button
          v-if="failureFile"
          type="primary"
          size="small"
          @click="downloadFailureFile"
        >
          下载导入失败表单
        </el-button>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import FileSaver from 'file-saver';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      templateUrl:
        'https://static.yzou.cn/template/excel/%E9%80%81%E7%A4%BC%E6%B8%85%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
      action: '/giftGivingOrder/import',
      importError: false,
      failureFile: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    submit() {
      if (this.fileList.length > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning('请选择需要导入的文件');
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        const { importResult, failureFile } = response.body;
        if (importResult) {
          // 导入成功，隐藏错误提示
          this.importError = false;
          this.failureFile = '';
        } else {
          // 导入失败，显示错误提示
          this.importError = true;
          // 如果后端返回了失败文件的下载链接，保存它
          this.failureFile = failureFile;
        }
      } else {
        this.$message.error(response.msg || '导入失败');
      }
    },
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    // 下载失败文件
    downloadFailureFile() {
      if (this.failureFile) {
        try {
          // 将Base64字符串解码为二进制数据
          const binaryString = atob(this.failureFile);
          const bytes = new Uint8Array(binaryString.length);

          // 将二进制字符串转换为字节数组
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          // 创建Blob对象
          const blob = new Blob([bytes], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          });

          // 下载文件
          FileSaver.saveAs(blob, '导入失败数据.xlsx');
        } catch (error) {
          console.error('下载失败文件出错:', error);
          this.$message.error('下载失败，文件数据格式错误');
        }
      } else {
        this.$message.error('暂无失败文件可下载');
      }
    },
    close() {
      // 关闭时重置错误状态
      this.importError = false;
      this.failureFile = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-main {
  padding: 20px;
}

.import-error-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;

  .error-message {
    margin-bottom: 12px;
    color: #f56c6c;
    font-size: 14px;
    display: flex;
    align-items: center;
  }
}
</style>
