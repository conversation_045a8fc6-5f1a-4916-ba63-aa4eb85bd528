<template>
  <div>
    <annexDialog :show="show" />
  </div>
</template>

<script>
import annexDialog from './annex-dialog';
export default {
  components: { annexDialog },
  data() {
    return {
      show: false
    };
  },
  created() {
    this.show = Boolean(this.$route.query.show);
    // ruleCode
    // learnId
    // stdId
    // annexId
    // idCard
    console.log(this.show, this.$route.query, '0000');
  }
};
</script>

<style>

</style>
