<template>
  <common-dialog
    :show-footer="true"
    width="60%"
    title="任务类型"
    :visible.sync='show'
    :confirmLoading="confirmLoading"
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-suffix="："
        size="small"
      >
        <el-form-item label="任务模式" prop="taskMode">
          <el-radio-group v-model.number="form.taskMode" :disabled="isEdit" @change="taskModeChange">
            <el-radio v-for="item in taskMode" :key="item.value" :label="item.value">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="任务类型" prop="taskTypeConfigId">
          <el-select v-model="form.taskTypeConfigId" placeholder="请选择任务类型" clearable :disabled="isEdit || !form.taskMode" @change="taskTypeChange">
            <el-option v-for="item in taskTypeList" :key="item.value" :label="item.taskName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="任务图标" prop="taskHeadImage">
          <upload-file
            :max-limit="1"
            :file-list="taskHeadImage"
            @remove="taskHeadImageRemove"
            @success="taskHeadImageSuccess"
          />
        </el-form-item>
        <el-form-item label="任务标题" prop="taskTitle">
          <el-input v-model="form.taskTitle" placeholder="用于前端展示" maxlength="8" show-word-limit />
        </el-form-item>
        <el-form-item label="任务明细" prop="taskDesc">
          <el-input v-model="form.taskDesc" placeholder="用于前端展示" maxlength="15" show-word-limit />
        </el-form-item>
        <el-form-item v-if="form.taskMode === 2" label="每日任务上限" prop="taskCompleteTimes">
          <el-input-number v-model="form.taskCompleteTimes" style="width: 100%;" placeholder="请输入1-99" :controls="false" :min="1" :max="99" :precision="0" :disabled="isEdit || currentTask.taskCode === 'PAGE_SIGN'" />
        </el-form-item>
        <!-- 普通帖子，跑步帖子，读书帖子 -->
        <el-form-item v-if="['NORMAL_POST','RUN_POST','READ_POST'].includes(currentTask.taskCode)" label='关联话题词' prop='taskBusinessId'>
          <el-input-number v-model="form.taskBusinessId" :disabled="isEdit" style="width: 100%;" placeholder="请输入关联话题词Id" :controls="false" :min="1" :precision="0" />
        </el-form-item>
        <el-form-item label="单次奖励智米数" prop="taskZhimi">
          <el-input-number v-model="form.taskZhimi" :disabled="isEdit" style="width: 100%;" placeholder="请输入1-9999" :controls="false" :min="1" :max="9999" :precision="0" />
        </el-form-item>
        <div class="tips">{{ currentTask.tips }}</div>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { taskMode } from './../../type';
// import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => {}
    }
  },
  inject: ['singleTasksList', 'dailyTasksList'],
  data() {
    return {
      isEdit: false,
      confirmLoading: false,
      taskMode,
      taskTypeList: this.singleTasksList,
      show: false,
      form: {
        taskTypeConfigId: undefined,
        taskHeadImage: ''
      },
      taskHeadImage: [],
      rules: {
        taskMode: [{ required: true, message: '请选择任务模式', trigger: 'change' }],
        taskTypeConfigId: [{ required: true, message: '请选择任务类型', trigger: 'change' }],
        taskHeadImage: [{ required: true, message: '请上传任务Icon', trigger: 'change' }],
        taskTitle: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
        taskDesc: [{ required: true, message: '请输入任务明细', trigger: 'blur' }],
        taskCompleteTimes: [{ required: true, message: '请输入每日任务上限', trigger: 'blur' }],
        taskBusinessId: [{ required: true, message: '请输入关联话题词Id', trigger: 'blur' }],
        taskZhimi: [{ required: true, message: '请输入单次奖励智米数', trigger: 'blur' }]
      },
      currentTask: {
        taskCode: '',
        tips: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 任务类型切换
    taskTypeChange(value) {
      const taskTypeList = [...this.singleTasksList, ...this.dailyTasksList];
      const obj = taskTypeList.find(item => item.id === value);
      this.currentTask = {
        tips: obj.taskDesc ? obj.taskDesc : '',
        taskCode: obj.taskCode
      };
      if (this.currentTask.taskCode === 'PAGE_SIGN') {
        this.form.taskCompleteTimes = '1';
      } else {
        this.form.taskCompleteTimes = undefined;
      }
    },
    // 任务图标删除
    taskHeadImageRemove({ file, fileList }) {
      this.form.taskHeadImage = '';
    },
    // 任务图标上传成功
    taskHeadImageSuccess({ response, file, fileList }) {
      this.form.taskHeadImage = response;
    },
    // 任务模式切换
    taskModeChange(value) {
      this.form.taskTypeConfigId = undefined;
      this.currentTask.taskCode = '';
      this.currentTask.tips = '';
      if (value === 1) {
        this.taskTypeList = this.singleTasksList;
      } else {
        this.taskTypeList = this.dailyTasksList;
      }
    },
    open() {
      if (this.currentRow.id) {
        this.isEdit = true;
        this.getDetail();
      }
    },
    getDetail() {
      this.$http.get(`/newbietask/config/detail/${this.currentRow.id}`, { json: true }).then(res => {
        this.isEdit = true;
        const { fail, body } = res;
        if (!fail) {
          const form = body;

          // 任务图标
          this.taskHeadImage = [{ url: form.taskHeadImage }];
          this.taskModeChange(form.taskMode);
          this.taskTypeChange(form.taskTypeConfigId);
          this.form = form;
        }
      });
    },
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));

        const apiKey = this.isEdit ? 'updateNewbieTaskConfig' : 'addNewbieTaskConfig';
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.tips {
  margin-left: 45px;
  color: #f00;
}
</style>
