<template>
  <el-cascader
    v-model="val"
    class="yz-city-cascader"
    filterable
    clearable
    :disabled="disabled"
    :options="options"
    :placeholder="placeholder"
    @change="onChange"
  />
</template>

<script>
import { getCityOptions } from '@/utils';

export default {
  name: 'CityCascader',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择省市区'
    },
    optionTypes: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      val: '',
      options: getCityOptions()
    };
  },
  watch: {
    value(v) {
      this.val = v;
    }
  },
  mounted() {
    this.val = this.value;
  },
  methods: {
    onChange(val) {
      this.$emit('input', val);
    }
  }
};
</script>

<style lang='scss' scoped>
  .yz-city-cascader{

  }
</style>
