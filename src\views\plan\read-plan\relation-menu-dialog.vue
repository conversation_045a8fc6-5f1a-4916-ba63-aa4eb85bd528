<template>
  <common-dialog
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="menuListInfo"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='一级菜单名称' prop='menuName'>
          <el-input v-model="menuListInfo.menuName" maxlength="20" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label='排序' prop='sort'>
          <el-input-number v-model="menuListInfo.sort" :max="999" :disabled="!isFormItemEdit" :controls="false" class="yz-input-number padding-left" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='一级菜单摘要' prop='menuSummary'>
          <el-input
            v-model="menuListInfo.menuSummary"
            rows="5"
            type="textarea"
            maxlength="200"
            show-word-limit
          />

        </el-form-item>
        <el-form-item label="关联书本" prop="puMenuBookVOS">
          <!-- 关联书本 -->
          <el-button class="padding-10" type="primary" :disabled="!isFormItemEdit" @click="courseDialogShow=true">关联</el-button>
          <el-table
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            :height="table_height"
            header-cell-class-name='table-cell-header'
            :data="bookListInfo"
          >
            <el-table-column prop="sort" label="顺序" align="center" />
            <el-table-column prop="bName" label="书本名称" align="center" />
            <el-table-column prop="planDay" label="章节数量" align="center" />
            <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="handleEditBookData(scope.row)">删除</el-button>
                </div>
              </template>
            </el-table-column>

          </el-table>
        </el-form-item>

      </el-form>
    </div>

    <!-- 关联书本弹窗 -->
    <common-dialog
      title="关联书本"
      width="650px"
      :visible.sync='courseDialogShow'
      @open="initBookListDialog"
      @close='bookDialogShowClose'
    >
      <div class="dialog-main">
        <el-form
          ref='searchForm'
          size='mini'
          :model='searchForm'
          label-width='80px'
          :rules="rules"
        >
          <el-form-item label="查询书本" prop="bName">
            <el-input v-model="searchForm.bName" maxlength="50" style="width:350px;margin-right:20px" show-word-limit placeholder="请输入书本名称" />
            <el-button type="primary" icon='el-icon-search' @click="initBookListDialog()">查询</el-button>
          </el-form-item>
          <div>
            <el-table
              v-loading="tableLoading"
              border
              size="small"
              style="width: 100%"
              height="400px"
              header-cell-class-name='table-cell-header'
              :data="searchBookInfo"
            >
              <el-table-column prop="bName" label="书本名称" align="center" />
              <el-table-column label="操作" align="center" width="150px">
                <template slot-scope="scope">
                  <div class="yz-button-area">
                    <el-button type="text" @click="selectionCourse(scope.row)">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="yz-table-pagination">
            <pagination
              :total='pagination.total'
              :page.sync="pagination.page"
              :limit.sync="pagination.limit"
              layout='prev, pager, next, sizes, total'
              @pagination="initBookListDialog"
            />
          </div>

        </el-form></div>
    </common-dialog>

    <!-- 编辑书本顺序 -->
    <common-dialog
      title="编辑书本"
      width="650px"
      :show-footer="true"
      :visible.sync='editBookDialogShow'
      @confirm="editBooksubmit"
      @open="initEditBookDialog"
      @close='editBookDialogShowClose'
    >
      <div class="dialog-main">
        <el-form
          ref='sortForm'
          size='mini'
          :model='sortForm'
          label-width='80px'
        >

          <el-form-item label='书本名称' prop='bName'>
            <el-input v-model="sortForm.bName" maxlength="20" placeholder="bName" autocomplete="off" readonly />
          </el-form-item>
          <el-form-item label='顺序' prop='sort' :rules="[{required: true,message: '不能为空，请输入正整数', trigger: 'blur'}]">
            <el-input v-model="sortForm.sort" maxlength="20" show-word-limit placeholder="请输入" @keyup.native="limitInputNumber()" />
          </el-form-item>

        </el-form>
      </div>
    </common-dialog>

  </common-dialog>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isFormItemEdit: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    courseId: {
      type: [String, Number],
      default: null
    },
    menuList: {
      type: [Object, Number],
      default: null
    }
  },
  data() {
    const checkPuMenuBookVOs = (rule, value, callback) => {
      if (value.length === 0) {
        callback('请关联书本');
      } else {
        callback();
      }
    };
    return {
      menuListInfo: {
        menuName: '',
        sort: undefined,
        menuSummary: '',
        puMenuBookVOSJson: '',
        puMenuBookVOS: []
      }, // 菜单详情
      bookListInfo: [], //  一级菜单下关联书本详情
      searchBookInfo: [], // 查询书本列表
      // 传递
      sendMenuInfo: {
        menuName: '',
        sort: '',
        menuSummary: '',
        puMenuBookVOS: []
      },
      readonly: true,
      courseDefaultOption: null,
      show: false,
      tableLoading: false,
      courseDialogShow: false,
      editBookDialogShow: false,
      tableData: [],
      courseCatalog: [], // 书本下拉搜索
      defaultSelected: null,
      currentSelectionCourseType: [],
      table_height: TABLE_HEIGHT,
      searchForm: {
        bName: '',
        sort: '',
        planDay: '',
        bookId: ''
      },
      sortForm: {
        bName: '',
        sort: ''
      },
      bookSortList: {},
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      form: {
        menuName: '',
        sort: '',
        menuSummary: '',
        puMenuBookVOS: []
      },
      originFileUrl: '',
      fileList: [],
      rules: {
        menuName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        menuSummary: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        puMenuBookVOS: [
          { required: true, validator: checkPuMenuBookVOs, trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }

  },
  methods: {
    limitInputNumber() {
      var pattern = /^-?[0-9]\d*$/; // 整数的正则表达式
      // 不符合正整数时
      if (!pattern.test(this.sortForm.sort)) {
        this.sortForm.sort = '';
        // callback('请输入整数');
      }
      // return val;
    },
    // 打开书本弹窗
    initBookListDialog() {
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        bName: '' || this.searchForm.bName
      };
      this.$post('getBookSelect', data).then(res => {
        const { fail, body } = res;
        this.searchBookInfo = body.data;
        this.pagination.total = body.recordsTotal;
        if (!fail) {
          this.courseCatalog = this.getTreeData(body);
        }
      });
    },
    // 删除书本
    handleEditBookData(row) {
      this.bookListInfo.splice(this.bookListInfo.indexOf(this.bookListInfo.find(function(element) { return element.bookId === row.bookId; })), 1);
    },
    initEditBookDialog() {

    },
    editBookDialogShowClose() {
      this.editBookDialogShow = false;
    },
    // 编辑书本顺序提交
    editBooksubmit() {
      this.$refs['sortForm'].validate((valid) => {
        if (valid) {
          this.searchForm.bName = this.bookSortList.bName;
          this.searchForm.bookId = this.bookSortList.bookId;
          this.searchForm.planDay = this.bookSortList.planDay;
          this.searchForm.sort = this.sortForm.sort;

          // 字段bookSortList 3 sortForm 2
          this.bookListInfo.push(this.searchForm);
          this.menuListInfo.puMenuBookVOS = this.bookListInfo;
          // 清除防止数组元素都和最后一次赋值相同
          this.searchForm = {};
          this.editBookDialogShow = false;
          this.courseDialogShow = false;
        }
      });
    },
    // 选择书本
    selectionCourse(row) {
      this.bookSortList = row;
      this.sortForm.bName = row.bName;
      this.sortForm.sort = '';
      this.editBookDialogShow = true;
      this.courseDialogShow = false;
    },
    getTreeData(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    open() {
      if (this.menuList.menuName) {
        this.getMenuInfo();
      }
    },
    getMenuInfo() {
      this.menuListInfo = this.menuList;
      this.bookListInfo = this.menuList.puMenuBookVOS;
    },

    // 一级菜单提交
    submit() {
      if (this.title === '编辑') {
        this.edit();
      } else {
        this.add();
      }
    },
    add() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.menuListInfo.puMenuBookVOS = this.bookListInfo;
          this.$emit('getAddMenuListInfo', this.menuListInfo);
          this.menuListInfo = {};
          this.show = false;
        }
      });
    },
    // 编辑一级菜单提交
    edit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // this.bookListInfo = this.bookListInfo.map(item => {
          //   delete item.bName;
          //   return item;
          // });
          this.menuListInfo.puMenuBookVOS = this.bookListInfo;
          this.$emit('getEditMenuListInfo', this.menuListInfo);
          this.menuListInfo = {};
          this.show = false;
        }
      });
    },

    close() {
      // this.$refs['form'].resetFields();
      Object.assign(this.$data, this.$options.data.call(this));
      this.fileList = [];
      this.form.bName = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    bookDialogShowClose() {
      this.courseDialogShow = false;
      this.tableData = [];
      this.$refs['searchForm'].resetFields();
      this.pagination.page = 1;
      this.pagination.total = 0;
    },
    handleRemoveImg({ file, fileList }) {
      this.form.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.fileUrl = response;
      this.form.isAdd = 1;
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
