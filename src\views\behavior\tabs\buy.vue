<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>

      <el-form-item label='用户姓名' prop='realName'>
        <el-input v-model="form.realName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='套餐名称' prop='goodsShelfName'>
        <el-input v-model="form.goodsShelfName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='时间' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item label='购买状态' prop='buyStatus'>
        <el-select
          v-model="form.buyStatus"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="未购买" value="1" />
          <el-option label="已购买" value="2" />
          <el-option label="已退款" value="3" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      class="table"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="goodsShelfName" label="套餐名称" align="center" />
      <el-table-column prop="browse" label="浏览量" align="center" />
      <el-table-column prop="buyClick" label="购买点击量" align="center" />
      <el-table-column prop="tryIt" label="试看点击量" align="center" />
      <el-table-column prop="buyStatus" label="购买状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.buyStatus | buyStatus }}
        </template>
      </el-table-column>
      <el-table-column prop="buyTime" label="购买时间" align="center" />
      <el-table-column prop="lastExecuteDate" label="最近访问时间" align="center" />
      <el-table-column width="100" prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="realName" label="用户名称" align="center" />
      <el-table-column prop="nickname" label="微信昵称" align="center" />
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

  </div>
</template>
<script>
import { handleDateControl } from '@/utils';
export default {
  filters: {
    buyStatus(value) {
      if (!value) return;
      const data = {
        1: '未购买',
        2: '已购买',
        3: '已退款'
      };
      return data[value];
    }
  },
  data() {
    return {
      tableLoading: false,
      form: {
        realName: '',
        goodsShelfName: '',
        time: '',
        buyStatus: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    getTableList() {
      this.tableLoading = true;
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.startTime = date[0];
      formData.endTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getUserGoodsBuy', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.table {
  margin-top: 15px;
}
</style>
