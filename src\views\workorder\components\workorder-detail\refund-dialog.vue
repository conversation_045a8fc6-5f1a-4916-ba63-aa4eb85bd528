<template>
  <common-dialog
    :visible.sync="dialogVisible"
    title="退费明细（仅退费填写）"
    width="600px"
    :show-footer="false"
    @close="handleClose"
  >
    <el-form
      ref="refundForm"
      :model="formData"
      :rules="rules"
      label-width="130px"
      size="small"
      class="!px-16 !py-10"
    >
      <el-form-item label="提出退费时间:" prop="refundCreateTime">
        <el-date-picker
          v-model="formData.refundCreateTime"
          type="datetime"
          placeholder="请选择时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="defaultTime"
          style="width: 100%"
        ></el-date-picker>
      </el-form-item>

      <el-form-item label="退费备注:" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="4"
          placeholder="若有备注请填写，若无请直接完结"
          :maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="退费原因:" prop="reason">
        <common-select
          v-model="formData.reason"
          type="workOrderDict"
          :extraParams="{
            parentId: 5,
          }"
          placeholder="请选择"
          :isSingle="false"
        />
      </el-form-item>

      <el-form-item label="是否3.7天退费:" prop="isThreeSeven">
        <common-select
          v-model="formData.isThreeSeven"
          type="isThreeSeven"
          placeholder="请选择"
        />
      </el-form-item>

      <el-form-item label="应退金额:" prop="shouldRefundMoney">
        <el-input-number
          v-model.number="formData.shouldRefundMoney"
          placeholder="请输入金额"
          :min="0"
          :max="99999999"
          :controls="false"
          style="width: 100%"
        ></el-input-number>
      </el-form-item>

      <el-form-item v-if="workOrderType !== '1'" label="实退金额:" prop="actualRefundMoney">
        <el-input-number
          v-model.number="formData.actualRefundMoney"
          placeholder="请输入金额"
          :min="0"
          :max="99999999"
          :controls="false"
          style="width: 100%"
        ></el-input-number>
      </el-form-item>

      <el-form-item>
        <el-button
          class="w-45"
          type="primary"
          @click="handleSave"
          :loading="loading"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  name: "RefundDialog",
  components: {
    CommonDialog: () => import("@/components/common-dialog"),
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  inject: ["orderType"],
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    workOrderId: {
      type: [String, Number],
      default: "",
    },
    workOrderType: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      defaultTime: new Date().toTimeString().slice(0, 8), // 默认时间为当前时间
      formData: {
        refundCreateTime: "", // 提出退费时间
        remark: "", // 退费备注
        reason: "", // 退费原因
        isThreeSeven: "", // 是否3.7天退费
        shouldRefundMoney: 0,
        actualRefundMoney: 0,
      },
      rules: {
        refundCreateTime: [
          { required: true, message: "请选择退费时间", trigger: "change" },
        ],
        // shouldRefundMoney: [{
        //   required: true,
        //   trigger: "blur",
        //   validator: (rule, value, callback) => {
        //     if (value === '' || value === undefined) {
        //       callback(new Error('请输入应退金额'));
        //       return;
        //     }
        //     // if (value >= 10000) {
        //     //   callback(new Error('请正确输入0-9999范围内的正整数'));
        //     //   return;
        //     // }
        //     callback();
        //   }
        // }],
        // actualRefundMoney: [
        //   { required: true, message: "请输入实退金额", trigger: "blur" },
        //   { type: "number", message: "金额必须为数字", trigger: "blur" },
        // ],
      },
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        // 初始化表单，设置默认值
        this.initForm();
      }
    },
  },
  methods: {
    // 初始化表单
    initForm() {
      const now = new Date();
      const formattedDate = `${now.getFullYear()}-${String(
        now.getMonth() + 1
      ).padStart(2, "0")}-${String(now.getDate()).padStart(2, "0")} ${String(
        now.getHours()
      ).padStart(2, "0")}:${String(now.getMinutes()).padStart(2, "0")}:${String(
        now.getSeconds()
      ).padStart(2, "0")}`;

      this.formData = {
        refundCreateTime: formattedDate,
        remark: "",
        reason: "",
        isThreeSeven: "",
        shouldRefundMoney: "",
        actualRefundMoney: "",
      };
    },

    // 关闭弹窗
    handleClose() {
      this.$refs.refundForm.resetFields();
      this.$emit("update:visible", false);
    },

    // 保存退费信息
    handleSave() {
      this.$refs.refundForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 构建保存数据
          const saveData = {
            type: this.orderType(),
            orderId: this.workOrderId,
            ...this.formData,
          };
          console.log("保存退费信息:", saveData);
          this.$post("refundWorkOrder", saveData, { json: true }).then(
            (res) => {
              const { ok, body, msg } = res;
              if (ok) {
                this.loading = false;
                this.$message.success("退费信息保存成功");
                this.$emit("success");
                this.handleClose();
              } else {
                this.$message.error(msg || "退费信息保存失败");
              }
            }
          );
        }
      });
    },
  },
};
</script>

<style scoped>
.el-form {
  padding: 20px 10px;
}
</style>
