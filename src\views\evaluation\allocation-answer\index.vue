<template>
  <common-dialog
    is-full
    title="测评结果配置"
    :visible.sync="visible"
    @open="getAnswerList"
    @close="close"
  >
    <div class="answer">
      <!-- 表单区 -->
      <el-form
        size="mini"
        :model="pagination"
        label-width="120px"
        class="yz-search-form"
        @submit.native.prevent="getAnswerList"
      >
        <el-form-item class="search-item" label="结果名称搜索">
          <el-input v-model="pagination.resultName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item class="search-item" label="结果id">
          <el-input v-model="pagination.id" placeholder="请输入" clearable />
        </el-form-item>
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
      </el-form>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="openResultBtn">新增测评结果</el-button>
      </div>
      <!-- 表格区 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
      >
        <el-table-column label="结果id" prop="id" align="center" />
        <el-table-column label="结果名称" prop="name" align="center" />
        <el-table-column label="测评结果图" prop="resultImg" align="center">
          <template slot-scope="scope">
            <img class="answer-img" :src="scope.row.resultImg" alt="">
          </template>
        </el-table-column>
        <el-table-column label="添加对应标签" prop="tagInfoList" align="center">
          <template slot-scope="scope">
            <ul class="answer-tag">
              <li v-for="item in scope.row.tagInfoList" :key="item.tagId" class="answer-li">{{ item.tagName }}</li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column label="胶囊位配置" prop="capsuleImg" align="center">
          <template slot-scope="scope">
            <img v-if="scope.row.capsuleFlag==1" class="answer-img" :src="scope.row.capsuleImg" alt="">
          </template>
        </el-table-column>
        <el-table-column label="导流模块配置" prop="diversionTitle" align="center" />
        <el-table-column label="创建人" prop="createEmpName" align="center" />
        <el-table-column label="创建时间" prop="createTime" align="center" />
        <el-table-column label="最近修改人" prop="updateEmpName" align="center" />
        <el-table-column label="最近修改时间" prop="updateTime" align="center" />
        <el-table-column label="操作" align="center" width="240">
          <template slot-scope="scope">
            <el-button v-if="scope.row.enableFlag == 0" type="success" size="small" @click="seeDetails({id:scope.row.id,inx:0})">启用</el-button>
            <el-button v-else type="info" size="small" @click="seeDetails({id:scope.row.id,inx:1})">禁用</el-button>
            <el-button type="warning" size="small" @click="openResultBtn({...scope.row,zdyType:1})">修改</el-button>
            <el-button type="primary" size="small" @click="openResultBtn({...scope.row,zdyType:2})">复制配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getAnswerList" />
      </div>
      <!-- 测试结果 -->
      <added-result :visible="showResult" :title="resultTitle" :ids="resultIds" :types="resultType" :resultFlag="resultFlag" @on-close="closeResultBtn" />
    </div>
  </common-dialog>
</template>

<script>
import addedResult from './added-result';
import { getCutDay } from '@/utils';

export default {
  components: { addedResult },
  props: {
    visible: { type: Boolean, default: false },
    isResultAuthority: { type: Boolean, default: false }
  },
  data() {
    return {
      tableData: [],
      resultIds: '',
      resultType: 0,
      resultFlag: 0,
      resultTitle: '',
      tableLoading: false,
      pagination: {
        id: '',
        resultName: '',
        total: 0,
        page: 1,
        rows: 10
      },
      showResult: false
    };
  },
  methods: {
    // 打开新增-修改结果弹窗
    openResultBtn(row) {
      console.log('打开新增-修改结果弹窗', row);
      // 判断是否有操作权限
      if (!this.isResultAuthority) {
        this.$message.error('您当前没有操作权限');
        return false;
      }
      console.log('测评结果配置-openResultBtn', row);
      this.resultIds = row?.id || '';
      this.resultType = row?.zdyType || 0;
      if (this.resultType) {
        this.resultFlag = this.resultType === 1 ? row?.enableFlag || 0 : 0;
        this.resultTitle = this.resultType === 1 ? '修改测评结果' : '复制测评结果';
      } else {
        this.resultFlag = 0;
        this.resultTitle = '新增测评结果';
      }
      this.showResult = true;
    },
    // 关闭新增-修改结果弹窗
    closeResultBtn(type = 0) {
      this.resultIds = '';
      this.resultType = 0;
      this.resultFlag = 0;
      this.resultTitle = '';
      this.showResult = false;
      type && this.getAnswerList();
    },
    // 禁用-启用
    seeDetails({ inx, id }) {
      // 判断是否有操作权限
      if (!this.isResultAuthority) {
        this.$message.error('您当前没有操作权限');
        return false;
      }
      const urs = inx === 1 ? 'resultDisable' : 'resultEnable';
      console.log('禁用-启用', urs);
      this.tableLoading = true;
      this.$post(urs, { id }).then((res) => {
        console.log('禁用-启用-res', res);
        if (res.code === '00') {
          this.$message({ message: '操作成功', type: 'success' });
          this.getAnswerList();
        } else {
          this.$message.error('操作失败');
          if (inx === 1) this.showBoxTips(res.body);
        }
        this.tableLoading = false;
      }).catch(() => {
        this.$message.error('操作失败');
        this.tableLoading = false;
      });
    },
    // 禁用失败的提示
    showBoxTips(arr = []) {
      console.log('禁用失败的提示', arr);
      const h = this.$createElement;
      const news = [];
      for (let i = 0; i < arr.length; i++) {
        news.push(h('li', { style: 'color: teal' }, [h('span', { style: 'font-weight: 700;' }, '· '), h('span', null, arr[i])]));
      }
      console.log('禁用失败的提示----', news);
      this.$msgbox({
        title: '禁用失败',
        message: h('p', null, [...news]),
        showCancelButton: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          done();
        }
      });
    },
    // 接口获取测评结果配置列表
    getAnswerList() {
      this.tableLoading = true;
      this.$post('resultList', this.pagination).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          body.data?.map(item => {
            item.updateTime = item.updateTime && getCutDay(item.updateTime);
            item.createTime = item.createTime && getCutDay(item.createTime);
          });
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
        this.tableLoading = false;
      }).catch(() => {
        this.tableData = [];
        this.tableLoading = false;
      });
    },
    // 关闭当前弹窗
    close() {
      this.$emit('on-close');
    }
  }
};
</script>

<style lang="scss">
.answer {
  margin: 30px;
  height: calc(100% - 60px);
  .search-item {
    margin-right: 40px;
  }
  .answer-img {
    width: 120px;
    height: 60px;
  }
  .answer-tag {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    .answer-li {
      margin: 0 4px 10px;
      padding: 0 4px;
      min-width: 40px;
      height: 20px;
      line-height:  20px;
      text-align: center;
      border: 1px solid #cecece;
      border-radius: 4px;
    }
  }
}
.answer::-webkit-scrollbar{
  display: none;
}
.el-message-box__btns {
  text-align: center;
  .el-button--default {
    width: 30%;
  }
}
</style>
