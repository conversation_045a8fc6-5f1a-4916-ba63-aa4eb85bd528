<template>
  <div class="container">
    <u-editor :width="500" :height="600" @ready="editorReady" />
  </div>
</template>

<script>
import UEditor from '@/components/UEditor';
export default {
  name: 'Demo',
  components: {
    UEditor
  },
  data() {
    return {
      list: []
    };
  },
  created() {
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    editorReady(editor) {},
    success() {},
    remove() {}
  }
};
</script>

<style lang='scss' scoped>

</style>
