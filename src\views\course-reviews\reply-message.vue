<template>
  <common-dialog
    width="500px"
    title="留言回复"
    show-footer
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-input
        v-model="content"
        class="font-14"
        type="textarea"
        rows="16"
        placeholder="请输入内容"
        maxlength="1000"
        show-word-limit
      />
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 留言id
    commentId: {
      type: Number,
      request: true,
      default: undefined
    },
    // 留言用户id
    commentUserId: {
      type: [Number, String],
      request: true,
      default: undefined
    }
  },
  data() {
    return {
      show: false,
      content: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      const data = {
        commentId: this.commentId
      };
      this.$post('getCommentInfo', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.content = body;
        }
      });
    },
    submit() {
      const data = {
        commentId: this.commentId,
        commentUserId: this.commentUserId,
        content: this.content
      };
      this.$post('replyUser', data).then(res => {
        const { fail } = res;
        if (!fail) {
          // this.$parent.getTableList();
          this.$emit('refresh');
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.show = false;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.font-14 {
  font-size: 14px;
}
</style>
