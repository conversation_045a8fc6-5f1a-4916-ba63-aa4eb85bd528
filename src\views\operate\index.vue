<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='创建人' prop='createUserName'>
        <el-input v-model="form.createUserName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='短链名称' prop='title'>
        <el-input v-model="form.title" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='是否启用' prop='state'>
        <el-select v-model="form.state" clearable>
          <el-option label="是" value="1" />
          <el-option label="否" value="2" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" @click="addOperate">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="title" label="短链名称" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column prop="createUserName" label="创建人" align="center" />
      <el-table-column prop="shortUrl" label="短链地址" align="center">
        <template slot-scope="scope">
          <span ref="copyElem" class="sortUrl">{{ scope.row.shortUrl }}</span>
          <el-button type="text" @click="copyUrl(scope.row,$event)">复制</el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="state" label="数据统计" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="statistics(scope.row)">数据统计</el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="state" label="是否启用" align="center">
        <template slot-scope="scope">
          {{ scope.row.state === '1' ? '是':'否' }}
        </template>
      </el-table-column>
      <el-table-column prop="state" label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="editOperate(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 新增编辑 -->
    <add-edit :visible.sync="aeVisible" :row="currentEditRow" />
  </div>
</template>
<script>
import addEdit from './add-edit.vue';
import clipboard from '@/utils/clipboard';
export default {
  components: {
    addEdit
  },
  data() {
    return {
      tableLoading: false,
      aeVisible: false,
      copyText: '',
      currentEditRow: {},
      form: {
        createUserName: '',
        title: '',
        state: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 新增
    addOperate() {
      this.currentEditRow = null;
      this.aeVisible = true;
    },
    editOperate(row) {
      this.currentEditRow = row;
      this.aeVisible = true;
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('shortLinkList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    copyUrl(row, event) {
      this.copyText = row.shortUrl;
      clipboard(this.copyText, event);
    },
    // 数据统计
    statistics() {}
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
.sortUrl{
  margin-right: 10px;
}
.yz-search-form{
  margin-bottom: 20px;
}
</style>
