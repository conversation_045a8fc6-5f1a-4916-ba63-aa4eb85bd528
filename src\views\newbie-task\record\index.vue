<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      @submit.native.prevent="search"
    >
      <el-form-item label="真实姓名" prop="userName">
        <el-input v-model="form.userName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label="远智编号" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" />
      </el-form-item>

      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入" />
      </el-form-item>

      <el-form-item label="是否黑名单" prop="blackStatus">
        <el-select v-model="form.blackStatus" clearable>
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <div class="tip">展示触发【新手任务】对应的用户名单</div>
      <el-button type="success" size="small" @click="exportData">导出数据</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column label="用户信息" align="center" width="150">
        <template slot-scope="{ row }">
          <div style="text-align: left">
            <p>昵称: {{ row.nickName || '无' }}</p>
            <p>远智编号: {{ row.yzCode || '无' }}</p>
            <p>真实姓名: {{ row.realName || '无' }}</p>
            <p>手机号: {{ row.mobile || '无' }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="newbieTaskStartTime" label="新手任务开始时间" align="center" />
      <el-table-column prop="newbieTaskEndTime" label="新手任务结束时间" align="center" />
      <el-table-column prop="getZhimi" label="新手任务奖励智米数" align="center" />
      <el-table-column prop="isBlack" label="是否黑名单" align="center">
        <template slot-scope="{ row }">
          <div class="yz-button-area">
            {{ row.isBlack === 1 ? "是" : "否" }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div class="operate-btn-box">
            <el-button size="small" type="primary" @click="handleTaskDetail(row)">查看任务明细</el-button>
            <el-button size="small" type="primary" @click="handleOperateLog(row)">操作记录</el-button>
            <el-button v-if="row.isBlack === 0" size="small" type="primary" @click="changeStatus(row)">加入黑名单</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 操作记录弹框 -->
    <operate-log-modal :visible.sync="operateLogVisible" :currentRow="currentRow" />
    <!-- 任务明细 -->
    <task-detail :visible.sync="taskDetailVisible" :currentRow="currentRow" />
  </div>
</template>

<script>
import { httpGetDownFile } from '@/utils/downExcelFile';
import operateLogModal from './components/operate-log-modal';
import taskDetail from './components/task-detail.vue';
export default {
  components: { operateLogModal, taskDetail },
  data() {
    return {
      currentRow: {},
      operateLogVisible: false, // 操作记录弹框
      taskDetailVisible: false, // 任务详情弹框
      tableLoading: false,
      form: {
        userName: '',
        yzCode: '',
        mobile: '',
        blackStatus: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 加入黑名单/取消黑名单
    changeStatus(row) {
      const text = `<div><p>是否将用户加入黑名单，加入后：</p><p>1、无法访问【新手任务】页</p><p>2、完成【新手任务】相关任务，将不再赠送智米奖励</p></div>`;
      this.$confirm(text, '请确认', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          id: row.id,
          blackStatus: 1 // 拉黑状态 1: 是 0: 否
        };
        this.$post('newbieTaskUserblack', params, { json: true }).then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$message.success('操作成功');
            this.getTableList();
          }
        });
      }).catch(() => {});
    },
    // 导出数据
    exportData() {
      const params = this.handleQueryParams();
      delete params.pageNum;
      delete params.pageSize;
      const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
      httpGetDownFile({
        url: `/newbietask/user/export?${queryString}`
      });
    },
    // 查看任务明细
    handleTaskDetail(row) {
      this.currentRow = row;
      this.taskDetailVisible = true;
    },
    // 查看操作记录
    handleOperateLog(row) {
      this.currentRow = row;
      this.operateLogVisible = true;
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        pageNum: (this.pagination.page - 1) * this.pagination.limit,
        pageSize: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const data = this.handleQueryParams();
      this.$post('getNewbieTaskUserList', data, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body?.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
  display: flex;
  justify-content: space-between;
  .tip {
    font-size: 14px;
    margin-left: 20px;
    display: flex;
    align-items: center;
    color: #f00;
  }
}
.operate-btn-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  .el-button {
    margin-top: 10px;
    margin-left: 0;
  }
}
</style>
