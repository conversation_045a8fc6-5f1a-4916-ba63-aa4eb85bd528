<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='推荐人远智编码' prop='recommendYzCode'>
        <el-input v-model="form.recommendYzCode" placeholder="请输入推荐人远智编码" />
      </el-form-item>
      <el-form-item label='购买人远智编码' prop='purchaseYzCode'>
        <el-input v-model="form.purchaseYzCode" placeholder="请输入购买人远智编码" />
      </el-form-item>
      <el-form-item v-if="show" label='推荐人' prop='recommendUserName'>
        <el-input v-model="form.recommendUserName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='购买状态' prop='purchaseStatus'>
        <el-select
          v-model="form.purchaseStatus"
          clearable
          placeholder="请选择支付状态"
        >
          <el-option label="未完成" value="0" />
          <el-option label="已购买" value="1" />
          <el-option label="已退款" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='购买渠道' prop='appType'>
        <el-select v-model="form.appType" placeholder="请选择">
          <el-option
            v-for="item in $localDict['platform']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='购买时间' prop='apurchaseTime'>
        <el-date-picker
          v-model="form.apurchaseTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item label='商品类型' prop='tradeCode'>
        <el-select
          v-model="form.tradeCode"
          clearable
          placeholder="请选择商品类型"
        >
          <el-option label="会员卡" value="vip" />
          <el-option label="课程商品" value="course" />
          <el-option label="读书计划" value="readPlan" />
        </el-select>
      </el-form-item>

      <el-form-item label='退款时间' prop='refundTime'>
        <el-date-picker
          v-model="form.refundTime"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="recommendYzCode" label="推荐人远智编码" align="center" />
      <el-table-column prop="recommendUserName" label="推荐人" align="center" />
      <el-table-column prop="purchaseYzCode" label="购买人远智编码" align="center" />
      <el-table-column prop="purchaseUserName" label="购买人" align="center" />
      <el-table-column prop="purchaseUserMobile" label="购买手机号" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.purchaseUserMobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.purchaseUserId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="tradeCode" label="商品类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.tradeCode | tradeCode }}
        </template>
      </el-table-column>
      <el-table-column prop="goodsName" label="购买商品" align="center" />
      <el-table-column prop="purchaseTime" label="购买时间" align="center" />
      <el-table-column prop="purchaseStatus" label="购买状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.purchaseStatus | purchaseStatus }}
        </template>
      </el-table-column>
      <el-table-column prop="refundTime" label="退款时间" align="center" />
      <!-- <el-table-column prop="tradeCode" label="退费状态" align="center" /> -->
      <el-table-column prop="appType" label="购买渠道" align="center">
        <template slot-scope="scope">
          {{ scope.row.appType | appType }}
        </template>
      </el-table-column>

    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
  </div>
</template>
<script>
import { exportExcel, handleDateControl } from '@/utils';
import LookMobile from '@/mixins/LookMobile';

export default {
  filters: {
    tradeCode(val) {
      if (!val) return '';
      const data = {
        'readPlan': '读书计划',
        'course': '课程',
        'vip': '会员卡'
      };
      return data[val];
    },
    purchaseStatus(val) {
      if (!val) return '';
      const data = {
        0: '未完成',
        1: '已购买',
        2: '已退款'
      };
      return data[val];
    },
    appType(val) {
      if (!val) return '';
      const data = {
        'WECHAT': '微信'
      };
      return data[val];
    }
    // platform(value) {
    //   if (!value) return;
    //   const dict = window.parent.dictJson;
    //   if (dict['platform']) {
    //     const data = dict['platform'].find(item => {
    //       return item.dictValue === value;
    //     });
    //     return data.dictName;
    //   }
    //   return value;
    // }
  },
  mixins: [LookMobile],
  props: {
    show: {
      type: Boolean,
      default: true
    },
    targetType: {
      type: String,
      default: null
    },
    targetId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      timer: null,
      form: {
        recommendUserName: '', // 推荐人姓名
        purchaseUserName: '', // 购买人
        purchaseUserMobile: '', // 手机号
        tradeCode: '', // 交易类型
        goodsName: '', // 商品名称
        apurchaseTime: '', // 购买时间
        purchaseStatus: '', // 购买状态
        refundTime: '', // 退款时间
        // purchaseStatus: '', // 退款状态
        appType: '', //	购买渠道
        recommendYzCode: '',
        purchaseYzCode: ''
      },
      tableData: [],
      eventOptions: [],
      eventSelect: {
        page: 1,
        limit: 10,
        total: 0
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      platform: []
    };
  },
  mounted() {
    this.platform = this.$localDict['platform'];
    if (!this.show) {
      this.form.targetType = this.targetType;
      this.form.targetId = this.targetId;
    }
    this.getTableList();
    this.getEventTypeList();
  },
  methods: {
    getEventTypeList(keyword) {
      const data = {
        targetName: keyword,
        page: this.eventSelect.page,
        rows: this.eventSelect.limit
      };
      this.$post('getEventTypeList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.eventOptions = body.data;
          this.eventSelect.total = body.recordsTotal;
        }
      });
    },
    loadmore() {
      if (this.eventOptions.length === this.eventSelect.total) {
        return;
      }
      this.eventSelect.page += 1;
      this.getEventTypeList();
    },
    selectSearch(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.eventSelect.page = 1;
        this.eventOptions = [];
        this.getEventTypeList(value);
      }, 300);
    },
    open() {
      this.eventSelect.page = 1;
      this.eventOptions = [];
      this.getEventTypeList();
    },
    // data参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));

      // const date = handleDateControl(formData.time);
      // console.log(formData);
      // formData.startTime = date[0];
      // formData.endTime = date[1];
      // delete formData.time;
      const apurchaseTime = handleDateControl(formData.apurchaseTime);
      formData.purchaseStartTime = apurchaseTime[0];
      formData.purchaseEndTime = apurchaseTime[1];
      delete formData.apurchaseTime;

      const refundTime = handleDateControl(formData.refundTime);
      formData.refundStartTime = refundTime[0];
      formData.refundEndTime = refundTime[1];
      delete formData.refundTime;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('referExportList', data);
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getReferencesList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
    margin-top:16px;
}
.table-btnbox{
  margin:20px 0 10px 0;
  .left{
    line-height: 33px;
  }
  .align-right{
    text-align:right;
  }
}
.font-14{
  font-size: 14px;
}
</style>
