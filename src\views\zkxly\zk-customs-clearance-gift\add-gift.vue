<template>
  <common-dialog
    show-footer
    width="800px"
    :title="editId ? '编辑通关礼品' : '新增通关礼品'"
    :visible.sync="show"
    @confirm="confirm"
    @close="show = false"
  >
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="formData"
      :rules="rules"
      label-width="150px"
      size="small"
      style="padding-top: 18px"
    >
      <el-form-item v-if="!editId" label="通关奖励：">
        <el-button
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="handleAdd"
        >
          新增奖励
        </el-button>
      </el-form-item>
      <template v-for="(domain, index) in formData.domains">
        <div :key="domain.key">
          <el-divider v-if="formData.domains.length > 1">
            <el-tag closable @close="formData.domains.splice(index, 1)">
              奖励 {{ index + 1 }}
            </el-tag>
          </el-divider>
          <el-form-item
            :label="'礼品类型：'"
            :prop="'domains.' + index + '.giftType'"
            :rules="rules.giftType"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.giftType"
                :disabled="disabled"
                clearable
                placeholder="请选择"
                @change="domain.giftName = ''"
              >
                <el-option
                  v-for="item in $dictJson['trainCampGiftType']"
                  :key="item.dictValue"
                  :label="item.dictName"
                  :value="item.dictValue"
                />
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item
            :label="'礼品名称：'"
            :prop="'domains.' + index + '.giftName'"
            :rules="rules.giftName"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.giftName"
                :disabled="disabled"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in handleGiftName(domain.giftType)"
                  :key="item.dictValue"
                  :label="item.dictName"
                  :value="item.dictValue"
                />
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="
              !(
                domain.giftType == DGiftType.ball &&
                domain.giftName == DGiftName.certificate
              )
            "
            :label="
              domain.giftType == DGiftType.article ? '礼品链接：' : '链接：'
            "
            :prop="'domains.' + index + '.giftJumpRoute'"
            :rules="rules.giftJumpRoute"
          >
            <el-col :span="10">
              <el-input
                v-model="domain.giftJumpRoute"
                placeholder="请输入"
                clearable
              />
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="domain.giftType == DGiftType.article"
            :label="'兑换形式：'"
            :prop="'domains.' + index + '.giftExchangeWay'"
            :rules="rules.giftExchangeWay"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.giftExchangeWay"
                :disabled="disabled"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in $dictJson['giftType']"
                  :key="item.dictValue"
                  :label="item.dictName"
                  :value="item.dictValue"
                />
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="domain.giftType == DGiftType.article"
            :key="domain.key"
            :label="'赠送智米数量：'"
            :prop="'domains.' + index + '.zhiMiNum'"
            :rules="rules.zhiMiNum"
          >
            <el-col :span="10">
              <el-input-number
                v-model="domain.zhiMiNum"
                :disabled="disabled"
                :min="1"
                :max="19999"
                :precision="0"
                style="width: 100%"
                placeholder="请输入"
              />
            </el-col>
          </el-form-item>
        </div>
      </template>
    </el-form>
  </common-dialog>
</template>

<script>
const rules = {
  giftType: [{ required: true, message: '请输入礼品类型：' }],
  giftName: [{ required: true, message: '请选择礼品名称：', trigger: 'blur' }],
  giftJumpRoute: [{ required: true, message: '请输入链接：' }],
  giftExchangeWay: [{ required: true, message: '请选择兑换形式：' }],
  zhiMiNum: [
    { required: true, message: '请输入赠送智米数量：', trigger: 'blur' }
  ]
};

// 礼品类型
const DGiftType = {
  article: 1, // 实物礼品
  virtual: 2, // 虚拟礼品
  ball: 3 // 弹窗礼品
};

// 礼品名称
const DGiftName = {
  certificate: 1 // 结营证书
};

const DGiftItem = {
  giftType: '',
  giftName: '',
  giftJumpRoute: '',
  giftExchangeWay: '',
  zhiMiNum: undefined
};

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      rules,
      DGiftType,
      DGiftName,
      formData: { domains: [{ ...DGiftItem }] },
      loading: false,
      disabled: false
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit('update:visible', v);
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        if (this.editId) {
          this.getDetail();
        }
      } else {
        this.disabled = false;
        this.$refs.formRef.resetFields();
        this.formData.domains = [{ ...DGiftItem }];
      }
    }
  },
  methods: {
    // 新增奖励
    handleAdd() {
      this.formData.domains.push({ ...DGiftItem });
    },
    // 获取详情
    getDetail() {
      this.loading = true;
      this.$post('selGift', { giftId: this.editId })
        .then((res) => {
          if (res.ok) {
            res.body.giftType = String(res.body.giftType);
            res.body.giftName = String(res.body.giftName);
            res.body.giftExchangeWay = String(res.body.giftExchangeWay);
            this.formData.domains = [res.body];
            this.disabled = res.body.receivePersonNum > 0;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    confirm() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          const data = this.formData.domains.map((v) => {
            return {
              ...v,
              giftId: this.editId,
              trainCampId: this.$route.query.id,
              zhiMiNum: v.giftType * 1 !== DGiftType.article ? '' : v.zhiMiNum,
              giftExchangeWay:
                v.giftType * 1 !== DGiftType.article ? '' : v.giftExchangeWay,
              giftJumpRoute: !(
                v.giftType * 1 === DGiftType.ball &&
                v.giftName * 1 === DGiftName.certificate
              )
                ? v.giftJumpRoute
                : ''
            };
          });
          this.$post(
            this.editId ? 'editGift' : 'addGift',
            this.editId ? data[0] : data,
            { json: !this.editId }
          ).then((res) => {
            if (res.ok) {
              this.$message.success(this.editId ? '编辑成功' : '新增成功');
              this.$emit('refresh');
              this.show = false;
            }
          });
        } else {
          this.$message.warning('请检查填写项');
        }
      });
    },
    // 礼品名称
    handleGiftName(giftType) {
      switch (giftType * 1) {
        case DGiftType.article: // 实物礼品
          return this.$dictJson['trainCampArticleGiftName'];
        case DGiftType.virtual: // 虚拟礼品
          return this.$dictJson['trainCampVirtualGiftName'];
        case DGiftType.ball: // 弹窗礼品
          return this.$dictJson['trainCampBallGiftName'];
        default:
          return [];
      }
    }
  }
};
</script>
