<template>
  <div>
    <!-- 表单 -->
    <open-packup>
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='150px'
        @submit.native.prevent='search'
      >
        <el-form-item label='商品名称' prop='distributionGoodsName'>
          <el-input v-model="form.distributionGoodsName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label='累计分销订单数排序' prop='distributionOrderSort'>
          <el-select v-model="form.distributionOrderSort" clearable>
            <el-option label="从高到低" value="desc" />
            <el-option label="从低到高" value="asc" />
          </el-select>
        </el-form-item>

        <el-form-item label='累计分销金额排序' prop='distributionAmountSort'>
          <el-select v-model="form.distributionAmountSort" clearable>
            <el-option label="从高到低" value="desc" />
            <el-option label="从低到高" value="asc" />
          </el-select>
        </el-form-item>

        <el-form-item label='累计返利金额排序' prop='distributionSettledAmountSort'>
          <el-select v-model="form.distributionSettledAmountSort" clearable>
            <el-option label="从高到低" value="desc" />
            <el-option label="从低到高" value="asc" />
          </el-select>
        </el-form-item>

        <el-form-item label='催促素材人数排序' prop='userUrgedCountSort'>
          <el-select v-model="form.userUrgedCountSort" clearable>
            <el-option label="从高到低" value="desc" />
            <el-option label="从低到高" value="asc" />
          </el-select>
        </el-form-item>

        <el-form-item label='是否分佣' prop='divideStatus'>
          <el-select v-model="form.divideStatus" clearable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label='状态' prop='enable'>
          <el-select v-model="form.enable" clearable>
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="2" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>
    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain @click="manager">官方运营人员管理</el-button>
      <el-button type="primary" size="small" plain @click="dataStatis('')">分销统计</el-button>
      <el-button type="primary" size="small" @click="addSale">添加分销商品</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="distributionGoodName" label="商品" align="center" />
      <el-table-column prop="sellPrice" label="售价" align="center" />
      <el-table-column prop="generalProportion" label="普通用户佣金" align="center">
        <template slot-scope="scope">
          {{ scope.row.generalProportion }}%
        </template>
      </el-table-column>
      <el-table-column prop="memProportion" label="会员用户佣金" align="center">
        <template slot-scope="scope">
          {{ scope.row.memProportion }}%
        </template>
      </el-table-column>
      <el-table-column prop="divideStatus" label="是否分佣" align="center">
        <template slot-scope="scope">
          {{ scope.row.divideStatus === 0 ? '否' : '是' }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="cycleMonth" label="结算周期" align="center" /> -->
      <el-table-column prop="distributionTotalOrder" label="累计分销订单" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="toSaleOrder(scope.row)">{{ scope.row.distributionTotalOrder || 0 }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="distributionTotalAmount" label="累计分销金额" align="center" />
      <el-table-column prop="distributionSettledAmount" label="累计结算佣金" align="center" />
      <el-table-column prop="material" label="素材管理" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="editMaterial(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column label="催促素材上架人数" prop="userUrgedCount" align="center" />
      <el-table-column prop="material" label="数据统计" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="dataStatis(scope.row)">数据统计</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.enable=='1'?'success':'danger'" size="mini" plain>
            {{ scope.row.enable=='1'?'已启用':"已禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" prop="enable">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="editSale(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 数据统计 弹窗 -->
    <data-statistics :row='currentEditRow' :visible.sync="dsVisible" @refresh-list="getTableList" />
    <!-- 新增编辑 -->
    <add-edit :visible.sync="aeVisible" :row="currentEditRow" />
    <!-- 素材管理 -->
    <material :visible.sync="materVisible" :row="currentEditRow" />
    <share-material-dialog :visible.sync="smDialogShow" :row-data="currentEditRow" />
    <manager-dialog :visible.sync="managerVisible" :row-data="currentEditRow" />
  </div>
</template>
<script>
import { exportExcel } from '@/utils';
import dataStatistics from './data-statistics';
import addEdit from './add-edit';
import material from './material';
import shareMaterialDialog from './share-material-dialog';
import managerDialog from './manager-dialog';

export default {
  components: {
    dataStatistics,
    material,
    addEdit,
    shareMaterialDialog,
    managerDialog
  },
  data() {
    return {
      smDialogShow: false,
      tableLoading: false,
      dsVisible: false,
      aeVisible: false,
      materVisible: false,
      managerVisible: false,
      currentEditRow: {},
      currentDsId: '',
      form: {
        distributionGoodsName: '',
        enable: '',
        distributionOrderSort: '', // 订单金额
        distributionAmountSort: '', // 分销金额
        distributionSettledAmountSort: '', // 结算金额
        userUrgedCountSort: '',
        divideStatus: '' // 是否分佣
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    toSaleOrder(row) {
      this.$emit('orderDetails', row.distributionGoodName);
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    // 导出函数
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('', data);
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('distributionList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 新增训练营
    addSale() {
      this.currentEditRow = null;
      this.aeVisible = true;
    },
    editSale(row) {
      this.currentEditRow = row;
      this.aeVisible = true;
    },
    editMaterial(row) {
      this.currentEditRow = row;
      // this.materVisible = true;
      this.smDialogShow = true;
    },
    // 数据统计
    dataStatis(row) {
      if (row) {
        this.currentEditRow = row;
      } else {
        this.currentEditRow = {};
      }
      this.dsVisible = true;
    },
    manager() {
      this.managerVisible = !this.managerVisible;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
</style>
