<template>
  <common-dialog is-full title="上课情况" :visible.sync='show' @open="open" @close='close'>
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='学员名称' prop='realName'>
          <el-input v-model="form.realName" />
        </el-form-item>

        <el-form-item label='手机号码' prop='mobile'>
          <el-input v-model="form.mobile" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='table-btnbox'>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="left">
              <span>本次课程学员：{{ tatolCount }}，实际上课学员：{{ classCount }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="align-right">
              <el-button type="success" size="small" icon="el-icon-upload2" @click="exportExcel">
                导出上课学员考勤情况
              </el-button>
              <a v-show="false" ref="exoprt" href="" />
            </div>
          </el-col>
        </el-row>

      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 225px)"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="courseName" label="课程名称" align="center" />
        <el-table-column prop="courseType" label="上课方式" align="center">
          <template slot-scope="scope">
            {{ scope.row.courseType | courseType }}
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="上课学员名称" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="appType" label="学习渠道" align="center">
          <template slot-scope="scope">
            {{ scope.row.appType | appType }}
          </template>
        </el-table-column>
        <el-table-column prop="onlineDuration" label="在线时长" align="center">
          <template slot-scope="scope">
            {{ scope.row.onlineDuration | formatHour }} &nbsp;&nbsp; 小时
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>
<script>
import { pagination } from '@/config/constant';
import { SplicingParams } from '@/utils';
import { downUri } from '@/config/request';
import { api } from '@/api';
export default {
  filters: {
    courseType(val) {
      const stringVal = String(val);
      if (!stringVal) return '';
      const data = {
        '0': '直播',
        '1': '录播',
        '2': '硬盘推流'
      };
      return data[stringVal];
    },
    appType(val) {
      const stringVal = String(val);
      if (!stringVal) return '';
      const data = {
        '1': 'web',
        '2': '微信',
        '3': '安卓',
        '4': 'ios'
      };
      return data[stringVal];
    },
    formatHour(second) {
      const hour = second % (24 * 3600) / 3600;
      return hour.toFixed(2);
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    // 课时id
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      form: {
        realName: '',
        mobile: ''
      },
      tatolCount: 0,
      classCount: 0, // 实际上课人数
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10,
        ...pagination
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    exportExcel() {
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        courseTimeId: this.id
      };
      const url = downUri + api['exportSignDetail'] + '?' + SplicingParams(data);
      window.open(url, '_blank');
    },
    open() {
      this.getTableList();
      this.getCount();
    },
    async getCount() {
      const data = {
        courseTimeId: this.id,
        tenantType: 1 // 租户类型 1->上进大学 (必须)
      };
      const { fail, body } = await this.$post('getWayOfClassCount', data);
      if (!fail) {
        this.tatolCount = body.tatolCount;
        this.classCount = body.classCount;
      }
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        courseTimeId: this.id
      };
      this.$post('getWayOfClassList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-main{
  .yz-table-btnbox{
    padding-top:20px;
  }
  .table-btnbox{
      margin:20px 0 10px 0;
      .left{
        line-height: 33px;
      }
      .align-right{
        text-align:right;
      }
    }
}

</style>
