<template>
  <el-dialog
    :top="top"
    destroy-on-close
    :append-to-body="true"
    v-bind="$attrs"
    :fullscreen="fullscreen"
    :custom-class="customClass"
    :show-close="false"
    :visible.sync="show"
    :width="width"
    :close-on-click-modal="false"
    v-on="$listeners"
    @open.native="_open"
    @close.native="close"
  >
    <div
      :ref="containerId"
      class="yz-common-dialog__container"
      :class="{ small: isSmall }"
    >
      <div class="yz-common-dialog__header">
        <span class="title">{{ title }}</span>
        <span class="icons">
          <i
            class="fa fa-lg fa-fw"
            :class="[smallIcons]"
            @click="changeSmall"
          />
          <i class="fa fa-fw fa-lg" :class="[fullIcons]" @click="changeFull" />
          <i class="fa fa-close fa-fw fa-lg" @click="_close" />
        </span>
      </div>
      <div class="yz-common-dialog__content" :class="[contentFull]">
        <slot />
      </div>
      <div v-if="showFooter" class="yz-common-dialog__footer">
        <slot name="footer">
          <el-button size="small" @click="cancel">{{ cancelText }}</el-button>
          <el-button
            :disabled="confirmLoading"
            :loading="confirmLoading"
            size="small"
            type="primary"
            @click="confirm"
            >{{ confirmText }}</el-button
          >
        </slot>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "CommonDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "50%",
    },
    showFooter: {
      type: Boolean,
      default: false,
    },
    confirmText: {
      type: String,
      default: "确 定",
    },
    cancelText: {
      type: String,
      default: "取 消",
    },
    isFull: {
      type: Boolean,
      default: false,
    },
    top: {
      type: String,
      default: "0px",
    },
    // 确定按钮的加载中状态, 防止再次点击
    confirmLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      fullscreen: false,
      isSmall: false,
    };
  },
  computed: {
    customClass() {
      return this.fullscreen ? "yz-common-dialog full" : "yz-common-dialog";
    },
    containerId() {
      return `yz-dialog-container__${Date.now()}`;
    },
    smallIcons() {
      return this.isSmall ? "fa-expand" : "fa-compress";
    },
    fullIcons() {
      return this.fullscreen ? "fa-window-restore" : "fa-window-maximize";
    },
    contentFull() {
      let contentClass = "";
      if (this.fullscreen) {
        if (this.showFooter) {
          contentClass = this.isOnly
            ? "fullWithOnlyAndFooter"
            : "fullWithFooter";
        } else {
          contentClass = this.isOnly ? "fullOnly" : "full";
        }
      }

      return contentClass;
    },
    isOnly() {
      const only = this.$route.query.only;
      return only && Number(only) === 1;
    },
  },
  watch: {
    visible(val) {
      this.show = val;
    },
  },
  mounted() {
    this.show = this.visible;
    this.fullscreen = this.isFull;
  },

  methods: {
    changeSmall() {
      this.isSmall = !this.isSmall;
      this.fullscreen = false;
    },
    changeFull() {
      this.fullscreen = !this.fullscreen;
      this.isSmall = false;
    },
    _open() {
      console.log("打开了");
      this.$emit("open");
    },
    _close() {
      this.$emit("close");
    },
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    cancel() {
      this.show = false;
      this.$emit("cancel");
    },
    confirm() {
      if (this.confirmLoading) {
        return;
      }
      this.$emit("confirm");
    },
  },
};
</script>

<style>
.el-dialog__wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

<style lang="scss">
.yz-common-dialog.el-dialog {
  border-radius: 10px;
  //position: absolute;
  //left: 50%;
  //top: 50%;
  //transform: translate(-50%, -50%);
  //width: 100%;
  //height: max-content;
  &.full {
    border-radius: 0;
  }
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
</style>

<style lang="scss" scoped>
.yz-common-dialog__container {
  &.small {
    max-height: 47px;
    overflow: hidden;
  }
  // &.only{
  //   padding-bottom: 30px;
  // }
}
.yz-common-dialog__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  min-height: 47px;
  border-bottom: 1px solid #ddd;
  .title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
  .icons {
    font-weight: 600;
    i {
      cursor: pointer;
      margin-left: 8px;
      &:hover {
        color: #409eff;
      }
      &:active {
        opacity: 0.5;
      }
    }
    i.fa-close {
      font-size: 22px;
      padding-top: 2px;
      &:hover {
        color: red;
      }
    }
  }
}
.yz-common-dialog__content {
  position: relative;
  max-height: 80vh;
  overflow: auto;
  &.full {
    // max-height: calc(100vh - 47px);
    max-height: none;
    height: calc(100vh - 48px);
  }
  &.fullOnly {
    // max-height: calc(100vh - 47px);
    max-height: none;
    height: calc(100vh - 78px);
  }
  &.fullWithFooter {
    // max-height: calc(100vh - 104px); // header+footer = 47px + 57px = 104px
    max-height: none;
    height: calc(100vh - 104px);
  }
  &.fullWithOnlyAndFooter {
    // max-height: calc(100vh - 104px); // header+footer = 47px + 57px = 104px
    max-height: none;
    height: calc(100vh - 134px);
  }
}
.yz-common-dialog__footer {
  padding: 12px;
  border-top: 1px solid #ddd;
  text-align: right;
}
</style>
