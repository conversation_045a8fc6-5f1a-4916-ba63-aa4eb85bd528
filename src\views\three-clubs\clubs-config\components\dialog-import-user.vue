<template>
  <common-dialog
    show-footer
    class="common-dialog"
    title="导入人员"
    :visible.sync="show"
    @close="close"
    @confirm="submit"
  >
    <!-- 表单 -->
    <el-form size="mini" label-width="100px" class="yz-search-form" style="padding: 20px">
      <el-form-item label="模板：" style="width: 100%">
        <a :href="templateUrl" download>
          <el-button icon="el-icon-download" type="primary" plain>
            下载模板
          </el-button>
        </a>

        <div>
          说明：
          <span style="color: red">
            请严格按模板填写，否则可能导致无法准确导入。
          </span>
        </div>
      </el-form-item>
      <el-form-item label="选择文件：" style="width: 100%">
        <el-upload
          ref="upload"
          drag
          :action="action"
          accept=".xlsx,.xls,.csv"
          :file-list="fileList"
          :on-change="handleChange"
          :on-success="uploadSuccess"
          :limit="1"
          :auto-upload="false"
        >
          <i class="el-icon-upload" />
          <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
        </el-upload>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      fileList: [],
      field: 'multipartFile',
      templateUrl: 'https://yzpres.oss-cn-guangzhou.aliyuncs.com/club/%E5%B0%8F%E7%A4%BE%E5%9B%A2%E5%AF%BC%E5%85%A5%E6%88%90%E5%91%98%E6%A8%A1%E6%9D%BF.xlsx'
    };
  },
  computed: {
    action() {
      return `/clubsActivityInfo/importClubActMember?clubActId=${this.row?.id}`;
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
      },
      immediate: true
    }
  },
  methods: {
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    uploadSuccess(response) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        this.$message.success('导入成功');
        this.show = false;
        this.$emit('refresh');
      } else {
        if (response.msg) {
          this.$message.error(response.msg);
        }
      }
    },
    submit() {
      if (this.fileList.length > 0) {
        this.$refs.upload.submit();
      } else {
        this.$message.warning('请选择需要导入的文件');
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>
