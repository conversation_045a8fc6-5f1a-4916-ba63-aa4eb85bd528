<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='单据号' prop='payNo'>
        <el-input v-model="form.payNo" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='子单据号' prop='orderDetailedNo'>
        <el-input v-model="form.orderDetailedNo" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='用户姓名' prop='userName'>
        <el-input v-model="form.userName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='手机号' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='收款方式' prop='paymentType'>
        <el-select
          v-model="form.paymentType"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="item in $dictJson['paymentType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='缴费时间' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="userName" label="用户" align="center" />
      <el-table-column label="手机号" align="center" prop="mobile" width="150">
        <template slot-scope="scope">
          <div>{{ scope.row.mobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="payAmount" label="缴费金额（元）" align="center" />
      <el-table-column prop="zmScale" label="智米抵扣（元）" align="center" />
      <el-table-column prop="demurrageScale" label="滞留金抵扣（元）" align="center" />
      <el-table-column prop="payTime" label="缴费日期" align="center" width="150" />
      <el-table-column prop="payNO" label="单据号" align="center" width="300" />
      <el-table-column prop="outNo" label="第三方订单号" align="center" width="300" />
      <el-table-column prop="paymentType" label="收款方式" align="center">
        <template slot-scope="scope">
          {{ scope.row.paymentType | tansformPayMethod }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="seeDetails(scope.row)">查看子订单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <sub-order
      :visible.sync="soVisible"
      :pay-no="subPayNo"
      :user-name="userName"
    />

  </div>
</template>

<script>
import { exportExcel, handleDateControl } from '@/utils';
import subOrder from './sub-order';
import LookMobile from '@/mixins/LookMobile';

export default {
  components: {
    subOrder
  },
  mixins: [LookMobile],
  data() {
    return {
      form: {
        userName: '',
        mobile: '',
        payNo: '',
        orderDetailedNo: '',
        time: ''
      },
      soVisible: false,
      subPayNo: null,
      userName: null,
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 查看子订单详情
    seeDetails(row) {
      this.subPayNo = row.payNO;
      this.userName = row.userName;
      this.soVisible = true;
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 导出函数
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportYzNewOrder', data);
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const time = handleDateControl(formData.time);
      formData.payTimeStart = time[0];
      formData.payTimeEnd = time[1];
      delete formData.time;
      const params = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return params;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;

      const params = this.handleQueryParams();

      this.$post('getCombinedPayOrder', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
        });
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form {
  margin-bottom: 10px;
}
</style>
