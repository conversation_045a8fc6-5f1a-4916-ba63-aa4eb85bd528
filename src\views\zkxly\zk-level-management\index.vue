<template>
  <div>
    <div class="back">
      <span style="font-size: 12px">关卡管理</span>
      <i class="el-icon-close" @click="$router.go(-1)" />
    </div>
    <div class="yz-base-container">
      <div style="text-align: right; margin-bottom: 10px">
        <el-button
          v-if="!isStart"
          type="primary"
          size="small"
          @click="handleLevel()"
        >
          新增关卡
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        size="small"
      >
        <el-table-column
          prop="barrierSerialNum"
          align="center"
          label="关卡序号"
        />
        <el-table-column prop="barrierName" label="关卡名称" align="center" />
        <el-table-column
          prop="barrierContent"
          label="关卡内容"
          align="center"
        />
        <el-table-column prop="barrierRule" label="关卡规则" align="center" />
        <el-table-column prop="barrierGuide" label="关卡指引" align="center">
          <template slot-scope="scope">
            <img
              v-if="scope.row.barrierGuide"
              :src="scope.row.barrierGuide"
              style="width: 70px; height: 50px"
            >
            <p v-else>无</p>
          </template>
        </el-table-column>
        <el-table-column label="关卡解锁模式" align="center">
          <template slot-scope="scope">
            {{ scope.row.levelMode == 1 ? "立即解锁 " : "按日解锁" }}
          </template>
        </el-table-column>
        <el-table-column width="150" label="是否启用" align="center">
          <template slot-scope="{ row }">
            <el-tag
              v-if="row.isAllow == 0"
              type="danger"
              effect="plain"
              size="small"
            >
              禁用
            </el-tag>
            <el-tag v-else type="success" effect="plain" size="small">
              启用
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="180" label="操作" align="center">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <template v-if="scope.row.isAllow != 0 && !isStart">
                <el-popconfirm
                  title="是否上移?"
                  cancel-button-type="button"
                  @confirm="editBarrierSort(scope.row, 1)"
                >
                  <el-button
                    v-if="scope.$index !== 0"
                    slot="reference"
                    icon="el-icon-top"
                    class="mr-10"
                    circle
                  />
                </el-popconfirm>
                <el-popconfirm
                  title="是否下移?"
                  cancel-button-type="button"
                  @confirm="editBarrierSort(scope.row, 2)"
                >
                  <el-button
                    v-if="scope.$index !== indexNum - 1"
                    slot="reference"
                    icon="el-icon-bottom"
                    class="mr-10"
                    circle
                  />
                </el-popconfirm>
              </template>
              <el-popconfirm
                v-if="!isStart"
                :title="scope.row.isAllow == 0 ? '是否启用?' : '是否禁用?'"
                cancel-button-type="button"
                @confirm="handleStatus(scope.row)"
              >
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  class="mr-10"
                >{{ scope.row.isAllow == 0 ? "启用" : "禁用" }}
                </el-button>
              </el-popconfirm>
              <el-button
                type="text"
                size="small"
                @click="handleLevel(scope.row.id)"
              >
                编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getBarrierList"
        />
      </div>
      <!-- 详情弹窗 -->
      <addLevel
        :visible.sync="addLevelShow"
        :editId="editId"
        @refresh="getBarrierList"
      />
    </div>
  </div>
</template>

<script>
import addLevel from './add-level';
export default {
  components: { addLevel },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      btnShow: false,
      addLevelShow: false,
      editId: '',
      info: {},
      infos: {},
      indexNum: null
    };
  },
  computed: {
    // 是否已经有人闯关
    isStart() {
      return this.$route.query.on;
    }
  },
  created() {
    this.getBarrierList();
  },
  methods: {
    // 禁用 | 启用
    handleStatus(row) {
      this.$http({
        method: 'post',
        url: `/trainCampBarrier/updateBarrierStatus/${row.id}/${
          row.isAllow === 1 ? 0 : 1
        }`
      }).then((res) => {
        if (res.ok) {
          this.$message.success(row.isAllow === 1 ? '禁用成功' : '启用成功');
          this.getBarrierList();
        }
      });
    },
    // 新增 | 编辑
    handleLevel(id = '') {
      this.editId = String(id);
      this.addLevelShow = true;
    },
    getBarrierList() {
      this.loading = true;
      const data = {
        ...this.form,
        campId: this.$route.query.id,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };

      this.$post('barrierList', data, { json: true })
        .then((res) => {
          if (res.ok) {
            this.tableData = res.body.data;
            this.pagination.total = res.body.recordsTotal;
            const obj = [];

            this.tableData.forEach((item) => {
              if (item.isAllow === 1) {
                obj.push(item);
                this.indexNum = obj.length;
              }
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    editBarrierSort(row, num) {
      this.tableData.forEach((item) => {
        if (num === 1) {
          if (item.barrierSerialNum === row.barrierSerialNum - 1) {
            this.info = item;
          }
        }
        if (num === 2) {
          if (item.barrierSerialNum === row.barrierSerialNum + 1) {
            this.infos = item;
          }
        }
      });
      let data = {};
      if (num === 1) {
        data = {
          campId: this.$route.query.id,
          barrierSortDtoList: [
            { id: row.id, barrierSerialNum: row.barrierSerialNum - 1 },
            {
              id: this.info.id,
              barrierSerialNum: this.info.barrierSerialNum + 1
            }
          ]
        };
      } else {
        data = {
          campId: this.$route.query.id,
          barrierSortDtoList: [
            { id: row.id, barrierSerialNum: row.barrierSerialNum + 1 },
            {
              id: this.infos.id,
              barrierSerialNum: this.infos.barrierSerialNum - 1
            }
          ]
        };
      }

      this.$post('editBarrierSort', data, { json: true }).then((res) => {
        if (res.ok) {
          this.$message.success('编辑顺序成功');
          this.getBarrierList();
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.icon {
  font-size: 18px;
  cursor: pointer;
}
.icon:hover {
  color: #409eff;
}
.mr-10 {
  margin-right: 10px;
}
.back {
  margin: 7px 15px 0 10px;
  color: #808c95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
}
.textGreen {
  display: inline-block;
  padding: 5px 15px;
  font-size: 12px;
  border-radius: 5px;
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}
.textRen {
  display: inline-block;
  padding: 5px 15px;
  font-size: 12px;
  border-radius: 5px;
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
</style>
