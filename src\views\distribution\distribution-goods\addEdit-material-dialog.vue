<template>
  <common-dialog
    show-footer
    :title="rowData ?'编辑': '新增'"
    width="800px"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='素材名称' prop='materialName'>
          <el-input
            v-model="form.materialName"
            placeholder="请输入素材名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='素材文案' prop='materialText'>
          <el-input
            v-model="form.materialText"
            type="textarea"
            :rows="6"
            placeholder="请输入内容"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='海报素材'>
          <upload-file
            :max-limit="3"
            exts='jpg|png'
            :file-list='fileList'
            @remove="handledFileRemove"
            @success="handledFileSuccess"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='state'>
          <el-radio-group v-model="form.state">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    },
    // 商品编码
    goodCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      fileList: [],
      form: {
        distributionNumber: '',
        materialName: '',
        materialText: '',
        state: '1',
        urlsFile: []
      },
      rules: {
        materialName: [
          { required: true, message: '请选择', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handledFileSuccess({ response, file, fileList }) {
      file.isAdd = 1;
      this.fileList = fileList;
    },
    handledFileRemove({ file, fileList }) {
      this.fileList = fileList;
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.fileList.length === 0 && this.form.materialText.trim() === '') {
            this.$message.error('素材文案和海报素材必须填写其中之一');
            return;
          }

          let apiKey = 'addGoodsShareMaterial';
          const params = { ...this.form };
          const files = this.fileList.map(item => {
            return {
              isAdd: item.isAdd || 0,
              fileUrl: item.response
            };
          });
          params.distributionNumber = this.goodCode;
          params.urlsFile = files;

          if (this.rowData) {
            apiKey = 'editGoodsShareMaterial';
            params.materialId = this.rowData.materialId;
          }

          this.$post(apiKey, params, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$emit('refreshList');
            }
          });
        }
      });
    },
    open() {
      if (this.rowData) {
        const params = { materialId: this.rowData.materialId };
        this.$post('getGoodsShareMaterialDetails', params, { json: true }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.materialName = body.materialName;
            this.form.materialText = body.materialText;
            this.form.state = body.state;
            if (Array.isArray(body.urlsList)) {
              body.urlsList.forEach(url => {
                this.fileList.push({
                  isAdd: 0,
                  response: url,
                  url: ossUri + url
                });
              });
            }
          }
        });
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">

</style>
