<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item v-if="show" label='推荐人' prop='targetReamrk'>
        <el-input v-model="form.targetReamrk" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='助学老师' prop='platform'>
        <el-select v-model="form.platform" clearable>
          <el-option
            v-for="item in $dictJson['platform']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='部门' prop='platform'>
        <el-select v-model="form.platform" clearable>
          <el-option
            v-for="item in $dictJson['platform']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="platform" label="身份" align="center">
        <template slot-scope="scope">
          {{ scope.row.platform | platform }}
        </template>
      </el-table-column>
      <el-table-column prop="targetName" label="推荐人" align="center" />
      <el-table-column prop="targetReamrk" label="部门" align="center" />
      <el-table-column prop="pv" label="推荐商品次数" align="center" />
      <el-table-column prop="uv" label="成交单数" align="center" />
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
  </div>
</template>
<script>
import { handleDateControl } from '@/utils';
export default {
  filters: {
    platform(value) {
      if (!value) return;
      const dict = window.parent.dictJson;
      if (dict['platform']) {
        const data = dict['platform'].find(item => {
          return item.dictValue === value;
        });
        return data.dictName;
      }
      return value;
    }
  },
  props: {
    show: {
      type: Boolean,
      default: true
    },
    targetType: {
      type: String,
      default: null
    },
    targetId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      timer: null,
      form: {
        platform: '', // 发布平台  平台（Android，IOS, WECHAT）安卓，苹果，微信公众号
        targetType: '', // 事件类型
        targetId: '', // 业务ID
        targetReamrk: '', // 事件名称
        type: 'day', // 统计类型 默认带 "day"
        time: ''
      },
      tableData: [],
      eventOptions: [],
      eventSelect: {
        page: 1,
        limit: 10,
        total: 0
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      platform: []
    };
  },
  mounted() {
    this.platform = this.$localDict['platform'];
    if (!this.show) {
      this.form.targetType = this.targetType;
      this.form.targetId = this.targetId;
    }
    this.getTableList();
    this.getEventTypeList();
  },
  methods: {
    getEventTypeList(keyword) {
      const data = {
        targetName: keyword,
        page: this.eventSelect.page,
        rows: this.eventSelect.limit
      };
      this.$post('getEventTypeList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.eventOptions = body.data;
          this.eventSelect.total = body.recordsTotal;
        }
      });
    },
    loadmore() {
      if (this.eventOptions.length === this.eventSelect.total) {
        return;
      }
      this.eventSelect.page += 1;
      this.getEventTypeList();
    },
    selectSearch(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.eventSelect.page = 1;
        this.eventOptions = [];
        this.getEventTypeList(value);
      }, 300);
    },
    open() {
      this.eventSelect.page = 1;
      this.eventOptions = [];
      this.getEventTypeList();
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.startTime = date[0];
      formData.endTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getEventCountList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form{
    margin: 30px 0;
}
.yz-table-btnbox{
    margin-top:16px;
}
.table-btnbox{
  margin:20px 0 10px 0;
  .left{
    line-height: 33px;
  }
  .align-right{
    text-align:right;
  }
}
.font-14{
  font-size: 14px;
}
</style>
