
/* eslint-disable no-undef */
import { MessageBox, Message } from 'element-ui';

/**
 * 现代复制函数 - 支持多种兼容性方案
 * @param {string} text - 要复制的文本
 * @param {string} prompt - 复制成功后的提示文字
 */
export const copyToClipboard = async(text, prompt = '复制文本成功') => {
  if (!text) {
    Message.error('复制内容为空');
    return Promise.reject('复制内容不能为空');
  }

  try {
    if (navigator.clipboard) {
      return navigator.clipboard
        .writeText(text)
        .then(() => {
          prompt && Message.success(prompt);
        })
        .catch(error => {
          MessageBox.alert('复制失败!请手动复制：' + text);
          return error;
        });
    }
    if (Reflect.has(document, 'execCommand')) {
      return new Promise((resolve, reject) => {
        try {
          const textArea = document.createElement('textarea');
          textArea.value = text;
          // 在手机 Safari 浏览器中，点击复制按钮，整个页面会跳动一下
          textArea.style.width = '0';
          textArea.style.position = 'fixed';
          textArea.style.left = '-999px';
          textArea.style.top = '10px';
          textArea.setAttribute('readonly', 'readonly');
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);

          prompt && Message.success(prompt);
          resolve();
        } catch (error) {
          MessageBox.alert('复制失败!请手动复制：' + text);
          reject(error);
        }
      });
    }
    return Promise.reject(`"navigator.clipboard" 或 "document.execCommand" 中存在API错误, 拷贝失败!`);
  } catch (error) {
    console.error('复制失败:', error);
    MessageBox.alert('复制失败!请手动复制：' + text);
    return false;
  }
};
/* eslint-enable no-undef */

/**
 *  用async await 来达到延迟效果
 * @param { Number } millisecond
 */
export const sleep = (millisecond) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve();
    }, millisecond);
  });
};

export const getTodayToSomeday = (num) => {
  const date1 = new Date();
  // 今天时间
  const now = date1.getFullYear() + '-' + (date1.getMonth() + 1) + '-' + date1.getDate();

  const date2 = new Date(date1);
  date2.setDate(date1.getDate() + num);

  // num是正数表示之后的时间，num负数表示之前的时间，0表示今天
  const time2 = date2.getFullYear() + '-' + (date2.getMonth() + 1) + '-' + date2.getDate();

  return [now, time2];
};
