
/**
 * copy元素复制文本
 * @param { element } elem
 */
export const CopyElemText = (elem) => {
  const range = document.createRange();
  range.selectNodeContents(elem);
  const selection = window.getSelection();
  selection.removeAllRanges();
  selection.addRange(range);
  document.execCommand('copy');
  selection.removeRange(range);
};

/**
 *  用async await 来达到延迟效果
 * @param { Number } millisecond
 */
export const sleep = (millisecond) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve();
    }, millisecond);
  });
};

export const getTodayToSomeday = (num) => {
  const date1 = new Date();
  // 今天时间
  const now = date1.getFullYear() + '-' + (date1.getMonth() + 1) + '-' + date1.getDate();

  const date2 = new Date(date1);
  date2.setDate(date1.getDate() + num);

  // num是正数表示之后的时间，num负数表示之前的时间，0表示今天
  const time2 = date2.getFullYear() + '-' + (date2.getMonth() + 1) + '-' + date2.getDate();

  return [now, time2];
};
