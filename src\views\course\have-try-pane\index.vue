<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='上课方式' prop='courseChannelCode'>
        <el-select
          v-model="form.courseChannelCode"
          clearable
          placeholder="请选择"
        >
          <el-option label="远智教育" value="YZ" />
          <el-option label="课火网" value="KHW" />
          <el-option label="课师宝" value="KSB" />
        </el-select>
      </el-form-item>

      <el-form-item label='课程名称' prop='courseName'>
        <el-input v-model="form.courseName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='试看名称' prop='tryName'>
        <el-input v-model="form.tryName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='状态' prop='status'>
        <el-select
          v-model="form.status"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="success" size="small" plain icon="el-icon-delete" @click="batchUpdateStatus('1')">批量启用</el-button>
      <el-button type="danger" size="small" plain icon="el-icon-delete" @click="batchUpdateStatus('2')">批量禁用</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="courseChannelCode" label="上课方式" align="center">
        <template slot-scope="scope">
          {{ scope.row.courseChannelCode | courseSupplier }}
        </template>
      </el-table-column>
      <el-table-column prop="courseName" label="课程名称" align="center" />
      <el-table-column prop="courseTimeName" label="课时名称" align="center" />
      <el-table-column prop="tryName" label="试看名称" align="center" />
      <el-table-column prop="sort" label="顺序" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '1' ?'success':'danger'">
            {{ scope.row.status | tansformStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <update-dialog :visible.sync="udVisible" :title="udTitle" :free-id="currentFreeId" />
  </div>
</template>
<script>
import updateDialog from './update-dialog';
import { pagination } from '@/config/constant';

export default {
  components: {
    updateDialog
  },
  data() {
    return {
      currentFreeId: null,
      udVisible: false,
      udTitle: '新增',
      tableLoading: false,
      form: {
        courseName: undefined,
        tryName: '',
        courseChannelCode: '',
        status: ''
      },
      selection: [],
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10,
        ...pagination
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    handleEdit(row) {
      this.udTitle = '编辑';
      this.udVisible = true;
      this.currentFreeId = row.freeId;
    },
    handleAdd() {
      this.currentFreeId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    batchUpdateStatus(status) {
      if (this.selection.length > 0) {
        let ids = '';
        this.selection.forEach(e => {
          ids += e.freeId + ',';
        });
        ids = ids.slice(0, ids.length - 1);
        const data = {
          freeIds: ids,
          status: status
        };
        this.$post('batchUpdateHaveTryStatus', data).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
      } else {
        this.$message.error('请勾选数据！');
      }
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getFreeVideoList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  padding-top:20px;
}
</style>
