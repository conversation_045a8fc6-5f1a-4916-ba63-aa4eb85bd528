<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">

      <el-tab-pane label="会员卡对账管理" name="memberCard">
        <member-card />
      </el-tab-pane>
      <el-tab-pane label="对账管理" name="course" lazy>
        <course />
      </el-tab-pane>
      <el-tab-pane label="分期对账管理" name="installment" lazy>
        <stages-reconciliation />
      </el-tab-pane>
      <!-- <el-tab-pane label="套餐对账管理" name="course" lazy>
        <course />
      </el-tab-pane>
      <el-tab-pane label="读书计划对账管理" name="readPlan" lazy>
        <readPlan />
      </el-tab-pane>
      <el-tab-pane label="学霸卡对账管理" name="studycard" lazy>
        <studyCard />
      </el-tab-pane> -->

      <!-- <el-tab-pane label="供应商结算管理" name="supplierSettlement" lazy>
        <supplier-settlement />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>
<script>
// import studyCard from './study-card/index';
// import readPlan from './read-plan/index';
import memberCard from './member-card/index';
import course from './course/index';
// import supplierSettlement from './supplier-settlement';
import stagesReconciliation from './stages-reconciliation';
export default {
  components: {
    course,
    memberCard,
    stagesReconciliation
    // readPlan,
    // studyCard
    // supplierSettlement
  },
  data() {
    return {
      activeName: 'memberCard'
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
