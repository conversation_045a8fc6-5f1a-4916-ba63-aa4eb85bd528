<template>
  <common-dialog
    title="添加备注"
    :visible.sync='show'
    :showFooter="true"
    width="500px"
    @confirm="submit"
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        :model='form'
        label-width='0'
        @submit.native.prevent='search'
      >
        <el-form-item label='' prop='name'>
          <el-input
            v-model='form.remark'
            type="textarea"
            :maxlength="200"
            :rows="8"
            :show-word-limit="true"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    distributionOrderNo: {
      type: String,
      default: ''
    },
    remark: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      aeDialogShow: false,
      form: {
        remark: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false,
      curEditRowData: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    distributionOrderNo(val) {
      this.distributionOrderNo = val;
    },
    remark(val) {
      this.form.remark = val;
    }
  },
  methods: {
    submit() {
      const params = { distributionOrderNo: this.distributionOrderNo, remark: this.form.remark };
      this.$post('distributionOrderRemark', params).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$emit('getTableList');
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.close();
        }
      });
    },
    open() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
