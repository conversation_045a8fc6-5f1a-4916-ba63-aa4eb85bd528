<template>
  <common-dialog
    is-full
    title="素材管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='统计时间' prop='time'>
          <el-date-picker
            v-model="form.time"
            placement="bottom-start"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="daterange"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增分销材料</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="sort" label="序号" align="center" />
        <el-table-column prop="materialName" label="素材名称" align="center" />
        <el-table-column prop="materialText" label="文本内容" align="center" width="200">
          <template slot-scope="scope">
            <div class="material-text">{{ scope.row.materialText }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="urlsCount" label="海报素材数（单位：张）" align="center" />
        <el-table-column prop="materialTextUserCount" label="文案复制次数（单位：人）" align="center" />
        <el-table-column prop="urlsUserCount" label="海报保存次数（单位：人）" align="center" />
        <el-table-column label="是否启用" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.stateBoolean"
              @change="(value) => handleMaterialState(value, scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column label="排序" align="center">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-top"
              circle
              size="mini"
              title="上移"
              @click="handleMaterialSort(scope.row, 'up')"
            />
            <el-button
              icon="el-icon-bottom"
              circle
              size="mini"
              title="下移"
              @click="handleMaterialSort(scope.row, 'down')"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="操作" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" plain @click="handleEditMaterial(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" plain @click="handleDeleteMaterial(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

      <add-edit-material-dialog
        :visible.sync="aeDialogShow"
        :row-data="curEditRowData"
        :good-code="rowData.distributionNumber"
        @refreshList="getTableList"
      />
    </div>
  </common-dialog>
</template>

<script>
import addEditMaterialDialog from './addEdit-material-dialog';
import { handleDateControl } from '@/utils';
export default {
  components: {
    addEditMaterialDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      aeDialogShow: false,
      form: {
        time: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false,
      curEditRowData: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleAdd() {
      this.curEditRowData = null;
      this.aeDialogShow = true;
    },
    handleMaterialSort(rowData, type) {
      const params = {
        materialId: rowData.materialId,
        distributionNumber: this.rowData.distributionNumber,
        operationType: type
      };
      this.$post('editMaterialSort', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '修改成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleMaterialState(state, rowData) {
      const params = {
        materialId: rowData.materialId,
        state: state ? 1 : 0
      };
      this.$post('updateMaterialState', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '修改成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleEditMaterial(rowData) {
      this.curEditRowData = rowData;
      this.aeDialogShow = true;
    },
    handleDeleteMaterial(rowData) {
      const params = { materialId: rowData.materialId };
      this.$post('deleteGoodsShareMaterial', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    getSerQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);

      return {
        distributionNumber: this.rowData.distributionNumber, // 分销商品编码
        startTime: date[0],
        endTime: date[1],
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.getSerQueryParams();
      this.$post('getGoodsShareMaterial', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          if (body) {
            this.tableData = body.data;
            body.data.forEach(item => {
              item.stateBoolean = item.state === '1';
            });
            this.pagination.total = body.recordsTotal;
          } else {
            this.tableData = [];
          }
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
