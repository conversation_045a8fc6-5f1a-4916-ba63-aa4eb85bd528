<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='是否启用' prop='allow'>
        <el-select v-model="form.allow" filterable clearable placeholder="请选择">
          <el-option
            v-for="item in $localDict['status']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='上架模块' prop='bannerBelong'>
        <el-select v-model="form.bannerBelong" filterable clearable placeholder="请选择">
          <el-option label="banner" value="5" />
          <el-option label="推荐区" value="6" />
          <el-option label="APP首页" value="7" />
          <el-option label="读书计划列表页banner" value="8" />
        </el-select>
      </el-form-item>

      <el-form-item label='推荐类型' prop='category'>
        <el-select v-model="form.category" filterable clearable placeholder="请选择">
          <el-option label="单课程套餐" value="1" />
          <el-option label="多课程套餐" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='操作时间' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="danger" size="small" plain icon="el-icon-delete" @click="handleDelete(1)">批量删除</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="sort" width="100" label="排序号" align="center" />
      <el-table-column prop="bannerUrl" label="图片" align="center" width="200px">
        <template slot-scope="scope">
          <span v-if="scope.row.bannerBelong=='7' || scope.row.bannerBelong=='6'">无</span>
          <img v-else :src="scope.row.bannerUrl | splitOssUrl" :title="scope.row.bannerName" style="object-fit: contain;width: 100%;">
        </template>
      </el-table-column>
      <el-table-column prop="bannerName" label="名称" align="center" />
      <el-table-column prop="bannerBelong" label="上架模块/推荐类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.bannerBelong | tansformShelfModule }}
        </template>
      </el-table-column>
      <el-table-column prop="redirectUrl" label="URL/ID" align="center" />
      <el-table-column prop="updateUserName" label="操作人" align="center" />
      <el-table-column prop="updateTime" label="操作时间" align="center" />
      <el-table-column prop="historyNum" label="点击数量" align="center" />
      <el-table-column prop="allow" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.allow==1" type="success">已启用</el-tag>
          <el-tag v-else type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="editStatus(scope.row)">{{ scope.row.allow==1?'禁用':"启用" }}</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(2,scope.row)">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <update-dialog :visible.sync="udVisible" :title="udTitle" :banner-id="currentModifyBannerId" />

  </div>
</template>
<script>

import { pagination, TABLE_HEIGHT } from '@/config/constant';
import { handleDateControl } from '@/utils';
import updateDialog from './update-dialog';
export default {
  components: {
    updateDialog
  },
  data() {
    return {
      table_height: TABLE_HEIGHT,
      udVisible: false,
      udTitle: '新增',
      tableLoading: false,
      currentModifyBannerId: null,
      selection: [],
      form: {
        allow: '',
        bannerBelong: '',
        category: '',
        time: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10,
        ...pagination
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    handleAdd() {
      this.currentModifyBannerId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    handleEdit(row) {
      this.currentModifyBannerId = row.bannerId;
      this.udTitle = '编辑';
      this.udVisible = true;
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    editStatus(row) {
      const data = {
        allow: row.allow === '1' ? '2' : '1',
        bannerId: row.bannerId,
        bannerBelong: row.bannerBelong
      };
      this.$post('updateBannerStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    // 1->批量删除，2->单个删除
    handleDelete(type, row) {
      const ids = [];
      const data = {
        bannerIds: []
      };
      if (type === 1) {
        if (this.selection.length <= 0) {
          this.$message.error('请勾选信息');
          return false;
        }
        this.selection.forEach(e => {
          ids.push(e.bannerId);
        });
      } else {
        ids.push(row.bannerId);
      }
      data.bannerIds = ids.join();

      this.$post('deleteBanner', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    // 获取列表数据
    getTableList() {
      // formData 搜索条件
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      delete formData.time;
      this.tableLoading = true;

      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        startTime: date[0],
        endTime: date[1],
        ...formData
      };
      this.$post('getBannerList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
