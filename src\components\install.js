import Vue from 'vue';
import DictSelect from './selects/dict-select';
import CityCascader from './selects/city-cascader';
import AllschoolSelect from './selects/allschool-select';
import MajorSelect from './selects/major';
import LocalDictSelect from './selects/local-dict-select';
import CommonDialog from './common-dialog';
import openPackup from './open-packup';
import Pagination from './Pagination';
import InfiniteSelects from './InfiniteSelects';
import UploadFile from './UploadFile';
import InputNumber from './InputNumber';
import RemoteSearchSelects from './RemoteSearchSelects';
import SearchSelect from '@/components/SearchSelect';
import LoadMoreSelect from '@/components/LoadMoreSelect';
import YzSelect from './YzSelect';
import Tinymce from './Tinymce';
import WangEditor from './WangEditor';
// 安装全局组件
const components = [
  DictSelect,
  CityCascader,
  AllschoolSelect,
  LocalDictSelect,
  CommonDialog,
  MajorSelect,
  openPackup,
  Pagination,
  InfiniteSelects,
  UploadFile,
  InputNumber,
  RemoteSearchSelects,
  LoadMoreSelect,
  SearchSelect,
  YzSelect,
  Tinymce,
  WangEditor
];

const install = (list) => {
  list.forEach((item) => { Vue.component(item.name, item); });
};

install(components);

