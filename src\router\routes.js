/* Layout */
import Layout from '@/layout';

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

// 自动引入当前目录下所有路由文件，并将其添加到路由数组中，避免每次新增路由文件都需要手动添加到路由数组中
const files = require.context('.', false, /\.js$/);
const importRouters = [];
files.keys().forEach(key => {
  const name = key.replace(/.\/|\.js$/g, '');
  if (!['index', 'routes'].includes(name)) {
    // TODO 优化排序问题
    importRouters.push(...(files(key).default || []));
  }
});

const routes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [{
      path: 'home',
      name: 'home',
      component: () => import('@/views/home/<USER>'),
      meta: { title: '首页', icon: 'el-icon-s-home' }
    }]
  },
  ...importRouters,
  {
    path: '/course-reviews',
    component: Layout,
    meta: {
      title: '上课心得',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'manage',
        component: () => import('@/views/course-reviews/index'),
        meta: {
          title: '上课心得'
        }
      }
    ]
  },
  {
    path: '/pay-rec',
    component: Layout,
    meta: {
      title: '商品推荐管理',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'manage',
        component: () => import('@/views/pay-rec/index'),
        meta: {
          title: '商品推荐管理'
        }
      }
    ]
  },
  {
    path: '/combined-pay-order',
    component: Layout,
    meta: {
      title: '合并缴费订单',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/combined-pay-order/index'),
        meta: {
          title: '合并缴费订单'
        }
      }
    ]
  },
  {
    path: '/single-book',
    component: Layout,
    meta: {
      title: '单本读书计划',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/single-book/index'),
        meta: {
          title: '单本读书计划'
        }
      }
    ]
  },
  {
    path: '/super-plus',
    component: Layout,
    meta: {
      title: '学霸卡课程管理',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/super-plus/index'),
        meta: {
          title: '学霸卡课程管理'
        }
      }
    ]
  },
  {
    path: '/train-camp',
    component: Layout,
    meta: {
      title: '训练营管理',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/train-camp/index'),
        meta: {
          title: '训练营管理'
        }
      }
    ]
  },
  {
    path: '/distribution',
    component: Layout,
    meta: {
      title: '分销管理',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/distribution/index'),
        meta: {
          title: '分销管理'
        }
      }
    ]
  },
  {
    path: '/operate',
    component: Layout,
    meta: {
      title: '运营管理',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/operate/index'),
        meta: {
          title: '运营管理'
        }
      }
    ]
  },
  {
    path: '/progresses',
    component: Layout,
    meta: {
      title: '上进打卡管理',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/progresses-clock/index'),
        meta: {
          title: '上进打卡管理'
        }
      }
    ]
  },
  {
    path: '/offsiteAnnex',
    component: Layout,
    meta: {
      title: '异地附件',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/offsite-annex/index'),
        meta: {
          title: '异地附件'
        }
      }
    ]
  },
  {
    path: '/goods-set',
    component: Layout,
    meta: {
      title: '职业教育产品系数配置',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/product/index'),
        meta: {
          title: '职业教育产品系数配置'
        }
      }
    ]
  },
  {
    path: '/kpi-detail',
    component: Layout,
    meta: {
      title: '个人绩效明细',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/distribution/education-kpi/list-component'),
        meta: {
          title: '个人绩效明细'
        }
      }
    ]
  },
  {
    path: '/education',
    component: Layout,
    meta: {
      title: '职业教育',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/education/index'),
        meta: {
          title: '职业教育'
        }
      }
    ]
  },
  {
    path: '/prezzie',
    component: Layout,
    meta: {
      title: '礼品清单',
      icon: 'el-icon-s-promotion',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/prezzie/index'),
        meta: {
          title: '礼品清单'
        }
      }
    ]
  },
  {
    path: '/test',
    component: () => import('@/views/test'),
    hidden: true
  },
  {
    path: '/demo',
    component: () => import('@/views/demo'),
    hidden: true
  },

  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
];

export default routes;
