<template>
  <common-dialog
    is-full
    title="分销订单"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <list-component ref='listComponent' :empId="empId" :isBtn="false" />
    </div>
  </common-dialog>
</template>

<script>
import listComponent from '../distribution-order/list-component';
export default {
  components: {
    listComponent
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    empId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
