<template>
  <div class="yz-base-container">
    <!-- 表单区 -->
    <el-form size="mini" :model="querys" label-width="120px" class="yz-search-form" @submit.native.prevent="submitNative">
      <el-form-item label="测评标题">
        <el-input v-model="querys.title" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-select v-model="querys.enableFlag" filterable clearable placeholder="请选择">
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="测评类型">
        <el-select v-model="querys.type" filterable clearable placeholder="请选择">
          <el-option label="趣味" value="0" />
          <el-option label="性格" value="1" />
          <el-option label="情感" value="2" />
          <el-option label="职场" value="3" />
          <el-option label="运势" value="4" />
        </el-select>
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click.stop="refreshBtn" />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class="yz-table-btnbox table-btn">
      <el-button type="primary" size="small" icon="el-icon-share" @click="showShare=true">分享奖励配置</el-button>
      <el-button type="primary" size="small" icon="el-icon-tickets" @click="openSubjectBtn">测评题目配置</el-button>
      <el-button type="primary" size="small" icon="el-icon-setting" @click="openAnswerBtn">测评结果配置</el-button>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="addupdateBtn({id:''})">新增</el-button>
    </div>
    <!-- 表格区info -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column label="测评Id" prop="id" align="center" />
      <el-table-column label="测评类型" prop="typeText" align="center" />
      <el-table-column label="测评标题" prop="title" align="center" />
      <el-table-column label="基数" prop="baseNum" align="center" />
      <el-table-column label="测评题目总数" prop="topicCount" align="center" />
      <el-table-column label="创建人" prop="createUserName" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="最近修改人" prop="updateUserName" align="center" />
      <el-table-column label="最近修改时间" prop="updateTime" align="center" />
      <el-table-column label="启用状态" prop="enableFlag" align="center">
        <template slot-scope="scope"><span>{{ Number(scope.row.enableFlag)?'启用':'禁用' }}</span></template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button v-if="scope.row.enableFlag == 0" type="success" size="small" @click="seeDetails({id:scope.row.id,inx:0})">启用</el-button>
          <el-button v-else type="info" size="small" @click="seeDetails({id:scope.row.id,inx:1})">禁用</el-button>
          <el-button type="warning" size="small" @click="addupdateBtn(scope.row)">修改</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
    </div>
    <!-- 分享奖励 -->
    <allocation-share :visible="showShare" @on-close="showShare=false" />
    <!-- 测评题目 -->
    <allocation-subject :visible="showSubject" @on-close="closeSubjectBtn" />
    <!-- 答案配置 -->
    <allocation-answer :visible="showAnswer" :isResultAuthority="isResultAuthority" @on-close="closeAnswerBtn" />
    <!-- 新增测评 -->
    <allocation-eval :visible="showEval" :params="evalParams" @on-close="closeEvalBtn" />
  </div>
</template>

<script>
import LookMobile from '@/mixins/LookMobile';
import allocationShare from './allocation-share/index';
import allocationSubject from './allocation-subject/index';
import allocationAnswer from './allocation-answer/index';
import allocationEval from './allocation-eval/index';
import { getCutDay } from '@/utils';

export default {
  components: { allocationShare, allocationSubject, allocationAnswer, allocationEval },
  mixins: [LookMobile],
  data() {
    return {
      isEvalAuthority: false,
      isResultAuthority: false,
      querys: { title: '', enableFlag: '' },
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        rows: 10
      },
      showShare: false,
      showSubject: false,
      showAnswer: false,
      showEval: false,
      evalParams: {}
    };
  },
  async created() {
    await this.getPermission();
    this.getTableList();
  },
  methods: {
    // 查询
    submitNative() {
      this.pagination = { page: 1, total: 0, rows: 10 };
      this.getTableList();
    },
    // 重置
    refreshBtn() {
      this.querys = { title: '', enableFlag: '' };
    },
    // 打开题目弹窗
    openSubjectBtn() {
      this.showSubject = true;
    },
    // 打开题目弹窗
    closeSubjectBtn() {
      this.showSubject = false;
    },
    // 打开答案弹窗
    openAnswerBtn() {
      this.showAnswer = true;
    },
    // 关闭答案弹窗
    closeAnswerBtn() {
      this.showAnswer = false;
    },
    // 新增/修改：打开测评题目弹窗
    addupdateBtn(rows) {
      // 判断是否有操作权限
      if (!this.isEvalAuthority) {
        this.$message.error('您当前没有操作权限');
        return false;
      }
      this.showEval = true;
      this.evalParams = rows;
      console.log('打开测评题目弹窗', rows);
    },
    // 关闭测评题目弹窗
    closeEvalBtn(type = 0) {
      this.evalParams = {};
      this.showEval = false;
      if (type) this.getTableList();
    },
    // 禁用-启用
    seeDetails({ inx, id }) {
      // 判断是否有操作权限
      if (!this.isEvalAuthority) {
        this.$message.error('您当前没有操作权限');
        return false;
      }
      const urs = inx === 1 ? 'evalDisable' : 'evalEnable';
      console.log('禁用-启用', urs);
      this.tableLoading = true;
      this.$post(urs, { id }).then((res) => {
        if (!res.fail) {
          this.$message({ message: '操作成功', type: 'success' });
          this.getTableList();
        } else {
          this.$message.error('操作失败');
        }
        this.tableLoading = false;
      }).catch(() => {
        this.$message.error('操作失败');
        this.tableLoading = false;
      });
    },
    // 接口调用
    getTableList() {
      this.tableLoading = true;
      const obs = { ...this.querys, ...this.pagination };
      const news = new FormData();
      for (const key in obs) {
        if (obs[key] === '') delete obs[key];
        else news.append(key, obs[key]);
      }
      console.log('请求参数', obs);
      if (obs.type === '') delete obs.type;
      this.$post('evalList', news, { uploadFile: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          body.data?.map(item => {
            // 0：趣味；1：性格；2：情感；3：职场；4：运势
            switch (Number(item.type)) {
              case 0:
                item.typeText = '趣味';
                break;
              case 1:
                item.typeText = '性格';
                break;
              case 2:
                item.typeText = '情感';
                break;
              case 3:
                item.typeText = '职场';
                break;
              case 4:
                item.typeText = '运势';
                break;
              default:
                item.typeText = '';
                break;
            }
            item.updateTime = item.updateTime && getCutDay(item.updateTime);
            item.createTime = item.createTime && getCutDay(item.createTime);
          });
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
        this.tableLoading = false;
      }).catch(() => {
        this.tableData = [];
        this.tableLoading = false;
      });
    },
    // 获取权限
    getPermission() {
      this.$post('evalPermission', this.pagination).then((res) => {
        const { code, body } = res;
        if (code === '00' && body) {
          this.isEvalAuthority = Boolean(body.find(item => item === 'eval:evalConfig'));
          this.isResultAuthority = Boolean(body.find(item => item === 'eval:evalResultConfig'));
        }
        console.log('evalPermission----1', this.isEvalAuthority);
        console.log('evalPermission----2', this.isResultAuthority);
      }).catch(() => {
        this.isEvalAuthority = false;
        this.isResultAuthority = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.table-btn {
  margin: 15px 0;
}
</style>
