<!-- 退款类工单表格组件 -->
<template>
  <div>
    <el-table
      v-loading="loading"
      border
      size="small"
      class="w-full"
      header-cell-class-name="table-cell-header"
      :data="tableData"
      @selection-change="handleSelectionChange"
      ref="multipleTable"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="stuName" label="学员姓名" align="center">
        <template slot-scope="scope">
          {{ scope.row.stuName || "" }}
        </template>
      </el-table-column>
      <el-table-column prop="stuXyCode" label="学业编码" align="center">
        <template slot-scope="scope">
          {{ scope.row.stuXyCode || "" }}
        </template>
      </el-table-column>
      <el-table-column
        prop="refundCreateTime"
        label="学员提出退费时间"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.refundCreateTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column
        prop="systemRefundTime"
        label="系统退费申请时间"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.row.systemRefundTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="receiveEmpName" label="受理人" align="center">
        <template slot-scope="scope">
          <el-tooltip
            v-if="
              scope.row.receiveEmpName && scope.row.receiveEmpName.length > 15
            "
            :content="scope.row.receiveEmpName"
            placement="top"
          >
            <span>{{ scope.row.receiveEmpName.substring(0, 15) + "..." }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.receiveEmpName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="reason" label="退费原因" align="center" />
      <el-table-column prop="remark" label="退费备注" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.remark">已填写</span>
        </template>
      </el-table-column>
      <el-table-column prop="isThreeSeven" label="是否3.7天退费" align="center">
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.isThreeSeven && String(scope.row.isThreeSeven) === '1'
            "
            >3天内</span
          >
          <span
            v-if="
              scope.row.isThreeSeven && String(scope.row.isThreeSeven) === '2'
            "
            >7天内</span
          >
          <span
            v-if="
              scope.row.isThreeSeven && String(scope.row.isThreeSeven) === '3'
            "
            >7天外</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="shouldRefundMoney"
        label="应退金额"
        align="center"
      />
      <el-table-column
        prop="actualRefundMoney"
        label="实退金额"
        align="center"
      />
      <el-table-column prop="workOrderId" label="关联工单" align="center">
        <template slot-scope="scope">
          <a
            v-if="scope.row.workOrderId"
            class="link-text"
            @click="goToDetail(scope.row)"
          >
            {{ scope.row.workOrderId }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="stuEnrollType" label="招生类型" align="center" />
      <el-table-column prop="payTime" label="缴费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.payTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="payType" label="支付方式" align="center" />
      <el-table-column prop="universityName" label="院校" align="center" />
      <el-table-column prop="majorName" label="专业" align="center" />
      <el-table-column prop="pfsnLevelText" label="层次" align="center" />
      <el-table-column prop="stdStage" label="学员阶段" align="center" />
      <el-table-column prop="recruitEmp" label="招生老师" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.recruitEmp }}</span>
          <el-tag v-if="scope.row.isEmpOut === '是'" type="info" size="mini"
            >离</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="recruitEmpDepart"
        label="招生老师部门"
        align="center"
      />
      <el-table-column prop="followEmpName" label="跟进老师" align="center" />
      <el-table-column
        prop="followEmpDepart"
        label="跟进老师部门"
        align="center"
      />
      <el-table-column prop="passCourseCount" label="通过科目数" align="center">
        <template slot-scope="scope">
          {{
            scope.row.passCourseCount === null ? "-" : scope.row.passCourseCount
          }}
        </template>
      </el-table-column>
      <el-table-column prop="isEmpOut" label="招生老师是否离职" align="center">
        <template slot-scope="scope">
          <span
            v-if="scope.row.isEmpOut === 'true' || scope.row.isEmpOut === true"
            >是</span
          >
          <span
            v-else-if="
              scope.row.isEmpOut === 'false' || scope.row.isEmpOut === false
            "
            >否</span
          >
          <span v-else>{{ scope.row.isEmpOut }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="isAssignOut" label="是否分配离" align="center">
        <template slot-scope="scope">
          <span
            v-if="
              scope.row.isAssignOut === 'true' || scope.row.isAssignOut === true
            "
            >是</span
          >
          <span
            v-else-if="
              scope.row.isAssignOut === 'false' ||
              scope.row.isAssignOut === false
            "
            >否</span
          >
          <span v-else>{{ scope.row.isAssignOut }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "RefundTable",
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  filters: {
    transformTimeStamp(timestamp) {
      if (!timestamp) return "";
      return new Date(timestamp).toLocaleString();
    },
  },
  methods: {
    handleSelectionChange(val) {
      this.$emit("selection-change", val);
    },
    goToDetail(row) {
      this.$emit("go-to-detail", row);
    },
  },
};
</script>

<style lang="scss" scoped>
.link-text {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}
</style>
