<template>
  <div class="dialog-main">
    <div class="title">个人绩效</div>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="personTableData"
    >
      <el-table-column prop="roleType" label="绩效所属" align="center" />
      <el-table-column prop="auditTotal" label="个人职业教育缴费标准系数总和" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleAll(scope.row, 'single', 0)">{{ scope.row.auditTotal }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="dropAuditTotal" label="个人职业教育退费标准系数总和" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleAll(scope.row, 'single', 1)">{{ scope.row.dropAuditTotal }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="performanceValue" label="个人单生绩效值" align="center" />
      <el-table-column prop="income" label="个人直招绩效" align="center" />
    </el-table>
    <template v-if="teamTableData.length > 0">
      <div class="title">团队绩效</div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="teamTableData"
      >
        <el-table-column prop="roleType" label="绩效所属" align="center" />
        <el-table-column prop="auditTotal" label="团队职业教育缴费标准系数总和" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleAll(scope.row, 'team', 0)">{{ scope.row.auditTotal }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="dropAuditTotal" label="团队职业教育退费标准系数总和" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleAll(scope.row, 'team', 1)">{{ scope.row.dropAuditTotal }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="performanceValue" label="团队单生绩效值" align="center" />
        <el-table-column prop="income" label="团队绩效" align="center" />
      </el-table>
    </template>
    <kpi-order-detail-dialog :visible.sync="orderVisible" :rowData="rowData" :type="type" :isRefund="isRefund" :month="month" />
  </div>
</template>

<script>
import kpiOrderDetailDialog from './kpi-order-detail-dialog';
export default {
  components: {
    kpiOrderDetailDialog
  },
  props: {
    month: {
      type: String,
      default: ''
    },
    empId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      orderVisible: false,
      tableLoading: false,
      personTableData: [],
      teamTableData: [],
      rowData: {},
      type: 'single', // single 个人的  team 团队
      isRefund: 1 // 0 退费 1 缴费
    };
  },
  watch: {
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const { empId, month } = this.$route.query;
      const data = {
        empId: this.empId || empId,
        month: this.month || month
      };
      // 个人绩效
      this.$post('queryEmployeePersonalPerformanceDetails', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.personTableData = body;
        }
      });
      // 团队绩效
      this.$post('queryEmployeeTeamPerformanceDetails', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.teamTableData = body;
        }
      });
    },
    handleAll(row, type, isRefund) {
      this.orderVisible = true;
      this.type = type;
      this.isRefund = isRefund;
      this.rowData = row;
    }
  }
};
</script>

<style lang='scss' scoped>
.title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0;
}
</style>
