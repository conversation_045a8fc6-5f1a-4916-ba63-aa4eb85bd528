<template>
  <div>
    <!-- 表单 -->
    <open-packup>
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='商品名称' prop='commodityName'>
          <el-input v-model="form.commodityName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='商品id' prop='commodityId'>
          <el-input v-model="form.commodityId" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='购买人姓名' prop='realName'>
          <el-input v-model="form.realName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='购买人远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='购买人手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='订单状态' prop='isRefund'>
          <el-select v-model="form.isRefund" clearable>
            <el-option label="已缴费" :value="0" />
            <el-option label="已退费" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label='邀约人' prop='inviteEmpName'>
          <el-input v-model="form.inviteEmpName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='邀约人手机号' prop='inviteMobile'>
          <el-input v-model="form.inviteMobile" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='分销人' prop='traderEmpName'>
          <el-input v-model="form.distributionUserName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='分销人手机号' prop='traderMobile'>
          <el-input v-model="form.distributionUserName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label='付费时间' prop='payTime'>
          <el-date-picker
            v-model="form.payTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <el-form-item label='退费时间' prop='refundTime'>
          <el-date-picker
            v-model="form.refundTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>
    </open-packup>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="commodityName" label="商品名称" align="center" />
      <el-table-column prop="commodityId" label="商品id" align="center" />
      <el-table-column prop="realName" label="购买人" align="center" />
      <el-table-column prop="mobile" label="购买人手机号" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.mobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <el-table-column prop="commodityOrderNo" label="订单号" align="center" />
      <el-table-column prop="commodityOrderStatusName" label="订单状态" align="center" />
      <el-table-column prop="payTime" label="付费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.payTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="refundTime" label="退费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="audit" label="商品系数" align="center" />
      <el-table-column prop="inviteEmpName" label="邀约人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.inviteEmpName }}</span>
          <el-tag v-if="scope.row.inviteEmpStatus === 0" type="info" size="mini">离</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="invitePerformanceValue" label="邀约人单生绩效值" align="center" />
      <el-table-column prop="inviteDivideProportion" label="邀约分成比例" align="center">
        <template slot-scope="scope">
          {{ scope.row.inviteDivideProportion * 100 + '%' }}
        </template>
      </el-table-column>
      <el-table-column prop="inviteIncome" label="邀约绩效" align="center" />
      <el-table-column prop="traderEmpName" label="分销人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.traderEmpName }}</span>
          <el-tag v-if="scope.row.traderEmpStatus === 0" type="info" size="mini">离</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="traderPerformanceValue" label="分销人单生绩效值" align="center" />
      <el-table-column prop="traderDivideProportion" label="分销分成比例" align="center">
        <template slot-scope="scope">
          {{ scope.row.traderDivideProportion * 100 + '%' }}
        </template>
      </el-table-column>
      <el-table-column prop="traderIncome" label="分销绩效" align="center" />
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 弹框区 -->
    <order-detail-dialog :visible.sync="orderDetailVisible" />
    <export-dialog :visible.sync="markVisible" />
  </div>
</template>
<script>
import { handleDateControl } from '@/utils';
import LookMobile from '@/mixins/LookMobile';
import orderDetailDialog from './order-detail-dialog';
import exportDialog from './export-dialog';

export default {
  components: {
    orderDetailDialog,
    exportDialog
  },
  mixins: [LookMobile],
  props: {
    orderParams: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      orderDetailVisible: false,
      markVisible: false,
      form: {
        commodityName: '',
        commodityId: '',
        realName: '',
        mobile: '',
        yzCode: '',
        isRefund: '',
        traderEmpName: '',
        traderMobile: '',
        inviteEmpName: '',
        inviteMobile: '',
        payTime: '',
        refundTime: ''
      },
      tableData: [],
      selects: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    orderParams(val) {
      this.form.distributionGoodsName = val;
      this.getTableList();
    },
    immediate: true
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const payTime = handleDateControl(formData.payTime);
      formData.payStartTime = payTime[0];
      formData.payEndTime = payTime[1];
      const refundTime = handleDateControl(formData.refundTime);
      formData.refundStartTime = refundTime[0];
      formData.refundEndTime = refundTime[1];
      delete formData.payTime;
      delete formData.refundTime;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('queryPersonalOrderRecordsPage', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelected(selects) {
      this.selects = selects;
    },
    batchSettle(status) {

    },
    // importData() {
    //   this.bidVisibel = true;
    // }
    exportData() {
      this.markVisible = !this.markVisible;
    },
    handleKickback() {
      this.orderDetailVisible = !this.orderDetailVisible;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
.settle-txt{
  padding-bottom: 10px;
  color: red;
}
</style>
