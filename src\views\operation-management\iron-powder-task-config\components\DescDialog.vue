<template>
  <common-dialog
    show-footer
    width="800px"
    title="文案内容配置"
    :visible.sync="visible"
    confirmText="保存"
    @confirm="onSave"
    @close="visible = false"
  >
    <div v-loading="loading" class="dialog-main">
      <el-form ref="form" label-width="135px" :model="form" :rules="rules">
        <el-form-item label="近1年转介绍" prop="introductionYearRemark">
          <el-input
            v-model="form.introductionYearRemark"
            type="textarea"
            :maxlength="200"
            show-word-limit
            :rows="4"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="近1年消费" prop="consumeYearRemark">
          <el-input
            v-model="form.consumeYearRemark"
            type="textarea"
            :maxlength="200"
            show-word-limit
            :rows="4"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="近1年动态" prop="dynamicsYearRemark">
          <el-input
            v-model="form.dynamicsYearRemark"
            type="textarea"
            :maxlength="200"
            show-word-limit
            :rows="4"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="近30天动态" prop="dynamicsMonthRemark">
          <el-input
            v-model="form.dynamicsMonthRemark"
            type="textarea"
            :maxlength="200"
            show-word-limit
            :rows="4"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item
          label="潜在铁粉列表缺省页-文案"
          prop="potentialFansRemark"
        >
          <el-input
            v-model="form.potentialFansRemark"
            type="textarea"
            :maxlength="50"
            show-word-limit
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item
          label="潜在铁粉列表缺省页-引导语标题"
          prop="potentialFansGuideTitle"
        >
          <el-input
            v-model="form.potentialFansGuideTitle"
            type="textarea"
            :maxlength="50"
            show-word-limit
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item
          label="潜在铁粉列表缺省页-引导语内容"
          prop="potentialFansGuideContent"
        >
          <el-input
            v-model="form.potentialFansGuideContent"
            type="textarea"
            :maxlength="500"
            show-word-limit
            :rows="4"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="铁粉列表缺省页-文案" prop="stanRemark">
          <el-input
            v-model="form.stanRemark"
            type="textarea"
            :maxlength="50"
            show-word-limit
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="铁粉列表缺省页-引导语标题" prop="stanGuideTitle">
          <el-input
            v-model="form.stanGuideTitle"
            type="textarea"
            :maxlength="50"
            show-word-limit
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item
          label="铁粉列表缺省页-引导语内容"
          prop="stanGuideContent"
        >
          <el-input
            v-model="form.stanGuideContent"
            type="textarea"
            :maxlength="500"
            show-word-limit
            :rows="3"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="铁粉列表缺省页-按钮名称" prop="stanGuideButton">
          <el-input
            v-model="form.stanGuideButton"
            :maxlength="10"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  name: 'DescDialog',
  props: {
    show: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      form: {
        taskTypeConfigId: null,
        introductionYearRemark: '',
        consumeYearRemark: '',
        dynamicsYearRemark: '',
        dynamicsMonthRemark: '',
        potentialFansRemark: '',
        potentialFansGuideTitle: '',
        potentialFansGuideContent: '',
        stanRemark: '',
        stanGuideTitle: '',
        stanGuideContent: '',
        stanGuideButton: ''
      },
      loading: false,
      rules: {
        introductionYearRemark: [
          {
            required: true,
            message: '请输入近1年转介绍',
            trigger: 'blur'
          }
        ],
        consumeYearRemark: [
          {
            required: true,
            message: '请输入近1年消费',
            trigger: 'blur'
          }
        ],
        dynamicsYearRemark: [
          {
            required: true,
            message: '请输入近1年动态',
            trigger: 'blur'
          }
        ],
        dynamicsMonthRemark: [
          {
            required: true,
            message: '请输入近30天动态',
            trigger: 'blur'
          }
        ],
        potentialFansRemark: [
          {
            required: true,
            message: '请输入潜在铁粉列表缺省页-文案',
            trigger: 'blur'
          }
        ],
        potentialFansGuideTitle: [
          {
            required: true,
            message: '请输入潜在铁粉列表缺省页-引导语标题',
            trigger: 'blur'
          }
        ],
        potentialFansGuideContent: [
          {
            required: true,
            message: '请输入潜在铁粉列表缺省页-引导语内容',
            trigger: 'blur'
          }
        ],
        stanRemark: [
          {
            required: true,
            message: '请输入铁粉列表缺省页-文案',
            trigger: 'blur'
          }
        ],
        stanGuideTitle: [
          {
            required: true,
            message: '请输入铁粉列表缺省页-引导语标题',
            trigger: 'blur'
          }
        ],
        stanGuideContent: [
          {
            required: true,
            message: '请输入铁粉列表缺省页-引导语内容',
            trigger: 'blur'
          }
        ],
        stanGuideButton: [
          {
            required: true,
            message: '请输入铁粉列表缺省页-按钮名称',
            trigger: 'blur'
          }
        ]
      }
    };
  },
  computed: {
    visible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getData();
        }
      }
    }
  },
  methods: {
    async getData() {
      try {
        this.loading = true;
        const res = await this.$http.get('/teacher/fans/copywritingConfig');
        if (res.ok && res.body) {
          this.form = {
            taskTypeConfigId: res.body.id || null,
            introductionYearRemark: res.body.introductionYearRemark || '',
            consumeYearRemark: res.body.consumeYearRemark || '',
            dynamicsYearRemark: res.body.dynamicsYearRemark || '',
            dynamicsMonthRemark: res.body.dynamicsMonthRemark || '',
            potentialFansRemark: res.body.potentialFansRemark || '',
            potentialFansGuideTitle: res.body.potentialFansGuideTitle || '',
            potentialFansGuideContent: res.body.potentialFansGuideContent || '',
            stanRemark: res.body.stanRemark || '',
            stanGuideTitle: res.body.stanGuideTitle || '',
            stanGuideContent: res.body.stanGuideContent || '',
            stanGuideButton: res.body.stanGuideButton || ''
          };
        }
      } catch (error) {
        console.error('获取文案配置失败:', error);
      } finally {
        this.loading = false;
      }
    },
    async onSave() {
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = { ...this.form };

            const res = await this.$http({
              method: 'post',
              url: '/teacher/fans/copywritingConfig/edit',
              data: params,
              json: true
            });

            if (res.ok) {
              this.$message.success('保存成功');
              this.visible = false;
              this.$emit('refresh');
            } else {
              this.$message.error(res.msg || '保存失败');
            }
          } catch (error) {
            console.error('保存文案配置失败:', error);
            this.$message.error('保存失败，请重试');
          } finally {
            this.loading = false;
          }
        }
      });
    }
  }
};
</script>
<style scoped>
::v-deep .el-textarea .el-input__count {
  background: transparent;
  right: 15px;
}
::v-deep .el-form-item__label{
  line-height: 1.5;
}
</style>
