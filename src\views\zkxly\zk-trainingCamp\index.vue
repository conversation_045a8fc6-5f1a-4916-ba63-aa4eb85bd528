<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="130px"
      @submit.native.prevent="search"
    >
      <el-form-item label="训练营名称:" prop="trainCampName">
        <el-input v-model="form.trainCampName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="训练营类型:" style="vertical-align: middle">
        <el-col :span="11">
          <el-form-item prop="trainCampLearnType" style="width: 100%">
            <el-select
              v-model="form.trainCampLearnType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in $dictJson['recruitType']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="line" :span="2">-</el-col>
        <el-col :span="11">
          <el-form-item prop="trainCampType" style="width: 100%">
            <el-select
              v-model="form.trainCampType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in $dictJson['trainCampType']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-form-item>

      <el-form-item label="是否启用:" prop="isAllow" placeholder="请选择">
        <el-select v-model="form.isAllow" clearable>
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="开始日期:" prop="enrollStartTime">
        <el-date-picker
          v-model="form.enrollStartTime"
          type="date"
          placeholder="选择日期"
          clearable
        />
      </el-form-item>

      <el-form-item label="结束日期:" prop="enrollEndTime">
        <el-date-picker
          v-model="form.enrollEndTime"
          type="date"
          placeholder="选择日期"
          :picker-options="pickerEndDate"
          clearable
        />
      </el-form-item>

      <el-form-item label="创建人:" prop="createUser">
        <el-input v-model="form.createUser" placeholder="请输入" clearable />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="search('reset')"
        />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class="yz-table-btnbox">
      <el-button
        type="primary"
        icon="el-icon-upload2"
        size="mini"
        @click="handleExport"
      >
        Excel 导出
      </el-button>

      <el-popover v-model="copyTipShow">
        <div style="text-align: center; margin-bottom: 10px">
          <i class="el-icon-question" style="color: #ff9900" />
          确认复制 {{ selection.length }} 个训练营？
        </div>
        <div style="text-align: center">
          <el-button size="mini" plain @click="copyTipShow = false">
            取消
          </el-button>
          <el-button type="primary" size="mini" @click="handleCopy">
            确定
          </el-button>
        </div>
        <el-button
          slot="reference"
          type="primary"
          size="mini"
          style="margin: 0 10px"
        >
          复制训练营
        </el-button>
      </el-popover>

      <el-button type="primary" size="mini" @click="handleDialog()">
        新增训练营
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="loading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      max-height="680px"
      :data="tableData"
      @selection-change="selection = $event"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column type="index" width="55" label="序号" align="center" />
      <el-table-column label="训练营类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.trainCampLearnType | recruitTypeMethod }}—{{
            scope.row.trainCampType | trainCampTypeMethod
          }}
        </template>
      </el-table-column>
      <el-table-column
        width="150"
        prop="trainCampName"
        label="训练营名称"
        align="center"
      >
        <template slot-scope="{ row }">
          <div
            :style="{
              color: row.trainCampName.endsWith('副本') ? 'red' : '',
            }"
          >
            {{ row.trainCampName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="80"
        prop="createUser"
        label="创建人"
        align="center"
      />
      <el-table-column width="80" label="关卡管理" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="levelEdit(scope.row, 1)"
          >编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column width="80" label="通关礼品" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="levelEdit(scope.row, 2)"
          >编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="activityDays"
        width="80"
        label="活动天数"
        align="center"
      />
      <el-table-column prop="enrollStartTime" label="开始日期" align="center" />
      <el-table-column prop="enrollEndTime" label="结束日期" align="center" />
      <el-table-column width="80" label="目标学员" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="levelEdit(scope.row, 3)"
          >添加学员</el-button>
        </template>
      </el-table-column>
      <el-table-column width="100" label="目标人数" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            @click="levelEdit(scope.row, 4)"
          >{{ scope.row.joinPersonNum }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        label="开启人数"
        width="80"
        align="center"
        prop="openPersonNum"
      />
      <el-table-column
        label="通关人数"
        width="80"
        align="center"
        prop="completedPersonNum"
      />
      <el-table-column
        label="通关率"
        width="80"
        align="center"
        prop="passingRate"
      />
      <el-table-column label="状态" width="65" align="center">
        <template slot-scope="{ row }">
          <el-tag
            v-if="row.isAllow == 0"
            type="danger"
            effect="plain"
            size="small"
          >
            禁用
          </el-tag>
          <el-tag v-else type="success" effect="plain" size="small">
            启用
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <div class="yz-button-area">
            <el-popover v-model="row.showForbid" placement="top">
              <div style="text-align: center; margin-bottom: 10px">
                <i class="el-icon-question" style="color: #ff9900" />
                {{ row.isAllow == 0 ? "是否启用" : "是否禁用" }}？
              </div>
              <div
                v-if="row.isAllow != 0"
                style="color: red; width: 220px; margin-bottom: 10px"
              >
                禁用后，训练营对所有目标学员不可见，请确认后再操作！
              </div>
              <div style="text-align: center">
                <el-button size="mini" plain @click="row.showForbid = false">
                  取消
                </el-button>
                <el-button
                  type="primary"
                  size="mini"
                  @click="handleStatus(row)"
                >
                  确定
                </el-button>
              </div>
              <el-button
                slot="reference"
                type="text"
                size="small"
                style="margin-right: 10px"
              >
                {{ row.isAllow == 0 ? "启用" : "禁用" }}
              </el-button>
            </el-popover>
            <el-button type="text" size="small" @click="handleDialog(row.id)">
              编辑
            </el-button>
            <el-button type="text" size="small" @click="handleDownload(row)">
              下载闯关情况
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getCampList"
      />
    </div>
    <addTrainingCamp
      :visible.sync="trainingCampShow"
      :type="type"
      :editId="editId"
      @fresh="getCampList"
    />
  </div>
</template>

<script>
import moment from 'moment';
import { SplicingParams } from '@/utils';
import { api } from '@/api';
import addTrainingCamp from './addTrainingCamp';
import { downFile2 } from '@/utils/downFile';
import { downUri } from '@/config/request';

export default {
  components: { addTrainingCamp },
  data() {
    return {
      loading: true,
      createUser: '',
      form: {
        trainCampName: '',
        trainCampLearnType: '',
        trainCampType: '',
        enrollStartTime: '',
        enrollEndTime: '',
        isAllow: '',
        createUser: ''
      },
      copyTipShow: false,
      tableData: [],
      selection: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      levelShoe: false,
      trainingCampShow: false,
      type: '',
      editId: '',
      pickerEndDate: {
        disabledDate: (time) => {
          const beginDateVal = this.form.enrollStartTime;
          if (beginDateVal) {
            return (
              time.getTime() <
              new Date(beginDateVal).getTime() + 1 * 24 * 60 * 60 * 1000
            );
          }
        }
      }
    };
  },
  watch: {
    copyTipShow(v) {
      if (!this.selection.length && v) {
        this.$message.warning('请至少选中一个训练营！');
        setTimeout(() => {
          this.copyTipShow = false;
        }, 0);
      }
    },
    selection(v) {
      if (this.copyTipShow && !v.length) {
        this.copyTipShow = false;
      }
    }
  },
  async created() {
    await this.getCurrUser();
    this.getCampList();
  },
  activated() {
    if (!this.loading) {
      this.getCampList();
    }
  },
  methods: {
    // 查询当前创建人
    async getCurrUser() {
      try {
        const res = await this.$http.post('/newTrainCamp/getCurrentUser');
        if (res.code === '00') {
          this.form.createUser = res.body;
          this.createUser = res.body;
        }
      } catch (error) {
        console.error(error);
      }
    },
    // table路由跳转
    levelEdit(row, num) {
      if (num === 1) {
        this.$router.push({
          path: `/levelManagement/index?id=${row.id}&${
            row.openPersonNum > 0 ? 'on=1&' : ''
          }only=1`
        });
      } else if (num === 2) {
        this.$router.push({
          path: `/customsClearanceGift/index?id=${row.id}&only=1`
        });
      } else if (num === 3) {
        this.$router.push({
          path: `/addStudent/index?id=${row.id}&code=TC_CODE&trainCampType=${row.trainCampType}&trainCampLearnType=${row.trainCampLearnType}&only=1`
        });
      } else {
        this.$router.push({
          path: `/viewTargetStudents/index?id=${row.id}&only=1`
        });
      }
    },
    // 查询
    search(type) {
      if (type === 'reset') {
        this.$refs['searchForm'].resetFields();
      }
      this.pagination.page = 1;
      this.getCampList();
    },
    // 列表数据
    getCampList() {
      this.loading = true;
      const data = {
        ...this.form,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };

      this.$post('campList', data, { json: true })
        .then((res) => {
          if (res.ok) {
            this.tableData = res.body.data.map((v) => ({
              ...v,
              showForbid: false
            }));
            this.pagination.total = res.body.recordsTotal;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 禁用 | 启用
    handleStatus(row) {
      this.$http({
        method: 'post',
        url: `/newTrainCamp/updateCampStatus/${row.id}/${
          row.isAllow === 1 ? 0 : 1
        }`
      }).then((res) => {
        row.showForbid = false;
        if (res.ok) {
          this.$message.success(row.isAllow === 1 ? '禁用成功' : '启用成功');
          this.getCampList();
        }
      });
    },
    // 编辑
    handleDialog(id = '') {
      this.type = id ? 'edit' : 'add';
      this.editId = String(id);
      this.trainingCampShow = true;
    },
    // 导出 Excel
    handleExport() {
      if (!this.selection.length) {
        return this.$message.warning('请至少选中一个训练营！');
      }
      window.location.href =
        downUri +
        '/newTrainCamp/exportAll?trainCampIds=' +
        this.selection.map((v) => v.id).join();
    },
    // 复制训练营
    async handleCopy() {
      if (!this.selection.length) {
        return this.$message.warning('请至少选中一个训练营！');
      }
      if (this.selection.length > 10) {
        return this.$message.warning('每次最多复制 10 条！');
      }
      try {
        const res = await this.$http({
          method: 'post',
          url: '/newTrainCamp/copyTrainCamp',
          data: this.selection.map((v) => v.id),
          json: true
        });
        if (res.code === '00') {
          this.$message.success('复制成功');
          this.getCampList();
        }
      } catch (error) {
        console.error(error);
      }
      this.copyTipShow = false;
    },
    // 下载单个训练营闯关情况
    handleDownload(row) {
      const fName = `${moment().format('YYYYMMDDhhmmss')}—[${
        row.trainCampName
      }]数据统计表.xlsx`;
      downFile2(
        `${api['exportAll']}?${SplicingParams({ trainCampId: row.id })}`,
        fName
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 10px;
}
.line {
  text-align: center;
}
.textGreen {
  display: inline-block;
  padding: 5px 15px;
  font-size: 12px;
  border-radius: 5px;
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}
.textRen {
  display: inline-block;
  padding: 5px 15px;
  font-size: 12px;
  border-radius: 5px;
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}
</style>
