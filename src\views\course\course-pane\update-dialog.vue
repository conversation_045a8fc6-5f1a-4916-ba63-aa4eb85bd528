<template>
  <common-dialog
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='课程编码' prop='courseId'>
          <el-input v-model="form.courseId" disabled placeholder="自动生成" />
        </el-form-item>

        <el-form-item label='供应渠道' prop='courseSupplyChannelId'>
          <remote-search-selects
            ref="remoteSearch"
            v-model="form.courseSupplyChannelId"
            :disabled="courseId!=null"
            :props="{apiName: 'getSupplierSelects', value: 'channelId', label: 'channelName', query: 'sName'}"
            :param="{sName:''}"
            :default-option="defaultSelected"
            @changeVal="setSupplychannelName"
          />
        </el-form-item>

        <el-form-item label='上课方式' prop='courseChannelCode'>
          <el-select
            v-model="form.courseChannelCode"
            :disabled="courseId!=null"
            placeholder="请选择"
            @change="resetMappingId"
          >
            <el-option
              v-for="(item, index) in $localDict['courseChannelCode']"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.courseChannelCode==='KHW'" label="关联课程" prop="mappingName">
          <el-input key="khw" v-model="form.mappingName" v-width="250" disabled />
          <el-button class="padding-10" type="primary" @click="courseDialogShow=true">选择课程</el-button>
        </el-form-item>

        <el-form-item v-if="form.courseChannelCode==='KSB'" label="关联课程" prop="mappingName">
          <el-input key="khw" v-model="form.mappingName" v-width="250" disabled />
          <el-button class="padding-10" type="primary" @click="kdVisible=true">选择课程</el-button>
        </el-form-item>

        <el-form-item v-if="form.courseChannelCode==='YIZHI'" label="关联课程" prop="mappingName">
          <el-input key="khw" v-model="form.mappingName" v-width="250" disabled />
          <el-button class="padding-10" type="primary" @click="yizhiVisible=true">选择课程</el-button>
        </el-form-item>

        <el-form-item label='课程名称' prop='courseName'>
          <el-input v-model="form.courseName" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>

        <el-form-item label='课程排序' prop='courseSort'>
          <el-input-number
            v-model="form.courseSort"
            class="yz-input-number"
            :min="0"
            :max="1000000000"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label='服务有效期' prop='serviceValidDay'>
          <el-input-number
            v-model="form.serviceValidDay"
            v-width="150"
            :min="1"
            :max="1000000000"
            class="yz-input-number day padding-left"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label="已购用户是否引导加企微" prop='openLink'>
          <el-select v-model="form.openLink" placeholder="请选择" @change="handleOpenLink">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.openLink === 1" label='小程序跳转链接' prop='linkUrl'>
          <el-input
            v-model="form.linkUrl"
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='课程图片' prop='uploadCourseFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='课程简介' prop='courseIntroduce'>
          <el-input
            v-model="form.courseIntroduce"
            rows="5"
            type="textarea"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='阶段是否可见' prop='display'>
          <el-radio-group v-model="form.display">
            <el-radio :label="1">可见</el-radio>
            <el-radio :label="2">不可见</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label='进群二维码' prop="uploadCodeFile.fileUrl">
          <upload-file
            :max-limit="1"
            :file-list='qrCode'
            @remove="handleQrCodeRemoveImg"
            @success="qrCodeUploadSuccess"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='allow'>
          <el-radio-group v-model="form.allow">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="考证须知">
          <u-editor :width="500" :height="600" @ready="editorReady" />
        </el-form-item>

      </el-form>
    </div>

    <common-dialog
      title="选择课程"
      width="650px"
      :visible.sync='courseDialogShow'
      @open="initCourseDialog"
      @close='courseDialogShowClose'
    >
      <div class="dialog-main">
        <el-form
          ref='searchForm'
          size='mini'
          :model='searchForm'
          label-width='80px'
        >
          <el-form-item label='选择目录' prop='allow'>
            <el-select
              v-model="suject"
              filterable
              @change="handleSelection"
            >
              <el-option
                v-for="(item,index) in courseCatalog"
                :key="index"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
            <!--            <el-cascader-->
            <!--              class="cascader"-->
            <!--              placeholder="试试搜索课程名称"-->
            <!--              :options="courseCatalog"-->
            <!--              :props="{value:'id',label:'name',children:'children'}"-->
            <!--              filterable-->
            <!--              @change="handleSelection"-->
            <!--            />-->
          </el-form-item>
        </el-form>

        <div>
          <el-table
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            height="400px"
            header-cell-class-name='table-cell-header'
            :data="tableData"
          >
            <el-table-column prop="name" label="课程" align="center" />
            <el-table-column label="操作" align="center" width="150px">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="selectionCourse(scope.row)">选择</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!--        <div class="yz-table-pagination">-->
        <!--          <pagination-->
        <!--            :total='pagination.total'-->
        <!--            :page.sync="pagination.page"-->
        <!--            :limit.sync="pagination.limit"-->
        <!--            layout='prev, pager, next, sizes, total'-->
        <!--            @pagination="getKhwCourseList"-->
        <!--          />-->
        <!--        </div>-->

      </div>
    </common-dialog>

    <!-- 课诗宝选择课程-->
    <ksb-dialog :visible.sync="kdVisible" @selected="selectedKsbCourse" />
    <!--  易智课程 -->
    <yizhi-dialog :visible.sync="yizhiVisible" @selected="yizhiSelectionCourse" />

  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
import ksbDialog from './ksb-dialog';
import yizhiDialog from './yizhi-dialog';
import { positiveInteger } from '@/common/vali';
import UEditor from '@/components/UEditor';
export default {
  components: {
    // WangEditor,
    UEditor,
    ksbDialog,
    yizhiDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    courseId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      kdVisible: false,
      yizhiVisible: false,
      show: false,
      tableLoading: false,
      courseDialogShow: false,
      tableData: [],
      courseCatalog: [],
      defaultSelected: null,
      currentSelectionCourseType: [],
      searchForm: {
        name: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      form: {
        courseSupplyChannelId: '',
        courseSupplyChannelName: '',
        courseChannelCode: null, // 上课方式
        mappingId: null, // 课火网课程id
        courseName: '',
        mappingName: '', // 课火网名称
        courseSort: '',
        allow: 1,
        display: 1,
        courseId: '',
        serviceValidDay: undefined,
        // fileUrl: '',
        // isAdd: 0,
        courseIntroduce: '',
        // 群二维码
        uploadCodeFile: {
          fileUrl: '',
          isAdd: 0
        },
        uploadCourseFile: {
          fileUrl: '',
          isAdd: 0
        },
        openLink: 0,
        linkUrl: '',
        examInstructions: ''
      },
      originFileUrl: '',
      fileList: [],
      qrCode: [],
      rules: {
        courseName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        courseSupplyChannelId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        mappingName: [
          { required: true, message: '请选择课程', trigger: 'change' }
        ],
        courseSort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        display: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        allow: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        courseChannelCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        serviceValidDay: [
          { required: true, validator: positiveInteger, trigger: 'change' }
        ],
        linkUrl: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      },
      suject: '',
      editor: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    editorReady(editor) {
      this.editor = editor;
    },
    handleOpenLink(val) {
      if (!val) {
        this.form.linkUrl = '';
      }
    },
    handleQrCodeRemoveImg() {
      this.form.uploadCodeFile.fileUrl = '';
    },
    // 二维码进群
    qrCodeUploadSuccess({ response, file, fileList }) {
      this.form.uploadCodeFile.fileUrl = response;
      this.form.uploadCodeFile.isAdd = 1;
    },
    handleRemoveImg({ file, fileList }) {
      this.form.uploadCourseFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.uploadCourseFile.fileUrl = response;
      this.form.uploadCourseFile.isAdd = 1;
    },
    // 课诗宝，选中课程回调
    selectedKsbCourse(row) {
      this.form.mappingName = row.title;
      this.form.mappingId = row.id;
      this.kdVisible = false;
    },
    setSupplychannelName({ value, label, source }) {
      this.form.courseSupplyChannelName = source.channelName;
    },
    // 课火网选择课程弹窗
    initCourseDialog() {
      this.$post('getKHWExamTypeList')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.courseCatalog = body.data;
            console.log(body.data);
          }
        });
      // this.$post('getKhwCourseType').then(res => {
      //   const { fail, body } = res;
      //   if (!fail) {
      //     this.courseCatalog = this.getTreeData(body);
      //   }
      // });
    },
    resetMappingId(value) {
      this.form.mappingId = null;
      this.form.courseName = null;
      this.form.mappingName = '';
    },
    selectionCourse(row) {
      this.form.mappingName = row.name;
      this.form.mappingId = row.id;
      this.courseDialogShow = false;
    },
    yizhiSelectionCourse(row) {
      this.form.mappingName = row.packageName;
      this.form.mappingId = row.id;
      this.yizhiVisible = false;
    },
    handleSelection(value) {
      for (let i = 0; i < this.courseCatalog.length; i++) {
        const item = this.courseCatalog[i];
        if (item.id === value) {
          this.tableData = item.child;
        }
      }
    },
    // handleSelection(value) {
    //   this.currentSelectionCourseType = value;
    //   this.pagination.page = 1;
    //   this.getKhwCourseList();
    // },
    // getKhwCourseList() {
    //   this.tableLoading = true;
    //   const data = {
    //     oneClassifyId: this.currentSelectionCourseType[0],
    //     twoClassifyId: this.currentSelectionCourseType[1],
    //     pageNo: this.pagination.page,
    //     pageSize: this.pagination.limit
    //   };
    //   this.$post('getKhwCourseList', data).then(res => {
    //     const { fail, body } = res;
    //     if (!fail) {
    //       this.tableData = body.datas;
    //       this.pagination.total = Number(body.totalCount);
    //       this.tableLoading = false;
    //     }
    //   });
    // },
    // getTreeData(data) {
    //   for (let i = 0; i < data.length; i++) {
    //     if (data[i].children.length < 1) {
    //       // children若为空数组，则将children设为undefined
    //       data[i].children = undefined;
    //     } else {
    //       // children若不为空数组，则继续 递归调用 本方法
    //       this.getTreeData(data[i].children);
    //     }
    //   }
    //   return data;
    // },
    getBaseInfo() {
      const data = { courseId: this.courseId };
      this.$post('getCourseSingle', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.form.mappingName = body.mappingName;
          this.form.courseName = body.courseName;
          this.form.courseSort = body.courseSort;
          this.form.allow = body.allow;
          this.form.display = body.display;
          this.form.courseId = body.courseId;
          this.form.uploadCourseFile.fileUrl = body.coursePic;
          this.form.uploadCodeFile.fileUrl = body.qrCode;
          this.form.courseIntroduce = body.courseIntroduce;
          this.form.courseChannelCode = body.courseChannelCode;
          this.form.serviceValidDay = body.serviceValidDay;
          this.form.mappingId = body.mappingId;
          this.form.courseSupplyChannelId = body.courseSupplyChannelId;
          this.defaultSelected = {
            channelId: body.courseSupplyChannelId,
            channelName: body.courseSupplyChannelName
          };
          this.form.openLink = body.openLink || 0;
          this.form.linkUrl = body.linkUrl;
          this.form.examInstructions = body.examInstructions;

          let timer = setInterval(() => {
            if (this.editor) {
              clearInterval(timer);
              timer = null;
              this.editor.setContent(body.examInstructions || '');
            }
          }, 300);

          if (body.qrCode) {
            this.qrCode.push({ url: ossUri + body.qrCode });
          }
          if (body.coursePic) {
            this.fileList.push({ url: ossUri + body.coursePic });
          }
        }
      });
    },
    open() {
      if (this.courseId) {
        this.getBaseInfo();
      }
    },
    add() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = JSON.parse(JSON.stringify(this.form));
          data.examInstructions = this.editor.getContent();

          this.$post('addCourse', data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              // this.show = false;
              this.$emit('update:visible', false);
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              // this.$parent.search(0);
              this.$parent.getTableList();
            }
          });
        }
      });
    },
    edit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = JSON.parse(JSON.stringify(this.form));
          data.examInstructions = this.editor.getContent();
          data.courseId = this.courseId;
          this.$post('editCourse', data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              // this.show = false;
              this.$emit('update:visible', false);
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              // this.$parent.search(0);
              this.$parent.getTableList();
            }
          });
        }
      });
    },
    submit() {
      if (this.courseId) {
        this.edit();
      } else {
        this.add();
      }
    },
    close() {
      // this.$refs['form'].resetFields();
      Object.assign(this.$data, this.$options.data());
      this.fileList = [];
      this.form.courseName = '';
      this.suject = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    courseDialogShowClose() {
      this.courseDialogShow = false;
      this.suject = '';
      this.tableData = [];
      this.$refs['searchForm'].resetFields();
      this.pagination.page = 1;
      this.pagination.total = 0;
    }
  }
};
</script>
<style lang="scss" scoped>
.hide{
    ::v-deep .el-upload--picture-card{
      display: none;
    }
}
.padding-10{
  margin:0 10px;
}
.cascader {
  width: 100%;
}
</style>
