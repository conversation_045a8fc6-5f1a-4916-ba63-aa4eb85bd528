<template>
  <div class="yz-base-container">
    <el-radio-group v-model="activeTab" style="margin-bottom: 10px" size="small">
      <el-radio-button label="join">加入社团审批</el-radio-button>
      <el-radio-button label="create">创建社团审批</el-radio-button>
      <el-radio-button label="expert">成为达人审批</el-radio-button>
    </el-radio-group>

    <transition name="fade" mode="out-in">
      <component :is="com" />
    </transition>
  </div>
</template>

<script>
import JoinCom from './components/join';
import CreateCom from './components/create';
import ExpertCom from './components/expert';
export default {
  components: {
    JoinCom,
    CreateCom,
    ExpertCom
  },
  data() {
    return {
      activeTab: 'join',
      com: JoinCom
    };
  },
  watch: {
    activeTab(val) {
      switch (val) {
        case 'join':
          this.com = JoinCom;
          break;
        case 'create':
          this.com = CreateCom;
          break;
        case 'expert':
          this.com = ExpertCom;
          break;
      }
    }
  }
};
</script>
