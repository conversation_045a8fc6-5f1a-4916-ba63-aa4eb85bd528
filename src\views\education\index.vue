<template>
  <div class="yz-base-container">
    <!-- 列表组件，传入订单参数 -->
    <list-component :orderParams="orderParams" />
  </div>
</template>

<script>
import listComponent from "./list-component";

export default {
  components: {
    listComponent, // 导入列表组件
  },
  data() {
    return {
      orderParams: "", // 订单参数，用于传递给列表组件进行查询过滤
    };
  },
  methods: {
    /**
     * 点击处理函数（预留，暂未使用）
     */
    handleClick() {},

    /**
     * 更新订单参数
     * @param {string} val - 新的订单参数值
     * 更新后会触发list-component组件中的watch监听，从而更新列表数据
     */
    updateOrder(val) {
      this.orderParams = val;
    },
  },
};
</script>

<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
