<template>
  <div>
    <common-dialog
      top="-100px"
      show-footer
      width="60%"
      confirmText="保存"
      :title="row ? '编辑' : '新增用户'"
      :visible.sync="show"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form ref="form" class="form" size="mini" :model="form" label-width="150px" :rules="rules">

          <el-form-item label="远智编号" prop="yzCode">
            <el-select
              v-model="form.yzCode"
              filterable
              remote
              reserve-keyword
              placeholder="请输入远智编号"
              :remote-method="yzCodeRemoteMethod"
              :loading="yzCodeLoading"
              @change="yzCodeChange"
            >
              <el-option
                v-for="item in yzCodeOptions"
                :key="item.userId"
                :label="item.userName"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="姓名" prop="userName">
            <el-input :value="form.userName" disabled />
          </el-form-item>

          <el-form-item label="公益时长" prop="welfareDurationDisplay">
            <el-input-number
              v-model="form.welfareDurationDisplay"
              placeholder="请输入公益时长"
              :min="0"
              :max="1000000"
              :precision="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label="月公益时长" prop="monthWelfareDurationDisplay">
            <el-input-number
              v-model="form.monthWelfareDurationDisplay"
              placeholder="请输入月公益时长"
              :min="0"
              :max="1000000"
              :precision="0"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label="公益次数" prop="welfareNum">
            <el-input-number
              v-model.number="form.welfareNum"
              class="welfareNum"
              :min="0"
              :max="1000000"
              :precision="0"
              :controls="false"
              placeholder="请输入公益次数"
            />
          </el-form-item>

          <el-form-item label="加入时间" prop="joinTime">
            <el-date-picker
              v-model="form.joinTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择加入时间"
            />
          </el-form-item>

          <el-form-item label="展示图片" prop="userImg">
            <upload-file
              :max-limit="1"
              accept="image/png,image/jpeg,image/gif"
              tip="建议尺寸 390px * 390px，比例 1:1 即可； 图片大小限制 2M 内"
              :file-list="form.userImg"
              @remove="form.userImg = []"
              @success="onUserImgSucc"
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      yzCodeLoading: false,
      yzCodeOptions: [],
      form: {
        yzCode: '',
        welfareDurationDisplay: undefined,
        monthWelfareDurationDisplay: undefined,
        welfareNum: undefined,
        joinTime: '',
        userImg: []
      },
      rules: {
        yzCode: [{ required: true, message: '请输入远智编号', trigger: 'blur' }],
        welfareDurationDisplay: [{ required: true, message: '请输入公益时长', trigger: 'blur' }],
        monthWelfareDurationDisplay: [{ required: true, message: '请输入月公益时长', trigger: 'blur' }],
        welfareNum: [{ required: true, message: '请输入公益次数', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 展示图片上传成功
    onUserImgSucc({ fileList }) {
      this.form.userImg = fileList;
      this.$refs.form.validateField('userImg');
    },
    // 弹窗打开时
    open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({
          ...this.row,
          monthWelfareDurationDisplay: this.row.monthWelfareDuration,
          welfareDurationDisplay: this.row.welfareDuration
        }));
        this.form.userImg = this.row.userImg ? [{ url: this.row.userImg, response: this.row.userImg }] : [];
      }
    },
    // 用户下拉数据
    async yzCodeRemoteMethod(query) {
      if (!query) return;
      try {
        this.yzCodeLoading = true;
        const { code, body } = await this.$http({
          method: 'get',
          url: `/welfareUserInfo/getByYzCode/${query}`
        });
        if (code !== '00') return;
        this.yzCodeOptions = body && body.userName ? [body] : [];
      } finally {
        this.yzCodeLoading = false;
      }
    },
    // 选择用户
    yzCodeChange(row) {
      this.form.userId = row.userId;
      this.form.yzCode = row.yzCode;
      this.form.userName = row.userName;
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row?.id,
              yzCode: this.row?.id ? undefined : this.form.yzCode,
              userId: this.form.userId,
              userName: this.form.userName,
              welfareDurationDisplay: this.form.welfareDurationDisplay,
              monthWelfareDurationDisplay: this.form.monthWelfareDurationDisplay,
              welfareNum: this.form.welfareNum,
              joinTime: this.form?.joinTime,
              userImg: this.form.userImg?.[0]?.response
            };
            const url = this.row ? '/welfareUserInfo/updateById' : '/welfareUserInfo/add';
            const { code } = await this.$http({
              method: 'post',
              url,
              json: true,
              data: params
            });
            if (code !== '00') return;
            this.$message.success('保存成功');
            this.show = false;
            this.$emit('confirm');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input-number {
  width: 100%;

  .el-input__inner {
    text-align: left;
    position: relative;
    padding-right: 40px;
  }

  &::after {
    content: '分钟';
    position: absolute;
    right: 10px;
    top: 2px;
  }

  &.welfareNum::after{
    content: '次'
  }
}
</style>
