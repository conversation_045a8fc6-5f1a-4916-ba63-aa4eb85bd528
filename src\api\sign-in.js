// 假数据
const mockData = {
  // 签到列表数据
  signInList: [
    {
      yzCode: 'YZ_1033010',
      realName: '欧阳静',
      nickname: '5qyn6Zlz552/6JCB',
      phone: '137****5687',
      signTime: '2025-06-24 09:13:08',
      rewardPoints: 20,
      continuousDays: 3
    },
    {
      yzCode: 'YZ_1033011',
      realName: '张三',
      nickname: 'zhangsan123',
      phone: '138****5678',
      signTime: '2025-06-24 09:12:08',
      rewardPoints: 30,
      continuousDays: 5
    },
    {
      yzCode: 'YZ_1033012',
      realName: '李四',
      nickname: 'lisi456',
      phone: '139****5679',
      signTime: '2025-06-24 09:11:08',
      rewardPoints: 40,
      continuousDays: 7
    }
  ],

  // 签到规则配置
  signInRules: {
    ruleContent: '<p>1. 每日签到可获得智米奖励</p><p>2. 连续签到可获得额外奖励</p><p>3. 中断签到将重新计算连续天数</p>',
    remindText: '记得每天签到领智米哦',
    rewardList: [
      { days: 1, points: 55 },
      { days: 2, points: 0 },
      { days: 3, points: 0 },
      { days: 4, points: 0 },
      { days: 5, points: 0 },
      { days: 6, points: 0 },
      { days: 7, points: 0 }
    ],
    changeRecords: [
      {
        editor: '郑正正',
        content: '连续签到1天: 奖励20智米\n连续签到2天: 奖励30智米',
        editTime: '2024-12-12 12:23:23'
      },
      {
        editor: '李大妈',
        content: '连续签到1天: 奖励20智米\n连续签到2天: 奖励30智米',
        editTime: '2024-10-12 12:23:23'
      }
    ]
  },

  // 智米配置列表
  smartRiceList: [
    {
      id: 1,
      icon: '',
      title: '遨游友好，赢豪礼',
      subtitle: '最高可得30000智米',
      jumpUrl: 'xxx',
      enabled: 1,
      weight: 9996
    },
    {
      id: 2,
      icon: '',
      title: '遨游友好，赢豪礼',
      subtitle: '最高可得30000智米',
      jumpUrl: 'xxx',
      enabled: 1,
      weight: 5555
    },
    {
      id: 3,
      icon: '',
      title: '遨游友好，赢豪礼',
      subtitle: '最高可得30000智米',
      jumpUrl: 'xxx',
      enabled: 1,
      weight: 3333
    }
  ]
};

// 获取签到列表
export function getSignInList(params) {
  return Promise.resolve({
    code: 200,
    data: {
      list: mockData.signInList,
      total: mockData.signInList.length
    }
  });
}

// 获取签到规则配置
export function getSignInRules() {
  return Promise.resolve({
    code: 200,
    data: mockData.signInRules
  });
}

// 更新签到规则配置
export function updateSignInRules(data) {
  return Promise.resolve({
    code: 200,
    message: '保存成功'
  });
}

// 获取智米配置列表
export function getSmartRiceList(params) {
  return Promise.resolve({
    code: 200,
    data: mockData.smartRiceList
  });
}

// 更新智米配置
export function updateSmartRice(data) {
  return Promise.resolve({
    code: 200,
    message: '更新成功'
  });
}

// 新增智米配置
export function addSmartRice(data) {
  return Promise.resolve({
    code: 200,
    message: '新增成功'
  });
}
