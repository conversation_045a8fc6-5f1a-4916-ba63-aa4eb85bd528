// 获取签到列表
export function getSignInList(params) {
  return {
    url: '/operate/sign/record/list',
    data: {
      pageNum: params.page || 1,
      pageSize: params.size || 10,
      realName: params.realName || '',
      yzCode: params.yzCode || '',
      mobile: params.phone || '',
      startTime: params.startDate || '',
      endTime: params.endDate || ''
    },
    // 数据映射函数
    mapResponse: (response) => {
      const { data } = response;
      if (data.code === '200') {
        // 数据字段映射
        const mappedList = data.data.list.map(item => ({
          yzCode: item.yzCode,
          realName: item.realName,
          nickname: item.nickname,
          phone: item.mobile,
          signTime: item.signDate,
          rewardPoints: item.rewardZhimi,
          continuousDays: item.cycleSignDays
        }));

        return {
          code: 200,
          data: {
            list: mappedList,
            total: data.data.recordsTotal
          }
        };
      } else {
        throw new Error(data.message || '获取签到列表失败');
      }
    }
  };
}

// 获取签到规则配置
export function getSignInRules() {
  return {
    // 需要调用两个接口
    configUrl: '/operate/sign/config/detail/1',
    rewardUrl: '/operate/sign/reward/all',
    // 数据映射函数
    mapResponse: (configResponse, rewardResponse) => {
      const configData = configResponse.data;
      const rewardData = rewardResponse.data;

      if (configData.code === '200' && rewardData.code === '200') {
        // 数据字段映射
        const rewardList = rewardData.data.map(item => ({
          days: item.signDays,
          points: item.rewardZhimi
        }));

        return {
          code: 200,
          data: {
            ruleContent: configData.body.signRuleDesc,
            remindText: configData.body.signTip,
            rewardList: rewardList,
            changeRecords: [] // API中没有变更记录，返回空数组
          }
        };
      } else {
        throw new Error('获取签到规则配置失败');
      }
    }
  };
}

// 更新签到规则配置
export function updateSignInRules(data) {
  return {
    // 需要调用两个接口
    configUrl: '/operate/sign/config/edit',
    configData: {
      id: 1, // 假设使用ID为1的配置
      signRuleDesc: data.ruleContent,
      signTip: data.remindText
    },
    rewardUrl: '/operate/sign/reward/batchUpdate',
    rewardData: {
      rewardList: data.rewardList.map(item => ({
        id: item.id || null,
        signDays: item.days,
        rewardZhimi: item.points
      })),
      operateContent: '批量更新签到奖励配置'
    },
    // 数据映射函数
    mapResponse: (configResponse, rewardResponse) => {
      if (configResponse.data.code === '200' && rewardResponse.data.code === '200') {
        return {
          code: 200,
          message: '保存成功'
        };
      } else {
        throw new Error('保存失败');
      }
    }
  };
}

// 获取智米配置列表
export function getSmartRiceList(params) {
  return {
    url: '/operate/sign/earn/list',
    data: {
      pageNum: params.page || 1,
      pageSize: params.size || 10,
      title: params.title || '',
      ifAllow: params.enabled || '',
      createUser: params.createUser || '',
      startTime: params.startTime || '',
      endTime: params.endTime || ''
    },
    // 数据映射函数
    mapResponse: (response) => {
      const { data } = response;
      if (data.code === '200') {
        // 数据字段映射
        const mappedList = data.data.list.map(item => ({
          id: item.id,
          icon: item.icon,
          title: item.title,
          subtitle: item.subTitle,
          jumpUrl: item.route,
          enabled: item.ifAllow,
          weight: parseInt(item.sort)
        }));

        return {
          code: 200,
          data: mappedList
        };
      } else {
        throw new Error(data.message || '获取智米配置列表失败');
      }
    }
  };
}

// 更新智米配置
export function updateSmartRice(data) {
  return {
    url: '/operate/sign/earn/edit',
    data: {
      id: data.id,
      title: data.title,
      subTitle: data.subtitle,
      icon: data.icon,
      route: data.jumpUrl,
      sort: data.weight.toString(),
      ifAllow: data.enabled
    },
    // 数据映射函数
    mapResponse: (response) => {
      if (response.data.code === '200') {
        return {
          code: 200,
          message: '更新成功'
        };
      } else {
        throw new Error(response.data.message || '更新失败');
      }
    }
  };
}

// 新增智米配置
export function addSmartRice(data) {
  return {
    url: '/operate/sign/earn/add',
    data: {
      title: data.title,
      subTitle: data.subtitle,
      icon: data.icon,
      route: data.jumpUrl,
      sort: data.weight.toString(),
      ifAllow: data.enabled
    },
    // 数据映射函数
    mapResponse: (response) => {
      if (response.data.code === '200') {
        return {
          code: 200,
          message: '新增成功'
        };
      } else {
        throw new Error(response.data.message || '新增失败');
      }
    }
  };
}
