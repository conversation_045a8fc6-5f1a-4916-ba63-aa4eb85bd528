<template>
  <el-radio-group v-model="conditionType" :disabled="disabled" class="vertical-radio-group" @change="handleConditionTypeChange">
    <div class="radio-item">
      <el-radio :label="1">首次跑步发帖</el-radio>
    </div>

    <div class="radio-item">
      <el-radio :label="2">单次跑量≥</el-radio>
      <el-input-number
        v-if="conditionType === 2"
        v-model="runData.distance"
        :disabled="disabled"
        :precision="2"
        :min="0.01"
        :controls="false"
        placeholder="请输入单次跑量"
        style="width: 180px;"
      />
      <span v-if="conditionType === 2">公里数</span>
    </div>

    <div class="radio-item">
      <el-radio :label="3">累计跑量≥</el-radio>
      <el-input-number
        v-if="conditionType === 3"
        v-model="runData.distanceTotal"
        :disabled="disabled"
        :precision="2"
        :min="0.01"
        :controls="false"
        placeholder="请输入累计跑量"
        style="width: 180px;"
      />
      <span v-if="conditionType === 3">公里数</span>
    </div>

    <div class="radio-item">
      <el-radio :label="4">累计跑步≥</el-radio>
      <el-input-number
        v-if="conditionType === 4"
        v-model="runData.dayTotal"
        :disabled="disabled"
        :precision="0"
        :min="1"
        :controls="false"
        placeholder="请输入累计跑步"
        style="width: 180px;"
      />
      <span v-if="conditionType === 4">天</span>
    </div>

    <div class="radio-item">
      <el-radio :label="5">跑步天数排行榜TOP10</el-radio>
      <el-date-picker
        v-if="conditionType === 5"
        v-model="runData.dayTop"
        :disabled="disabled"
        value-format="yyyy-MM"
        type="month"
        format="yyyy-MM"
        placeholder="选择年月"
        :picker-options="{
          disabledDate(time) {
            const current = new Date();
            return time.getFullYear() < current.getFullYear() ||
              (time.getFullYear() === current.getFullYear() && time.getMonth() < current.getMonth());
          }
        }"
        style="width: 180px;"
      />
    </div>

    <div class="radio-item">
      <el-radio :label="6">月度跑量排行榜TOP10</el-radio>
      <el-date-picker
        v-if="conditionType === 6"
        v-model="runData.sumTop"
        :disabled="disabled"
        value-format="yyyy-MM"
        type="month"
        format="yyyy-MM"
        placeholder="选择年月"
        :picker-options="pickerOptions"
        style="width: 180px;"
      />
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'MedalCondition',
  props: {
    runData: {
      type: Object,
      default: () => ({
        first: undefined, // 首次跑步发帖
        distance: undefined, // 单次跑量公里数
        distanceTotal: undefined, // 累计跑量公里数
        dayTotal: undefined, // 累计跑步天
        dayTop: undefined, // 跑步天数排行榜TOP10日期
        sumTop: undefined // 月度跑量排行榜TOP10日期
      })
    },
    disabled: {
      type: Boolean,
      default: false
    },
    pickerOptions: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      conditionType: null
    };
  },
  watch: {
    runData: {
      handler(val) {
        // 当外部 runData 变化时，不需要做特殊处理，因为计算属性会自动更新
      },
      deep: true
    }
  },
  mounted() {
    if (this.runData.first) {
      this.conditionType = 1;
    } else if (this.runData.distance) {
      this.conditionType = 2;
    } else if (this.runData.distanceTotal) {
      this.conditionType = 3;
    } else if (this.runData.dayTotal) {
      this.conditionType = 4;
    } else if (this.runData.dayTop) {
      this.conditionType = 5;
    } else if (this.runData.sumTop) {
      this.conditionType = 6;
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.conditionType) {
        return { valid: false, message: '请选择获取条件' };
      } else if (this.conditionType === 2 && !this.runData.distance) {
        return { valid: false, message: '请输入单次跑量' };
      } else if (this.conditionType === 3 && !this.runData.distanceTotal) {
        return { valid: false, message: '请输入累计跑量' };
      } else if (this.conditionType === 4 && !this.runData.dayTotal) {
        return { valid: false, message: '请输入累计跑步天数' };
      } else if (this.conditionType === 5 && !this.runData.dayTop) {
        return { valid: false, message: '请选择跑步天数排行榜年月' };
      } else if (this.conditionType === 6 && !this.runData.sumTop) {
        return { valid: false, message: '请选择月度跑量排行榜年月' };
      }
      return { valid: true };
    },

    // 处理条件类型变化
    handleConditionTypeChange(val) {
      // 重置所有属性为undefined
      Object.keys(this.runData).forEach(key => {
        this.runData[key] = undefined;
      });

      // 如果选择了首次跑步发帖，设置first为true
      if (val === 1) {
        this.runData.first = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }

    span {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}
</style>
