
import FileSaver from 'file-saver';
import axios from 'axios';
import { Message } from 'element-ui';

/**
 * 下载文件
 * @param url 文件的网络地址
 * @param fileName 文件名
 */
const downFile = (url, fileName) => {
  const config = {
    method: 'GET',
    responseType: 'blob'
  };
  axios(url, config).then(res => {
    FileSaver.saveAs(res.data, fileName || '文件'); // 利用file-saver保存文件,注意：第一个参数是blod二进制
  }).catch(error => {
    console.log(error);
  });
};

/**
 * 下载文件
 *
 * @param {*} url
 * @param {*} fileName
 */
export const downFile2 = (url, fileName) => {
  axios
    .get(url, { responseType: 'blob' })
    .then((res) => {
      if (res.data && res.data.size === 0) {
        // 权限判断：接口返回空，说明没有权限。（即便没有数据也会有表头，文件大小不会为空）
        Message.error('无操作权限！');
        return;
      }
      FileSaver.saveAs(res.data, fileName || '文件');
      Message.success('已创建下载任务，请留意浏览器下载列表');
    })
    .catch((error) => {
      console.error('请求失败:', error);
      Message.error('下载失败，请稍后再试~');
    });
};

export default downFile;

// url需要传入完整地址
// 注意 如果url路径上没有文件后缀  则fileName要带上文件格式 xxx.xlsx  xxx.jpg
// 如:downFile(ossUri + file, fileName);

