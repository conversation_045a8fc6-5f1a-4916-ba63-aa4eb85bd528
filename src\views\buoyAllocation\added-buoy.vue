<template>
  <common-dialog
    v-loading="tableLoading"
    is-full
    :showFooter="true"
    :visible.sync="visible"
    class="buoys"
    @open="initForm"
    @close="closeBtn"
    @confirm="submitBtn"
  >
    <el-form ref="buoyForm" class="buoys-main" size='mini' :model="buoys" :rules="buoyRule">
      <div class="main-title main-props">展示页面：</div>
      <div class="main-capsule">
        <el-form-item label="展示小程序：" prop="programName">
          <el-input v-model="buoys.programName" maxlength="30" show-word-limit placeholder="请输入" :disabled="true" />
        </el-form-item>
        <div class="main-pages">
          <div class="pages-title">
            <div class="pages-text main-props">页面地址：</div>
            <el-button type="primary" size="mini" @click="addPages">新增地址</el-button>
            <a class="pages-alink" style="margin-left: 10px;" target="_blank" href="https://doc.weixin.qq.com/doc/w3_AbwAogYYAGkDEGCSG82TRWXby3Rcb?scode=AKQAXQc6AFAA9a0XzAAbwAogYYAGk&version=4.1.22.6014&platform=win">若不清楚如何配置，请点击查看操作指南 ></a>
          </div>
          <div v-if="buoys.routeAddressList" class="pages-ul">
            <el-form-item v-for="(item,index) in buoys.routeAddressList" :key="index" label="" :prop="`routeAddressList.${index}.routeAddress`" :rules="buoyRule.routeAddress">
              <el-input v-model="item.routeAddress" show-word-limit placeholder="请输入路径名称" />
              <el-button v-if="buoys.routeAddressList.length >= 2" class="main-ites" type="danger" size="mini" @click="deletePages(index)">
                删除
              </el-button>
            </el-form-item>
          </div>
        </div>
      </div>
      <el-form-item label="展示用户（剔除老师身份）：" prop="userGroup" class="main-title">
        <el-radio-group v-model="buoys.userGroup" @change="changeVeri">
          <el-radio :label="0">实时用户群</el-radio>
          <el-radio :label="1">标签分群</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="isVeriFlag" class="main-capsule">
        <div class="main-double">
          <el-form-item label="注册渠道：" :prop="isVeriFlag?'regChannel':''" class="double-one">
            <el-select v-model="buoys.regChannel" filterable clearable placeholder="请选择注册渠道">
              <el-option v-for="item in regChaSelect" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>
          <el-form-item :prop="isVeriFlag?'regOrigin':''" class="double-two">
            <el-select v-model="buoys.regOrigin" filterable clearable placeholder="请选择邀约类型">
              <el-option v-for="item in regOriSelect" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="报读情况：" :prop="isVeriFlag?'signStatus':''">
          <el-radio-group v-model="buoys.signStatus">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">未报读</el-radio>
            <el-radio :label="2">已报读</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邀约人类型：" :prop="isVeriFlag?'inviterType':''">
          <el-radio-group v-model="buoys.inviterType">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">邀约人=老师</el-radio>
            <el-radio :label="2">邀约人=粉丝</el-radio>
            <el-radio :label="3">无邀约人</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="跟进人企微：" :prop="isVeriFlag?'followQwStatus':''">
          <el-radio-group v-model="buoys.followQwStatus">
            <el-radio :label="0">全部</el-radio>
            <el-radio :label="1">未添加</el-radio>
            <el-radio :label="2">已添加</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div v-else class="main-capsule">
        <el-button type="primary" size="mini" style="margin: 0 10px 20px 0;" @click="openUser">选择用户分群</el-button>
        <span v-show="tagGroupInfo.tagGroupName">当前已选择用户分群：{{ tagGroupInfo.tagGroupName || '' }}</span>
      </div>
      <added-user :visible="showUser" :params="tagGroupInfo" @on-close="closeUser" />
      <el-form-item label="上架时间：" prop="onlieTime" class="main-title">
        <el-date-picker v-model="buoys.onlieTime" type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" />
      </el-form-item>
      <el-form-item label="展示权重：" prop="weight" class="main-title main-inputs">
        <el-input-number v-model="buoys.weight" class="inputs" :controls="false" :min="0" show-word-limit placeholder="请输入0-9999正整数" />
        <p>请输入0-9999，相同用户群，数值越大优先展示，若数值相同，则优先展示新创建的浮标</p>
      </el-form-item>
      <el-form-item label="浮标图片：" prop="floatImg" class="main-title buoy-iconImg">
        <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg,image/gif" :imgWidth="152" :imgHeight="152" :file-list="buoys.buoySrcList" @remove="removeHeadImg" @success="successHeadImg" />
      </el-form-item>
      <el-form-item label="跳转地址：" prop="jumpAddress" class="main-title">
        <el-radio-group v-model="buoys.jumpAddress" @change="changeFlag">
          <el-radio :label="0">跳转弹窗</el-radio>
          <el-radio :label="1">跳转小程序</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="isDiverFlag" class="main-capsule">
        <el-form-item label="导流标题：" :prop="isDiverFlag?'title':''">
          <el-input v-model="buoys.title" maxlength="10" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="导流文案：" :prop="isDiverFlag?'writer':''">
          <el-input v-model="buoys.writer" type="textarea" rows="4" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="请选择导流二维码：" :prop="isDiverFlag?'orCodeType':''">
          <el-radio-group v-model="buoys.orCodeType">
            <el-radio :label="0">对应跟进人的企微</el-radio>
            <el-radio :label="1">固定企微</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="main-capsule">
          <div v-show="buoys.orCodeType==0" class="main-tips">若用户当前无跟进人，将展示以下老师信息</div>
          <el-form-item label="老师名字：" :prop="isDiverFlag?'teacherName':''">
            <el-input v-model="buoys.teacherName" maxlength="8" show-word-limit placeholder="请输入" />
          </el-form-item>
          <el-form-item label="老师简介：" :prop="isDiverFlag?'teacherProfile':''">
            <el-input v-model="buoys.teacherProfile" maxlength="15" show-word-limit placeholder="请输入" />
          </el-form-item>
          <el-form-item label="企微二维码：" :prop="isDiverFlag?'qwOrCode':''" class="buoy-teacherQrs buoy-erImg">
            <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg" :imgWidth="112" :imgHeight="112" :file-list="buoys.teacherQrCodeList" @remove="removeTeacherQrCodeImg" @success="successTeacherQrCodeImg" />
          </el-form-item>
        </div>
      </div>
      <div v-else class="main-capsule">
        <p style="margin-bottom: 20px;"><a class="pages-alink" target="_blank" href="https://doc.weixin.qq.com/doc/w3_AbwAogYYAGkDEGCSG82TRWXby3Rcb?scode=AKQAXQc6AFAA9a0XzAAbwAogYYAGk&version=4.1.22.6014&platform=win">若不清楚如何配置，请点击查看操作指南 ></a></p>
        <el-form-item label="小程序appId：" :prop="!isDiverFlag?'appletAppId':''">
          <el-input v-model="buoys.appletAppId" maxlength="30" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="跳转路由地址：" :prop="!isDiverFlag?'skipRouteAddress':''">
          <el-input v-model="buoys.skipRouteAddress" show-word-limit placeholder="请输入" />
        </el-form-item>
      </div>
    </el-form>
  </common-dialog>
</template>

<script>
import addedUser from './added-user.vue';
import { getCutDay } from '@/utils';
import { ossUri } from '@/config/request';

export default {
  components: { addedUser },
  props: {
    visible: { type: Boolean, default: false },
    rows: { type: Object, default: () => {} }
  },
  data() {
    return {
      tableLoading: false,
      isDiverFlag: true,
      isVeriFlag: true,
      showUser: false,
      tagGroupInfo: {},
      regChaSelect: [
        {
          'dictValue': '1',
          'dictName': '学员系统录入',
          'dictId': 'regChannel.1'
        },
        {
          'dictValue': '2',
          'dictName': '钉钉应用录入',
          'dictId': 'regChannel.2'
        },
        {
          'dictValue': '3',
          'dictName': '公众号注册',
          'dictId': 'regChannel.3'
        },
        {
          'dictValue': '4',
          'dictName': '刷题小程序注册',
          'dictId': 'regChannel.4'
        },
        {
          'dictValue': '5',
          'dictName': '安卓注册',
          'dictId': 'regChannel.5'
        },
        {
          'dictValue': '6',
          'dictName': 'IOS注册',
          'dictId': 'regChannel.6'
        },
        {
          'dictValue': '7',
          'dictName': '官网注册',
          'dictId': 'regChannel.7'
        },
        {
          'dictValue': '9',
          'dictName': '企业微信',
          'dictId': 'regChannel.9'
        },
        {
          'dictValue': '10',
          'dictName': '第三方渠道（零一裂变）',
          'dictId': 'regChannel.10'
        },
        {
          'dictValue': '11',
          'dictName': '上进青年注册',
          'dictId': 'regChannel.11'
        },
        {
          'dictValue': '12',
          'dictName': '上进学社',
          'dictId': 'regChannel.12'
        },
        {
          'dictValue': '17',
          'dictName': '学历助手小程序',
          'dictId': 'regChannel.17'
        },
        {
          'dictValue': '31',
          'dictName': '考试平台智能刷题邀约注册',
          'dictId': 'regChannel.31'
        },
        {
          'dictValue': '44',
          'dictName': '自考小程序',
          'dictId': 'regChannel.44'
        },
        {
          'dictValue': '45',
          'dictName': '上进青年小程序',
          'dictId': 'regChannel.45'
        },
        {
          'dictValue': '46',
          'dictName': 'AI测评小程序',
          'dictId': 'regChannel.46'
        }
      ],
      regOriSelect: [
        {
          'dictValue': '0',
          'dictName': '其他',
          'dictId': 'regOrigin.0'
        },
        {
          'dictValue': '1',
          'dictName': '日签海报',
          'dictId': 'regOrigin.1'
        },
        {
          'dictValue': '58',
          'dictName': '上进证书分享',
          'dictId': 'regOrigin.58'
        },
        {
          'dictValue': '60',
          'dictName': 'APP生日推广',
          'dictId': 'regOrigin.60'
        },
        {
          'dictValue': '2',
          'dictName': '分享有礼',
          'dictId': 'regOrigin.2'
        },
        {
          'dictValue': '3',
          'dictName': '分享海报',
          'dictId': 'regOrigin.3'
        },
        {
          'dictValue': '4',
          'dictName': '助学名片',
          'dictId': 'regOrigin.4'
        },
        {
          'dictValue': '5',
          'dictName': '转介绍',
          'dictId': 'regOrigin.5'
        },
        {
          'dictValue': '6',
          'dictName': 'APP签到',
          'dictId': 'regOrigin.6'
        },
        {
          'dictValue': '7',
          'dictName': 'APP分享有礼',
          'dictId': 'regOrigin.7'
        },
        {
          'dictValue': '8',
          'dictName': 'APP分享海报',
          'dictId': 'regOrigin.8'
        },
        {
          'dictValue': '9',
          'dictName': 'APP八月冲刺活动',
          'dictId': 'regOrigin.9'
        },
        {
          'dictValue': '10',
          'dictName': 'APP岭南活动',
          'dictId': 'regOrigin.10'
        },
        {
          'dictValue': '13',
          'dictName': '职业教育',
          'dictId': 'regOrigin.13'
        },
        {
          'dictValue': '14',
          'dictName': '活动邀约-认证',
          'dictId': 'regOrigin.14'
        },
        {
          'dictValue': '16',
          'dictName': '活动邀约',
          'dictId': 'regOrigin.16'
        },
        {
          'dictValue': '17',
          'dictName': '录取通知书h5',
          'dictId': 'regOrigin.17'
        },
        {
          'dictValue': '18',
          'dictName': '圈子',
          'dictId': 'regOrigin.18'
        },
        {
          'dictValue': '19',
          'dictName': '新年红包注册',
          'dictId': 'regOrigin.19'
        },
        {
          'dictValue': '20',
          'dictName': '海报二维码注册',
          'dictId': 'regOrigin.20'
        },
        {
          'dictValue': '21',
          'dictName': '试听课程',
          'dictId': 'regOrigin.21'
        },
        {
          'dictValue': '22',
          'dictName': '3月强职计划',
          'dictId': 'regOrigin.22'
        },
        {
          'dictValue': '23',
          'dictName': '圈子分享',
          'dictId': 'regOrigin.23'
        },
        {
          'dictValue': '24',
          'dictName': '派券工具',
          'dictId': 'regOrigin.24'
        },
        {
          'dictValue': '25',
          'dictName': 'App直播间',
          'dictId': 'regOrigin.25'
        },
        {
          'dictValue': '27',
          'dictName': '双11购卷邀约',
          'dictId': 'regOrigin.27'
        },
        {
          'dictValue': '28',
          'dictName': '自主注册',
          'dictId': 'regOrigin.28'
        },
        {
          'dictValue': '29',
          'dictName': '上进大学注册',
          'dictId': 'regOrigin.29'
        },
        {
          'dictValue': '30',
          'dictName': '上进大学会员卡活动',
          'dictId': 'regOrigin.30'
        },
        {
          'dictValue': '31',
          'dictName': '全额奖学金邀请函',
          'dictId': 'regOrigin.31'
        },
        {
          'dictValue': '35',
          'dictName': '首页邀约',
          'dictId': 'regOrigin.35'
        },
        {
          'dictValue': '36',
          'dictName': '题目邀约',
          'dictId': 'regOrigin.36'
        },
        {
          'dictValue': '37',
          'dictName': '上进大学读书计划',
          'dictId': 'regOrigin.37'
        },
        {
          'dictValue': '39',
          'dictName': '老师圈',
          'dictId': 'regOrigin.39'
        },
        {
          'dictValue': '40',
          'dictName': '618活动邀请',
          'dictId': 'regOrigin.40'
        },
        {
          'dictValue': '41',
          'dictName': '专本套读',
          'dictId': 'regOrigin.41'
        },
        {
          'dictValue': '42',
          'dictName': '院校主页',
          'dictId': 'regOrigin.42'
        },
        {
          'dictValue': '43',
          'dictName': '研究生主页',
          'dictId': 'regOrigin.43'
        },
        {
          'dictValue': '44',
          'dictName': '公众号主页',
          'dictId': 'regOrigin.44'
        },
        {
          'dictValue': '45',
          'dictName': '自考主页',
          'dictId': 'regOrigin.45'
        },
        {
          'dictValue': '47',
          'dictName': '国开主页',
          'dictId': 'regOrigin.47'
        },
        {
          'dictValue': '48',
          'dictName': '城市主页',
          'dictId': 'regOrigin.48'
        },
        {
          'dictValue': '49',
          'dictName': '专业邀约',
          'dictId': 'regOrigin.49'
        },
        {
          'dictValue': '50',
          'dictName': '上进故事',
          'dictId': 'regOrigin.50'
        },
        {
          'dictValue': '51',
          'dictName': '成考主页',
          'dictId': 'regOrigin.51'
        },
        {
          'dictValue': '52',
          'dictName': '成考冲刺月',
          'dictId': 'regOrigin.52'
        },
        {
          'dictValue': '53',
          'dictName': '四川成教',
          'dictId': 'regOrigin.53'
        },
        {
          'dictValue': '54',
          'dictName': '四川国开',
          'dictId': 'regOrigin.54'
        },
        {
          'dictValue': '55',
          'dictName': '四川自考',
          'dictId': 'regOrigin.55'
        },
        {
          'dictValue': '57',
          'dictName': '上进学社单本读书',
          'dictId': 'regOrigin.57'
        },
        {
          'dictValue': '61',
          'dictName': '2022新年活动',
          'dictId': 'regOrigin.61'
        },
        {
          'dictValue': '62',
          'dictName': '上进奖学金',
          'dictId': 'regOrigin.62'
        },
        {
          'dictValue': '63',
          'dictName': '直播课程',
          'dictId': 'regOrigin.63'
        },
        {
          'dictValue': '71',
          'dictName': '学历助手-邀约注册',
          'dictId': 'regOrigin.71'
        },
        {
          'dictValue': '72',
          'dictName': '学历助手-自主注册',
          'dictId': 'regOrigin.72'
        },
        {
          'dictValue': '73',
          'dictName': '活动分享',
          'dictId': 'regOrigin.73'
        },
        {
          'dictValue': '74',
          'dictName': '习惯分享',
          'dictId': 'regOrigin.74'
        },
        {
          'dictValue': '75',
          'dictName': '学历助手-老师名片',
          'dictId': 'regOrigin.75'
        },
        {
          'dictValue': '76',
          'dictName': '2023新年活动',
          'dictId': 'regOrigin.76'
        },
        {
          'dictValue': '77',
          'dictName': '321活动主页',
          'dictId': 'regOrigin.77'
        },
        {
          'dictValue': '78',
          'dictName': '321海报邀约',
          'dictId': 'regOrigin.78'
        },
        {
          'dictValue': '79',
          'dictName': '系统生产',
          'dictId': 'regOrigin.79'
        },
        {
          'dictValue': '80',
          'dictName': '321活动-许愿',
          'dictId': 'regOrigin.80'
        },
        {
          'dictValue': '81',
          'dictName': '小程序帖子邀约注册',
          'dictId': 'regOrigin.81'
        },
        {
          'dictValue': '82',
          'dictName': '2023年4月自考红包活动',
          'dictId': 'regOrigin.82'
        },
        {
          'dictValue': '83',
          'dictName': '618直播预约邀请',
          'dictId': 'regOrigin.83'
        },
        {
          'dictValue': '84',
          'dictName': '拉黑来源特殊类型',
          'dictId': 'regOrigin.84'
        },
        {
          'dictValue': '85',
          'dictName': 'APP-活动分享海报',
          'dictId': 'regOrigin.85'
        },
        {
          'dictValue': '86',
          'dictName': 'APP-活动微信',
          'dictId': 'regOrigin.86'
        },
        {
          'dictValue': '87',
          'dictName': 'APP-活动朋友圈',
          'dictId': 'regOrigin.87'
        },
        {
          'dictValue': '88',
          'dictName': 'APP-活动QQ',
          'dictId': 'regOrigin.88'
        },
        {
          'dictValue': '89',
          'dictName': 'APP-活动企业微信',
          'dictId': 'regOrigin.89'
        },
        {
          'dictValue': '90',
          'dictName': '小程序-活动分享',
          'dictId': 'regOrigin.90'
        },
        {
          'dictValue': '91',
          'dictName': '小程序-活动海报分享',
          'dictId': 'regOrigin.91'
        },
        {
          'dictValue': '92',
          'dictName': '上进学社-微信投放',
          'dictId': 'regOrigin.92'
        },
        {
          'dictValue': '93',
          'dictName': '上进学社-头条投放',
          'dictId': 'regOrigin.93'
        },
        {
          'dictValue': '95',
          'dictName': '上进学社-协助分销',
          'dictId': 'regOrigin.95'
        },
        {
          'dictValue': '94',
          'dictName': '习惯邀约注册',
          'dictId': 'regOrigin.94'
        },
        {
          'dictValue': '100',
          'dictName': '自考小程序-自主注册',
          'dictId': 'regOrigin.100'
        },
        {
          'dictValue': '101',
          'dictName': '自考小程序-邀约注册',
          'dictId': 'regOrigin.101'
        },
        {
          'dictValue': '102',
          'dictName': '上进证书报告分享',
          'dictId': 'regOrigin.102'
        },
        {
          'dictValue': '103',
          'dictName': '2023年10月自考红包活动',
          'dictId': 'regOrigin.103'
        },
        {
          'dictValue': '104',
          'dictName': '2024年1月新年红包活动',
          'dictId': 'regOrigin.104'
        },
        {
          'dictValue': '105',
          'dictName': '2024年1月自考红包活动',
          'dictId': 'regOrigin.105'
        },
        {
          'dictValue': '106',
          'dictName': '2024年321活动',
          'dictId': 'regOrigin.106'
        },
        {
          'dictValue': '107',
          'dictName': '测评小程序-自主注册',
          'dictId': 'regOrigin.107'
        },
        {
          'dictValue': '108',
          'dictName': '测评小程序-邀约注册',
          'dictId': 'regOrigin.108'
        }
      ],
      buoys: {
        programName: '学历助手小程序',
        routeAddress: '',
        userGroup: 0,
        regChannel: '',
        regOrigin: '',
        signStatus: 0,
        inviterType: 0,
        followQwStatus: 0,
        labelId: '',
        onlieTime: [],
        weight: undefined,
        appletAppId: '',
        jumpAddress: 0,
        floatImg: '',
        buoySrcList: [],
        routeAddressList: [{ routeAddress: '' }],
        title: '',
        writer: '',
        skipRouteAddress: '',
        orCodeType: 0,
        teacherName: '',
        teacherProfile: '',
        qwOrCode: '',
        teacherQrCodeList: []
      },
      buoyRule: {
        programName: [{ required: true, message: '请输入', trigger: 'blur' }],
        routeAddress: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入路径名称'));
              return;
            }
            const reg = /[\u4E00-\u9FA5]/g;
            if (reg.test(value)) {
              callback(new Error('不可以输入中文'));
              return;
            }
            callback();
          }
        }],
        userGroup: [{ required: true, message: '请选择', trigger: 'change' }],
        regChannel: [{ required: true, message: '请选择', trigger: 'change' }],
        regOrigin: [{ required: true, message: '请选择', trigger: 'change' }],
        signStatus: [{ required: true, message: '请选择', trigger: 'change' }],
        inviterType: [{ required: true, message: '请选择', trigger: 'change' }],
        followQwStatus: [{ required: true, message: '请选择', trigger: 'change' }],
        labelId: [{ required: true, message: '请选择', trigger: 'change' }],
        onlieTime: [{ required: true, message: '请选择', trigger: 'change' }],
        weight: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入0-9999正整数'));
              return;
            }
            if (value >= 10000) {
              callback(new Error('请正确输入0-9999范围内的正整数'));
              return;
            }
            callback();
          }
        }],
        jumpAddress: [{ required: true, message: '请选择', trigger: 'change' }],
        floatImg: [{ required: true, message: '请上传浮标图片', trigger: 'change' }],
        writer: [{ required: true, message: '请输入', trigger: 'blur' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        orCodeType: [{ required: true, message: '请选择', trigger: 'change' }],
        teacherName: [{ required: true, message: '请输入', trigger: 'blur' }],
        teacherProfile: [{ required: true, message: '请输入', trigger: 'blur' }],
        qwOrCode: [{ required: true, message: '请上传企微二维码', trigger: 'change' }],
        appletAppId: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入'));
              return;
            }
            const reg = /[\u4E00-\u9FA5]/g;
            if (reg.test(value)) {
              callback(new Error('不可以输入中文'));
              return;
            }
            callback();
          }
        }],
        skipRouteAddress: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入'));
              return;
            }
            const reg = /[\u4E00-\u9FA5]/g;
            if (reg.test(value)) {
              callback(new Error('不可以输入中文'));
              return;
            }
            callback();
          }
        }]
      }
    };
  },
  methods: {
    initForm() {
      if (this.rows.id) {
        const obs = JSON.parse(JSON.stringify(this.rows));
        console.log('表单初始化-rows：', obs);
        // 页面新增地址
        if (!obs.routeAddressList?.length) {
          obs['routeAddressList'] = [{ routeAddress: '' }];
        }
        // 图片处理
        if (obs.floatImg) {
          obs['buoySrcList'] = [{ url: ossUri + obs.floatImg }];
        } else obs['buoySrcList'] = [];
        if (obs.qwOrCode) {
          obs['teacherQrCodeList'] = [{ url: ossUri + obs.qwOrCode }];
        } else obs['teacherQrCodeList'] = [];
        // 时间处理
        if (obs.startTime && obs.endTime) {
          obs['onlieTime'] = [obs.startTime, obs.endTime];
        }
        // 标签分群处理
        if (obs?.tagGroupInfo) {
          this.tagGroupInfo = {
            ...obs?.tagGroupInfo,
            tagGroupName: obs?.tagGroupInfo?.name
          };
        }
        console.log('标签分群处理', this.tagGroupInfo);
        // 单选处理
        obs.userGroup = Number(obs?.userGroup || 0);
        obs.jumpAddress = Number(obs?.jumpAddress || 0);
        obs.signStatus = Number(obs?.signStatus || 0);
        obs.inviterType = Number(obs?.inviterType || 0);
        obs.followQwStatus = Number(obs?.followQwStatus || 0);
        obs.orCodeType = Number(obs?.orCodeType || 0);
        // 依赖处理
        this.changeVeri(obs.userGroup);
        this.changeFlag(obs.jumpAddress);
        this.buoys = obs;
      }
    },
    // 添加路径
    addPages() {
      const narr = this.buoys?.routeAddressList || [];
      const nus = Number(narr[narr.length - 1]?.id || 0);
      this.buoys.routeAddressList.push({ id: nus + 1, routeAddress: '' });
    },
    // 删除当前选择路径
    deletePages(ins) {
      const news = JSON.parse(JSON.stringify(this.buoys?.routeAddressList || []));
      if (news?.length <= 1) {
        this.$message({ message: '无法删除！题目选项至少2个', type: 'warning' });
        return false;
      }
      news.splice(ins, 1);
      this.buoys.routeAddressList = news;
    },
    // 成功上传浮标图片
    successHeadImg(evt) {
      this.buoys.floatImg = evt?.response;
    },
    // 删除浮标图片
    removeHeadImg() {
      this.buoys.floatImg = '';
      this.buoys.buoySrcList = [];
    },
    // 选择用户标签群--打开
    openUser() {
      this.showUser = true;
    },
    // 选择用户标签群--接收参数
    closeUser(obs) {
      if (this.showUser) {
        console.log('closeUser-obs', obs);
        if (obs) {
          this.buoys.labelId = obs.tagGroupId;
          this.tagGroupInfo = obs;
        }
        this.showUser = false;
      }
    },
    // 修改表单校验规则：展示用户
    changeVeri(ve) {
      this.isVeriFlag = Boolean(ve === 0);
      console.log('展示用户', ve);
      for (const key in this.buoyRule) {
        if (key === 'followQwStatus' || key === 'inviterType' || key === 'signStatus' || key === 'regOrigin' || key === 'regChannel') {
          this.buoyRule[key][0].required = this.isVeriFlag;
        }
      }
    },
    // 修改表单校验规则：跳转地址
    changeFlag(ve) {
      console.log('跳转地址', ve);
      this.isDiverFlag = Boolean(ve === 0);
      for (const key in this.buoyRule) {
        // 跳转弹窗
        if (key === 'title' || key === 'writer' || key === 'orCodeType' || key === 'teacherName' || key === 'teacherProfile' || key === 'qwOrCode') {
          this.buoyRule[key][0].required = this.isDiverFlag;
        }
        // 跳转小程序
        if (key === 'appletAppId' || key === 'skipRouteAddress') {
          this.buoyRule[key][0].required = !this.isDiverFlag;
        }
      }
    },
    // 成功上传企微二维码图
    successTeacherQrCodeImg(evt) {
      this.buoys.qwOrCode = evt?.response;
    },
    // 删除企微二维码：
    removeTeacherQrCodeImg() {
      this.buoys.qwOrCode = '';
      this.buoys.teacherQrCodeList = [];
    },
    submitBtn() {
      this.$refs['buoyForm'].validate((valid) => {
        if (valid) {
          this.buoys['startTime'] = getCutDay(this.buoys.onlieTime[0]);
          this.buoys['endTime'] = getCutDay(this.buoys.onlieTime[1]);
          console.log('submie', JSON.stringify(this.buoys));
          // 判断是不是“标签分群”，是，则进一步判断是否有值
          if (!this.isVeriFlag && !this.buoys.labelId) {
            this.$message.error('请选择用户分群');
            return false;
          }
          // 调用接口
          const formData = JSON.parse(JSON.stringify(this.buoys));
          delete formData.buoySrcList;
          delete formData.teacherQrCodeList;
          this.$post('updateFloatConfig', formData, { json: true })
            .then((res) => {
              const { code } = res;
              if (code === '00') {
                this.$message.success('提交成功');
                this.$emit('on-close');
              } else this.$message.error('提交失败');
              this.tableLoading = false;
            }).catch(() => {
              this.$message.error('提交失败');
              this.tableLoading = false;
            });
        }
      });
    },
    // 关闭当前弹窗
    closeBtn() {
      console.log('关闭当前弹窗');
      if (this.visible) {
        this.isDiverFlag = true;
        this.isVeriFlag = true;
        this.buoys = {
          programName: '学历助手小程序',
          routeAddress: '',
          userGroup: 0,
          regChannel: '',
          regOrigin: '',
          signStatus: 0,
          inviterType: 0,
          followQwStatus: 0,
          labelId: '',
          onlieTime: [],
          weight: undefined,
          appletAppId: '',
          jumpAddress: 0,
          floatImg: '',
          buoySrcList: [],
          routeAddressList: [{ routeAddress: '' }],
          title: '',
          writer: '',
          skipRouteAddress: '',
          orCodeType: 0,
          teacherName: '',
          teacherProfile: '',
          qwOrCode: '',
          teacherQrCodeList: []
        };
        this.tagGroupInfo = {};
        this.$refs['buoyForm']?.resetFields();
        this.$emit('on-close');
      }
    }
  }
};
</script>

<style lang="scss">
.buoys {
  .buoys-main {
    width: 70%;
    margin: 40px auto;
    .el-form-item {
      display: flex;
      align-items: center;
    }
    .el-form-item__content {
      width: 80%;
    }
    .main-title {
      margin-bottom: 20px;
      font-weight: 550;
      p {
        margin-top: 4px;
        font-size: 13px;
        color: #9e9e9e;
        font-weight: normal;
      }
    }
    .main-pages {
      .pages-title {
        margin: 0 20px 20px 0;
        display: flex;
        align-items: center;
      }
      .pages-ul {
        margin-left: 40px;
      }
    }
    .main-capsule {
      margin-left: 7%;
      .el-form-item__content {
        width: 60%;
        display: flex;
      }
      .main-ites {
        margin-left: 10px;
      }
    }
    .main-double {
      display: flex;
      .el-form-item__content {
        width: 60%;
      }
      .double-one {
        width: 40%;
      }
      .double-two {
        width: 50%;
      }
    }
    .main-inputs {
      .inputs {
        width: 100%;
      }
      .el-input__inner{
        text-align: left;
      }
      align-items: unset;
    }
    .main-tips {
      margin-bottom: 20px;
      font-size: 13px;
      color: #9e9e9e;
    }
    .main-props {
      margin-left: 10px;
      position: relative;
    }
    .main-props::after {
      position: absolute;
      top: 4px;
      left: -10px;
      font-size: 13px;
      color: #ff0505;
      font-weight: normal;
      content: "*";
    }
    .pages-alink {
      color: #9e9e9e;
    }
    .pages-alink:hover {
      color: #6db4fc;
    }
  }

  .yz-common-dialog__footer {
    text-align: center !important;
  }

  .buoy-iconImg,
  .buoy-erImg {
    position: relative;
  }
  .buoy-iconImg::after,
  .buoy-erImg::after {
    position: absolute;
    left: 280px;
    bottom: 10px;
    font-size: 13px;
    color: #9e9e9e;
    font-weight: normal;
    content: "仅支持上传PNG/JPEG/GIF，尺寸为152*152，上传最大不能超过500KB";
  }
  .buoy-erImg::after {
    content: "仅支持上传PNG/JPEG，尺寸为112*112，上传最大不能超过2M";
  }
}
</style>
