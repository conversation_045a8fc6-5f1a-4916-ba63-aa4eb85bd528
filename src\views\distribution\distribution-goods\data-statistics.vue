<template>
  <common-dialog
    :show-footer="false"
    is-full
    width="600px"
    title="分销统计"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <div class="header">
        <span class="title">统计时间</span>
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"

          :picker-options="pickerOptions"
          @change='changeTimeRange'
        />
        <div class="statis-box">
          <div class="item" :class="statisType === 1 ?'statisType': ''" @click="changStatis(1,'分销订单')">
            <p>分销订单</p>
            <span>{{ payOrderNum || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 2 ?'statisType': ''" @click="changStatis(2,'分销金额')">
            <p>分销金额</p>
            <span>{{ payOrderAmount || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 3 ?'statisType': ''" @click="changStatis(3,'返利金额')">
            <p>返利金额</p>
            <span>{{ rewardAmount || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 4 ?'statisType': ''" @click="changStatis(4,'退货订单')">
            <p>退款订单</p>
            <span>{{ refundOrderNum || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 5 ?'statisType': ''" @click="changStatis(5,'退货金额')">
            <p>退款金额</p>
            <span>{{ refundOrderAmount || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 6 ?'statisType': ''" @click="changStatis(6,'取消返利金额')">
            <p>退款金额返利</p>
            <span>{{ refundRewardAmount || 0 }}</span>
          </div>

        </div>
      </div>
      <h3 class="tableTitle">{{ tableTitle }}  趋势图</h3>
      <div ref='chart' class="chart" />
      <h3>具体数据</h3>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="day" label="日期" align="center">
          <template slot-scope="scope">
            {{ scope.row.day | changeLimitTime }}
          </template>
        </el-table-column>
        <el-table-column prop="payOrderNum" label="分销订单" align="center" />
        <el-table-column prop="payOrderAmount" label="订单金额" align="center" />
        <el-table-column prop="rewardAmount" label="返利金额" align="center" />
        <el-table-column prop="refundOrderNum" label="退款订单" align="center" />
        <el-table-column prop="refundOrderAmount" label="退款金额" align="center" />
        <el-table-column prop="refundRewardAmount" label="取消返利金额" align="center">
          <template slot-scope="scope">
            {{ scope.row.refundRewardAmount }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
var echarts = require('echarts');
import { formatTimeStamp } from '@/utils';
export default {
  components: {},
  filters: {
    changeLimitTime(val) {
      return formatTimeStamp(val, 'YYYY-MM-DD');
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
      option: {}
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      recently: '1',
      statisType: 1,
      // 默认最近7天
      timeRange: [formatTimeStamp(new Date().getTime() - 3600 * 1000 * 24 * 7, 'YYYY-MM-DD'), formatTimeStamp(new Date().getTime(), 'YYYY-MM-DD')],
      tableData: [],
      xAxisData: [], // 图表x轴数据
      seriesData: [], // 表内数据
      tableTitle: '订单量',
      // tableTitleDay: '近7天',
      payOrderNum: '', // 分销订单
      payOrderAmount: '', // 分销金额
      rewardAmount: '', // 返利金额
      refundOrderNum: '', // 退款订单
      refundOrderAmount: '', // 退货金额
      refundRewardAmount: '', // 取消返利金额

      payOrderNumList: [], // 分销订单总计
      payOrderAmountList: [], // 分销金额总计
      rewardAmountList: [], // 返利金额总计
      refundOrderNumList: [], // 退款订单总计
      refundOrderAmountList: [], // 退货金额总计
      refundRewardAmountList: [], // 取消返利金额总计

      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近7天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime((start).getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近15天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近30天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},

  methods: {
    initChart() {
      const option = {
        // title: {
        //   text: this.tableTitle
        // },
        tooltip: {},
        xAxis: {
          splitNumber: 9,
          data: this.xAxisData
        },
        yAxis: {},
        series: [{
          name: '数量',
          type: 'line',
          data: this.seriesData,
          smooth: true
        }]
      };
      this.$nextTick(() => {
        // dom已存在，需销毁
        echarts.dispose(this.$refs.chart);
        const chart = echarts.init(this.$refs.chart);
        chart.setOption(option);
      });
    },
    open() {
      this.getTableList();
      this.initChart();
    },
    changStatis(type, title) {
      this.statisType = type;
      this.tableTitle = title;
      this.seriesData = type === 1 ? this.payOrderNumList : this.seriesData;
      this.seriesData = type === 2 ? this.payOrderAmountList : this.seriesData;
      this.seriesData = type === 3 ? this.rewardAmountList : this.seriesData;
      this.seriesData = type === 4 ? this.refundOrderNumList : this.seriesData;
      this.seriesData = type === 5 ? this.refundOrderAmountList : this.seriesData;
      this.seriesData = type === 6 ? this.refundRewardAmountList : this.seriesData;
      this.initChart();
    },
    changeTimeRange(val) {
      if (!val) {
        this.timeRange = [];
        return;
      }

      this.getTableList();
    },
    getTableList() {
      // 默认8点改成0点
      const startTimeTxt = this.timeRange[0] + ' 00:00:00';
      const endTimeTxt = this.timeRange[1] + ' 00:00:00';
      // const startTime = new Date(this.timeRange[0]).getTime();
      const startTime = new Date(startTimeTxt).getTime();
      const endTime = new Date(endTimeTxt).getTime();
      const data = {
        distributionId: '',
        distributionNumber: '',
        startTime: startTime,
        endTime: endTime,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      // console.log(data, 'data');

      let distributionAll = 'distributionFindAll'; // 总统计
      let listUrl = 'distributionFindAllList'; // 列表图分页
      let allListUrl = 'distributionAllChartList'; // 总列表图（没分页）
      if (this.row.distributionId) {
        data['distributionId'] = this.row.distributionId;
        data['distributionNumber'] = this.row.distributionNumber;
        distributionAll = 'distributionFindSingle';
        listUrl = 'distributionFindSingleList';
        allListUrl = 'distributionSingleChartList';
      }
      this.$post(distributionAll, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.payOrderNum = body.payOrderNum;
          this.payOrderAmount = body.payOrderAmount;
          this.rewardAmount = body.rewardAmount;
          this.refundOrderNum = body.refundOrderNum;
          this.refundOrderAmount = body.refundOrderAmount;
          this.refundRewardAmount = body.refundRewardAmount;
        }
      });
      this.$post(listUrl, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          // console.log(body, 'this.tableData');
          // this.pagination.total = body.recordsTotal;
        }
      });
      this.$post(allListUrl, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.xAxisData = [];

          // 返回数据只有有数据的天数，x轴需要补充时间段内的天数
          for (let i = startTime; i <= endTime; i = i + 24 * 60 * 60 * 1000) {
            this.xAxisData.push(formatTimeStamp(i, 'YYYY-MM-DD'));
          }

          this.seriesData = [];
          this.payOrderNumList = [];
          this.payOrderAmountList = [];
          this.rewardAmountList = [];
          this.refundOrderNumList = [];
          this.refundOrderAmountList = [];
          this.refundRewardAmountList = [];

          const groupData = {};
          // 若x轴没数据 则填充为0

          body.map(item => {
            const day = formatTimeStamp(item.day, 'YYYY-MM-DD');
            groupData[day] = item;
          });

          this.xAxisData.forEach(item => {
            const numData = groupData[item];
            if (numData) {
              this.payOrderNumList.push(numData.payOrderNum);
              this.payOrderAmountList.push(numData.payOrderAmount);
              this.rewardAmountList.push(numData.rewardAmount);
              this.refundOrderNumList.push(numData.refundOrderNum);
              this.refundOrderAmountList.push(numData.refundOrderAmount);
              this.refundRewardAmountList.push(numData.refundRewardAmount);
            } else {
              this.payOrderNumList.push(0);
              this.payOrderAmountList.push(0);
              this.rewardAmountList.push(0);
              this.refundOrderNumList.push(0);
              this.refundOrderAmountList.push(0);
              this.refundRewardAmountList.push(0);
            }
          });

          if (this.statisType === 1) {
            this.seriesData = this.payOrderNumList;
          } else if (this.statisType === 2) {
            this.seriesData = this.payOrderAmountList;
          } else if (this.statisType === 3) {
            this.seriesData = this.rewardAmountList;
          } else if (this.statisType === 4) {
            this.seriesData = this.refundOrderNumList;
          } else if (this.statisType === 5) {
            this.seriesData = this.refundOrderAmountList;
          } else if (this.statisType === 6) {
            this.seriesData = this.refundRewardAmountList;
          }

          this.initChart();
          // console.log(this.xAxisData);
          // console.log(this.seriesData);
        }
      });
    },
    submit() {
      this.getTableList();
    },
    close() {
      this.$emit('refresh-list', true);
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
  .tableTitle{
    margin: 0;
    font-size: 20px;
  }
  .chart {
    width: 800px;
    height: 400px;
  }
  .statisType{
    color: #478cb2;
  }
  .header{
    margin-bottom: 20px;
    .title{
      font-size: 16px;
    }
    .el-select{
      width: 150px;
      margin: 0 20px;
    }
    .el-input__inner{
      width: 500px;
      margin: 0 20px;
    }
    .statis-box{
      display: flex;
      margin-top: 30px;

      .item{
        width: 120px;
        height: 70px;
        text-align: center;
        margin-right: 20px;
        padding: 3px;
        cursor: pointer;
        P{
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
        }
        span{
          font-size: 18px;
          font-weight: 600;
        }
      }
      .item:nth-child(2){
        margin-right: 60px;
      }
    }
  }
  .header .el-input__inner{
    height: 32px;
    line-height: 32px;
  }
::v-deep .el-date-editor .el-range-separator{
    line-height: 25px;
  }
::v-deep .el-date-editor .el-range__icon{
  line-height: 25px;
}
::v-deep .el-date-editor .el-range__close-icon{
  line-height: 25px;
}

</style>
