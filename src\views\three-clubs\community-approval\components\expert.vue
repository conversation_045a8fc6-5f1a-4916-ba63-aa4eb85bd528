<template>
  <div>
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="100px"
      @submit.native.prevent="onSearch"
    >
      <el-form-item label="社团名称" prop="clubsActName">
        <el-input v-model="form.clubsActName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="远智编码" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="状态" prop="checkStatus">
        <el-select v-model="form.checkStatus" placeholder="请选择">
          <el-option
            v-for="item in DClubAuditStatus"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="社团类型" prop="clubsActType">
        <el-select v-model="form.clubsActType" placeholder="请选择">
          <el-option
            v-for="item in DClubsActType"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="姓名" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入" clearable />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="onSearch('reset')"
        />
      </div>
    </el-form>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="loading"
      border
      size="small"
      class="table-container"
      :data="list"
    >
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <el-table-column prop="realName" label="姓名" align="center" />
      <el-table-column
        label="社团类型"
        align="center"
        :formatter="handleClubsActType"
      />
      <el-table-column prop="clubsActName" label="社团名称" align="center" />
      <el-table-column prop="applyReason" label="申请理由" align="center" />
      <el-table-column prop="createTime" label="申请时间" align="center" />
      <el-table-column label="审批时间" align="center">
        <template #default="{ row }">
          {{ row.checkTime || "/" }}
        </template>
      </el-table-column>
      <el-table-column label="审批人姓名" align="center">
        <template #default="{ row }">
          {{ row.checkName || "/" }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        :formatter="handleCheckStatus"
      />
      <el-table-column prop="checkRemark" label="理由" align="center">
        <template #default="{ row }">
          {{ row.checkRemark || "/" }}
        </template>
      </el-table-column>
      <el-table-column prop="checkRemark" label="手机号" align="center">
        <template #default="{ row }">
          <el-popover trigger="click">
            <div>手机号：{{ row.applyMobile || "空" }}</div>
            <el-button slot="reference" type="text" size="small">
              查看
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <div v-if="row.checkStatus == DClubAuditStatusMap.approval">
            <el-button
              type="text"
              size="small"
              @click="onApproval(row, DClubAuditStatusMap.agree)"
            >
              同意
            </el-button>
            <el-button
              type="text"
              size="small"
              style="color: red"
              @click="onApproval(row, DClubAuditStatusMap.reject)"
            >
              拒绝
            </el-button>
          </div>
          <div v-else>/</div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="total"
        :page.sync="pager.pageNum"
        :limit.sync="pager.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      v-loading="loading"
      :visible.sync="rejectDialog.visible"
      width="400px"
      element-loading-text="正在处理中，请稍后"
    >
      <div slot="title" class="title">
        <i class="el-icon-warning" />
        确认<span style="color: red">拒绝</span>成为达人？
      </div>
      <el-input
        v-model="rejectDialog.checkRemark"
        type="textarea"
        :rows="3"
        maxlength="50"
        minlength="6"
        show-word-limit
        placeholder="至少写满6字以上理由哦~"
      />
      <div slot="footer" style="text-align: center">
        <el-button size="small" @click="rejectDialog.visible = false">
          取 消
        </el-button>
        <el-button
          type="primary"
          size="small"
          :disabled="loading"
          @click="handleReject"
        >
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  ClubsActType as DClubsActType,
  DClubAuditStatus,
  DApplyIdentity,
  DClubAuditStatusMap
} from '@/dict';
export default {
  data() {
    return {
      form: {
        clubsActName: '',
        yzCode: '',
        checkStatus: '',
        clubsActType: '',
        realName: ''
      },
      loading: true,
      list: [],
      pager: { pageNum: 1, pageSize: 10 },
      total: 0,
      rejectDialog: {
        visible: false,
        checkRemark: void 0,
        row: null
      },
      DClubsActType,
      DClubAuditStatus,
      DApplyIdentity,
      DClubAuditStatusMap
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.loading = true;
        const params = { ...this.pager, ...this.form };
        const { code, body } = await this.$http({
          method: 'post',
          url: '/clubsExpertApply/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.total = body.recordsTotal;
        this.list = body.data;
      } finally {
        this.loading = false;
      }
    },
    // 处理社团类型显示
    handleClubsActType(row) {
      return DClubsActType.find((v) => v.dictValue === row.clubsActType)
        ?.dictName;
    },
    // 处理审批状态显示
    handleCheckStatus(row) {
      return (
        DClubAuditStatus.find((v) => v.dictValue === row.checkStatus)
          ?.dictName || '/'
      );
    },
    // 搜索
    onSearch(type) {
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // 审批请求
    async handleApproval(row, checkStatus) {
      try {
        this.loading = true;
        const { code } = await this.$http({
          method: 'post',
          url: `/clubsExpertApply/audit`,
          data: {
            id: row.id,
            checkStatus,
            checkRemark: this.rejectDialog.checkRemark
          },
          json: true
        });
        this.loading = false;
        if (code !== '00') return;
        this.$message.success('操作成功');
        this.rejectDialog.visible = false;
        this.getData();
      } catch (error) {
        this.loading = false;
      }
    },
    // btn 审批同意 | 拒绝
    onApproval(row, checkStatus) {
      this.rejectDialog.checkRemark = void 0;
      if (checkStatus === DClubAuditStatusMap.reject) {
        this.rejectDialog.visible = true;
        this.rejectDialog.row = row;
      } else {
        this.$confirm('', '确认同意成为达人？', { type: 'warning', center: true }).then(
          () => {
            this.handleApproval(row, checkStatus);
          }
        );
      }
    },
    // 拒绝
    handleReject() {
      if (!this.rejectDialog.checkRemark) {
        this.$message.warning('请填写拒绝理由~');
        return;
      }
      if (this.rejectDialog.checkRemark.length < 6) {
        this.$message.warning('至少写满6字以上理由哦~');
        return;
      }
      this.handleApproval(this.rejectDialog.row, DClubAuditStatusMap.reject);
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0 30px;
}

.title {
  display: flex;
  font-size: 18px;
  align-items: center;
  justify-content: center;
  color: #303133;
  i {
    color: #e6a23c;
    font-size: 22px;
    margin-right: 10px;
  }
}
</style>
