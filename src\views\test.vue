<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='输入框' prop='demo1'>
        <el-input v-model="form.demo1" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='下拉' prop='demo2'>
        <el-select v-model="form.demo2" clearable>
          <el-option label="测试" value="1" />
        </el-select>
      </el-form-item>

      <el-form-item label='时间控件' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="test" label="列表1" align="center" />
      <el-table-column prop="test" label="列表2" align="center" />
      <el-table-column prop="test" label="列表3" align="center" />
      <el-table-column prop="test" label="列表4" align="center" />
      <el-table-column prop="test" label="列表5" align="center" />
      <el-table-column prop="test" label="列表6" align="center" />
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

  </div>
</template>
<script>
import { exportExcel, handleDateControl } from '@/utils';
export default {
  data() {
    return {
      tableLoading: false,
      form: {
        demo1: '',
        demo2: '',
        time: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.startTime = date[0];
      formData.endTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 导出函数
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('', data);
    },
    // 获取列表数据
    // getTableList() {
    //   this.tableLoading = true;
    //   const data = this.handleQueryParams();
    //   this.$post('', data).then(res => {
    //     const { fail, body } = res;
    //     if (!fail) {
    //       this.tableLoading = false;
    //       this.tableData = body.data;
    //       this.pagination.total = body.recordsTotal;
    //     }
    //   });
    // },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
</style>
