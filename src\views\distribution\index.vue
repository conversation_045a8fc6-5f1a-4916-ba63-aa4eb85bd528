<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="分销商品" name="goods">
        <distribution-goods @orderDetails="updateOrder" />
      </el-tab-pane>
      <el-tab-pane label="分销订单" name="order">
        <distribution-order :orderParams="orderParams" />
      </el-tab-pane>
      <el-tab-pane label="分销人明细" name="detail">
        <distribution-detail />
      </el-tab-pane>
      <el-tab-pane label="职业教育订单" name="educationOrder">
        <education-order />
      </el-tab-pane>
      <el-tab-pane label="职业教育月度绩效" name="educationKpi">
        <education-kpi />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import distributionGoods from './distribution-goods/index';
import distributionOrder from './distribution-order/index';
import distributionDetail from './distribution-detail/index';
import educationOrder from './education-order/index';
import educationKpi from './education-kpi/index';

export default {
  components: {
    distributionGoods,
    distributionOrder,
    distributionDetail,
    educationOrder,
    educationKpi
  },
  data() {
    return {
      activeName: 'goods',
      orderParams: ''
    };
  },
  methods: {
    handleClick() {},
    updateOrder(val) {
      this.activeName = 'order';
      this.orderParams = val;
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
