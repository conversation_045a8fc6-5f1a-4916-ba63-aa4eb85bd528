<template>
  <div class="UE">
    <script :id="elemId" type="text/plain" />
  </div>
</template>
<script>
import '../../../public/ueditor/ueditor.config';
import '../../../public/ueditor/ueditor.all';
import '../../../public/ueditor/ueditor.parse';
import '../../../public/ueditor/lang/zh-cn/zh-cn';

export default {
  name: 'UEditor',
  props: {
    maxWordCount: {
      type: Number,
      default: 10000
    },
    width: {
      type: Number,
      default: 800
    },
    height: {
      type: Number,
      default: 600
    }
  },
  data() {
    return {
      // 编辑器实例
      elemId: 'editor' + new Date().getTime(),
      editor: null,
      ueditorConfig: {
        // serverUrl: '', // [默认值：URL + "php/controller.php"] 服务器统一请求接口路径
        initialFrameWidth: this.width, // 初始化编辑器宽度，默认1000
        initialFrameHeight: this.height, // 初始化编辑器高度，默认320
        zIndex: 1000,
        maximumWords: this.maxWordCount,
        autoHeightEnabled: false
      }
    };
  },
  // 此时--el挂载到实例上去了,可以初始化对应的编辑器了
  mounted() {
    this.initEditor();
  },
  beforeDestroy() {
    // 组件销毁的时候，要销毁 UEditor 实例
    if (this.editor !== null && this.editor.destroy) {
      this.editor.destroy();
    }
  },
  methods: {
    initEditor() {
      // dom元素已经挂载上去了
      this.$nextTick(() => {
        this.editor = UE.getEditor(this.elemId, this.ueditorConfig);
        // 绑定事件，当 UEditor 初始化完成后，将编辑器实例通过自定义的 ready 事件交出去
        this.editor.addListener('ready', () => {
          this.$emit('ready', this.editor);
        });
      });
    },
    setContent(content) {
      if (this.editor) {
        this.editor.setContent(content);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '../../../public/ueditor/themes/default/css/ueditor.css';
</style>
