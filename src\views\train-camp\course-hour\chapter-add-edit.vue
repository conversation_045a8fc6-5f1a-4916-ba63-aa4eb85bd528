<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='来源' prop='source'>
          <el-select v-model="form.source" clearable :disabled="!!row && !!row.classHourId" @change="handleSourceChange">
            <el-option label="保利" value="BAOLI" />
            <el-option label="远智" value="YZ" />
            <el-option label="腾讯云" value="TXY" />
          </el-select>
        </el-form-item>

        <el-form-item label='上课类型' prop='type'>
          {{ form.source === 'BAOLI' || form.source === 'TXY' ? '录播' : '外部链接' }}
        </el-form-item>

        <el-form-item label='课时名称' prop='classHourName'>
          <el-input
            v-model="form.classHourName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item v-if="form.source !== 'TXY'" label='vids/链接' prop='vids'>
          <el-input
            v-model="form.vids"
          />
        </el-form-item>

        <el-form-item v-if="isShowTxy" label='腾讯云回放链接' prop='txyVideoUrl'>
          <el-input v-model="form.txyVideoUrl" placeholder='请输入腾讯云回放链接' />
        </el-form-item>

        <el-form-item label='关卡文案' prop='copySetting'>
          <el-input
            v-model="form.copySetting"
          />
        </el-form-item>

        <el-form-item label='关联试卷' prop='id'>
          <search-selects
            v-model="form.id"
            :default-option="typeDefaultOption"
            :props="{
              apiName: 'findClassExamPaper',
              value: 'id',
              label: 'name',
              query: 'paperName'
            }"
            :param="{
              paperPurpose: 'sjxs',
            }"
            @changeVal='changeTypeVal'
          />
        </el-form-item>
        <el-form-item label='课程资源' prop='fileInfo'>
          <el-upload
            class="upload-demo"
            :action="action"
            :on-success="handleSuccess"
            :on-progress="handlePreview"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            multiple
            :limit="3"
            :on-exceed="handleExceed"
            :file-list="classList"
          >
            <el-button size="small" type="primary">点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              上传文件大小需控制在100M以内<br>
              仅支持pdf、doc、docx、ppt、text、jpg、jpeg、png格式</div>
          </el-upload>
        </el-form-item>
        <el-form-item label='小结资料' prop="summaryInformationFileList">
          <upload-file
            :max-limit="10"
            :file-list='summaryList'
            @remove="handleDetailsRemoveImg"
            @success="detailsUploadSuccess"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
// import { splitChar } from '@/utils';
import { checkInput } from '@/common/vali';
import searchSelects from './search-selects';
export default {
  components: {
    searchSelects
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    directoryId: {
      type: String,
      default: ''
    },
    trainingId: {
      type: String,
      default: ''
    }
  },
  data() {
    // const validateFileList = (rule, value, callback) => {
    //   if (value.length === 0) {
    //     callback(new Error('请上传图片'));
    //   } else {
    //     callback();
    //   }
    // };
    return {
      show: false,
      action: '/file/webuploaderByName',
      exts: 'pdf,doc,docx,ppt,text,jpg,jpeg,png,zip,rar',
      size: 100,
      classHourId: '',
      typeDefaultOption: null,
      classList: [], // 课程资源回显
      summaryList: [], // 小结资料回显
      detailsImgs: [],
      classResourcesTemp: [],
      summaryTemp: [],
      form: {
        id: '',
        classHourName: '',
        source: 'TXY',
        type: 'playback',
        vids: '',
        paperId: '',
        paperName: '',
        copySetting: '',
        fileInfo: [],
        summaryInformation: '',
        summaryInformationFileList: [], // 小结资料文件集合
        classResources: '',
        classResourcesFileList: [], // 课程资源文件集合
        txyVideoUrl: ''
      },
      // vidsRules: { required: true, validator: checkInput, trigger: 'blur' },
      txyVideoUrlRules: { required: true, message: '请输入腾讯云回放链接', trigger: 'change' },
      rules: {
        classHourName: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ],
        copySetting: [
          { required: true, message: '请填写', trigger: 'blur' }
        ],
        source: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        // type: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ],
        vids: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ],
        txyVideoUrl: [
          // { required: true, message: '请输入腾讯云回放链接', trigger: 'change' }
        ]
        // summaryInformationFileList: [
        //   { required: true, validator: validateFileList, trigger: 'change' }
        // ]
      },
      attendClassId: null
    };
  },
  computed: {
    isShowTxy() {
      const isEdit = this.row?.classHourId; // 当前是否编辑状态
      const source = this.form.source;
      // 1.来源是腾讯云显示腾讯云回放链接输入框
      // 2.来源是保利且当前是编辑状态才显示腾讯云回放链接输入框
      const isTXY = source === 'TXY';
      const rule = isTXY ? [this.txyVideoUrlRules] : [];
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.rules.txyVideoUrl = [].concat(rule);
      return isTXY || (isEdit && source === 'BAOLI');
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
    // 'form.source'(val) {
    //   const rule = val !== 'TXY' ? [this.vidsRules] : [];
    //   this.rules.vids = [].concat(rule);
    // }
  },
  mounted() {
  },
  methods: {
    changeTypeVal(item) {
      this.form.paperId = item.value;
      this.form.paperName = item.label;
    },
    handleSourceChange(val) {
      if (val === 'BAOLI' || val === 'TXY') {
        this.form.type = 'playback';
      } else {
        this.form.type = 'toUrl';
      }
      this.$refs.form.clearValidate('txyVideoUrl');
    },
    open() {
      // this.classHourId = this.row.classHourId;
      if (this.row?.classHourId) {
        this.classHourId = this.row.classHourId;
        this.getClassInfo();
      }
    },
    getClassInfo() {
      const params = {
        classHourId: this.row.classHourId
      };
      this.$post('findClassHour', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.attendClassVO.content = JSON.parse(body.attendClassVO.content);
            this.form.classHourName = body.classHourName;
            this.form.copySetting = body.copySetting;
            this.form.classHourId = body.classHourId;
            this.form.source = body.type;
            this.form.type = body.attendClassVO.content.type;
            5;
            this.form.vids = body.attendClassVO.content.vidsUrl;
            this.classHourId = body.attendClassVO.classHourId;
            this.form.txyVideoUrl = body.txyVideoUrl;
            // 处理图片回显
            if (body.classResources) {
              this.form.classResources = body.classResources;
              this.classResourcesTemp = body.classResources.split(',');
              this.classResourcesTemp.forEach(url => {
                this.form.classResourcesFileList.push({ fileUrl: url, isAdd: 0 });
                this.classList.push({ name: url, url: ossUri + url, response: url });
              });
            }

            if (body.summaryInformation) {
              this.form.summaryInformation = body.summaryInformation;
              this.summaryTemp = body.summaryInformation.split(',');
              this.summaryTemp.forEach(url => {
                this.form.summaryInformationFileList.push({ fileUrl: url, isAdd: 0 });
                this.summaryList.push({ name: url, url: ossUri + url, response: url });
              });
            }

            this.form.id = body.paperId;
            this.form.paperId = body.paperId;
            this.form.paperName = body.paperName;
            this.typeDefaultOption = {
              id: body.paperId,
              name: body.paperName
            };

            // this.classList.push({ url: ossUri + body.coverPic });
            // classResources
            // summaryInformation
            // this.coverImgs.push({ url: ossUri + body.coverPic });
            // this.form.coverPicFile.fileUrl = body.coverPic;
            // const detailsImgs = splitChar(body.detailsPic);
            // detailsImgs.forEach(url => {
            //   this.form.summaryInformationFileList.push({ fileUrl: url, isAdd: 0 });
            //   this.detailsImgs.push({ url: ossUri + url, response: url });
            // });
          }
        });
    },

    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // console.log(this.row);
          // const formData = JSON.parse(JSON.stringify(this.form));
          // console.log(formData);
          // console.log(this.form.id);
          let apiKey = 'addTrainHourList';
          const params = {
            directoryId: this.directoryId,
            // paperId: this.form.id,
            ...this.form
          };

          if (this.row?.classHourId) {
            console.log(this.row);
            apiKey = 'editTrainHourList';
            params.classHourId = this.row.classHourId;
            params.attendClassId = this.row.attendClassId;
            params.classHourTrainId = this.row.classHourTrainId;
            params.trainingId = this.trainingId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$emit('refreshList');
              }
            });
        }
      });
    },
    // 小结资料
    handleDetailsRemoveImg({ file, fileList }) {
      // this.detailsImgs = fileList;
      // console.log('this.detailsImgs');
      // console.log(this.detailsImgs);
      let index = 0;
      for (let i = 0; i < this.form.summaryInformationFileList.length; i++) {
        const item = this.form.summaryInformationFileList[i];
        if ((item.fileUrl) === file.response) {
          index = i;
          break;
        }
      }

      this.form.summaryInformationFileList.splice(index, 1);
      this.summaryTemp.splice(index, 1);
      this.form.summaryInformation = this.summaryTemp.join(',');
    },
    detailsUploadSuccess({ response, file, fileList }) {
      console.log(file, 'file');
      console.log(fileList, 'fileList');
      console.log(file.response);
      this.summaryTemp.push(file.response);
      this.form.summaryInformation = this.summaryTemp.join(',');
      this.form.summaryInformationFileList.push({ fileUrl: file.response, isAdd: 1 });
    },

    // 课程资源
    handleRemove(file, fileList) {
      console.log(file, fileList);
      let index = 0;
      for (let i = 0; i < this.classResourcesTemp.length; i++) {
        const item = this.classResourcesTemp[i];
        if ((item.fileUrl) === file.response) {
          index = i;
          break;
        }
      }

      this.classResourcesTemp.splice(index, 1);
      this.form.classResourcesFileList.splice(index, 1);
      this.form.classResources = this.classResourcesTemp.join(',');
    },
    handlePreview(event, file, fileList) {
      // console.log(event, file, fileList);
    },
    handleSuccess(response, file, fileList) {
      console.log('课程资源');
      // console.log(file);
      // console.log(response);
      // console.log(fileList);
      this.classResourcesTemp.push(file.response);
      this.form.classResources = this.classResourcesTemp.join(',');
      this.form.classResourcesFileList.push({ fileUrl: file.response, isAdd: 1 });
      console.log(this.form.classResourcesFileList, 'this.form.classResourcesFileList');
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeUpload(file, fileList) {
      console.log(file);
      const fileType = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      const specifyFormat = this.exts.split(',');
      const isFitSize = file.size / 1024 / 1024 < this.size; // 是否符合规定大小
      const isFormat = specifyFormat.includes(fileType); // 是否符合规定文件格式

      if (this.exts) {
        if (!isFormat) {
          this.$message.error('请上传格式为 ' + specifyFormat.join('，') + '的文件');
          return false;
        }
      }

      if (this.size !== 0) {
        if (!isFitSize) {
          this.$message.error('文件大小不能超过 ' + this.size + 'M');
          return false;
        }
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
