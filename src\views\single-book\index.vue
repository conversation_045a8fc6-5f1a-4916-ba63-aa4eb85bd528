<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='名称' prop='courseName'>
        <el-input v-model="form.courseName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='领读人' prop='ledReadId'>
        <remote-search-selects
          v-model="form.ledReadId"
          :props="{
            apiName: 'getLedReadAllowList',
            value: 'ledReadId',
            label: 'ledReadName',
            query: 'ledReadName'
          }"
        />
      </el-form-item>

      <el-form-item label='分类' prop='goodsTypeId'>
        <remote-search-selects
          v-model="form.goodsTypeId"
          :props="{
            apiName: 'getSelectCfList',
            value: 'goodsTypeId',
            label: 'goodsTypeName',
            query: 'goodsTypeName'
          }"
          :param="{
            status: 1,
            goodsTypeLevel: '2',
            pGoodsTypeId: '3',
          }"
        />
      </el-form-item>

      <el-form-item label='状态' prop='courseStatus'>
        <el-select v-model="form.courseStatus" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain @click="tdVisible = true">领读人管理</el-button>
      <el-button type="primary" size="small" plain @click="cVisible = true">分类管理</el-button>
      <el-button type="success" size="small" @click="addBook">新增单本读书</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >

      <el-table-column prop="courseName" label="单本读书名称" align="center" />
      <el-table-column prop="ledReadName" label="领读人" align="center" />
      <el-table-column prop="goodsTypeName" label="分类" align="center" />
      <el-table-column prop="marketPrice" label="市场价" align="center" />
      <el-table-column prop="vipPrice" label="会员价" align="center" />
      <el-table-column prop="test" label="课时管理" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="editChapter(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="courseStatus" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.courseStatus === 1? 'success':'danger'">
            {{ scope.row.courseStatus === 1 ? '启用':'禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" align="center" width="100px" />
      <el-table-column prop="test" label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <el-button type="text" @click="updateStatus(scope.row)">
            {{ scope.row.courseStatus === 0 ? '启用': '禁用' }}
          </el-button>
          <el-button type="text" @click="edit(scope.row)">编辑</el-button>
          <el-button type="text" @click="editSort(scope.row)">排序</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <classification :visible.sync="cVisible" />
    <add-edit :visible.sync="aeVisible" :row="currentEditRow" />
    <chapter-dialog :visible.sync="cdVisible" :row="currentEditChapter" />
    <tracher-dialog :visible.sync="tdVisible" />
  </div>
</template>
<script>
import classification from './classification';
import addEdit from './add-edit';
import chapterDialog from './chapter-dialog';
import tracherDialog from './teacher';
export default {
  components: {
    classification,
    addEdit,
    chapterDialog,
    tracherDialog
  },
  data() {
    return {
      tableLoading: false,
      form: {
        courseName: '',
        goodsTypeId: '',
        ledReadId: '',
        courseStatus: null
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      cVisible: false,
      aeVisible: false,
      cdVisible: false,
      tdVisible: false,
      currentEditRow: null,
      currentEditChapter: null
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 编辑章节
    editChapter(row) {
      this.currentEditChapter = row;
      this.cdVisible = true;
    },
    // 编辑排序
    editSort(row) {
      this.$prompt('请输入排序（数字）', '修改', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.sort,
        inputPattern: /^[+]{0,1}(\d+)$/,
        inputErrorMessage: '请输入数字'
      }).then(({ value }) => {
        const params = {
          courseId: row.courseId,
          sort: value
        };
        this.$post('updateSingleBookSort', params)
          .then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
            }
          });
      });
    },
    updateStatus(row) {
      const params = {
        courseId: row.courseId
      };
      this.$post('updateSingleBookStatus', params)
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
    },
    addBook() {
      this.currentEditRow = null;
      this.aeVisible = true;
    },
    edit(row) {
      this.currentEditRow = row;
      this.aeVisible = true;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getSingleBook', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
}
</style>
