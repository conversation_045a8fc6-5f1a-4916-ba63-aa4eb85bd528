<template>
  <common-dialog
    show-footer
    :title="title"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <el-form ref="form" class="form" size='mini' :model="form" label-width="120px" :rules="rules">

      <el-form-item label="类型" prop="mebFreeType">
        <el-radio-group v-model="form.mebFreeType" :disabled="status" @change="handleMebTypeChange">
          <el-radio
            v-for="item in $localDict['mebFreeType']"
            :key="item.dictValue"
            :label="item.dictValue"
          >
            {{ item.dictName }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="卡种类" prop="mebPayType">
        <el-radio-group v-model="form.mebPayType" :disabled="!isFormItemEdit">
          <el-radio
            v-for="item in $localDict['mebPayType']"
            :key="item.dictValue"
            :label="item.dictValue"
          >
            {{ item.dictName }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="卡名称" prop="mebName">
        <el-input v-model="form.mebName" :disabled="!isFormItemEdit" placeholder="请输入" maxlength="4" show-word-limit />
      </el-form-item>

      <!-- 免费卡显示 -->
      <el-form-item v-if="freeFormShow" label="原价" prop="marketPrice">
        <el-input-number
          v-model="form.marketPrice"
          :disabled="!isFormItemEdit"
          placeholder="请输入"
          :max="1000000000"
          :precision="2"
          class="yz-input-number yuan"
          :controls="false"
        />
      </el-form-item>

      <!-- 免费卡显示 -->
      <el-form-item v-if="freeFormShow" label="支持领用人群" prop="receivingCrowd">
        <el-radio-group v-model="form.receivingCrowd" :disabled="!isFormItemEdit">
          <el-radio label="new">新注册用户</el-radio>
          <el-radio label="any">所有用户</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 付费卡显示 -->
      <el-form-item v-if="payFormShow" label="市场价" prop="marketPrice">
        <!-- <el-input v-model.number="form.marketPrice">
          <span slot="suffix">元</span>
        </el-input> -->
        <el-input-number
          v-model="form.marketPrice"
          :disabled="!isFormItemEdit"
          placeholder="请输入"
          :max="1000000000"
          :precision="2"
          class="yz-input-number yuan"
          :controls="false"
        />
      </el-form-item>

      <!-- 付费卡显示 -->
      <el-form-item v-if="payFormShow" label="是否有首购活动" prop="fristBuy">
        <el-radio-group v-model="form.fristBuy" :disabled="status" @change="handleFirstPurchase">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 付费卡显示 -->
      <el-form-item v-if="payFormShow && isFirstPurchase" label="首购价" prop="sell">
        <!-- 学员 -->
        <div class="distance">
          <span>学员首购</span>
          <el-input-number
            v-model="sell['S'].price"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
          <span class="padding-left text">，</span>
          <span class="padding-left text">续费</span>
          <el-input-number
            v-model="sell['S'].renewPrice"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>

        <!-- 老师 -->
        <div class="distance">
          <span>老师首购</span>
          <el-input-number
            v-model="sell['T'].price"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
          <span class="padding-left text">，</span>
          <span class="padding-left text">续费</span>
          <el-input-number
            v-model="sell['T'].renewPrice"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>

        <!-- 会员 -->
        <div class="distance">
          <span>其他首购</span>
          <el-input-number
            v-model="sell['M'].price"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
          <span class="padding-left text">，</span>
          <span class="padding-left text">续费</span>
          <el-input-number
            v-model="sell['M'].renewPrice"
            v-width="150"
            :disabled="!isFormItemEdit"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>

      </el-form-item>

      <!-- 付费卡显示 -->
      <el-form-item v-if="payFormShow && !isFirstPurchase" label="售价" prop="sell">
        <div class="distance">
          <span>学员</span>
          <el-input-number
            v-model="noSell['S'].price"
            v-width="150"
            :disabled="status"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>
        <div class="distance">
          <span>老师</span>
          <el-input-number
            v-model="noSell['T'].price"
            v-width="150"
            :disabled="status"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>
        <div class="distance">
          <span>其他</span>
          <el-input-number
            v-model="noSell['M'].price"
            v-width="150"
            :disabled="status"
            :max="1000000000"
            :precision="2"
            class="yz-input-number yuan padding-left"
            :controls="false"
          />
        </div>
      </el-form-item>

      <el-form-item label="库存" prop="limitMax">
        <el-input-number
          v-model="form.limitMax"
          placeholder="请输入10位以内的有效数字"
          :precision="0"
          class="yz-input-number"
          :controls="false"
          @blur="limitLength"
        />
      </el-form-item>

      <el-form-item :label="freeFormShow?'领取次数/人':'购买次数/人'" prop="limitGet">
        <input-number :disabled="!isFormItemEdit" :value.sync="form.limitGet" :length="10" placeholder="请输入10位以内的有效数字" :precision='0'>
          <span slot="suffix">次</span>
        </input-number>
      </el-form-item>

      <el-form-item v-if="payFormShow" label="基数" prop="baseShow">
        <input-number
          :value.sync="form.baseShow"
          :length="10"
          placeholder="请输入10位以内的有效数字"
          :precision='0'
        />
      </el-form-item>

      <!-- 付费卡显示 -->
      <el-form-item v-if="payFormShow" label="抵扣方式" prop="deductType">
        <el-checkbox-group v-model="form.deductType" :disabled="!isFormItemEdit">
          <el-checkbox v-for="item in $localDict['deductType']" :key="item.dictValue" :label="item.dictValue">
            {{ item.dictName }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          placeholder="请输入"
          class="yz-input-number"
          :controls="false"
        />
      </el-form-item>

      <el-form-item label="卡片介绍" prop="remark">
        <el-input
          v-model="form.remark"
          rows="3"
          type="textarea"
          placeholder="请输入内容"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="分享摘要" prop="summary">
        <el-input
          v-model="form.summary"
          :disabled="!isFormItemEdit"
          rows="3"
          type="textarea"
          placeholder="请输入内容"
          maxlength="23"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="是否公开" prop="hiddenStatus">
        <el-radio-group
          v-model="form.hiddenStatus"
          @change="handleFirstPurchase"
        >
          <el-radio label="1">公开</el-radio>
          <el-radio label="0">不公开</el-radio>
        </el-radio-group>
      </el-form-item>

    </el-form>
  </common-dialog>
</template>
<script>
import { sliceString } from '@/utils';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    cardId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    // 售价/首购价验证
    const validateSell = (rule, value, callback) => {
      if (this.form.fristBuy) {
        for (const key in this.sell) {
          const item = this.sell[key];
          if (item.price === '' || item.price === null || item.price === undefined) {
            callback(new Error('请填写'));
            break;
          }
          if (item.renewPrice === '' || item.renewPrice === null || item.renewPrice === undefined) {
            callback(new Error('请填写'));
            break;
          }
        }
        callback();
        // 验证首购价
      } else {
        for (const key in this.noSell) {
          const item = this.noSell[key];
          if (item.price === '' || item.price === null || item.price === undefined) {
            callback(new Error('请填写'));
            break;
          }
        }
        callback();
      }
    };

    return {
      isFormItemEdit: true, // true可以编辑，false不可编辑
      sourceData: null,
      show: false,
      limitMaxLabel: '',
      // 首购
      sell: {
        T: { price: undefined, renewPrice: undefined, ruleKey: '' }, // 老师
        S: { price: undefined, renewPrice: undefined, ruleKey: '' }, // 学员
        M: { price: undefined, renewPrice: undefined, ruleKey: '' } // 会员
      },
      // no 首购
      noSell: {
        T: { price: undefined, ruleKey: '' }, // 老师
        S: { price: undefined, ruleKey: '' }, // 学员
        M: { price: undefined, ruleKey: '' } // 会员
      },
      form: {
        receivingCrowd: '', // 领用人群
        sell: [],
        fristBuy: true,
        mebFreeType: 'free', // 默认免费体验卡
        mebPayType: '',
        remark: '',
        summary: '',
        mebName: '',
        deductType: [],
        sort: undefined,
        limitGet: undefined, // 一人领取次数
        limitMax: undefined, // 领取人数
        marketPrice: undefined, // 市场价/原价
        saleRules: [],
        baseShow: undefined,
        hiddenStatus: '1'
      },
      rules: {
        sell: [{ validator: validateSell, trigger: 'blur' }],
        mebFreeType: [{ required: true, message: '请选择', trigger: 'change' }],
        mebPayType: [{ required: true, message: '请选择', trigger: 'change' }],
        fristBuy: [{ required: true, message: '请选择', trigger: 'change' }],
        mebName: [{ required: true, message: '请填写', trigger: 'blur' }],
        deductType: [{ required: true, message: '请选择', trigger: 'change' }],
        receivingCrowd: [{ required: true, message: '请选择', trigger: 'change' }],
        marketPrice: [{ required: true, message: '请填写', trigger: 'blur' }],
        sort: [{ required: true, message: '请填写', trigger: 'blur' }],
        remark: [{ required: true, message: '请填写', trigger: 'blur' }],
        summary: [{ required: true, message: '请填写', trigger: 'blur' }],
        limitGet: [{ required: true, message: '请填写', trigger: 'blur' }],
        baseShow: [{ required: true, message: '请填写', trigger: 'blur' }],
        limitMax: [{ required: true, message: '请填写', trigger: 'blur' }],
        hiddenStatus: [{ required: true, message: '请选择', trigger: 'change' }]
        // summaryTitle: [{ required: true, message: '请输入分享标题', trigger: 'blur' }]
      }
    };
  },
  computed: {
    status() {
      if (this.cardId) {
        return true;
      }
      return false;
    },
    freeFormShow() {
      if (this.form.mebFreeType === 'free') {
        return true;
      } else {
        return false;
      }
    },
    payFormShow() {
      if (this.form.mebFreeType === 'pay') {
        return true;
      } else {
        return false;
      }
    },
    isFirstPurchase() {
      if (this.form.fristBuy) {
        return true;
      } else {
        return false;
      }
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      if (this.cardId !== null && this.cardId !== '') {
        this.getCardInfo();
      }
    },
    handleMebTypeChange() {
      this.form.limitGet = undefined;
      this.form.limitMax = undefined;
      this.form.marketPrice = undefined;
      this.$refs['form'].clearValidate();
    },
    limitLength() {
      let val = sliceString(this.form.limitMax, 10);
      if (isNaN(val)) {
        val = undefined;
      }
      this.form.limitMax = val;
    },
    // 获取会员卡信息
    getCardInfo() {
      this.$post('getCardInfo', { mebId: this.cardId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          // 回显表单
          this.form.hiddenStatus = body.hiddenStatus;
          this.form.fristBuy = body.fristBuy;
          this.form.mebFreeType = body.mebFreeType;// 卡类型
          this.form.mebPayType = body.mebPayType;// 卡种类
          this.form.mebName = body.mebName;// 卡名称
          this.form.limitMax = body.saleRules[0].limitMax;
          this.form.limitGet = body.saleRules[0].limitGet;
          this.form.sort = body.sort;// 排序
          this.form.remark = body.remark;// 卡片介绍
          this.form.summary = body.summary;// 分享摘要
          this.form.marketPrice = body.saleRules[0].marketPrice;
          this.form.deductType = typeof body.deductType === 'string' ? body.deductType.split(',') : [];// 抵扣方式
          this.form.receivingCrowd = body.saleRules[0].receiverType;// 支持领用人群
          this.form.baseShow = body.baseShow;
          this.sourceData = body;

          // 购买人数大于0,则部分表单不能编辑
          if (body.buyerCount > 0) {
            this.isFormItemEdit = false;
          }

          // 是否首购
          if (body.fristBuy) {
            body.saleRules.forEach(e => {
              if (this.sell[e.buyerType]) {
                // this.form.sell.push(e.buyerType);
                this.sell[e.buyerType].price = e.firstBuyPrice;
                this.sell[e.buyerType].renewPrice = e.renewPrice;
                this.sell[e.buyerType].ruleKey = e.ruleKey;
              }
            });
          } else {
            body.saleRules.forEach(e => {
              if (this.noSell[e.buyerType]) {
                // this.form.sell.push(e.buyerType);
                // this.noSell[e.buyerType].price = e.normalPrice;
                this.noSell[e.buyerType].price = e.commonPrice;
                this.noSell[e.buyerType].ruleKey = e.ruleKey;
              }
            });
          }
        }
      });
    },
    // 处理首购切换
    handleFirstPurchase(val) {
      this.form.sell = null;
    },
    async submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addVipCard';
          const sourceData = JSON.parse(JSON.stringify(this.form));
          sourceData.deductType = sourceData.deductType.join();
          const data = {
            'fristBuy': sourceData.fristBuy,
            'mebFreeType': sourceData.mebFreeType,
            'mebPayType': sourceData.mebPayType,
            'remark': sourceData.remark,
            'summary': sourceData.summary,
            'mebName': sourceData.mebName,
            'deductType': sourceData.deductType,
            'sort': sourceData.sort,
            'hiddenStatus': sourceData.hiddenStatus,
            'saleRules': []
          };

          // 判断 免费卡 或 vip卡
          if (data.mebFreeType === 'free') {
            data.saleRules.push({
              receiverType: sourceData.receivingCrowd, // 支持领取人群
              // buyerType: sourceData.receivingCrowd,
              limitGet: sourceData.limitGet,
              limitMax: sourceData.limitMax,
              firstBuyPrice: 0,
              renewPrice: 0,
              normalPrice: 0,
              marketPrice: sourceData.marketPrice,
              ruleKey: this.sourceData ? this.sourceData.saleRules[0].ruleKey : null
            });
          } else {
            data.baseShow = sourceData.baseShow;
            // 有首购
            if (sourceData.fristBuy) {
              for (const key in this.sell) {
                const obj = this.sell[key];
                data.saleRules.push({
                  ruleKey: obj.ruleKey,
                  buyerType: key,
                  firstBuyPrice: obj.price,
                  renewPrice: obj.renewPrice,
                  normalPrice: 0,
                  marketPrice: sourceData.marketPrice,
                  limitGet: sourceData.limitGet,
                  limitMax: sourceData.limitMax
                });
              }
            } else {
              for (const key in this.noSell) {
                const obj = this.noSell[key];
                data.saleRules.push({
                  ruleKey: obj.ruleKey,
                  buyerType: key,
                  firstBuyPrice: 0,
                  renewPrice: 0,
                  // normalPrice: obj.price,
                  commonPrice: obj.price,
                  marketPrice: sourceData.marketPrice,
                  limitGet: sourceData.limitGet,
                  limitMax: sourceData.limitMax
                });
              }
            }
          }
          if (this.cardId) {
            apiKey = 'editCard';
            data.mebId = this.cardId;
          }

          this.$post(apiKey, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(res => {
            if (!res.fail) {
              this.show = false;
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      this.isFormItemEdit = true;
      this.form.deductType = [];// element 重置表单后如果是数组类型，默认会有个空的项
      Object.assign(this.$data.sell, this.$options.data().sell);// 重置data
      Object.assign(this.$data.noSell, this.$options.data().noSell);// 重置data
      Object.assign(this.$data.form, this.$options.data().form);
      this.$emit('update:visible', false);
    }
  }
};
</script>
<style lang="scss" scoped>
.form{
  padding:20px;
  .text{
    font-size:14px;
  }
  .padding-left{
    padding-left: 8px;
  }
  .distance {
    margin:2px 0;
  }

}
</style>

