<template>
  <common-dialog
    :title="empName + '职业教育月度绩效明细'"
    width="800px"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <div class="header_content">
        <el-row :gutter="20" type="flex" align="middle">
          <el-col :span="3">年度</el-col>
          <el-col :span="10">
            <el-date-picker
              v-model="selectAnnual"
              type="year"
              placeholder="选择年度"
              value-format="yyyy"
            />
          </el-col>
        </el-row>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="month" label="月份" align="center" />
        <el-table-column prop="income" label="职业教育累计绩效" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleAll(scope.row)">{{ scope.row.income }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <kpi-person-detail-dialog :visible.sync="allVisible" :empId="empId" :empName="empName" :month="month" />
  </common-dialog>
</template>

<script>
import kpiPersonDetailDialog from './kpi-person-detail-dialog';
export default {
  components: {
    kpiPersonDetailDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    empId: {
      type: String,
      default: ''
    },
    empName: {
      type: String,
      default: ''
    },
    annual: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      allVisible: false,
      tableLoading: false,
      tableData: [],
      selectAnnual: '',
      month: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    selectAnnual(val) {
      if (val) {
        this.getTableList();
      }
    }
  },
  mounted() {
  },
  methods: {
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = {
        annual: this.selectAnnual,
        empId: this.empId
      };
      this.$post('queryEmployeePerformanceDetails', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body;
        }
      });
    },
    open() {
      this.selectAnnual = this.annual;
      this.getTableList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleAll(row) {
      const { month } = row;
      this.allVisible = true;
      this.month = month;
    }
  }
};
</script>

<style lang='scss' scoped>
.header_content {
  margin-bottom: 30px;
}
</style>
