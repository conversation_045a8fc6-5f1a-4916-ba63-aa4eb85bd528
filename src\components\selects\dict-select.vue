<template>
  <el-select v-model="val" class="yz-dict-select" :disabled="disabled" filterable clearable :placeholder="placeholder" @change="onChange">
    <el-option v-for="item in $dictJson[keys]" :key="item.dictValue" :value="item.dictValue" :label="item.dictName" />
  </el-select>
</template>

<script>
export default {
  name: 'DictSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    keys: { // 字典键值
      type: String,
      default: '',
      required: true
    },

    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      val: ''
    };
  },
  watch: {
    value(v) {
      this.val = v;
    }
  },
  mounted() {
    this.val = this.value;
  },
  methods: {
    onChange(val) {
      this.$emit('input', val);
    }
  }
};
</script>

<style lang='scss' scoped>
  .yz-dict-select{

  }
</style>
