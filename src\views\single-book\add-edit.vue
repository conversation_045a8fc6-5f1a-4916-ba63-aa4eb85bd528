<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='单本读书名称' prop='courseName'>
          <el-input
            v-model="form.courseName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='分类' prop='goodsTypeId'>
          <remote-search-selects
            v-model="form.goodsTypeId"
            :default-option="typeDefaultOption"
            :props="{
              apiName: 'getSelectCfList',
              value: 'goodsTypeId',
              label: 'goodsTypeName',
              query: 'goodsTypeName'
            }"
            :param="{
              status: 1,
              goodsTypeLevel: '2',
              pGoodsTypeId: '3',
            }"
          />
        </el-form-item>

        <el-form-item label='领读人' prop='ledReadId'>
          <remote-search-selects
            v-model="form.ledReadId"
            :default-option="ledReadIdDefaultOption"
            :props="{
              apiName: 'getLedReadAllowList',
              value: 'ledReadId',
              label: 'ledReadName',
              query: 'ledReadName'
            }"
          />
        </el-form-item>

        <el-form-item label='单本书本封面图' prop='coverPicFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='coverImgs'
            @remove="handleCoverRemoveImg"
            @success="coverUploadSuccess"
          />
        </el-form-item>

        <el-form-item label='简介' prop="detailsPicFileList">
          <upload-file
            :max-limit="10"
            :file-list='detailsImgs'
            @remove="handleDetailsRemoveImg"
            @success="detailsUploadSuccess"
          />
        </el-form-item>

        <el-form-item label='概述' prop='introductionText'>
          <el-input
            v-model="form.introductionText"
            :rows="4"
            type="textarea"
            placeholder="请输入内容"
            maxlength="30"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='市场价' prop='marketPrice'>
          <el-input-number v-model="form.marketPrice" :min="0" />
        </el-form-item>

        <el-form-item label='会员价' prop='vipPrice'>
          <el-input-number v-model="form.vipPrice" :min="0" />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { splitChar } from '@/utils';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    const validateFileList = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请上传图片'));
      } else {
        callback();
      }
    };
    return {
      show: false,
      form: {
        courseName: '',
        goodsTypeId: '',
        introductionText: '',
        ledReadId: '',
        marketPrice: '',
        vipPrice: '',
        coverPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        detailsPicFileList: []
      },
      rules: {
        courseName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        goodsTypeId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        ledReadId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        introductionText: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        marketPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        vipPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        'coverPicFile.fileUrl': [
          { required: true, message: '请上传', trigger: 'change' }
        ],
        detailsPicFileList: [
          { required: true, validator: validateFileList, trigger: 'change' }
        ]
      },
      coverImgs: [],
      detailsImgs: [],
      typeDefaultOption: null,
      ledReadIdDefaultOption: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      if (this.row) {
        this.getcourseInfo();
      }
    },
    getcourseInfo() {
      const params = {
        courseId: this.row.courseId
      };
      this.$post('getSingleBookInfo', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.courseName = body.courseName;
            this.form.goodsTypeId = body.goodsTypeVo.goodsTypeId;
            this.form.introductionText = body.introductionText;
            this.form.ledReadId = body.ledReadDTO.ledReadId;
            this.form.marketPrice = body.marketPrice;
            this.form.vipPrice = body.vipPrice;
            this.typeDefaultOption = {
              goodsTypeName: body.goodsTypeVo.goodsTypeName,
              goodsTypeId: body.goodsTypeVo.goodsTypeId
            };
            this.ledReadIdDefaultOption = {
              ledReadName: body.ledReadDTO.ledReadName,
              ledReadId: body.ledReadDTO.ledReadId
            };

            // 处理图片回显
            this.coverImgs.push({ url: ossUri + body.coverPic });
            this.form.coverPicFile.fileUrl = body.coverPic;
            const detailsImgs = splitChar(body.detailsPic);
            detailsImgs.forEach(url => {
              this.form.detailsPicFileList.push({ fileUrl: url, isAdd: 0 });
              this.detailsImgs.push({ url: ossUri + url, response: url });
            });
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addSingleBook';
          const params = {
            ...this.form
          };

          if (this.row) {
            apiKey = 'editSingleBook';
            params.courseId = this.row.courseId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$parent.getTableList();
              }
            });
        }
      });
    },
    handleCoverRemoveImg({ file, fileList }) {
      this.form.coverPicFile.fileUrl = '';
    },
    coverUploadSuccess({ response, file, fileList }) {
      this.form.coverPicFile.fileUrl = response;
      this.form.coverPicFile.isAdd = 1;
    },
    handleDetailsRemoveImg({ file, fileList }) {
      this.detailsImgs = fileList;
      let index = 0;
      for (let i = 0; i < this.form.detailsPicFileList.length; i++) {
        const item = this.form.detailsPicFileList[i];
        if ((item.fileUrl) === file.response) {
          index = i;
          break;
        }
      }

      this.form.detailsPicFileList.splice(index, 1);
    },
    detailsUploadSuccess({ response, file, fileList }) {
      this.detailsImgs = fileList;
      this.form.detailsPicFileList.push({ fileUrl: response, isAdd: 1 });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
