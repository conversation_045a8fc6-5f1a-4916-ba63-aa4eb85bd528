/**
 * element-ui rules校验
 *
 */
export const checkInput = (rule, value, callback) => {
  if (value.trim() !== '') {
    callback();
  } else {
    return callback(new Error('请输入'));
  }
};

//  图片
export function validatePicList(rule, value, callback) {
  if (value.length === 0) {
    callback(new Error('请上传图片'));
  } else {
    callback();
  }
}

// element时间段校验
export function checkReTime(rule, value, callback) {
  if (!value) {
    callback('请选择');
  } else {
    if (value[0] === '' && value[1] === '') {
      callback('请选择');
    } else {
      callback();
    }
  }
}

// 正整数
export function positiveInteger(rule, value, callback) {
  var pattern = /^-?[0-9]\d*$/; // 整数的正则表达式
  if (!value) {
    callback('请输入');
  } else if (!pattern.test(value)) {
    // input 框绑定的内容为空
    value = '';
    callback('请输入整数');
  } else {
    callback();
  }
}
