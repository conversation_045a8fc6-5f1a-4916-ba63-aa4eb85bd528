<template>
  <common-dialog
    :show-footer="true"
    width="1000px"
    :title="title"
    :visible.sync="show"
    @open="open"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        v-loading="loading"
        class="form"
        size="mini"
        :model="form"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="选择送礼人" prop="consignorEmpId">
          <SearchSelects
            key="consignorEmpId"
            v-model="form.consignorEmpId"
            placeholder="请输入姓名/手机号/远智编号搜索"
            :props="{
              apiName: api['getConsignorPage'],
              value: 'id',
              label: 'name',
              method: 'get',
              query: 'search',
            }"
            :param="{ search: '' }"
            :disabled="!!id"
            :default-option="consignorEmpDefaultOption"
          />
        </el-form-item>

        <el-form-item label="选择礼品" prop="productId">
          <SearchSelects
            key="productId"
            v-model="form.productId"
            placeholder="请输入商品id查询"
            :props="{
              apiName: 'getProductPage',
              value: 'id',
              label: 'productName',
              query: 'id',
              method: 'post',
            }"
            :param="{ productType: 'SELF_ENTITY_PRODUCT'}"
            :default-option="productDefaultOption"
            @changeVal="handleChangeGoods"
          />
        </el-form-item>
        <el-form-item
          v-if="form.productId"
          label="商品名称"
        >{{ productName }}</el-form-item>
        <el-form-item
          v-if="form.productId"
          label="商品规格"
          prop="productSpecId"
        >
          <el-radio-group v-model="form.productSpecId">
            <el-radio v-for="item in productSpecDetailList" :key="item.id" :label="item.id">{{ item.specName }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="赠送件数" prop="productAmount">
          <el-input-number
            v-model="form.productAmount"
            placeholder="请输入"
            class="yz-input-number"
            :min="1"
            :controls="false"
            :step="1"
            :precision="0"
          />
        </el-form-item>

        <el-form-item label="选择收礼人" prop="consigneeUserId">
          <SearchSelects
            key="consigneeUserId"
            v-model="form.consigneeUserId"
            placeholder="请输入姓名/手机号/远智编号搜索"
            :props="{
              apiName: api['getConsigneePage'],
              value: 'id',
              label: 'name',
              method: 'get',
              query: 'search',
            }"
            :param="{ search: '' }"
            :default-option="consigneeUserDefaultOption"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import SearchSelects from './search-selects';
import { api } from '@/api';
export default {
  components: {
    SearchSelects
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number, Object],
      default: null
    }
  },
  data() {
    return {
      api,
      show: false,
      consignorEmpDefaultOption: null,
      productDefaultOption: null,
      consigneeUserDefaultOption: null,
      loading: false,
      productSpecDetailList: [], // 商品规格
      productName: '',
      form: {
        consignorEmpId: null,
        consigneeUserId: null,
        productId: null,
        productSpecId: null,
        productAmount: undefined
      },
      rules: {
        consignorEmpId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        consigneeUserId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        productSpecId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        productId: [{ required: true, message: '请选择', trigger: 'change' }],
        productAmount: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      },
      isFlag: false // 作用回显编辑的时候
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    'form.productId'(value) {
      if (value) {
        this.loading = true;
        this.getGoodsDetail(value);
      }
    }
  },
  methods: {
    handleChangeGoods() {
      this.isFlag = true;
    },
    // 获取商品详情
    getGoodsDetail(val) {
      const url = api['getProductDetail'].replace('{id}', val);
      this.$http.get(url)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.productSpecDetailList = body.productSpecDetailList;
            if (body.productSpecDetailList.length > 0) {
              if (this.isFlag) {
                this.form.productSpecId = body.productSpecDetailList[0].id;
              }
              this.productName = body?.productName;
            }
          }
        }).finally(() => { this.loading = false; });
    },

    open() {
      if (this.id) {
        this.loading = true;
        const url = api['getGivingOrderDetail'].replace('{id}', this.id);
        this.$http.get(url).then((res) => {
          const { fail, body } = res;
          if (!fail) {
            this.form.consignorEmpId = body.consignorEmpId;
            this.form.consigneeUserId = body.consigneeUserId;
            this.form.productId = body.productId;
            this.form.productSpecId = body.productSpecId;
            this.form.productAmount = body.productAmount;
            this.productName = body.productName;

            this.consignorEmpDefaultOption = {
              id: body.consignorEmpId,
              name: `${body.consignorEmpName}(${body.consignorEmpCode})`
            };
            this.consigneeUserDefaultOption = {
              id: body.consigneeUserId,
              name: `${body.consigneeUserName}(${body.consigneeUserCode})`
            };
            this.productDefaultOption = {
              id: body.productId,
              productName: body.productName
            };
          }
          this.loading = false;
        });
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let url = api['addGivingOrder'];
          const formData = JSON.parse(JSON.stringify(this.form));
          const params = {
            ...formData,
            productName: this.productName,
            productSpecName: this.productSpecDetailList.find(item => item.id === formData.productSpecId)?.specName
          };

          if (this.id) {
            url = api['updateGivingOrder'].replace('{id}', this.id);
          }
          this.$http.post(url, params, { json: true }).then((res) => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$parent.getTableList();
            }
          });
        }
      });
    },

    close() {
      // 这里必须 call 指向当前的实例
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.show = false;
    }
  }
};
</script>

<style lang="scss">
.inline-block {
  display: inline-block;
}

.group-select {
  margin-right: 14px;
}
</style>
