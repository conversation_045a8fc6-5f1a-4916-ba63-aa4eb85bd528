<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="90px"
      @submit.native.prevent="getData"
    >
      <el-form-item label="工具名称" prop="title">
        <el-input v-model="form.title" placeholder="请输入工具名称" clearable />
      </el-form-item>

      <el-form-item label="状态" prop="isEnable">
        <el-select v-model="form.isEnable" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
        >搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetForm" />
      </div>
    </el-form>

    <div class="table-tools">
      <el-button
        type="primary"
        size="mini"
        @click="broadcastVisible = true"
      >广播配置</el-button>
      <el-button type="primary" size="mini" @click="onEdit()">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="loading"
      border
      size="small"
      class="table-container"
      :data="list"
    >
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="title" label="工具名称" align="center" />
      <el-table-column prop="buttonName" label="按钮名称" align="center" />
      <el-table-column prop="tabName" label="工具概括" align="center" />
      <el-table-column
        prop="context"
        label="工具简介"
        align="center"
      />
      <el-table-column
        prop="isEnable"
        label="状态"
        align="center"
        :formatter="handleIsEnable"
      />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <!-- <div class="yz-table-pagination">
      <pagination
        :total="pager.total"
        :page.sync="pager.pageNum"
        :limit.sync="pager.pageSize"
        @pagination="getData"
      />
    </div> -->

    <!-- 新增 | 编辑弹框 -->
    <DialogDetail
      :show.sync="detailVisible"
      :row="editRow"
      @confirm="getData"
    />

    <!-- 广播配置弹框 -->
    <DialogBroadcast :show.sync="broadcastVisible" />
  </div>
</template>

<script>
import DialogDetail from './components/DialogDetail.vue';
import DialogBroadcast from './components/DialogBroadcast.vue';

const formInit = {
  title: undefined,
  isEnable: undefined
};
export default {
  components: { DialogDetail, DialogBroadcast },
  data() {
    return {
      loading: true,
      detailVisible: false,
      broadcastVisible: false,
      editRow: null,
      list: [],
      form: { ...formInit }
      // pager: {
      //   pageNum: 1,
      //   pageSize: 10,
      //   total: 0
      // }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.loading = true;
        // const params = { ...this.pager, ...this.form };
        // delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/bmsAdmin/marketToolList',
          data: { ...this.form },
          json: true
        });
        if (code !== '00') return;
        // this.pager.total = body.recordsTotal;
        this.list = body;
      } finally {
        this.loading = false;
      }
    },
    // 重置表单
    resetForm() {
      this.form = { ...formInit };
      // this.pager.pageNum = 1;
      this.getData();
    },

    // 处理状态显示
    handleIsEnable(row) {
      return row.isEnable === 1 ? '启用' : '禁用';
    },
    onEdit(row) {
      this.editRow = row;
      this.detailVisible = true;
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}
</style>
