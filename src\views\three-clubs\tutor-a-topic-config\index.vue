<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='70px'
      @submit.native.prevent='onSearch'
    >

      <el-form-item label='模块' prop="moduleType">
        <el-select v-model="form.moduleType" placeholder="请选择模块" clearable>
          <el-option v-for="item in moduleOpts" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label='标题' prop="title">
        <el-input v-model="form.title" placeholder="请输入标签" clearable />
      </el-form-item>

      <el-form-item label='状态' prop="ifAllow">
        <el-select v-model="form.ifAllow" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch("reset")' />
      </div>

    </el-form>

    <div class="table-tools">
      <el-button type="primary" size="mini" @click="onEdit()">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="moduleType" label="模块" align="center" :formatter="handleModuleType" />
      <el-table-column prop="title" label="标题" align="center" />
      <el-table-column prop="createUser" label="创建人" align="center" />
      <el-table-column prop="createTime" label="创建时间" align="center" />
      <el-table-column prop="ifAllow" label="状态" align="center" :formatter="handleStatus" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)"> 编辑 </el-button>
          <el-button type="text" size="small" @click="onStatus(row)"> {{ row.ifAllow === 1 ? '禁用' : '启用' }} </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div>
    <!-- 新增|编辑弹框 -->
    <dialog-operation :visible.sync="operationVisible" :row="editRow" @confirm="getData" />
  </div>
</template>

<script>
import DialogOperation from './components/dialog-operation.vue';
import { DModuleType } from '@/dict';

export default {
  components: { DialogOperation },
  data() {
    return {
      moduleOpts: DModuleType,
      tableLoading: true,
      operationVisible: false,
      editRow: null,
      tableData: [],
      form: {
        moduleType: undefined,
        title: undefined,
        ifAllow: undefined
      },
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/moduleRecommendConfig/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 处理模块显示
    handleModuleType(row) {
      return this.moduleOpts.find((v) => v.dictValue === row.moduleType)?.dictName;
    },
    // 处理状态显示
    handleStatus(row) {
      return row.ifAllow === 1 ? '启用' : '禁用';
    },
    // btn 搜索 | 重置
    onSearch(type) {
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 新增|编辑
    onEdit(row) {
      this.editRow = row;
      this.operationVisible = true;
    },
    // btn 修改状态
    onStatus(row) {
      this.$confirm(`您确定${row.ifAllow === 1 ? '禁用' : '启用'}此模块？`, '提示', {
        type: 'warning',
        confirmButtonText: '是',
        cancelButtonText: '否'
      }).then(async(e) => {
        if (e !== 'confirm') return;
        this.tableLoading = true;
        const { code } = await this.$http({
          method: 'post',
          url: `/moduleRecommendConfig/updateStatus/${row.id}/${row.ifAllow === 1 ? 0 : 1}`
        });
        this.tableLoading = false;
        if (code !== '00') return;
        this.$message.success(`${row.ifAllow === 1 ? '禁用' : '启用'}成功`);
        this.getData();
      }).catch(() => {
        this.tableLoading = false;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}
</style>
