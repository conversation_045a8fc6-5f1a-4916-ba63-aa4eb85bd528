<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='来源' prop='source'>
          <el-select v-model="form.source" clearable :disabled="!!classHourId" @change="handleSourceChange">
            <el-option label="保利" value="BAOLI" />
            <el-option label="远智" value="YZ" />
            <el-option label="腾讯云" value="TXY" />
          </el-select>
        </el-form-item>

        <el-form-item label='上课类型' prop='type'>
          {{ form.source === 'BAOLI' || form.source === 'TXY' ? '录播' : '外部链接' }}
        </el-form-item>

        <el-form-item label='课时名称' prop='classHourName'>
          <el-input
            v-model="form.classHourName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item v-if="form.source !== 'TXY'" label='vids/链接' prop='vids'>
          <el-input
            v-model="form.vids"
          />
        </el-form-item>

        <el-form-item v-if="isShowTxy" label='腾讯云回放链接' prop='txyVideoUrl'>
          <el-input v-model="form.txyVideoUrl" placeholder='请输入腾讯云回放链接' />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { checkInput } from '@/common/vali';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    directoryId: {
      type: [String, Number],
      default: null
    },
    classHourId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      form: {
        classHourName: '',
        source: 'TXY',
        type: 'playback',
        vids: '',
        txyVideoUrl: ''
      },
      // vidsRules: { required: true, validator: checkInput, trigger: 'blur' },
      txyVideoUrlRules: { required: true, message: '请输入', trigger: 'blur' },
      rules: {
        classHourName: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ],
        source: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        // type: [
        //   { required: true, message: '请选择', trigger: 'change' }
        // ],
        vids: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ],
        txyVideoUrl: [
          // { required: true, message: '请输入腾讯云回放链接', trigger: 'change' }
        ]
      },
      attendClassId: null
    };
  },
  computed: {
    isShowTxy() {
      const isEdit = this.classHourId; // 当前是否编辑状态
      const source = this.form.source;
      // 1.来源是腾讯云显示腾讯云回放链接输入框
      // 2.来源是保利且当前是编辑状态才显示腾讯云回放链接输入框
      const isTXY = source === 'TXY';
      const rule = isTXY ? [this.txyVideoUrlRules] : [];
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.rules.txyVideoUrl = [].concat(rule);
      return isTXY || (isEdit && source === 'BAOLI');
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
    // 'form.source'(val) {
    //   const rule = val !== 'TXY' ? [this.vidsRules] : [];
    //   this.rules.vids = [].concat(rule);
    // }
  },
  mounted() {},
  methods: {
    handleSourceChange(val) {
      if (val === 'BAOLI' || val === 'TXY') {
        this.form.type = 'playback';
      } else {
        this.form.type = 'toUrl';
      }
      this.$refs.form.clearValidate('vids');
      this.$refs.form.clearValidate('txyVideoUrl');
    },
    open() {
      if (this.classHourId) {
        this.getClassInfo();
      }
    },
    getClassInfo() {
      const params = {
        classHourId: this.classHourId
      };
      this.$post('getSingleBookClassHourInfo', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.attendClassVO.content = JSON.parse(body.attendClassVO.content);
            this.form.classHourName = body.classHourName;
            this.form.source = body.attendClassVO.type;
            this.form.type = body.attendClassVO.content.type;
            this.form.vids = body.attendClassVO.content.vidsUrl;
            this.attendClassId = body.attendClassVO.attendClassId;
            this.form.txyVideoUrl = body.txyVideoUrl;
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addSingleBookClassHour';
          const params = {
            directoryId: this.directoryId,
            ...this.form
          };

          if (this.classHourId) {
            apiKey = 'editSingleBookClassHour';
            params.classHourId = this.classHourId;
            params.attendClassId = this.attendClassId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$emit('refreshList');
              }
            });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
