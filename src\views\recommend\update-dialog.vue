<template>
  <common-dialog
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="init"
    @confirm='submit'
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >

        <el-form-item label='名称' prop='bannerName'>
          <el-input
            v-model="form.bannerName"
            maxlength="50"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='上架模块' prop='bannerBelong'>
          <el-select v-model="form.bannerBelong" @change="handleSwitchModel">
            <el-option label="banner" value="5" />
            <el-option label="推荐区" value="6" />
            <el-option label="APP首页" value="7" />
            <el-option label="读书计划列表页banner" value="8" />
          </el-select>
        </el-form-item>

        <el-form-item label='排序号' prop='sort'>
          <el-input-number
            v-model="form.sort"
            placeholder='请输入排序'
            :precision="0"
            class="yz-input-number"
            :min="0"
            :max="1000000000"
            :controls="false"
          />
        </el-form-item>

        <el-form-item v-if="form.bannerBelong == '5'||form.bannerBelong == '8'" label='图片' prop='fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <!-- 上架模块选择为 banner 显示 -->
        <el-form-item v-if="form.bannerBelong==='5' ||form.bannerBelong == '8' " label='跳转类型' prop='bannerType'>
          <el-radio-group v-model="form.bannerType">
            <el-radio :label="1">链接跳转</el-radio>
            <el-radio :label="2">内部跳转</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item
          v-if="form.bannerBelong==='5' ||form.bannerBelong == '8'"
          label='跳转url'
          prop='redirectUrl'
        >
          <el-input v-model="form.redirectUrl" maxlength="500" placeholder="请输入" />
        </el-form-item>

        <!-- 上架模块选择为推荐区,app显示-->
        <el-form-item
          v-if="appORarea"
          label='推荐套餐类型'
          prop='category'
        >
          <el-select v-model="form.category" @change="handleCategoryChange">
            <el-option label="单课程套餐" value="1" />
            <el-option label="多课程套餐" value="2" />
          </el-select>
        </el-form-item>

        <!-- 上架模块选择为推荐区,app显示 -->
        <el-form-item v-if="appORarea" label='套餐ID' prop='mappingId'>
          <el-select
            v-model="form.mappingId"
            v-loadmore="loadmoreSetMeal"
            filterable
            remote
            clearable
            :remote-method="remoteMethod"
            @clear="resetList"
          >
            <el-option
              v-for="item in filterSetMeal"
              :key="item.goodsShelfId"
              :label="item.goodsShelfName"
              :value="item.goodsShelfId "
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.bannerBelong==='5' ||form.bannerBelong == '8'" label='可见人群' prop='crowd'>
          <el-checkbox-group v-model="form.crowd">
            <el-checkbox label="1">会员可见</el-checkbox>
            <el-checkbox label="2">非会员可见</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label='状态' prop='allow'>
          <el-radio-group v-model="form.allow">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    bannerId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      show: false,
      queryVal: null,
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      form: {
        sort: undefined,
        fileUrl: '',
        bannerName: '',
        bannerBelong: '',
        bannerType: '',
        redirectUrl: '',
        category: '',
        mappingId: '',
        crowd: [],
        allow: '',
        isAdd: 0
      },
      setMealList: [],
      fileList: [],
      SetMealPage: 1,
      rules: {
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        fileUrl: [
          { required: true, message: '请上传图片', trigger: 'blur' }
        ],
        bannerName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        redirectUrl: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        bannerBelong: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        bannerType: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        category: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        mappingId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        crowd: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        allow: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  computed: {
    filterSetMeal() {
      const res = new Map();
      return this.setMealList.filter((arr) => !res.has(arr.goodsShelfId) && res.set(arr.goodsShelfId, 1));
    },
    appORarea() {
      return this.form.bannerBelong === '6' || this.form.bannerBelong === '7';
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    async init() {
      if (this.bannerId) {
        const { fail, body } = await this.$post('getBannerInfo', { bannerId: this.bannerId });
        if (!fail) {
          this.getSetMealList();
          this.setMealList.push({
            goodsShelfId: Number(body.mappingId),
            goodsShelfName: body.goodsShelfName
          });

          this.form.sort = body.sort;
          this.form.fileUrl = body.bannerUrl;

          this.form.bannerName = body.bannerName;
          this.form.bannerBelong = body.bannerBelong;
          this.form.bannerType = body.bannerType;
          this.form.redirectUrl = body.redirectUrl;
          this.form.category = body.category;
          this.form.mappingId = Number(body.mappingId);
          this.form.crowd = body.crowd ? body.crowd.split(',') : '';
          this.form.allow = body.allow;
          this.form.sort = body.sort;

          if (body.bannerBelong === '5' || body.bannerBelong === '8') {
            this.fileList.push({ url: ossUri + body.bannerUrl });
          }
        }
      }
    },
    handleCategoryChange() {
      this.form.mappingId = '';
      this.pagination.page = 1;
      this.setMealList = [];
      this.getSetMealList();
    },
    resetList() {
      this.remoteMethod();
    },
    loadmoreSetMeal() {
      if (this.pagination.total === this.setMealList.length) {
        return;
      }
      this.pagination.page += 1;
      this.getSetMealList();
    },
    remoteMethod(query) {
      this.setMealList = [];
      this.queryVal = query;
      this.pagination.page = 1;
      this.getSetMealList();
    },
    getSetMealList() {
      const data = {
        page: this.pagination.page,
        rows: this.pagination.limit,
        goodsShelfType: this.form.category, // 套餐类型
        shelfStatus: 1,
        goodsShelfName: this.queryVal// 名称
      };
      this.$post('getSetMealList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.setMealList = this.setMealList.concat(body.data);
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    handleRemoveImg({ file, fileList }) {
      this.form.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.fileUrl = response;
      this.form.isAdd = 1;
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addBanner';
          const formData = JSON.parse(JSON.stringify(this.form));

          if (formData.bannerBelong === '5') {
            formData.crowd = formData.crowd.join();
          } else {
            formData.crowd = '';
          }

          if (this.bannerId) {
            apiKey = 'editBanner';
            formData.bannerId = this.bannerId;
          }

          const data = {
            ...formData
          };
          this.$post(apiKey, data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
              this.show = false;
            }
          });
        } else {
          return false;
        }
      });
    },
    handleSwitchModel(value) {
      if (value === '5') {
        this.form.category = null;
        this.form.mappingId = null;
      } else if (value === '6') {
        this.form.bannerType = null;
        this.form.redirectUrl = null;
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-main{
  padding:20px;
  .hide{
    ::v-deep .el-upload--picture-card{
      display: none;
    }
  }
}
</style>
