<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="导入学员"
    :visible.sync='show'
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='form'
        label-width='120px'
      >
        <el-form-item label='模板：'>
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label='选择文件：'>
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            <div slot="tip" class="el-upload__tip">
              <p>说明：</p>
              <p>(用户名, 赠送id, 赠送名称, 赠送类型）为必填列</p>
              <p>远智编码和手机号可同时填写，也可只填写其中一项，但不允许都为空。</p>
              <p style="color: red;">请严格按模板填写，否则可能导致无法准确导入。</p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { downUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      field: 'excelPerformance',
      templateUrl: downUri + '/excel/MarketingGiveTemplate.xlsx',
      action: '/puMarketGive/importUpdateGive'
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    submit() {
      console.log(this.fileList);
      if (this.fileList.length > 0) {
        this.$confirm('确认赠送？一经赠送将无法撤销，请谨慎操作！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$refs.upload.submit();
        });
      } else {
        this.$message.error('请选择需要导入的文件');
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        this.$alert(response.body, '提示', {
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.show = false;
          this.$emit('refresh');
        });
      } else {
        if (response.msg) {
          this.$message.error(response.msg);
        }
      }
    },
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
