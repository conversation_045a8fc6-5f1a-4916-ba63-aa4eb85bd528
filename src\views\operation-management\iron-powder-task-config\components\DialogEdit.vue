<template>
  <div>
    <common-dialog
      show-footer
      width="800px"
      title="编辑"
      :visible.sync="visible"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size="mini"
          :model="form"
          label-width="140px"
          :rules="rules"
        >
          <el-form-item label="任务名称" prop="context" label-width="140px">
            {{ form.context }}
          </el-form-item>
          <el-form-item
            label="分享按钮名称"
            prop="shareButtonName"
            label-width="140px"
          >
            <el-input
              v-model="form.shareButtonName"
              type="textarea"
              placeholder="请输入"
              maxlength="6"
              show-word-limit
              clearable
            />
          </el-form-item>
          <el-form-item
            label="海报二维码跳转"
            prop="jumpUrl"
            label-width="140px"
          >
            <el-input v-model="form.jumpUrl" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="生成海报" prop="posterUrl" label-width="140px">
            <el-upload
              class="avatar-uploader"
              action="/file/uploadFile.do"
              :on-success="uploadSuccess"
              :show-file-list="false"
              accept="image/*"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="form.posterUrl"
                :src="`${ossUri}${form.posterUrl}`"
                class="avatar"
              >
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
            <p>支持图片格式：jpg,png,图片尺寸960X1332，大小10M以内</p>
          </el-form-item>
          <el-form-item label="海报引导语" prop="jumpText" label-width="140px">
            <el-input
              v-model="form.jumpText"
              type="textarea"
              placeholder="请输入海报引导语"
              maxlength="30"
              show-word-limit
              clearable
            />
          </el-form-item>
          <el-form-item label="任务排序" prop="sort" label-width="140px">
            <el-input
              v-model="form.sort"
              placeholder="数值较大的排在前面"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      ossUri,
      form: {
        context: '',
        shareButtonName: '',
        jumpUrl: '',
        posterUrl: '',
        jumpText: '',
        sort: ''
      },
      rules: {
        context: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        shareButtonName: [
          { required: true, message: '请输入分享按钮名称', trigger: 'blur' }
        ],
        jumpUrl: [
          { required: true, message: '请输入海报二维码跳转', trigger: 'blur' }
        ],
        posterUrl: [
          { required: true, message: '请上传海报图片', trigger: 'blur' }
        ],
        jumpText: [
          { required: true, message: '请输入海报引导语', trigger: 'blur' }
        ],
        sort: [{ required: true, message: '请输入排序数值', trigger: 'blur' }]
      }
    };
  },
  computed: {
    visible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      }
    }
  },
  methods: {
    open() {
      this.form = { ...this.row };
    },
    uploadSuccess(response) {
      if (response.code !== '00') return;
      this.form.posterUrl = response.body;
      this.$refs.form.validateField('posterUrl');
    },
    beforeAvatarUpload(file) {
      const isJPG = [
        'image/bmp',
        'image/png',
        'image/jpg',
        'image/jpeg'
      ].includes(file.type);
      if (!isJPG) {
        this.$message.error('请按格式要求上传图片！');
        return false;
      }
      const sizeLimit = file.size / 1024 / 1024 < 10;
      if (!sizeLimit) {
        this.$message.error('上传图片大小不能超过 10M！');
        return false;
      }
      return true;
    },
    submit() {
      if (this.loading) return;
      this.$refs.form.validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = { ...this.form };
            const { code } = await this.$http({
              method: 'post',
              url: '/bmsAdmin/updateFansConfig',
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.form = {};
    }
  }
};
</script>

<style lang="scss" scoped>
.avatar-uploader ::v-deep {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin-right: 75px;
  }
  .el-upload:hover {
    border-color: #409eff;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
}
</style>
