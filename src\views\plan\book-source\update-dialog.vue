<template>
  <common-dialog
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='书本名称' prop='bName'>
          <el-input v-model.trim="form.bName" maxlength="20" show-word-limit placeholder="请输入" />
        </el-form-item>

        <el-form-item label='摘要' prop='remark'>
          <el-input
            v-model="form.remark"
            rows="5"
            type="textarea"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='作者' prop='author'>
          <el-input v-model.trim="form.author" maxlength="10" show-word-limit placeholder="请输入" />
        </el-form-item>

        <el-form-item label="章节安排" prop="puPlanChapterVOs" class="chapter">
          <div class="distance">
            <span>周一</span>
            <el-input v-model.trim="week['One'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['One'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['One'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['One'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周二</span>
            <el-input v-model.trim="week['Two'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Two'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Two'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Two'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周三</span>
            <el-input v-model.trim="week['Three'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Three'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Three'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Three'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周四</span>
            <el-input v-model.trim="week['Four'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Four'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Four'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Four'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周五</span>
            <el-input v-model.trim="week['Five'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Five'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Five'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Five'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周六</span>
            <el-input v-model.trim="week['Six'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Six'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Six'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Six'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
          <div class="distance">
            <span>周日</span>
            <el-input v-model.trim="week['Seven'].cName" v-width="200" maxlength="20" :precision="2" placeholder="请输入视频名称" />
            <el-select v-model="week['Seven'].puAttendClassVO.type" class="ipt">
              <el-option label="保利" value="BAOLI" />
              <el-option label="外部链接" value="TOURL" />
              <el-option label="腾讯云" value="TXY" />
            </el-select>
            <el-input v-model="week['Seven'].vids" v-width="200" :precision="2" maxlength="100" />
            <p v-if="bookId">{{ format(week['Seven'].puAttendClassVO.txyStatus + '', 'txyStatus') }}</p>
          </div>
        </el-form-item>

        <el-form-item label='书图片' prop='coverFile.fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='有效打卡时间' prop='clockLimitTime'>
          <el-input-number
            v-model="form.clockLimitTime"
            v-width="300"
            :max="200"
            :min="1"
            class="yz-input-number minute padding-left"
            :controls="false"
          />
          <span class="clockTips">(周一到周六每天至少要阅读X分钟才能打卡)</span>
        </el-form-item>
      </el-form>
    </div>

  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
import { getTextFromDict } from '@/utils';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    bookId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const checkNum = (rule, value, callback) => {
      if (!value) {
        return new Error('请输入');
      } else {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入整数'));
        } else {
          if (value < 0) {
            callback(new Error('不能小于0'));
          } else {
            callback();
          }
        }
      }
    };
    // 章节验证
    const checkPlanChapterVOs = (rule, value, callback) => {
      let PlanChapterNum = 0;
      for (const key in this.week) {
        if (this.week[key].cName !== '' && this.week[key].vids !== '') {
          PlanChapterNum = PlanChapterNum + 1;
        }
      }
      // if (PlanChapterNum === 0) { //至少要有一天安排
      if (PlanChapterNum < 7) {
        callback('请输入');
        // callback(new Error('至少要有一天安排'));
      } else {
        callback();
      }
    };

    return {
      PlanChapterNum: 0,
      kdVisible: false,
      show: false,
      tableLoading: false,
      courseDialogShow: false,
      tableData: [],
      defaultSelected: null,
      currentSelectionCourseType: [],
      fileList: [],
      searchForm: {
        name: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },

      week: {
        One: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '1' }, // 周一
        Two: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '2' }, // 周二
        Three: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '3' }, // 周三
        Four: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '4' }, // 周四
        Five: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '5' }, // 周五
        Six: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '6' }, // 周六
        Seven: { chapterId: '', cName: '', vids: '', puAttendClassVO: { type: 'TXY', content: '' }, day: '7' } // 周日
      },
      puPlanChapterVOs: [], //	章节集合
      puAttendClassVO: {}, // 上课方式
      form: {
        bName: '', // 书名
        author: '', // 作者
        remark: '', // 备注
        clockLimitTime: undefined, // 打卡限制时间
        puPlanChapterVOs: [], // 章节集合
        coverFile: {
          fileUrl: '',
          isAdd: 0
        },
        bookId: ''
      },
      originFileUrl: '',
      rules: {
        bName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        author: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        'coverFile.fileUrl': [
          { required: true, message: '请上传图片', trigger: 'blur' }
        ],
        clockLimitTime: [
          { required: true, message: '请输入' }, { validator: checkNum, trigger: 'blur' }
        ],
        puPlanChapterVOs: [
          { required: true, validator: checkPlanChapterVOs, trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    format(valueKey, key) {
      return getTextFromDict(valueKey, key);
    },
    getTreeData(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          // children若为空数组，则将children设为undefined
          data[i].children = undefined;
        } else {
          // children若不为空数组，则继续 递归调用 本方法
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    getBaseInfo() {
      const data = { bookId: this.bookId };
      this.$post('getPlanBook', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.form.bName = body.bName;
          this.form.remark = body.remark;
          this.form.author = body.author;
          this.form.coverFile.fileUrl = body.cover;
          this.form.clockLimitTime = body.clockLimitTime;
          this.form.puPlanChapterVOs = body.puPlanChapterVOs;
          this.fileList.push({ url: ossUri + body.cover });

          const weekObj = this.form.puPlanChapterVOs.map(item => {
            // weekObjs 临时变量
            const weekObjs = {};
            weekObjs['cName'] = item.cName;
            weekObjs['chapterId'] = item.chapterId;
            weekObjs['bookId'] = item.bookId;
            weekObjs['day'] = item.day;
            weekObjs['txyStatus'] = item.puAttendClassVO.txyStatus;

            const contentObj = JSON.parse(item.puAttendClassVO.content);
            if (item.puAttendClassVO.txyVideoUrl) {
              weekObjs['vids'] = item.puAttendClassVO.txyVideoUrl;
              weekObjs['readPlanType'] = 'TXY';
            } else if (contentObj.vids) {
              weekObjs['vids'] = contentObj.vids;
              weekObjs['readPlanType'] = 'BAOLI';
            } else if (contentObj.toUrl) {
              weekObjs['vids'] = contentObj.toUrl;
              weekObjs['readPlanType'] = 'TOURL';
            } else {
              weekObjs['vids'] = '';
            }
            return weekObjs;
          });

          for (const key in this.week) {
            weekObj.map(item => {
              if (this.week[key].day === item.day + '') {
                this.week[key].cName = item.cName;
                this.week[key].vids = item.vids;
                this.week[key].chapterId = item.chapterId;
                this.week[key].puAttendClassVO.type = item.readPlanType;
                this.week[key].puAttendClassVO.txyStatus = item.txyStatus;
                this.form.bookId = item.bookId;
              }
            });
          }
        }
      });
    },
    open() {
      if (this.bookId) {
        this.getBaseInfo();
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apikey = 'addPlanBook';
          const data = JSON.parse(JSON.stringify(this.form));

          if (this.bookId) {
            apikey = 'editPlanBook';
            data.bookId = this.bookId;
          }

          const chapterArr = [];
          for (const key in this.week) {
            delete this.week[key].puAttendClassVO.txyStatus;
            if (this.week[key].puAttendClassVO.type === 'BAOLI') {
              this.week[key].puAttendClassVO.content = '{"type":"playback","vids":"' + this.week[key].vids + '"}';
            } else {
              this.week[key].puAttendClassVO.content = '{"toUrl":"' + this.week[key].vids + '"}';
            }
            chapterArr.push(this.week[key]);
          }

          data.puPlanChapterVOs = chapterArr;

          this.$post(apikey, data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$emit('update:visible', false);
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
            }
          });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    courseDialogShowClose() {
      this.courseDialogShow = false;
      this.tableData = [];
      this.$refs['searchForm'].resetFields();
      this.pagination.page = 1;
      this.pagination.total = 0;
    },
    handleRemoveImg({ file, fileList }) {
      this.form.coverFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.coverFile.fileUrl = response;
      this.form.coverFile.isAdd = 1;
    }
  }
};
</script>
<style lang="scss" scoped>
.hide {
  ::v-deep .el-upload--picture-card{
    display: none;
  }
}
.padding-10 {
  margin:0 10px;
}
.cascader {
  width: 100%;
}
.chapter {
  margin-top: 25px;
  .el-input {
    margin: 0 10px;
    margin-top: 2px;
  }
}
.clockTips {
  color: #999;
  margin-left: 5px;
}
.distance{
  display: flex;
  span{
    width: 40px;
  }
  .ipt{
    width: 120px;
  }
}
</style>
