<template>
  <el-select
    v-model="val"
    class="yz-allschool-select"
    filterable
    clearable
    :disabled="disabled"
    :placeholder="placeholder"
    @change="onChange"
    @visible-change="visibleChange"
  >
    <el-option v-for="item in options" :key="item.unvsId" :value="item.unvsId" :label="getLabel(item)" />
  </el-select>
</template>

<script>
import { request } from '@/api';

export default {
  name: 'AllschoolSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择院校'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      val: '',
      options: []
    };
  },
  watch: {
    value(v) {
      this.val = v;
    }
  },
  mounted() {
    this.val = this.value;
    if (this.options.length === 0) {
      this.getUnvs();
    }
  },
  methods: {
    onChange(val) {
      this.$emit('input', val);
    },
    getLabel(item) {
      return `${item.unvsName}[${item.unvsCode}]`;
    },
    visibleChange(show) {
      if (show) {
        this.getUnvs();
      }
    },
    async getUnvs() {
      const res = await request('getUnvs', { page: 1, rows: 999, ...this.params });
      if (!res.fail) {
        this.options = res.body.data;
      }
    }
  }
};
</script>

<style lang='scss' scoped>
  .yz-allschool-select{

  }
</style>
