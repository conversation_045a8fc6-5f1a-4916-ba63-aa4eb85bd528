<!-- 咨询类工单表格组件 -->
<template>
  <div>
    <el-table
      v-loading="loading"
      border
      size="small"
      class="w-full"
      header-cell-class-name="table-cell-header"
      :data="tableData"
      @selection-change="handleSelectionChange"
      ref="multipleTable"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="consultId" label="工单号" align="center">
        <template slot-scope="scope">
          <a class="link-text" @click="goToDetail(scope.row)">{{
            scope.row.consultId
          }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="priorityText" label="优先级" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.priority === 1 ? 'info' : 'danger'"
            size="mini"
          >
            {{ scope.row.priorityText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="标题" width="160px" align="center">
        <template slot-scope="scope">
          <a class="link-text" @click="goToDetail(scope.row)">{{
            scope.row.title
          }}</a>
        </template>
      </el-table-column>
      <el-table-column prop="consultStatusText" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="
              scope.row.consultStatus === 1
                ? 'success'
                : scope.row.consultStatus === 2
                ? 'primary'
                : 'warning'
            "
            size="mini"
          >
            {{ scope.row.consultStatusText }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="followCount" label="跟进次数" align="center" />
      <el-table-column prop="orderTypeText" label="工单类型" align="center" />
      <el-table-column
        prop="category1DictIdText"
        label="一级分类"
        align="center"
      />
      <el-table-column
        prop="category2DictIdText"
        label="二级分类"
        align="center"
      />
      <el-table-column
        prop="content"
        label="工单内容"
        width="160px"
        align="center"
      >
        <template slot-scope="scope">
          <a class="link-text" @click="goToDetail(scope.row)">
            {{
              scope.row.content && scope.row.content.length > 15
                ? scope.row.content.substring(0, 15) + "..."
                : scope.row.content
            }}
          </a>
        </template>
      </el-table-column>
      <el-table-column prop="createEmpIdText" label="发起人" align="center" />
      <el-table-column
        prop="handleStatusText"
        label="处理情况"
        align="center"
      />
      <el-table-column prop="handleRemark" label="处理备注" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.handleRemark">已填写</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="needReplyText"
        label="是否需要回复"
        align="center"
      />
      <el-table-column
        prop="needGiftText"
        label="是否给予礼品"
        align="center"
      />
      <el-table-column prop="needGiftName" label="礼品名称" align="center" />
      <el-table-column prop="receiveEmpName" label="受理人" align="center">
        <template slot-scope="scope">
          <el-tooltip
            v-if="
              scope.row.receiveEmpName && scope.row.receiveEmpName.length > 15
            "
            :content="scope.row.receiveEmpName"
            placement="top"
          >
            <span>{{ scope.row.receiveEmpName.substring(0, 15) + "..." }}</span>
          </el-tooltip>
          <span v-else>{{ scope.row.receiveEmpName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="circulationReason"
        label="流转原因"
        align="center"
      />
      <el-table-column prop="createTime" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.createTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="circulationTime" label="转交时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.circulationTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="finishTime" label="完结时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.finishTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="stuName" label="学员姓名" align="center" />
      <el-table-column prop="stuXyCode" label="学业编码" align="center" />
      <el-table-column prop="stuYzCode" label="远智编码" align="center" />
      <el-table-column prop="stuPhone" label="学员电话" align="center" />
      <el-table-column prop="payType" label="支付方式" align="center" />
      <el-table-column prop="pfsnLevelText" label="层次" align="center" />
      <el-table-column prop="stdStageText" label="学员阶段" align="center" />
      <el-table-column prop="recruitEmp" label="招生老师" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.recruitEmp }}</span>
          <el-tag v-if="scope.row.isEmpOut === '是'" type="info" size="mini"
            >离</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="recruitEmpDepart"
        label="招生老师部门"
        align="center"
      />
      <el-table-column
        prop="isEmpOut"
        label="招生老师是否离职"
        align="center"
      />
      <el-table-column prop="isAssignOut" label="是否分配离" align="center" />
      <el-table-column
        fixed="right"
        label="操作"
        :width="hasDeletePermission ? 100 : 60"
        align="center"
      >
        <template slot-scope="scope">
          <div class="flex justify-between px-1">
            <el-button type="text" @click="goToDetail(scope.row)"
              >查看</el-button
            >
            <el-popconfirm
              v-if="hasDeletePermission"
              :title="`删除${scope.row.title}工单`"
              cancel-button-type="button"
              @confirm="deleteWorkOrder(scope.row)"
            >
              <el-button slot="reference" type="text" style="color: #f56c6c"
                >删除
              </el-button>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "ConsultTable",
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      hasDeletePermission: false, // 是否有操作权限
    };
  },
  async created() {
    // 检查是否有操作权限，如果有operateAll权限，则必定有操作权限
    this.hasDeletePermission = await this.$checkPermission("workOrder:delete");
  },
  filters: {
    transformTimeStamp(timestamp) {
      if (!timestamp) return "";
      return new Date(timestamp).toLocaleString();
    },
  },
  methods: {
    handleSelectionChange(val) {
      this.$emit("selection-change", val);
    },
    goToDetail(row) {
      this.$emit("go-to-detail", row);
    },

    deleteWorkOrder(row) {
      this.$emit("del-order", row);
    },
  },
};
</script>

<style lang="scss" scoped>
.link-text {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}
</style>
