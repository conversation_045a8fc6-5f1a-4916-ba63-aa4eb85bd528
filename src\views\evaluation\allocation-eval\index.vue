<template>
  <common-dialog
    is-full
    title=""
    :showFooter="true"
    :visible.sync="visible"
    class="eval"
    @open="init"
    @close="onCloseBtn"
    @confirm="submitBtn"
  >
    <el-form ref="stemForm" v-loading="tableLoading" class="eval-main" size="mini" :model="stems" :rules="stemRule">
      <!-- 基础表单 -->
      <el-form-item label="测评类型：" prop="type">
        <el-select v-model="stems.type" placeholder="请选择" :disabled="evalDisables" class="eval-input">
          <el-option label="趣味" value="0" />
          <el-option label="性格" value="1" />
          <el-option label="情感" value="2" />
          <el-option label="职场" value="3" />
          <el-option label="运势" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="测评标题：" prop="title">
        <el-input v-model="stems.title" class="eval-input" maxlength="30" show-word-limit placeholder="请输入" />
      </el-form-item>
      <el-form-item label="基数配置：" prop="baseNum">
        <el-input-number v-model="stems.baseNum" class="eval-input" :controls="false" :min="0" show-word-limit placeholder="请输入0-10000正整数" />
      </el-form-item>
      <el-form-item label="测评图片：" prop="img" class="eval-img">
        <upload-file :max-limit="1" :size="2" accept="image/png,image/jpg,image/jpeg" :file-list="stems.imgSrcList" @success="successEvalImg" @remove="removeEvalImg" />
      </el-form-item>
      <el-form-item label="测评头图：" prop="headImg" class="eval-headImg">
        <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg" :file-list="stems.headSrcList" @remove="removeHeadImg" @success="successHeadImg" />
      </el-form-item>
      <el-form-item label="小程序卡片图：" prop="appletCardImg" class="eval-cardImg">
        <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg" :file-list="stems.appletSrcList" :disabled="evalDisables" @remove="removeAppletImg" @success="successAppletImg" />
      </el-form-item>
      <!-- 题目 -->
      <div class="main-table">
        <div class="table-title">
          测评题目配置<span class="btn-text">最多可配置15道题，最少为2题</span>
        </div>
        <el-form-item label="请选择模式" prop="model" style="margin-bottom: 15px;">
          <el-radio-group v-model="stems.model" :disabled="Boolean(params.id)">
            <el-radio :label="1">跳转匹配</el-radio>
            <el-radio :label="0">分数匹配</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <!-- 匹配模式 -->
      <ul class="main-ul">
        <li v-for="(topm, index) in stems.topicList" :key="index" class="main-li">
          <div class="li-select">
            <el-form-item class="select-title" :label="`题目${index+1}: `" required :prop="`topicList.${index}.title`" :rules="stemRule.newTopics('title')">
              <el-select
                v-model="topm.title"
                v-el-select-loadmore="()=>handleLoadmore(0)"
                popper-class="popclass"
                filterable
                placeholder="请选择"
                :remote="true"
                :disabled="Boolean(params.id)"
                :loading="answerLoading"
                :remote-method="(e)=>answerRemote(0,e)"
                @change="(e)=>changeStemedBtn(e,index)"
              >
                <el-option v-for="timu in stemedData" :key="timu.id" :label="timu.stemedTitle" :value="timu.id" />
              </el-select>
            </el-form-item>
            <div>
              <el-button v-if="stems.model==0" type="primary" size="small" icon="el-icon-top" :disabled="Boolean(params.id)" @click="moveUp(index,topm)">上移</el-button>
              <el-button v-if="stems.model==0" type="primary" size="small" icon="el-icon-bottom" :disabled="Boolean(params.id)" @click="moveDown(index,topm)">下移</el-button>
              <el-button type="danger" size="small" icon="el-icon-delete" :disabled="Boolean(params.id)" @click="deleteStem(index,topm)">删除题目</el-button>
            </div>
          </div>
          <el-table class="li-options" :data="topm.optionList" empty-text="暂无选项" :show-header="false" size="small">
            <el-table-column label="" prop="content" :width="stems.model==0?540:320" />
            <!-- 分数模式 -->
            <el-table-column v-if="stems.model==0" label="" align="center" width="300">
              <template slot-scope="scope">
                <el-form-item label="" :prop="`topicList.${index}.optionList.${scope.$index}.score`" :rules="stemRule.newTopics('score')">
                  <el-input-number v-model="scope.row.score" class="eval-number" :disabled="Boolean(params.id)" :controls="false" :min="0" :precision="0" size="small" @blur="autoScore(topm)" />
                </el-form-item>
              </template>
            </el-table-column>
            <!-- 跳转模式 -->
            <el-table-column v-else label="" width="640">
              <template slot-scope="scope">
                <div class="options-jump">
                  <span>跳转至</span>
                  <el-form-item label="" class="jump-input1" :prop="`topicList.${index}.optionList.${scope.$index}.skipType`" :rules="stemRule.newTopics('skipType')">
                    <el-select v-model="scope.row.skipType" placeholder="请选择" :disabled="Boolean(params.id)" @change="(e)=>getSkipList(topm,scope.row,e)">
                      <el-option label="跳转题目" value="0" />
                      <el-option label="跳转结果" value="1" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="" class="jump-input2" :prop="`topicList.${index}.optionList.${scope.$index}.skipId`" :rules="stemRule.newTopics('skipId')">
                    <el-input v-if="Boolean(params.id)" v-model="topm.optionList[scope.$index].skipName" class="eval-number" :disabled="Boolean(params.id)" />
                    <el-select v-else v-model="scope.row.skipId" filterable placeholder="请选择" :disabled="Boolean(params.id)">
                      <el-option v-for="(skim,skix) in topm.optionList[scope.$index].skipData" :key="skix" :label="skim.title" :value="skim.id" />
                    </el-select>
                  </el-form-item>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="">
              <template slot-scope="scope">
                <el-form-item label="">
                  <div class="options-tags">
                    <el-button type="primary" plain size="small" :disabled="Boolean(params.id)" @click="addTagBtn(index,scope.$index)">设置选项标签</el-button>
                    <div style="margin-left: 20px;">
                      选项标签：
                      <el-button v-for="(taem,tams) in topm.optionList[scope.$index].tagsData" :key="taem.tagInfoId" type="info" plain size="small" :disabled="Boolean(params.id)">
                        {{ taem.tagInfoName || '' }}
                        <div v-if="!params.id" class="tags-active" @click="delTagBtn(index,scope.$index,tams)">x</div>
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </li>
      </ul>
      <div class="main-btn">
        <el-button type="primary" size="small" icon="el-icon-plus" :disabled="Boolean(params.id)" @click="openStemBtn">新增题目</el-button>
      </div>
      <!-- 分数模式才出现 -->
      <div v-if="stems.model==0" class="main-table">
        <div class="table-title"><span class="table-text">*</span>测评结果页配置</div>
        <div class="table-box">
          <el-form-item label="请先设置答案数量：" label-width="150px" prop="selectNum">
            <el-select v-model="stems.selectNum" filterable placeholder="请选择" :disabled="evalDisables" @change="setNum">
              <el-option v-for="item in 20" :key="item" :label="item" :value="item" />
            </el-select>
            <span>个</span>
          </el-form-item>
        </div>
        <el-table size="small" :data="stems.resultList" style="width: 100%" header-cell-class-name="table-cell-header">
          <el-table-column label="序号" align="center" type="index" width="60" />
          <el-table-column label="最高分（包含）" align="center" prop="maxScore">
            <template slot-scope="scope">
              <el-form-item label="" :prop="`resultList.${scope.$index}.maxScore`" :rules="stemRule.maxScore(scope.$index)">
                <el-input-number v-model="scope.row.maxScore" class="eval-number" :disabled="numberDisabled(1,scope.$index)" :controls="false" :min="0" :precision="0" size="small" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="最低分（包含）" align="center" prop="minScore">
            <template slot-scope="scope">
              <el-form-item label="" :prop="`resultList.${scope.$index}.minScore`" :rules="stemRule.minScore(scope.$index)">
                <el-input-number v-model="scope.row.minScore" class="eval-number" :disabled="numberDisabled(2,scope.$index)" :controls="false" :min="0" :precision="0" size="small" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="测评结果" align="center" prop="evalResultConfId">
            <template slot-scope="scope">
              <el-form-item label="" :prop="`resultList.${scope.$index}.evalResultConfId`" :rules="stemRule.evalResultConfId">
                <el-select
                  v-model="scope.row.evalResultConfId"
                  v-el-select-loadmore="()=>handleLoadmore(1)"
                  popper-class="popclass"
                  filterable
                  placeholder="请选择"
                  :remote="true"
                  :disabled="evalDisables"
                  :loading="answerLoading"
                  :remote-method="(e)=>answerRemote(1,e)"
                >
                  <el-option v-for="item in answerList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <!-- 新增个人标签 -->
    <added-tags :visible="showTags" :showDefault="false" @on-close="closeTags" />
  </common-dialog>
</template>

<script>
// 新增测评
import addedTags from '../allocation-answer/added-tags';

export default {
  components: { addedTags },
  directives: {
    'el-select-loadmore': {
      bind(el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.popclass');
        SELECTWRAP_DOM.addEventListener('scroll', function() {
          // 临界值的判断滑动到底部就触发
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  props: {
    visible: { type: Boolean, default: false },
    params: { type: Object, default: () => {} }
  },
  data() {
    return {
      evalDisables: false,
      tableLoading: false,
      minsScore: 0,
      maxsScore: 0,
      answers: {
        title: '',
        total: 0,
        resultName: '',
        page: 1,
        rows: 20,
        enableFlag: 1
      },
      skipList: [],
      stemedData: [],
      answerList: [],
      answerLoading: false,
      showTags: false,
      tagsObj: {},
      stems: {
        type: '',
        title: '',
        model: 1,
        baseNum: undefined,
        img: '',
        imgSrcList: [],
        headImg: '',
        headSrcList: [],
        appletCardImg: '',
        appletSrcList: [],
        topicList: [
          { sort: 1, title: '', type: '2', optionList: [] },
          { sort: 2, title: '', type: '2', optionList: [] }
        ],
        selectNum: 3,
        capsuleFlag: 1,
        resultList: [
          { maxScore: 0, minScore: 0, evalResultConfId: '' },
          { maxScore: 0, minScore: 0, evalResultConfId: '' },
          { maxScore: 0, minScore: 0, evalResultConfId: '' }
        ]
      },
      stemRule: {
        type: [{ required: true, message: '请输入', trigger: 'blur' }],
        model: [{ required: true, message: '请选择', trigger: 'change' }],
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        baseNum: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入0-10000正整数'));
              return;
            }
            if (value > 10000) {
              callback(new Error('请正确输入0-10000范围内的正整数'));
              return;
            }
            callback();
          }
        }],
        img: [{ required: true, message: '请上传图片', trigger: 'change' }],
        headImg: [{ required: true, message: '请上传图片', trigger: 'change' }],
        appletCardImg: [{ required: true, message: '请上传图片', trigger: 'change' }],
        topicList: [{
          required: true,
          trigger: 'change',
          validator: (rule, value, callback) => {
            const reu = this.stems.topicList?.length || 0;
            if (reu) callback();
            else callback(new Error('至少添加一道测试题'));
          }
        }],
        newTopics: (key) => {
          return [{
            required: true,
            trigger: 'change',
            validator: (rule, value, callback) => {
              if ((value === '' || value === null)) {
                callback(new Error('请选择'));
                return;
              }
              if ((value < 0 || value > 999) && key === 'score') {
                callback(new Error('请输入0-999'));
                return;
              }
              callback();
            }
          }];
        },
        selectNum: [{ required: true, message: '请选择', trigger: 'change' }],
        capsuleFlag: [{ required: true, message: '请选择', trigger: 'change' }],
        maxScore: (inx) => this.configValidator(inx, 'minScore'),
        minScore: (inx) => this.configValidator(inx, 'maxScore'),
        evalResultConfId: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    };
  },
  methods: {
    async init() {
      await this.getAnswersList();
      await this.getStemedList();
      if (this.params?.id) {
        this.evalDisables = Number(this.params.enableFlag) === 1;
        this.getFormDetail();
      }
    },
    // 测评题目配置校验规则
    configValidator($index, key) {
      return [{
        required: true,
        trigger: 'blur',
        validator: (rule, value, callback) => {
          let text = '';
          /* 第一级：输入值为空 */
          if (value === '' || value === undefined) text = '请输入当前最低分';
          else {
            text = '';
            const resultList = this.stems.resultList;
            const inx = resultList?.length - 1;
            /* 第二级：当前序位是最高序位 */
            const max = resultList[0].maxScore;
            const min = resultList[inx].minScore;
            if (value > max) {
              text = '输入分数大于最高分数，请重新填写！';
            } else if (value < min) {
              text = '输入分数小于最低分数，请重新填写！';
            } else if ($index) {
              /* 第三级：当前序位是中间序位 */
              const isErs = key === 'minScore';
              const max_pre = resultList[isErs ? ($index - 1) : $index][key];
              let min_pre = 0;
              /* 第四级：当前序位是最后序位 */
              if ($index === inx) min_pre = resultList[inx].minScore;
              else min_pre = resultList[isErs ? $index : ($index + 1)][key];
              // 三四判断提示
              if (value !== max_pre - 1) {
                text = isErs ? '输入分数不等于上一序位最低分数减一，请重新填写！' : '';
                if (!text) {
                  if (value > max_pre) {
                    text = `输入分数大于${isErs ? '上一' : '当前'}序位${isErs ? '最低分数' : '最高分数'}，请仔细校验！`;
                  } else if (value < min_pre) {
                    text = `输入分数小于${isErs ? '当前' : '下一'}序位${isErs ? '最低分数' : '最高分数'}，请仔细校验！`;
                  }
                }
              }
            }
          }
          // 错误反馈提示
          if (text) {
            setTimeout(() => {
              this.$message({ message: text, type: 'error' });
            }, 0);
            callback(new Error(text));
          } else callback();
        }
      }];
    },
    // 是否可以编辑
    numberDisabled(type = 0, index) {
      if (type === 1 && index === 0) return true;
      else {
        const cut = this.stems.resultList.length;
        if (type === 2 && index === cut - 1) return true;
        else return this.evalDisables;
      }
    },
    // 打开新增题目弹窗
    openStemBtn() {
      if (this.params.id) return;
      if (this.evalDisables) {
        this.$message({ message: '启用状态下，不可操作', type: 'warning' });
        return;
      }
      const lists = this.stems.topicList || [];
      if (lists.length >= 25) {
        this.$message.warning('新增失败，最多配置25个选项！');
        return;
      }
      let maxSort = 0;
      lists.forEach((item) => {
        maxSort = item.sort > maxSort ? item.sort : maxSort;
      });
      lists.push({ sort: maxSort + 1, title: '', type: '2', optionList: [] });
      // this.stemTitle = row ? '修改题目选项配置' : '新增题目选项配置';
      console.log('打开新增题目弹窗-topicList', this.stems.topicList);
    },
    // 自动计算最高最低分
    autoScore(obs) {
      obs.optionList.forEach((item, index) => {
        item.score = Number(item?.score || 0);
        if (index === 0) {
          obs.min = item.score;
          obs.max = item.score;
        } else {
          obs.min = item.score > obs.min ? obs.min : item.score;
          obs.max = item.score > obs.max ? item.score : obs.max;
        }
      });
      console.log('获取关闭新增题目弹窗2-obs', obs);
      this.closeStemBtn();
    },
    // 关闭新增题目弹窗-计算最高最低分
    closeStemBtn() {
      const topics = JSON.parse(JSON.stringify(this.stems?.topicList || []));
      console.log('计算当前课程总分值', topics);
      // 计算当前课程总分值
      this.minsScore = 0;
      this.maxsScore = 0;
      topics.forEach(item => {
        this.minsScore += Number(item.min || 0);
        this.maxsScore += Number(item.max || 0);
      });
      const ins = this.stems?.resultList?.length;
      this.stems.resultList[0]['maxScore'] = this.maxsScore;
      this.stems.resultList[ins - 1]['minScore'] = this.minsScore;
      console.log('获取关闭新增题目弹窗2-topics', topics);
      this.stems.topicList = topics;
      this.$forceUpdate();
    },
    // 成功上传测评图
    successEvalImg(evt) {
      console.log('成功上传测评图-2', evt, this.stems);
      this.stems.img = evt?.response || '';
    },
    // 删除测评图
    removeEvalImg(evt) {
      console.log('删除测评图-2', evt, this.stems);
      this.stems.img = '';
    },
    // 成功上传头图
    successHeadImg(evt) {
      console.log('成功上传头图-2', evt, this.stems);
      this.stems.headImg = evt?.response || '';
    },
    // 删除头图
    removeHeadImg(evt) {
      this.stems.headImg = '';
      console.log('删除头图-2', evt, this.stems.headSrcList);
    },
    // 成功上传小程序卡片图
    successAppletImg(evt) {
      console.log('成功上传小程序卡片图-2', evt, this.stems.appletSrcList);
      this.stems.appletCardImg = evt?.response || '';
    },
    // 删除小程序卡片图
    removeAppletImg(evt) {
      this.stems.appletCardImg = '';
      console.log('删除小程序卡片图-2', evt, this.stems.appletSrcList);
    },
    // 向上移动当前项
    moveUp($index, row) {
      if (this.params.id) return;
      if (this.evalDisables) {
        this.$message({ message: '启用状态下，不可操作', type: 'warning' });
        return;
      }
      if ($index === 0) {
        this.$message({ message: '当前为第一题！', type: 'warning' });
        return;
      }
      // 移动逻辑
      const olds = this.stems.topicList[$index - 1];
      const cutSort = Number(row.sort || 0);
      row.sort = Number(olds.sort || 0);
      olds.sort = cutSort;
      console.log('向上移动当前项-row', $index, olds);
      this.stems.topicList.splice($index, 1, olds);
      this.stems.topicList.splice($index - 1, 1, row);
      // this.$forceUpdate();
    },
    // 向下移动当前项
    moveDown($index, row) {
      if (this.params.id) return;
      if (this.evalDisables) {
        this.$message({ message: '启用状态下，不可操作', type: 'warning' });
        return;
      }
      if ($index === this.stems.topicList.length - 1) {
        this.$message({ message: '当前为最后一题！', type: 'warning' });
        return;
      }
      // 移动逻辑
      const olds = this.stems.topicList[$index + 1];
      const cutSort = Number(row.sort || 0);
      row.sort = Number(olds.sort || 0);
      olds.sort = cutSort;
      console.log('向下移动当前项-row', $index, row, olds);
      this.stems.topicList.splice($index, 1, olds);
      this.stems.topicList.splice($index + 1, 1, row);
      this.$forceUpdate();
    },
    // 删除当前目录表单项
    deleteStem($index, row) {
      if (this.params.id) return;
      if (this.evalDisables) {
        this.$message({ message: '启用状态下，不可操作', type: 'warning' });
        return;
      }
      if (this.stems.topicList.length <= 2) {
        this.$message({ message: '题目至少2个', type: 'warning' });
        return;
      }
      console.log('删除当前目录表单项-vas', $index, row);
      this.stems.topicList.splice($index, 1);
      this.stems.topicList.map((item, ins) => {
        item.sort = ins + 1;
      });
      // 删除后，重新计算当前总分值
      this.minsScore = 0;
      this.maxsScore = 0;
      this.stems.topicList.forEach(item => {
        this.minsScore += Number(item.min || 0);
        this.maxsScore += Number(item.max || 0);
      });
      const ins = this.stems?.resultList?.length;
      this.stems.resultList[0]['maxScore'] = this.maxsScore;
      this.stems.resultList[ins - 1]['minScore'] = this.minsScore;
      this.$forceUpdate();
    },
    // 动态设置答案数量
    setNum(vas) {
      const news = [];
      console.log('动态设置答案数量-vas', vas);
      for (let k = 0; k < vas; k++) {
        news.push({
          maxScore: k === 0 ? this.maxsScore : 0,
          minScore: k === (vas - 1) ? this.minsScore : 0,
          evalResultConfId: ''
        });
      }
      this.stems.resultList = news;
      console.log('动态设置答案数量', this.stems);
    },
    // 答案列表选择项的输入搜索查询
    answerRemote(type, vas) {
      if (this.answerLoading) {
        return;
      }
      this.answers.page = 1;
      this.answerLoading = true;
      if (type === 1) {
        this.answers.resultName = vas;
        this.answerList = [];
        this.getAnswersList();
      } else {
        this.answers.title = vas;
        this.stemedData = [];
        this.getStemedList();
      }
      console.log('答案列表搜索查询-answers', type, this.answers);
    },
    // 答案列表选择项的滚动下拉
    handleLoadmore(type) {
      console.log('答案列表选择项的滚动下拉-type', type);
      // 没有数据，不触发提示
      if (type === 1 && !this.answerList?.length || !type && !this.stemedData?.length) return;
      // 判断是否已经加载全部
      if (this.answers.page > this.answers.total) {
        this.$message({ message: '已经到底了', type: 'warning' });
        return;
      }
      this.answers.page++;
      this.answerLoading = true;
      if (type === 1) this.getAnswersList();
      else this.getStemedList();
      console.log('答案列表选择项的滚动下拉', this.params);
    },
    // 获取选中的题目
    changeStemedBtn(ids, inx) {
      console.log('获取选中的题目-ids', ids, inx);
      const topicList = JSON.parse(JSON.stringify(this.stems.topicList));
      const testData = topicList.filter(item => item.title === ids);
      console.log('获取选中的题目-testData', testData);
      if (testData?.length >= 2) {
        this.$message({ message: '当前题目已经被选中，请重新选择', type: 'warning', duration: 2000 });
        topicList[inx] = { ...topicList[inx], title: '', type: '2', optionList: [] };
      } else {
        // 赋值
        const stemedData = JSON.parse(JSON.stringify(this.stemedData));
        stemedData.map(element => {
          element.min = 0;
          element.max = 0;
          if (element.options?.length) {
            element.options.map((opem, index) => {
              opem.evalTopicId = ids;
              opem.score = Number(opem?.score || 0);
              if (index === 0) {
                element.min = opem.score;
                element.max = opem.score;
              } else {
                element.min = opem.score > element.min ? element.min : opem.score;
                element.max = opem.score > element.max ? opem.score : element.max;
              }
            });
          }
          topicList.map((item, index) => {
            item.id = item.title;
            if (!item.optionList) item.optionList = [];
            else if (element.id === ids && inx === index) {
              item.optionList = [...element.options];
              item.min = element.min;
              item.max = element.max;
            }
          });
        });
      }
      this.stems = { ...this.stems, topicList };
      console.log('获取选中的题目-topicList', topicList);
      setTimeout(() => this.closeStemBtn(), 0);
    },
    // 新增标签
    addTagBtn(index, chils) {
      if (this.params.id) return;
      this.tagsObj = { index, chils };
      this.showTags = true;
    },
    // 关闭标签
    closeTags(obs) {
      if (obs) {
        const topicList = JSON.parse(JSON.stringify(this.stems.topicList));
        topicList.map((item, i) => {
          if (this.tagsObj.index === i) {
            item.optionList.map((opem, j) => {
              if (this.tagsObj.chils === j) {
                const is = opem.tagsData?.filter(taem => taem.tagInfoId === obs.tagInfoId);
                console.log('关闭标签-is', is);
                if (!is?.length) {
                  opem.tagsData.push(obs);
                } else this.$message({ message: '该标签已存在', type: 'warning' });
              }
            });
          }
        });
        this.stems = { ...this.stems, topicList };
      }
      this.showTags = false;
      this.tagsObj = {};
    },
    // 删除标签
    delTagBtn(index, chils, tams) {
      // console.log('删除标签-item', index, chils, tams);
      if (this.params.id) return;
      const topicList = JSON.parse(JSON.stringify(this.stems.topicList));
      topicList.map((item, i) => {
        if (i === index) {
          item?.optionList?.map((opem, j) => {
            if (j === chils) {
              // console.log('删除标签-opem', opem);
              opem?.tagsData?.splice(tams, 1);
            }
            // return opem;
          });
        }
      });
      this.stems = JSON.parse(JSON.stringify({ ...this.stems, topicList }));
    },
    // 提交表单
    submitBtn() {
      console.log('提交表单', this.stems);
      this.$refs['stemForm'].validate((valid) => {
        if (valid) {
          const olds = JSON.parse(JSON.stringify(this.stems));
          olds.topicList.map(item => {
            item.optionList.map(opem => {
              // 详情的时候，tagIds有值
              if (!this.params?.id) {
                opem.tagIds = '';
                opem.tagsData.map(taem => {
                  opem.tagIds += taem.tagInfoId + ',';
                });
                opem.tagIds = opem.tagIds?.slice(0, -1) || '';
              }
              delete opem.skipData;
              delete opem.tagsData;
            });
          });
          const news = {
            id: this.params?.id || '',
            title: olds.title,
            type: olds.type,
            baseNum: olds.baseNum,
            img: olds.img,
            model: olds.model,
            headImg: olds.headImg,
            appletCardImg: olds.appletCardImg,
            resultList: olds.resultList,
            topicList: olds.topicList
          };
          console.log('this.news', news);
          // 所有校验已通过，可以提交
          this.tableLoading = true;
          const usl = this.params?.id ? 'evalUpdate' : 'evalAdd';
          this.$post(usl, news, { json: true }).then((res) => {
            const { code } = res;
            if (code === '00') {
              this.$message.success('提交成功');
              this.$emit('on-close', 1);
            } else this.$message.error('提交失败');
            this.tableLoading = false;
          }).catch(() => {
            this.$message.error('提交失败');
            this.tableLoading = false;
          });
        }
      });
    },
    // 获取答案列表数据
    getAnswersList() {
      this.$post('resultList', this.answers).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          const data = body?.data || [];
          data.map(item => {
            item.evalResultConfId = item.id;
          });
          const newsData = this.answerList.concat(data);
          const uniques = Array.from(new Set(newsData.map(JSON.stringify))).map(JSON.parse);
          this.answerList = uniques;
          this.answers.total = Math.ceil(Number(body?.recordsTotal || 0) / 20);
        }
        this.answerLoading = false;
      }).catch((err) => {
        this.answerLoading = false;
        console.log('滚动下拉-err', err);
      });
    },
    // 获取测评题目列表
    getStemedList() {
      const news = JSON.parse(JSON.stringify(this.answers));
      delete news.resultName;
      delete news.enableFlag;
      this.$post('getTopicOtptions', news).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          const data = body || [];
          // console.log('获取测评题目列表-data', data);
          data.map(item => {
            item.stemedTitle = `题目id: ${item.id}（${item.title || ''}）`;
            item.sort = Number(item.sort || 0);
            if (!item.options) item.options = [];
            else {
              item.options.map(opem => {
                if (!opem.skipData) opem.skipData = [];
                if (!opem.tagsData) opem.tagsData = [];
              });
            }
          });
          const newsData = this.stemedData.concat(data);
          const uniques = Array.from(new Set(newsData.map(JSON.stringify))).map(JSON.parse);
          // console.log('获取测评题目列表-uniques', uniques);
          this.stemedData = uniques;
        }
        this.answerLoading = false;
      }).catch(() => {
        this.answerLoading = false;
      });
    },
    // 获取匹配题目答案数据
    getSkipList(topm, row, e) {
      const param = { topicId: topm.title, skipType: e };
      this.$post('getOtptionSkip', param).then((res) => {
        const { code, body } = res;
        if (code === '00' && body) {
          const topicList = JSON.parse(JSON.stringify(this.stems.topicList));
          topicList.map(item => {
            item.optionList.map(opem => {
              if (row.id === opem.id) {
                opem.skipData = [];
                if (opem.skipId) opem.skipId = '';
                for (let i = 0; i < body.length; i++) {
                  opem.skipData.push({ title: body[i].title || body[i].name, id: body[i].id });
                }
              }
            });
          });
          this.stems = { ...this.stems, topicList };
          // console.log('匹配题目答案数据-data', [...topicList]);
        }
      });
    },
    // 获取表单详情数据
    getFormDetail() {
      this.tableLoading = true;
      this.stems.id = this.params?.id || '';
      this.$post('evalDetail', { id: this.stems.id }).then((res) => {
        const { code, body } = res;
        console.log('测评配置-详情-stems', this.stems);
        if (code === '00') {
          if (body.img) {
            body['imgSrcList'] = [{ url: body.img }];
          }
          if (body.headImg) {
            body['headSrcList'] = [{ url: body.headImg }];
          }
          if (body.appletCardImg) {
            body['appletSrcList'] = [{ url: body.appletCardImg }];
          }
          // 计算每项的最高最低分
          this.minsScore = 0;
          this.maxsScore = 0;
          // 分数模式-跳转模式：需要后面单独处理
          if (body?.topicList?.length) {
            body.topicList.map(item => {
              let min = 0;
              let max = 0;
              if (item?.optionList?.length) {
                item.optionList.map((chils, index) => {
                  if (index === 0) {
                    min = chils.score;
                    max = chils.score;
                  } else {
                    min = chils.score > min ? min : chils.score;
                    max = chils.score > max ? chils.score : max;
                  }
                  // 添加标签数据
                  chils.tagsData = [];
                  if (chils.tagNames) {
                    const newTags = chils.tagNames.split(',');
                    for (let i = 0; i < newTags.length; i++) {
                      chils.tagsData.push({ tagInfoName: newTags[i], id: i });
                    }
                  }
                  // 跳转结果/题目数据
                  chils.skipData = [];
                  if (chils.skipName) {
                    const newSkips = chils.skipName.split(',');
                    for (let i = 0; i < newSkips.length; i++) {
                      chils.skipData.push({ title: newSkips[i], id: i });
                    }
                  }
                  chils.sort = Number(chils.sort || 0);
                  chils.skipType = String(chils?.skipType || '0');
                });
              }
              item.min = min;
              item.max = max;
              this.minsScore += min;
              this.maxsScore += max;
            });
          }
          // 计算总数结果的最高最低分
          const ins = body?.resultList?.length;
          if (ins) {
            body.resultList[0]['maxScore'] = this.maxsScore;
            body.resultList[ins - 1]['minScore'] = this.minsScore;
            body['selectNum'] = ins;
          } else {
            body['selectNum'] = body['selectNum'] || 3;
            body.resultList = [
              { maxScore: 0, minScore: 0, evalResultConfId: '' },
              { maxScore: 0, minScore: 0, evalResultConfId: '' },
              { maxScore: 0, minScore: 0, evalResultConfId: '' }
            ];
          }
          body.type = String(body?.type || 0);
          console.log('测评配置-详情-body', body);
          this.stems = { ...this.stems, ...body };
        }
        this.tableLoading = false;
      }).catch(() => {
        this.tableLoading = false;
      });
    },
    // 关闭当前弹窗
    onCloseBtn() {
      this.answers = {
        title: '',
        total: 0,
        resultName: '',
        page: 1,
        rows: 20,
        enableFlag: 1
      };
      this.skipList = [];
      this.stemedData = [];
      this.answerList = [];
      this.evalDisables = false;
      this.stems = {
        type: '',
        title: '',
        model: 1,
        baseNum: undefined,
        img: '',
        imgSrcList: [],
        headImg: '',
        headSrcList: [],
        appletCardImg: '',
        appletSrcList: [],
        topicList: [
          { sort: 1, title: '', type: '2', optionList: [] },
          { sort: 2, title: '', type: '2', optionList: [] }
        ],
        selectNum: 3,
        capsuleFlag: 1,
        resultList: [
          { maxScore: 0, minScore: 0, evalResultConfId: '' },
          { maxScore: 0, minScore: 0, evalResultConfId: '' },
          { maxScore: 0, minScore: 0, evalResultConfId: '' }
        ]
      };
      this.$refs['stemForm'].resetFields();
      this.$emit('on-close', 0);
    }
  }
};
</script>

<style lang="scss">
.popclass {
  height: 200px;
  overflow: hidden;
  overflow-y: scroll;
}
.eval {
  .el-input--mini .el-input__inner {
    text-align: left;
  }
  .eval-main {
    margin: 30px 60px;
    .eval-input {
      width: 30%;
    }
    .eval-number {
      width: 100%;
    }
    .main-table {
      margin: 40px 0;
      .table-title {
        margin-left: 8px;
      }
      .table-text {
        color: #F56C6C;
        margin-right: 4px;
      }
      .table-box {
        margin: 15px 0;
        display: flex;
        align-items: center;
        .el-select {
          width: 40%;
          margin: 0 10px;
        }
      }
      .table-option {
        display: flex;
        justify-content: flex-start;
      }
      .el-form-item {
        margin-bottom: 0;
      }
    }
    .main-ul {
      border: 1px solid #e7e7e7;
      border-radius: 8px;
      .main-li {
        padding: 10px;
        .li-select {
          margin-bottom: 20px;
          display: flex;
          .select-title {
            width: calc(100% - 270px);
            display: flex;
            align-items: center;
            .el-form-item__content {
              width: 40%;
            }
          }
          .select-button {
            width: 100px;
            height: 34px;
          }
        }
        .li-options {
          .options-title {
            min-width: 360px;
          }
          .options-jump {
            display: flex;
            align-items: center;
            justify-content: center;
            .jump-input1 {
              margin: 0 10px;
              width: 20%;
            }
            .jump-input2 {
              margin: 0;
              width: 36%;
            }
          }
          .options-tags {
            margin: 0 30px 0 20px;
            display: flex;
            align-items: center;
            .el-button {
              margin-top: 10px;
              position: relative;
            }
            .el-button:hover {
              cursor: default;
            }
            .tags-active {
              position: absolute;
              top: -6px;
              right: -6px;
              z-index: 9;
              cursor: pointer;
              width: 14px;
              height: 14px;
              line-height: 9px;
              text-align: center;
              color: #ffffff;
              border-radius: 50%;
              border: 1px solid #fd4c4c;
              background-color: #F56C6C;
            }
          }
        }
      }
      .main-li:first-child {
        margin-top: 0;
      }
    }
    .main-btn {
      margin-top: 20px;
      width: 100%;
      height: 30px;
      button {
        float: right;
        width: 20%;
      }
    }
  }
  .yz-common-dialog__footer {
    text-align: center !important;
  }
  .eval-img,
  .eval-headImg,
  .eval-cardImg {
    position: relative;
  }
  .eval-img::after,
  .eval-headImg::after,
  .eval-cardImg::after {
    position: absolute;
    left: 280px;
    bottom: 10px;
    font-size: 12px;
    content: '仅支持上传PNG/JPEG，尺寸为750*1448，上传最大不能超过2M';
  }
  .eval-headImg::after {
    content: '仅支持上传PNG/JPEG，尺寸为750*420，上传最大不能超过2M';
  }
  .eval-cardImg::after {
    content: '仅支持上传PNG/JPEG，尺寸为500*400，上传最大不能超过2M';
  }
}
</style>

