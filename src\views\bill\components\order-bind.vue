<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    title="订单绑定"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >

        <el-form-item label='绑定分销人' prop='userId'>
          <el-select
            v-model="value"
            filterable
            remote
            reserve-keyword
            placeholder="请输入手机号"
            :remote-method="remoteMethod"
            :loading="loading"
            @change="changeSelect"
          >
            <el-option
              v-for="item in options"
              :key="item.userId"
              :label="item.realName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    },
    orderIdentify: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      typeDefaultOption: null,
      rules: {

      },
      form: {
        userId: '',
        distributionUserId: '',
        realName: ''
      },
      options: [],
      value: [],
      list: [],
      loading: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      console.log(this.row, 'this.row');
    },
    submit() {
      this.$confirm('确定绑定此订单？绑定后不可取消！请谨慎操作。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // this.$refs.upload.submit();
        const params = {
          ...this.form,
          'operationType': 'pay',
          'userIdentity': this.row.userIdentity || '',
          'orderNo': this.row.orderNo || '',
          'orderIdentify': this.orderIdentify || ''
          // ...this.row,
          // distributionGoodsId: '',
          // orderState: '',
          // payTime: '',
          // orderAmount: '',
          // zmScale: '',
          // couponScale: '',
          // demurrageScale: '',
          // calibrationTime: '',
          // distributionGoodsName: '',
          // distributionGoodsType: '',
          // buyUserId: '',
          // buyUserMobile: '',
          // buyUserName: ''

        };
        console.log(params);
        this.$post('bindDistributionOrder', params).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.close();
          }
        });
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // setMappingName({ value, label, source }) {
    //   this.form.userId = value;
    //   this.form.distributionUserId = value;
    //   this.form.realName = label;
    //   console.log(value, 'value');
    // },
    changeSelect(value) {
      // this.form.realName = label;
      let obj = {};
      obj = this.options.find((item) => {
        return item.userId === value;
      });
      this.form.userId = this.form.distributionUserId = obj.userId;
      this.form.realName = obj.realName;
      console.log(obj, 'value');
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        this.$post('findDistributionUser', { mobile: query }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.loading = false;
            this.options = body;
            console.log(this.options, 'this.options');
            //   this.options = this.list.filter(item => {
            //     return item.label.toLowerCase()
            //       .indexOf(query.toLowerCase()) > -1;
            //   });
          }
        });
      } else {
        this.options = [];
      }
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
