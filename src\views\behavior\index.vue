<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="用户行为" name="1">
        <buy />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import buy from './tabs/buy';
export default {
  components: {
    buy
  },
  data() {
    return {
      activeName: '1'
    };
  },
  methods: {
    handleClick() {}
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
