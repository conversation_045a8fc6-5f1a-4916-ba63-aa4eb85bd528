<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="操作记录"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <el-table v-loading="tableLoading" header-cell-class-name="table-cell-header" size="small" :data="tableData" style="width: 100%;" border>
        <el-table-column prop="operateTime" label="操作时间" align="center" />
        <el-table-column prop="operateUser" label="操作人" align="center" />
        <el-table-column prop="operateContent" label="变更信息" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 打开弹框
    open() {
      if (this.currentRow.id) this.getTableList();
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = {
        operateBusinessId: this.currentRow.id,
        operateBusinessType: 1, // 1：新手任务--任务禁用启用操作类型 2:新手任务--用户拉黑操作类型
        pageNum: (this.pagination.page - 1) * this.pagination.limit,
        pageSize: this.pagination.limit
      };
      this.$post('newbieTaskLog', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body?.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    }
  }
};
</script>
