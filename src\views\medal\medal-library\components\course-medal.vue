<template>
  <el-radio-group v-model="conditionType" :disabled="disabled" class="vertical-radio-group" @change="handleConditionTypeChange">
    <div class="radio-item">
      <el-radio :label="1">首次上课</el-radio>
    </div>

    <div class="radio-item">
      <el-radio :label="2">累计上课≥</el-radio>
      <el-input-number
        v-if="conditionType === 2"
        v-model="courseData.sum"
        :disabled="disabled"
        :precision="0"
        :min="1"
        :controls="false"
        placeholder="请输入累计上课天数"
        style="width: 180px;"
      />
      <span v-if="conditionType === 2">天</span>
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'CourseMedal',
  props: {
    courseData: {
      type: Object,
      default: () => ({
        first: undefined, // 首次上课
        sum: undefined // 累计上课天
      })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      conditionType: null
    };
  },
  watch: {
    courseData: {
      handler(val) {
      },
      deep: true
    }
  },
  mounted() {
    // 根据传入的courseData，设置默认的conditionType
    if (this.courseData.first) {
      this.conditionType = 1;
    } else if (this.courseData.sum) {
      this.conditionType = 2;
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.conditionType) {
        return { valid: false, message: '请选择获取条件' };
      } else if (this.conditionType === 2 && !this.courseData.sum) {
        return { valid: false, message: '请输入累计上课天数' };
      }
      return { valid: true };
    },
    // 处理条件类型变化
    handleConditionTypeChange(val) {
      // 重置所有属性为undefined
      Object.keys(this.courseData).forEach(key => {
        this.courseData[key] = undefined;
      });

      // 如果选择了首次公益发帖，设置first为true
      if (val === 1) {
        this.courseData.first = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }

    span {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}
</style>
