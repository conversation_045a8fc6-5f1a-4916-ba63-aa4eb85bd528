<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='onSearch'
    >

      <el-form-item label='导航栏名称' prop='circleTabName'>
        <el-input v-model="form.circleTabName" placeholder="请输入导航栏名称" />
      </el-form-item>

      <el-form-item label='社团名称' prop='circleName'>
        <el-input v-model="form.circleName" placeholder='请输入社团名称' />
      </el-form-item>

      <el-form-item label='是否推荐' prop='ifRecommend'>
        <el-select v-model="form.ifRecommend" placeholder="请选择是否推荐">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label='状态' prop="ifShow">
        <el-select v-model="form.ifShow" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch("reset")' />
      </div>

    </el-form>

    <div class="table-tools">
      <el-button type="primary" size="mini" @click="onEdit()">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="circleTabName" label="导航栏名称" align="center" width="90" />
      <el-table-column prop="circleName" label="社团名称" align="center" width="160" />
      <el-table-column prop="introduce" label="简介" align="center" />
      <el-table-column prop="totalMemberNum" label="总成员" align="center" width="80" />
      <el-table-column prop="totalCreationNum" label="总数" align="center" width="80" />
      <el-table-column label="话题推荐" align="center" width="100">
        <template #default="{ row }">
          <div style="color:#66b1ff;cursor: pointer;" @click="onRecommendTopic(row)">
            已选：{{ row.topicRecommendCount || 0 }}个
          </div>
        </template>
      </el-table-column>
      <el-table-column label="习惯推荐" align="center" width="100">
        <template #default="{ row }">
          <div style="color:#66b1ff;cursor: pointer;" @click="onRecommendHabit(row)">
            已选：{{ row.habitRecommendCount || 0 }}个
          </div>
        </template>
      </el-table-column>
      <el-table-column label="活动推荐" align="center" width="100">
        <template #default="{ row }">
          <div style="color:#66b1ff;cursor: pointer;" @click="onRecommendActivity(row)">
            已选：{{ row.activityRecommendCount || 0 }}个
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否推荐圈子" align="center" :formatter="handleIfRecommend" width="100" />
      <el-table-column label="状态" align="center" :formatter="handleIfShow" width="60" />
      <!-- TODO: 排序 -->
      <el-table-column label="排序" align="center" prop="sort" width="60" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)">编辑</el-button>
          <el-button type="text" size="small" @click="onExpertConfig(row)">配置达人</el-button>
          <el-button type="text" size="small" @click="onRecommendTopic(row)">话题推荐</el-button>
          <el-button type="text" size="small" @click="onRecommendHabit(row)">习惯推荐</el-button>
          <el-button type="text" size="small" @click="onRecommendActivity(row)">活动推荐</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div>

    <!-- 达人配置弹框 -->
    <dialog-expert :visible.sync="expertVisible" :row="editRow" @close="getData" />

    <!-- 新增|编辑弹框 -->
    <dialog-edit :visible.sync="editVisible" :row="editRow" @confirm="editRow ? getData() : onSearch()" />

    <!-- 话题推荐弹框 -->
    <dialog-recommend-topic :visible.sync="recommendTopicVisible" :row="editRow" @close="getData" />

    <!-- 习惯推荐弹框 -->
    <dialog-recommend-habit :visible.sync="recommendHabitVisible" :row="editRow" @close="getData" />

    <!-- 活动推荐弹框 -->
    <dialog-recommend-activity :visible.sync="recommendActivityVisible" :row="editRow" @close="getData" />
  </div>
</template>

<script>
import DialogEdit from './components/dialog-edit.vue';
import DialogExpert from './components/dialog-expert.vue';
import DialogRecommendTopic from './components/dialog-recommend-topic.vue';
import DialogRecommendHabit from './components/dialog-recommend-habit.vue';
import DialogRecommendActivity from './components/dialog-recommend-activity.vue';

const IS_YES = 1;

export default {
  components: { DialogEdit, DialogExpert, DialogRecommendTopic, DialogRecommendHabit, DialogRecommendActivity },
  data() {
    return {
      tableLoading: true,
      editVisible: false,
      expertVisible: false,
      recommendTopicVisible: false,
      recommendHabitVisible: false,
      recommendActivityVisible: false,
      editRow: {},
      tableData: [],
      form: {
        circleTabName: undefined, // 圈子tab名
        circleName: undefined, // 圈子名称
        ifShow: undefined, // 是否显示 0:隐藏 1:显示
        ifRecommend: undefined // 是否推荐 0:否 1:是
      },
      pager: { pageNum: 1, pageSize: 10, total: 0 }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form };
        delete params.joinTime;
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/circleConfig/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 是否
    getIsYes(status) {
      return status === IS_YES;
    },
    // 处理是否推荐显示
    handleIfRecommend(row) {
      return this.getIsYes(row.ifRecommend) ? '是' : '否';
    },
    // 处理状态
    handleIfShow(row) {
      return this.getIsYes(row.ifShow) ? '启用' : '禁用';
    },
    // btn 搜索 | 重置
    onSearch(type) {
      if (this.tableLoading) return;
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 编辑
    onEdit(row) {
      this.editRow = row;
      this.editVisible = true;
    },
    // btn 配置达人
    onExpertConfig(row) {
      this.editRow = row;
      this.expertVisible = true;
    },
    // btn 话题推荐
    onRecommendTopic(row) {
      this.editRow = row;
      this.recommendTopicVisible = true;
    },
    // btn 习惯推荐
    onRecommendHabit(row) {
      this.editRow = row;
      this.recommendHabitVisible = true;
    },
    // btn 活动推荐
    onRecommendActivity(row) {
      this.editRow = row;
      this.recommendActivityVisible = true;
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}
</style>
