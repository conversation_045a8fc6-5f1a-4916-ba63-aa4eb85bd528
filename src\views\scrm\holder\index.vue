<template>
  <div class="yz-base-container">
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>
      <el-form-item label='学员姓名' prop='realName'>
        <el-input v-model="form.realName" placeholder="请输入学员姓名" />
      </el-form-item>
      <el-form-item label='学员身份证号' prop='idCard'>
        <el-input v-model="form.idCard" placeholder="请输入学员身份证号" />
      </el-form-item>
      <el-form-item label='学员手机号' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入学员手机号" />
      </el-form-item>
      <el-form-item label='跟进人姓名' prop='empName'>
        <el-input v-model="form.empName" placeholder="请输入跟进人姓名" />
      </el-form-item>
      <el-form-item label='辅导员姓名' prop='fdyEmpName'>
        <el-input v-model="form.fdyEmpName" placeholder="请输入辅导员姓名" />
      </el-form-item>
      <el-form-item label='其他辅导员姓名' prop='otherTutorEmpName'>
        <el-input v-model="form.otherTutorEmpName" placeholder="请输入其他辅导员姓名" />
      </el-form-item>
      <el-form-item label='协作人姓名' prop='coordinationEmpName'>
        <el-input v-model="form.coordinationEmpName" placeholder="请输入协作人姓名" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" @click="exportData">导出协作人</el-button>
      <el-button type="primary" size="small" @click="bidVisibel = true">导入协作人</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="realName" label="学员姓名" align="center" />
      <el-table-column prop="empName" label="跟进人" align="center" />
      <el-table-column prop="fdyEmpName" label="辅导员" align="center" />
      <el-table-column prop="otherTutorEmpName" label="其他辅导员" align="center" />
      <el-table-column prop="coordinationEmpName" label="协作人" align="center" />
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <batch-import-dialog :visible.sync="bidVisibel" @refresh="getTableList" />
  </div>
</template>

<script>
import { exportExcel } from '@/utils';
import batchImportDialog from './batch-import-dialog';
export default {
  name: 'Holder',
  components: {
    batchImportDialog
  },
  data() {
    return {
      tableLoading: false,
      form: {
        yzCode: '',
        realName: '',
        idCard: '',
        mobile: '',
        empName: '',
        fdyEmpName: '',
        otherTutorEmpName: '',
        coordinationEmpName: ''
      },
      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableData: [],
      selects: [],
      bidVisibel: false
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    exportData() {
      if (this.selects.length <= 0) return this.$message.error('请勾选数据');
      const ids = this.selects.map(item => item.userId).join(',');
      const data = {
        userIdList: ids
      };
      exportExcel('exportScrmHolderList', data);
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    // 处理查询参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      // TODO
      this.$post('getScrmHolderList', data).then(res => {
      // this.$post('getCourseCommentList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          body.data.forEach(item => {
            item.showMobile = false;
            item.score = Number(item.score);
          });
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 数据查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
