<template>
  <common-dialog
    :show-footer="true"
    width="60%"
    title="新手任务配置页"
    :visible.sync='show'
    :confirmLoading="confirmLoading"
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        label-suffix="："
        size="small"
      >
        <el-form-item label="新手任务标题" prop="taskTitle">
          <el-input v-model="form.taskTitle" placeholder="请输入新手任务标题" maxlength="8" show-word-limit />
        </el-form-item>
        <el-form-item label="页面头图" prop="taskHeadImage">
          <upload-file
            :max-limit="1"
            :file-list="taskHeadImage"
            @remove="taskHeadImageRemove"
            @success="taskHeadImageSuccess"
          />
        </el-form-item>
        <el-form-item label="活动规则" prop="taskRuleDesc">
          <wang-editor ref="taskRuleDesc" v-model="form.taskRuleDesc" :height="300" :content.sync="form.taskRuleDesc" />
        </el-form-item>

        <h2>新手任务模块：</h2>
        <el-form-item label="新手任务模块标题">
          <el-input v-model="form.taskModuleTitle" placeholder="请输入新手任务模块标题" maxlength="8" show-word-limit />
        </el-form-item>

        <h2>邀约有礼模块：</h2>
        <el-form-item label="邀约有礼胶囊位" prop="taskInviteImage">
          <upload-file
            :max-limit="1"
            :file-list="taskInviteImage"
            @remove="taskInviteImageRemove"
            @success="taskInviteImageSuccess"
          />
        </el-form-item>

        <h2>富文本模块：</h2>
        <el-form-item label="富文本标题">
          <el-input v-model="form.taskRichTextTitle" placeholder="请输入富文本标题" maxlength="8" show-word-limit />
        </el-form-item>
        <el-form-item label="富文本内容" prop="taskRichTextContent">
          <wang-editor ref="taskRichTextContent" v-model="form.taskRichTextContent" :height="500" :content.sync="form.taskRichTextContent" />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { taskMode } from './../../type';
import { arrToEnum } from '@/utils';
const taskModeEnum = arrToEnum(taskMode);
export default {
  filters: {
    taskModeEnum(val) {
      return taskModeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    checkStatus: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isEdit: false,
      confirmLoading: false,
      show: false,
      form: {
        taskHeadImage: '',
        taskInviteImage: '',
        taskRuleDesc: undefined,
        taskRichTextContent: undefined
      },
      taskHeadImage: [],
      taskInviteImage: [],
      rules: {
        taskTitle: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
        taskHeadImage: [{ required: true, message: '请上传页面头图', trigger: 'change' }],
        taskRuleDesc: [{ required: true, message: '请输入活动规则', trigger: 'blur' }],
        taskInviteImage: [{ required: true, message: '请上传邀约有礼胶囊位图', trigger: 'change' }],
        taskRichTextContent: [{ required: true, message: '请输入富文本内容', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 任务图标删除
    taskHeadImageRemove({ file, fileList }) {
      this.form.taskHeadImage = '';
    },
    // 任务图标上传成功
    taskHeadImageSuccess({ response, file, fileList }) {
      this.form.taskHeadImage = response;
    },
    // 胶囊图删除
    taskInviteImageRemove({ file, fileList }) {
      this.form.taskInviteImage = '';
    },
    // 胶囊图上传成功
    taskInviteImageSuccess({ response, file, fileList }) {
      this.form.taskInviteImage = response;
    },
    open() {
      if (this.checkStatus === 'UPDATE') {
        this.isEdit = true;
        this.getDetail();
      }
    },
    // 新手任务--配置详情
    getDetail() {
      this.$http.get('/newbietask/detail').then(res => {
        const { fail, body } = res;
        if (!fail) {
          const form = body;
          this.$refs.taskRuleDesc.setContent(form.taskRuleDesc);
          this.$refs.taskRichTextContent.setContent(form.taskRichTextContent);
          this.taskHeadImage = [{ url: form.taskHeadImage }];
          this.taskInviteImage = [{ url: form.taskInviteImage }];
          this.form = form;
        }
      });
    },
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.confirmLoading = true;

        const form = JSON.parse(JSON.stringify(this.form));

        const apiKey = this.isEdit ? 'updateNewbieTask' : 'addNewbieTask';
        this.$post(apiKey, form, { json: true }).then(res => {
          const { fail } = res;
          if (!fail) {
            this.show = false;
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            if (!this.isEdit) {
              this.$parent.getNewbieTaskCheck();
            }
            this.$parent.getTableList();
          }
        }).finally(() => {
          this.confirmLoading = false;
        });
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
