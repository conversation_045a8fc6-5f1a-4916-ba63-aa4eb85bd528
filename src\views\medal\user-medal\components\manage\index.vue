<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      label-suffix=":"
      @submit.native.prevent="search"
    >
      <el-form-item label="勋章Id" prop="medalTimeId">
        <el-input v-model="form.medalTimeId" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="勋章分类" prop="timeType">
        <el-select v-model="form.timeType" placeholder="请选择" clearable>
          <el-option
            v-for="item in medalCategoryList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="勋章类型" prop="medalType">
        <el-select v-model="form.medalType" placeholder="请选择" clearable>
          <el-option
            v-for="item in medalTypeList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户姓名" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="远智编号" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="勋章名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="启用状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否佩戴" prop="use">
        <el-select v-model="form.use" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="获得时间" prop="dateRange">
        <el-date-picker
          v-model="form.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          :disabled="tableLoading"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          :disabled="tableLoading"
          @click="search(0)"
        />
      </div>
    </el-form>

    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column
        label="用户信息"
        width="180"
        align="center"
      >
        <template slot-scope="{ row }">
          <div style="text-align: left;">
            <div>昵称：{{ row.nickname }}</div>
            <div>远智编号：{{ row.yzCode }}</div>
            <div>真实姓名：{{ row.realName }}</div>
            <div>手机号码：{{ row.mobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="medalTimeId"
        label="勋章Id"
        align="center"
      />
      <el-table-column
        label="勋章分类"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.timeType | medalCategoryEnum }}
        </template>
      </el-table-column>
      <el-table-column
        label="勋章类型"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.medalType | medalTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        label="勋章名称"
        align="center"
      />
      <el-table-column
        label="获得日期"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ Number(row.gainTime) | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column
        label="当前是否佩戴"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.use === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column
        label="启用状态"
        align="center"
      >
        <template slot-scope="{ row }">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button
            size="small"
            :type="row.status === 1 ? 'info' : 'primary'"
            @click="handleStatusChange(row)"
          >{{ row.status === 1 ? '禁用' : '启用' }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
  </div>
</template>

<script>
import { httpPostDownFile } from '@/utils/downExcelFile';
import { medalCategoryList, medalTypeList } from '../../../type';
import { arrToEnum, handleDateControl } from '@/utils';

const medalCategoryEnum = arrToEnum(medalCategoryList, 'value', 'name');
const medalTypeEnum = arrToEnum(medalTypeList, 'value', 'name');

export default {
  name: 'MedalManage',
  filters: {
    medalTypeEnum(val) {
      return medalTypeEnum[val] || '/';
    },
    medalCategoryEnum(val) {
      return medalCategoryEnum[val] || '/';
    }
  },
  data() {
    return {
      // 搜索表单
      form: {
        medalTimeId: '',
        timeType: '',
        medalType: '',
        realName: '',
        yzCode: '',
        name: '',
        status: '',
        use: '',
        dateRange: []
      },
      // 表格加载状态
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      // 勋章分类选项
      medalCategoryList,
      // 勋章类型选项
      medalTypeList
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 导出数据
    exportData() {
      const params = this.handleQueryParams();
      delete params.pageNum;
      delete params.pageSize;
      httpPostDownFile({
        url: '/bmsAdmin/userMedalTimeExport',
        params
      });
    },
    // 处理查询参数
    handleQueryParams() {
      const form = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(form.dateRange);
      form.gainTimeBegin = date[0];
      form.gainTimeEnd = date[1];
      delete form.dateRange;
      const data = {
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit,
        ...form
      };
      return data;
    },

    // 处理状态变更
    handleStatusChange(row) {
      const isEnable = row.status !== 1;
      const confirmMsg = isEnable
        ? '启用后，该用户可触发弹窗提醒及勋章佩戴'
        : '禁用后，该用户将不触发弹窗提醒及勋章佩戴';

      this.$confirm(confirmMsg, '请确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          userMedalTimeId: row.userMedalTimeId,
          status: isEnable ? 1 : 0
        };
        this.$post('updateUserMedalTime', params, { json: true })
          .then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message.success('操作成功');
              this.getTableList();
            }
          });
      }).catch(() => {});
    },

    // 搜索
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      }
      this.pagination.page = 1;
      this.getTableList();
    },

    // 获取表格数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const data = this.handleQueryParams();

      this.$post('getUserMedalTimeList', data, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body?.data || [];
            this.pagination.total = body?.recordsTotal || 0;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.search-reset-box {
  display: flex;
  justify-content: center;
}

</style>
