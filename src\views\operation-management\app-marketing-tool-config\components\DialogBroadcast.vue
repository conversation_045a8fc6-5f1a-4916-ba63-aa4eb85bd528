<template>
  <div>
    <common-dialog
      show-footer
      width="500px"
      title="广播配置"
      :visible.sync="visible"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size="mini"
          :model="form"
          label-width="100px"
        >
          <el-form-item
            label="广播内容"
            prop="context"
            :rules="form.jumpUrl && rules.context || []"
          >
            <el-input
              v-model="form.context"
              placeholder="请输入广播内容"
              show-word-limit
              maxlength="30"
              clearable
            />
          </el-form-item>

          <el-form-item label="跳转路由" prop="jumpUrl">
            <el-input
              v-model="form.jumpUrl"
              placeholder="请输入跳转路由"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
export default {
  props: {
    show: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        context: '',
        jumpUrl: ''
      },
      rules: {
        context: [{ required: true, message: '请输入广播内容' }]
      }
    };
  },
  computed: {
    visible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getBroadcastConfig();
      }
    }
  },
  methods: {
    open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({ ...this.row }));
      }
    },
    async getBroadcastConfig() {
      try {
        this.loading = true;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/bmsAdmin/getFansDataRedis',
          json: true
        });
        if (code !== '00') return;
        this.form.context = body?.context;
        this.form.jumpUrl = body?.jumpUrl;
      } finally {
        this.loading = false;
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              context: this.form.context,
              jumpUrl: this.form.jumpUrl
            };
            const url = '/bmsAdmin/setFansDataRedis';
            const { code } = await this.$http({
              method: 'post',
              url,
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      this.visible = false;
    }
  }
};
</script>
