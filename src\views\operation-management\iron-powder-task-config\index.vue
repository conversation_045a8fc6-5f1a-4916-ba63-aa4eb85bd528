<template>
  <div class="yz-base-container">
    <div style="margin-bottom: 16px">
      <el-button
        type="primary"
        size="small"
        @click="descVisible = true"
      >文案内容配置</el-button>
    </div>
    <!-- 表格 -->
    <el-table v-loading="loading" border size="small" :data="list">
      <el-table-column prop="sort" label="任务排序" align="center" />
      <el-table-column prop="context" label="任务名称" align="center" />
      <el-table-column
        prop="shareButtonName"
        label="分享按钮名称"
        align="center"
      />
      <el-table-column prop="jumpUrl" label="海报二维码跳转" align="center" />
      <el-table-column prop="jumpText" label="海报引导语" align="center" />
      <el-table-column prop="updateTime" label="更新时间" align="center" />
      <el-table-column prop="updateUser" label="最后操作人" align="center" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button
            type="text"
            size="small"
            @click="onEdit(row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 编辑弹窗 -->
    <DialogEdit :show.sync="editVisible" :row="editRow" @confirm="getData" />

    <!-- 说明配置弹窗组件化 -->
    <DescDialog :show.sync="descVisible" />
  </div>
</template>

<script>
import DialogEdit from './components/DialogEdit.vue';
import DescDialog from './components/DescDialog.vue';
export default {
  components: { DialogEdit, DescDialog },
  data() {
    return {
      loading: true,
      editRow: null,
      editVisible: false,
      descVisible: false,
      list: []
    };
  },
  created() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.loading = true;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/bmsAdmin/fansShareConfig'
        });
        if (code !== '00') return;
        this.list = body;
      } finally {
        this.loading = false;
      }
    },
    // btn 编辑
    onEdit(row) {
      this.editRow = row;
      this.editVisible = true;
    }
  }
};
</script>
