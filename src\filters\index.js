import moment from 'moment';
import { splitOssImgUrl, getTextFromDict, transformSecond } from '@/utils';
import {
  courseType,
  mebFreeType,
  cardSpecies,
  status,
  approvalStatus,
  orderChannel
} from '@/config/constant';

export const splitOssUrl = splitOssImgUrl;

export const getDictVal = getTextFromDict;

// 转换上课类型
export const tansformCourseType = (val) => {
  return courseType[val] || '';
};

// 转换时间戳
export const transformTimeStamp = (val, format = 'YYYY-MM-DD HH:mm:ss', empty = '') => {
  return !val ? empty : moment(val).format(format);
};

export const timestampToSecond = transformSecond;

// 会员卡类型
export const memberCardType = (val) => {
  if (!val) return '';
  return mebFreeType[val];
};

// 会员卡种类
export const tansformCardSpecies = (val) => {
  if (!val) return '';
  return cardSpecies[val];
};

export const tansformStatus = (val) => {
  if (!val) return '';
  return status[val];
};

// 上课方式
export const tansformWayOfClass = (val) => {
  const stringVal = String(val);
  if (!stringVal) return '';
  const data = {
    '0': '直播',
    '1': '录播',
    '2': '硬盘推流',
    '3': '回放'
  };
  return data[stringVal];
};

// 上架模块
export const tansformShelfModule = (val) => {
  if (!val) return '';
  const data = {
    '5': 'banner',
    '6': '推荐区',
    '7': 'APP首页',
    '8': '读书计划列表页banner'

  };
  return data[val];
};

// 审核状态
export const tansformApprovalStatus = (val) => {
  if (!val) return '';
  return approvalStatus[val];
};

// 收款方式
export const tansformPayMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'paymentType');
};
// 学业类型
export const recruitTypeMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'recruitType');
};
// 训练营类型
export const trainCampTypeMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'trainCampType');
};
export const trainCampGiftTypeMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'trainCampGiftType');
};
export const trainCampArticleGiftNameMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'trainCampArticleGiftName');
};
export const trainCampVirtualGiftNameMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'trainCampVirtualGiftName');
};
export const trainCampBallGiftNameMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'trainCampBallGiftName');
};
export const stdStageMethod = (val) => {
  const stringValue = String(val);
  if (!stringValue) return '';
  return getTextFromDict(stringValue, 'stdStage');
};
// 订单渠道
export const tansformOrderChannel = (val) => {
  if (!val) return '';
  return orderChannel[val];
};

/**
 * 隐藏手机号码中间四位
 * */
export const hidePhone = (phone, len = 6, show) => {
  if (!phone) return phone;
  if (show) return phone;
  return phone.replace(new RegExp('^(\\d{3}).+(\\d{' + (8 - len) + '})$'), `$1${'*'.repeat(len)}$2`);
};

export const courseSupplier = (val) => {
  if (!val) return '';
  const data = {
    'KHW': '课火网',
    'YZ': '远智教育',
    'KSB': '课师宝',
    'YIZHI': '易智'
  };
  return data[val];
};

// 订单来源
export const orderSource = (val) => {
  if (!val) return '';
  const data = {
    lingYi: '零一裂变',
    YZ: '远智'
  };
  return data[val];
};

