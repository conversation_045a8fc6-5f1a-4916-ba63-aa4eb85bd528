<template>
  <common-dialog
    is-full
    title="购买课程明细"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialog-main">
      <open-packup>
        <el-form
          ref='searchForm'
          class='yz-search-form'
          size='mini'
          :model='form'
          label-width='120px'
          @submit.native.prevent='search'
        >
          <el-form-item label='远智编码' prop='yzCode'>
            <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
          </el-form-item>
          <el-form-item label='用户姓名' prop='userName'>
            <el-input v-model="form.userName" placeholder="请输入用户姓名" />
          </el-form-item>

          <el-form-item label='手机号码' prop='mobile'>
            <el-input v-model="form.mobile" placeholder="请输入手机号码" />
          </el-form-item>

          <el-form-item label='订单来源' prop='orderChannel'>
            <el-select
              v-model="form.orderChannel"
              clearable
              placeholder="请选择订单来源"
            >
              <el-option label="零一裂变" value="lingYi" />
              <el-option label="远智" value="YZ" />
            </el-select>
          </el-form-item>

          <el-form-item label='最近售卖渠道' prop='appType'>
            <el-select v-model="form.appType" placeholder="请选择售卖渠道">
              <el-option
                v-for="item in $localDict['platform']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label='跟进人' prop='followUserName'>
            <el-input v-model="form.followUserName" placeholder="请输入跟进人" />
          </el-form-item>

          <el-form-item label='最近购买时间' prop='time'>
            <el-date-picker
              v-model="form.time"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>

          <el-form-item label='课程套餐名称' prop='goodsShelfName'>
            <el-input v-model="form.goodsShelfName" placeholder="请输入套餐名称" />
          </el-form-item>

          <el-form-item label='推荐人' prop='recommendUserName'>
            <el-input v-model="form.recommendUserName" placeholder="请输入推荐人姓名" />
          </el-form-item>

          <el-form-item label='招生老师' prop='empName'>
            <el-input v-model="form.empName" placeholder="请输入招生老师姓名" />
          </el-form-item>

          <div class="search-reset-box">
            <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
          </div>

        </el-form>
      </open-packup>

      <!-- 按钮区 -->
      <!-- <div class='yz-table-btnbox'>
        <el-button type="success" size="small" icon="el-icon-upload2" @click="exportListExcel">EXCEL导出</el-button>
      </div> -->

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 290px)"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="userName" label="用户" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="goodsShelfName" label="课程套餐名称" align="center" />
        <el-table-column label="订单来源" align="center" prop="orderChannel">
          <template slot-scope="scope">
            {{ scope.row.orderChannel | orderSource }}
          </template>
        </el-table-column>
        <el-table-column prop="appType" label="售卖渠道" align="center">
          <template slot-scope="scope">
            {{ scope.row.appType | channel }}
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="购买时间" align="center" />
        <el-table-column prop="payStatus" label="购买状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.payStatus | payStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="recommendUserName" label="推荐人" align="center" />
        <el-table-column prop="empName" label="招生老师" align="center" />
        <el-table-column prop="empDpName" label="招生老师部门" align="center" />
        <el-table-column prop="followName" label="跟进人" align="center" />
        <el-table-column prop="followDpName" label="跟进人部门" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>
<script>
import { handleDateControl, exportExcel } from '@/utils';
import openPackup from '@/components/open-packup';
export default {
  components: {
    openPackup
  },
  filters: {
    channel(val) {
      if (!val) return '';
      const data = {
        WECHAT: '微信',
        APP: 'APP'
      };
      return data[val];
    },
    payStatus(val) {
      if (!val) return;
      const data = {
        '2': '已购买',
        '3': '已退款'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      show: false,
      form: {
        time: '',
        userName: '',
        mobile: '',
        goodsShelfName: '',
        appType: '',
        recommendUserName: '',
        followUserName: '',
        empName: '',
        createStartTime: '',
        createEndTime: '',
        tradeCode: 'course', // 交易编码 课程：course，读书计划：readPlan
        orderChannel: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    exportListExcel() {
      const data = this.handleQueryParams();
      exportExcel('exportUserGoodsShelfPayList', data);
    },
    init() {
      this.getTableList();
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.createStartTime = date[0];
      formData.createEndTime = date[1];
      delete formData.time;

      const data = {
        userId: this.userId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...formData
      };
      return data;
    },
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getBoughtCourseList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
