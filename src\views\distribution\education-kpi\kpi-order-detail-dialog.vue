<template>
  <common-dialog
    isFull
    :title="dialogTitle"
    width="1000px"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <open-packup>
        <el-form
          ref='searchForm'
          class='yz-search-form'
          size='mini'
          :model='form'
          label-width='120px'
          @submit.native.prevent='search'
        >
          <el-form-item label='商品名称' prop='commodityName'>
            <el-input v-model="form.commodityName" placeholder="请输入商品名称" />
          </el-form-item>

          <el-form-item label='商品id' prop='commodityId'>
            <el-input v-model="form.commodityId" placeholder="请输入商品id" />
          </el-form-item>

          <el-form-item label='购买人姓名' prop='realName'>
            <el-input v-model="form.realName" placeholder="请输入购买人姓名" />
          </el-form-item>

          <el-form-item label='购买人手机号码' prop='mobile'>
            <el-input v-model="form.mobile" placeholder="请输入购买人手机号码" />
          </el-form-item>

          <el-form-item label='购买人远智编码' prop='yzCode'>
            <el-input v-model="form.yzCode" placeholder="请输入购买人远智编码" />
          </el-form-item>

          <el-form-item label='成交人' prop='traderEmpName'>
            <el-input v-model="form.traderEmpName" placeholder="请输入成交人" />
          </el-form-item>

          <el-form-item label='成交人手机' prop='traderMobile'>
            <el-input v-model="form.traderMobile" placeholder="请输入成交人手机" />
          </el-form-item>

          <el-form-item label='邀约人' prop='inviteEmpName'>
            <el-input v-model="form.inviteEmpName" placeholder="请输入邀约人" />
          </el-form-item>

          <el-form-item label='邀约人手机' prop='inviteMobile'>
            <el-input v-model="form.inviteMobile" placeholder="请输入邀约人手机" />
          </el-form-item>

          <el-form-item label='付费时间' prop='payTime'>
            <el-date-picker
              v-model="form.payTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placement="bottom-start"
            />
          </el-form-item>

          <el-form-item label='退费时间' prop='refundTime'>
            <el-date-picker
              v-model="form.refundTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placement="bottom-start"
            />
          </el-form-item>
          <div class="search-reset-box">
            <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
          </div>

        </el-form>
      </open-packup>
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" plain style="opacity: 0;">占位</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="commodityName" label="商品名称" align="center" />
        <el-table-column prop="commodityId" label="商品id" align="center" />
        <el-table-column prop="realName" label="购买人" align="center" />
        <el-table-column prop="mobile" label="购买人手机号" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.mobile }}</div>
            <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="yzCode" label="远智编号" align="center" />
        <el-table-column prop="commodityOrderNo" label="订单号" align="center" />
        <el-table-column prop="commodityOrderStatusName" label="订单状态" align="center" />
        <el-table-column prop="payTime" label="付费时间" align="center" />
        <el-table-column prop="refundTime" label="退费时间" align="center" />
        <el-table-column prop="audit" label="商品系数" align="center" />
        <el-table-column prop="inviteEmpName" label="邀约人" align="center" />
        <el-table-column prop="traderEmpName" label="分销人" align="center" />
        <el-table-column prop="invitePerformanceValue" :label="invitePerformanceTitle" align="center" />
        <el-table-column prop="inviteDivideProportion" label="分成比例" align="center">
          <template slot-scope="scope">
            {{ scope.row.inviteDivideProportion * 100 + '%' }}
          </template>
        </el-table-column>
        <el-table-column prop="inviteIncome" :label="inviteIncome" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { handleDateControl } from '@/utils';
import LookMobile from '@/mixins/LookMobile';
export default {
  components: {},
  mixins: [LookMobile],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String,
      default: ''
    },
    isRefund: {
      type: Number,
      default: 0
    },
    month: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      allVisible: false,
      tableLoading: false,
      form: {
        commodityName: '',
        commodityId: '',
        realName: '',
        mobile: '',
        yzCode: '',
        traderEmpName: '',
        traderMobile: '',
        inviteEmpName: '',
        inviteMobile: '',
        payTime: '',
        refundTime: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  computed: {
    dialogTitle() {
      const title = `职业教育缴费报名${this.isRefund === 1 ? '退费' : '缴费'}系数总和明细`;
      return this.type === 'single' ? `个人${title}` : `团队${title}`;
    },
    invitePerformanceTitle() {
      const title = '单生绩效值';
      return this.type === 'single' ? `个人${title}` : `${this.rowData.roleType}${title}`;
    },
    inviteIncome() {
      const title = '绩效';
      return this.type === 'single' ? `个人${title}` : `${this.rowData.roleType}${title}`;
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const payTime = handleDateControl(formData.payTime);
      formData.payStartTime = payTime[0];
      formData.payEndTime = payTime[1];
      const refundTime = handleDateControl(formData.refundTime);
      formData.refundStartTime = refundTime[0];
      formData.refundEndTime = refundTime[1];
      delete formData.payTime;
      delete formData.refundTime;
      const { performanceType, empTitle, empId } = this.rowData;
      const data = {
        ...formData,
        month: this.month,
        performanceType: performanceType,
        empTitle: empTitle,
        empId: empId,
        isRefund: this.isRefund,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('queryOrderRecordsPage', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    open() {
      this.getTableList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleAll() {
      this.allVisible = !this.allVisible;
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
