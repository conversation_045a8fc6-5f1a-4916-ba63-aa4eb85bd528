<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='课程名称' prop='courseName'>
        <el-input v-model="form.courseName" placeholder="请输入课程名称" />
      </el-form-item>

      <el-form-item label='课时名称' prop='courseTimeName'>
        <el-input v-model="form.courseTimeName" placeholder="请输入课时名称" />
      </el-form-item>

      <el-form-item label='上课方式' prop='courseType'>
        <el-select
          v-model="form.courseType"
          filterable
          clearable
          placeholder="请选择上课方式"
        >
          <el-option
            v-for="item in $localDict['courseType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />

        </el-select>
      </el-form-item>

      <el-form-item label='状态' prop='allow'>
        <el-select
          v-model="form.allow"
          filterable
          clearable
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in $localDict['status']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='开课时间' prop='time'>
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <el-form-item label='是否推流成功' prop='complete'>
        <el-select
          v-model="form.complete"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="成功" value="1" />
          <el-option label="失败" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='转存状态' prop='txyStatus'>
        <el-select
          v-model="form.txyStatus"
          clearable
          placeholder="选择转存状态"
        >
          <el-option
            v-for="item in $dictJson['txyStatus']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="success" size="small" plain @click="batchEditStatus(1)">批量启用</el-button>
      <el-button type="danger" size="small" plain @click="batchEditStatus(2)">批量禁用</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="courseId" label="课程编码" align="center" />
      <el-table-column prop="courseName" label="课程名称" align="center" />
      <el-table-column prop="stageName" label="阶段名称" align="center" />
      <el-table-column prop="courseTimeName" label="课时名称" align="center" />
      <el-table-column prop="courseType" label="上课方式" align="center">
        <template slot-scope="scope">
          {{ scope.row.courseType| tansformWayOfClass }}
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="开课时间" align="center" width="150" />
      <el-table-column prop="endTime" label="截至时间" align="center" width="150" />
      <el-table-column prop="type" label="上课情况" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleSee(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="result" label="硬盘推流结果" align="center">
        <template />
      </el-table-column>
      <el-table-column prop="txyStatus" label="腾讯云转存状态" align="center">
        <template slot-scope="scope">
          {{ format(scope.row.txyStatus.toString(), 'txyStatus') }}
        </template>
      </el-table-column>
      <el-table-column prop="txyFileId" label="腾讯云ID" align="center" />
      <el-table-column prop="sort" label="排序" align="center" />
      <el-table-column prop="allow" label="是否启用" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.allow===1?'success':'danger'">
            {{ scope.row.allow | tansformStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <update-dialog :visible.sync="udVisible" :title="udTitle" :course-time-id="currentEditCourseId" />
    <see-happening-dialog :id="currentSeeCourseTimeId" :visible.sync="shdVisible" />
  </div>
</template>
<script>

import { pagination, TABLE_HEIGHT } from '@/config/constant';
import { handleDateControl, jsonParse } from '@/utils/index';
import updateDialog from './update-dialog';
import seeHappeningDialog from './see-happening-dialog';
import { getTextFromDict } from '@/utils';
export default {
  components: {
    updateDialog,
    seeHappeningDialog
  },
  data() {
    return {
      table_height: TABLE_HEIGHT,
      udVisible: false,
      shdVisible: false,
      udTitle: '新增',
      tableLoading: false,
      selection: [],
      currentSeeCourseTimeId: null,
      currentEditCourseId: null,
      form: {
        time: '',
        courseName: '',
        courseTimeName: '',
        courseType: '',
        allow: '',
        startTime: '',
        endTime: '',
        complete: '',
        txyStatus: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10,
        ...pagination
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    format(valueKey, key) {
      return getTextFromDict(valueKey, key);
    },
    handleSee(row) {
      this.currentSeeCourseTimeId = row.courseTimeId;
      this.shdVisible = true;
    },
    handleAdd() {
      this.udTitle = '新增';
      this.currentEditCourseId = null;
      this.udVisible = true;
    },
    handleEdit(row) {
      this.udTitle = '编辑';
      this.currentEditCourseId = row.courseTimeId;
      this.udVisible = true;
    },
    // 获取列表数据
    async getTableList() {
      this.tableLoading = true;
      const sourceData = JSON.parse(JSON.stringify(this.form));
      const time = handleDateControl(sourceData.time);
      sourceData.startTime = time[0];
      sourceData.endTime = time[1];
      delete sourceData.time;
      const data = {
        ...sourceData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      const { fail, body } = await this.$post('getCourseTime', data);
      if (!fail) {
        body.data.forEach(item => {
          if (item.courseType === 2) {
            const data = jsonParse(item.result);
            if (data.code) {
              item.result = data.code === 200 ? '成功' : '失败';
            }
          }
        });
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
        this.tableLoading = false;
      }
    },
    // status 1启，2停
    async batchEditStatus(status) {
      if (this.selection.length > 0) {
        let Ids = '';
        this.selection.forEach(e => {
          Ids += e.courseTimeId + ',';
        });
        Ids = Ids.slice(0, Ids.length - 1);
        const data = {
          status: status,
          courseTimeIds: Ids
        };
        const { fail } = await this.$post('editCourseTimeStage', data);
        if (!fail) {
          this.getTableList();
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      } else {
        this.$message.error('请勾选数据');
      }
    },
    handleSelectionChange(selection) {
      this.selection = selection;
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  padding-top:20px;
}
</style>
