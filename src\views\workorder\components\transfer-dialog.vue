<template>
  <common-dialog
    :visible.sync="visible"
    :title="selectedCount > 1 ? '批量转交' : '转交'"
    width="400px"
    :show-footer="true"
    @confirm="handleConfirm"
    :confirm-loading="loading"
    @close="handleClose"
  >
    <div class="transfer-dialog">
      <div class="dialog-info" v-if="selectedCount > 1">
        已选择{{ selectedCount }}条工单批量操作，请谨慎操作
      </div>

      <el-form ref="transferForm" :model="form" :rules="rules" label-width="0">
        <el-form-item label="" prop="reason">
          <div class="label">流转原因</div>
          <common-select
            v-model="form.reason"
            :type="
              orderType() === '1'
                ? 'workOrderDict'
                : 'workOrderDict'
            "
            :extraParams="{
              parentId: orderType() === '1' ? 4 : 6,
            }"
            placeholder="请选择"
            class="select-input"
          />
        </el-form-item>

        <el-form-item label="" prop="toEmpId">
          <div class="label">受理人</div>
          <common-select
            v-model="form.toEmpId"
            type="handler"
            placeholder="请选择"
            class="select-input"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  name: "TransferDialog",
  components: {
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  inject: ["orderType"],

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectedWorkOrders: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      form: {
        reason: "",
        toEmpId: "",
      },
      rules: {
        toEmpId: [
          { required: true, message: "请选择受理人", trigger: "change" },
        ],
      },
    };
  },
  computed: {
    selectedCount() {
      return this.selectedWorkOrders.length;
    },
  },
  watch: {
    orderType: {
      handler(newVal) {
        //如果工单类型为投诉，则默认选择投诉原因
        if (newVal() === "2") {
          this.form.reason = "";
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      this.$refs.transferForm?.resetFields();
      this.$emit("update:visible", false);
    },
    async handleConfirm() {
      this.$refs.transferForm.validate(async (valid) => {
        if (!valid) {
          return;
        }

        this.loading = true;

        try {
          // 获取所有选中工单的ID
          const orderId = this.selectedWorkOrders.map(
            (item) => item.consultId || item.complaintId || item.id
          );

          const { fail, body } = await this.$post(
            "transferBatchWorkOrder",
            {
              type: this.orderType(),
              orderId,
              ...this.form,
            },
            { json: true }
          );

          if (!fail) {
            this.$message.success("转交成功");
            this.$emit("success");
            this.$emit("update:visible", false);
          }
        } catch (error) {
          console.error("转交工单失败", error);
          this.$message.error("转交失败，请重试");
        } finally {
          this.loading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.transfer-dialog {
  padding: 20px;

  .dialog-info {
    color: #666;
    font-size: 14px;
    margin-bottom: 20px;
  }

  .form-item {
    margin-bottom: 20px;
  }

  .label {
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
  }

  .select-input {
    width: 100%;
  }
}
</style>
