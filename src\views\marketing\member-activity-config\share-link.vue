<template>
  <common-dialog
    width="1140px"
    :title="title"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialog-main">
      <div class="operation">
        <el-button
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="childDialogShow=true"
        >
          微信渠道链接
        </el-button>
      </div>
      <div class="table">
        <el-table
          v-loading="tableLoading"
          border
          size="small"
          style="width: 100%"
          header-cell-class-name='table-cell-header'
          :data="tableData"
        >
          <el-table-column prop="channelName" label="推广渠道" align="center" />
          <el-table-column prop="channelUrl" label="外部链接" align="center" width="280">
            <template slot-scope="scope">
              <a class="link" @click="previewPage(scope.row.channelUrl)">{{ scope.row.channelUrl }}</a>
              <el-button
                class="copyBtan"
                size="mini"
                type="primary"
                @click="copyLink(scope.row,'channelUrl','copyBtnName')"
              >{{ scope.row.copyBtnName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="channelUrl" label="内部链接" align="center" width="280">
            <template slot-scope="scope">
              <span>{{ scope.row.insideChannelUrl }}</span>
              <el-button
                class="copyBtan"
                size="mini"
                type="primary"
                @click="copyLink(scope.row,'insideChannelUrl','internalBtnLinkName')"
              >{{ scope.row.internalBtnLinkName }}</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="buyTimes" label="渠道购买人数" align="center" />
          <el-table-column prop="pv" label="PV" align="center" />
          <el-table-column prop="uv" label="UV" align="center" />
          <el-table-column label="明细数据" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                @click="lookAccess(scope.row)"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div ref="copyElem" class="hidden">{{ copyText }}</div>
    <!-- 新增微信渠道链接弹窗 -->
    <common-dialog
      width="400px"
      title="新增"
      :visible.sync='childDialogShow'
      :show-footer="true"
      @close='childDialogClose'
      @confirm='addWxlink'
    >
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          label-width="80px"
          :rules="rules"
        >
          <el-form-item label='渠道名' prop='channelName'>
            <el-input
              v-model="form.channelName"
              maxlength="20"
              show-word-limit
              placeholder="请输入"
            />
          </el-form-item>
          <el-form-item label='微信链接' prop='link'>
            <el-input
              v-model="form.link"
              placeholder="自动生成"
              disabled
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>

    <carrier :visible.sync="cVisible" :target-type='targetType' :target-id='targetId' />

    <el-dialog
      class="preview"
      :visible.sync="preview"
      width="375px"
      append-to-body
      :show-close="false"
      top="50px"
    >
      <!-- <div ref="maskLayer" class="mask-layer" /> -->
      <iframe ref="iframe" class="iframe" :src="lookLink" />
    </el-dialog>

  </common-dialog>
</template>
<script>
import { CopyElemText } from '@/common';
import carrier from './carrier';
export default {
  components: {
    carrier
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    mappingid: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      preview: false,
      // maskLayerHeight: 0,
      lookLink: '',
      targetType: '',
      targetId: '',
      copyText: '',
      show: false,
      cVisible: false,
      tableLoading: false,
      tableData: [],
      rules: {},
      form: {
        channelName: '',
        link: ''
      },
      childDialogShow: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      this.getPromoteLinkList();
    },
    previewPage(url) {
      // const self = this;
      this.lookLink = url;
      this.preview = true;
      this.$nextTick(() => {
        const elem = this.$refs.iframe;
        elem.onload = function() {
          // self.maskLayerHeight = elem.offsetHeight + 'px';
          // console.log(elem.contentWindow.document.getElementsByTagName('body'), 'body');
        };
      });
    },
    lookAccess(row) {
      this.targetType = row.targetType;
      this.targetId = row.channelId;
      this.cVisible = true;
    },
    copyLink(row, field, btnNameField) {
      this.copyText = row[field];
      this.$nextTick(() => {
        CopyElemText(this.$refs.copyElem);
        row[btnNameField] = '复制成功';
        setTimeout(() => {
          row[btnNameField] = '复制';
        }, 800);
      });
    },
    addWxlink() {
      const data = {
        mappingId: this.mappingid,
        channelName: this.form.channelName
      };
      this.$post('addWxLink', data, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.childDialogShow = false;
            this.getPromoteLinkList();
          }
        });
    },
    getPromoteLinkList() {
      this.tableLoading = true;
      const data = {
        mappingId: this.mappingid
      };
      this.$post('getPromoteLinkList', data, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.forEach(item => {
              item.copyBtnName = '复制';
              item.internalBtnLinkName = '复制';
            });
            this.tableLoading = false;
            this.tableData = body;
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
    },
    childDialogClose() {
      this.$refs['form'].resetFields();
      this.childDialogShow = false;
      // this.$emit('update:visible', false);
    }
  }
};
</script>
<style lang="scss" scoped>
.dialog-main{
  .operation{
    text-align: right;
  }
  .table{
    min-height: 350px;
    margin-top: 16px;
  }
  .copyBtan {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translate(-50%,0);
    padding: 4px;
  }
  .link {
    text-decoration:underline;
    margin-bottom: 30px;
    display: inline-block;
  }

}
.hidden {
  position: absolute;
  top:0;
  opacity: 0 !important;
}

.preview{
  ::v-deep .el-dialog__body {
    padding:0 !important;
  }
  .mask-layer{
    position: absolute;
    width: 100%;
    height: 1400px;
    z-index: 2;
  }
  .iframe {
    width: 375px;
    height: 667px;
    border: none;
  }
}
</style>
