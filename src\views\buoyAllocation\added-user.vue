<template>
  <common-dialog
    is-full
    title="选择用户分群"
    class="aUsers"
    :showFooter="true"
    :visible.sync="visible"
    @open="initBtn"
    @close="closeBtn"
    @confirm="confirmBtn"
  >
    <!-- 表单区 -->
    <el-form
      size="mini"
      :model="querys"
      label-width="120px"
      class="yz-search-form"
      style="margin: 20px 20px 0 0;"
      @submit.native.prevent="searchBtn"
    >
      <el-form-item label="分群名称：">
        <el-input v-model="querys.tagGroupName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="创建人：">
        <el-input v-model="querys.createUser" placeholder="请输入" />
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click.stop="refreshBtn" />
      </div>
    </el-form>
    <!-- 表格区 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%;margin-top: 30px;"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-radio v-model="scope.row.shos" :label="1" @input="selectRadio(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column label="分群名称" prop="tagGroupName" align="center" />
      <el-table-column label="预估人数" prop="tagGroupUserCount" align="center" />
      <el-table-column label="创建人" prop="createUserName" align="center" />
      <el-table-column label="创建时间" prop="createTime" align="center" />
      <el-table-column label="分群失效时间" prop="expirationTime" align="center" />
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.rows" @pagination="getTableList" />
    </div>
  </common-dialog>
</template>

<script>
import { getCutDay } from '@/utils';

export default {
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: '' },
    params: { type: Object, default: () => {} }
  },
  data() {
    return {
      querys: {
        tagGroupName: '',
        createUser: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        rows: 10
      }
    };
  },
  methods: {
    initBtn() {
      console.log('querys', this.params);
      this.querys = JSON.parse(JSON.stringify(this.params));
      this.getTableList();
    },
    // 重置
    refreshBtn() {
      this.querys = JSON.parse(JSON.stringify(this.params));
    },
    // 勾选
    selectRadio(row) {
      const data = JSON.parse(JSON.stringify(this.tableData));
      console.log('selectRadio', row.tagGroupId, row.shos);
      data.map(item => {
        item.shos = 0;
        if (row.tagGroupId === item.tagGroupId) {
          item.shos = row.shos;
        }
      });
      this.tableData = data;
    },
    searchBtn() {
      this.pagination.page = 1;
      this.getTableList();
    },
    // 接口获取配置列表
    getTableList() {
      this.tableLoading = true;
      const { createUser, tagGroupName } = this.querys;
      this.$http.post(
        `/tagGroup/queryByPage.do?page=${this.pagination.page}&rows=${this.pagination.rows}`,
        { state: 1, tagGroupName, createUser },
        { headers: { 'Content-Type': 'application/json' }}
      )
        .then((res) => {
          const { code, body } = res;
          console.log('接口获取配置列表-3');
          if (code === '00') {
            body.data?.map(item => {
              item.createTime = item.createTime && getCutDay(item.createTime);
              item.expirationTime = item.expirationTime && getCutDay(item.expirationTime);
            });
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
          this.tableLoading = false;
        }).catch(() => {
          this.tableData = [];
          this.tableLoading = false;
        });
    },
    // 关闭当前弹窗
    closeBtn() {
      console.log('关闭当前弹窗-3');
      this.querys = {
        tagGroupName: '',
        createUser: ''
      };
      this.pagination = {
        page: 1,
        total: 0,
        rows: 10
      };
      this.$emit('on-close');
    },
    // 提交关闭
    confirmBtn() {
      const obs = this.tableData.find(item => item.shos === 1);
      if (!obs) {
        this.$message({ message: '请选择用户分群', type: 'error' });
        return;
      }
      console.log('提交关闭-4', obs);
      this.$emit('on-close', obs);
    }
  }
};
</script>

<style lang="scss">
.aUsers {
  margin: 60px;
  .el-dialog.is-fullscreen {
    overflow: hidden;
  }
  .el-dialog__body,
  .yz-common-dialog__container {
    height: 100%;
  }
  .el-radio__label {
    display: none;
  }
  .yz-common-dialog__content {
    height: calc(100% - 104px) !important;
    overflow: auto;
  }
}
</style>
