//获取字符串以最后点号为分割的数组,即文件名与后缀名分割的数组
export const getNameDitPart = (flieName) => {
  const ditIndex = flieName.lastIndexOf(".");
  const partArray = [flieName.slice(0, ditIndex), flieName.slice(ditIndex + 1)];
  return partArray;
};

export const getRandomString = (len = 32) => {
  var chars = new String("ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678");
  var maxPos = chars.length;
  var pwd = "";
  for (let i = 0; i < len; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos));
  }
  // console.log(pwd)
  return pwd;
};

//视频后缀名数组
const videoDataLower = [
  "avi",
  "wmv",
  "mpeg",
  "mp4",
  "m4v",
  "mov",
  "asf",
  "flv",
  "f4v",
  "rmvb",
  "rm",
  "3gp",
  "vob",
];
const videoDataUpper = videoDataLower.map((item) => item.toUpperCase());
export const videoData = [...videoDataLower, ...videoDataUpper];
export const videoAcceptData = videoData.map((item) => `.${item}`).join(",");

//图片后缀名数组
const imageDataLower = [
  "bmp",
  "jpg",
  "jpeg",
  "png",
  "tiff",
  "gif",
  "pcx",
  "tga",
  "exif",
  "fpx",
  "svg",
  "psd",
  "cdr",
  "pcd",
  "dxf",
  "ufo",
  "eps",
  "ai",
  "raw",
  "wmf",
  "webp",
  "avif",
  "apng",
];
const imageDataUpper = imageDataLower.map((item) => item.toUpperCase());
export const imageData = [...imageDataLower, ...imageDataUpper];
export const imageAcceptData = imageData.map((item) => `.${item}`).join(",");

//文件类型（包含word文档以及pdf）
const fileDataLower = ["doc", "docx", "pdf"];
const fileDataUpper = fileDataLower.map((item) => item.toUpperCase());
export const fileData = [...fileDataLower, ...fileDataUpper];
export const fileAcceptData = fileData.map((item) => `.${item}`).join(",");
