<template>
  <div>
    <div class="back">
      <span style="font-size: 12px">通关礼品</span>
      <i class="el-icon-close" @click="$router.go(-1)" />
    </div>
    <div class="yz-base-container">
      <div style="text-align: right; margin-bottom: 10px">
        <!-- 提醒 赠送 剔除 -->
        <el-button
          type="primary"
          size="small"
          @click="handleDialog()"
        >新增通关礼品</el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        size="small"
      >
        <el-table-column
          type="index"
          width="100"
          align="center"
          label="礼品序号"
        />
        <el-table-column prop="giftType" align="center" label="礼品类型">
          <template slot-scope="scope">
            {{ scope.row.giftType | trainCampGiftTypeMethod }}
          </template>
        </el-table-column>
        <el-table-column prop="giftName" align="center" label="礼品名称">
          <template slot-scope="scope">
            <span
              v-if="scope.row.giftType === 1"
            >{{ scope.row.giftName | trainCampArticleGiftNameMethod }}
            </span>
            <span v-else-if="scope.row.giftType === 2">
              {{ scope.row.giftName | trainCampVirtualGiftNameMethod }}
            </span>
            <span v-else>
              {{ scope.row.giftName | trainCampBallGiftNameMethod }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="giftJumpRoute" align="center" label="礼品链接" />
        <el-table-column
          prop="completedPersonNum"
          align="center"
          label="通关人数"
        />
        <el-table-column
          prop="receivePersonNum"
          align="center"
          label="领取人数"
        />
        <el-table-column align="center" label="是否启用">
          <template slot-scope="{ row }">
            <el-tag
              v-if="row.isAllow == 0"
              type="danger"
              effect="plain"
              size="small"
            >
              禁用
            </el-tag>
            <el-tag v-else type="success" effect="plain" size="small">
              启用
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="100" label="操作" align="center">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-popconfirm
                cancel-button-type="button"
                :title="scope.row.isAllow == 0 ? '是否启用?' : '是否禁用?'"
                @confirm="handleClick(scope.row)"
              >
                <el-button
                  slot="reference"
                  type="text"
                  size="small"
                  style="margin-right: 10px"
                >{{ scope.row.isAllow == 0 ? "启用" : "禁用" }}
                </el-button>
              </el-popconfirm>
              <el-button
                type="text"
                size="small"
                @click="handleDialog(scope.row.id)"
              >编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getGiftList"
        />
      </div>
      <addGift
        :visible.sync="addGiftShow"
        :editId="editId"
        @refresh="getGiftList"
      />
    </div>
  </div>
</template>

<script>
import addGift from './add-gift';
export default {
  components: { addGift },
  data() {
    return {
      loading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      addGiftShow: false,
      editId: ''
    };
  },
  created() {
    this.getGiftList();
  },
  methods: {
    handleClick(row) {
      const data = {
        giftId: row.id,
        isAllow: row.isAllow === 1 ? 0 : 1
      };

      this.$post('editStatus', data).then((res) => {
        if (res.ok) {
          this.$message.success(row.isAllow === 1 ? '禁用成功' : '启用成功');
          this.getGiftList();
        }
      });
    },
    // 新增 | 编辑 弹窗
    handleDialog(id = '') {
      this.editId = String(id);
      this.addGiftShow = true;
    },
    getGiftList() {
      this.loading = true;
      const data = {
        trainCampId: String(this.$route.query.id),
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('giftList', data)
        .then((res) => {
          if (res.ok) {
            this.tableData = res.body.data;
            this.pagination.total = res.body.recordsTotal;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style>
.back {
  margin: 7px 15px 0 10px;
  color: #808c95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
}
</style>
