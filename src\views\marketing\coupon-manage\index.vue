<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item label='优惠券名称' prop='couponName'>
        <el-input v-model.trim="form.couponName" placeholder="请输入优惠券名称" maxlength="100" />
      </el-form-item>
      <el-form-item label='优惠券ID' prop='couponId'>
        <el-input v-model.trim="form.couponId" placeholder="请输入优惠券ID" />
      </el-form-item>
      <el-form-item label='优惠券类型' prop='couponType'>
        <el-select v-model="form.couponType" filterable placeholder="请选择">
          <el-option label="满减券" value="1" />
          <el-option label="抵扣券" value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="状态" prop="state">
        <el-select v-model="form.state" clearable placeholder="请选择状态">
          <el-option label="禁用" value="0" />
          <el-option label="启用" value="1" />
        </el-select>
      </el-form-item>

      <el-form-item label="领取时间" prop="limitTime">
        <el-date-picker
          v-model="form.limitTime"
          type="datetimerange"
          value-format="timestamp"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <!-- <el-form-item label='分发范围' prop='Range'>
        <el-select v-model="form.Range" filterable placeholder="请选择">
          <el-option label="私密券" value="1" />
          <el-option label="公开券" value="2" />
        </el-select>
      </el-form-item> -->

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain @click="dataStatis()">数据统计</el-button>
      <el-button type="success" size="small" plain @click="couponManage">派券管理</el-button>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="addCoupon">创建优惠券</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column prop="couponId" label="优惠券ID" width="150" align="center" />
      <el-table-column prop="couponName" label="优惠券名称" align="center" />
      <el-table-column prop="couponType" label="优惠券类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.couponType | couponType }}
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="优惠券面额" align="center">
        <template slot-scope="scope">
          {{ Number(scope.row.amount)/100 }}
        </template>
      </el-table-column>
      <el-table-column prop="scopeUse" width="105" label="使用范围" align="center">
        <template slot-scope="scope">
          {{ scope.row.scopeUse | useRange }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="state" label="分发范围" align="center">
        <template slot-scope="scope">
          {{ scope.row.state | handRange }}
        </template>
      </el-table-column> -->
      <el-table-column prop="limitStartTime" width="138" label="可领取时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.limitStartTime | transformTimeStamp }} {{ scope.row.limitEndTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="startTime" label="优惠券有效时间" width="138" align="center">
        <template slot-scope="scope">
          {{ scope.row | changEffectTime }}
        </template>
      </el-table-column>
      <el-table-column prop="couponLimitCount" width="170" label="发行张数" align="center">
        <template slot-scope="scope">
          <div>
            <el-button
              size="mini"
              style="float:left; padding:7px 10px;"
              type="danger"
              plain
              @click="open(2,'库存减少',scope.row)"
            >-</el-button>
            <span style="margin:0 10px">{{ scope.row.couponLimitCount || 0 }}</span>
            <el-button
              size="mini"
              type="success"
              style="float:right; padding:7px 10px;"
              plain
              @click="open(1,'库存增加',scope.row)"
            >+</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="receivedNum" label="已领取" align="center">
        <template slot-scope="scope">
          <el-link v-if="scope.row.receivedNum !== 0" type="success" @click.stop="receiveDetails(scope.row)">{{ scope.row.receivedNum }} </el-link>
          <el-link v-if="scope.row.receivedNum === 0" type="success">{{ scope.row.receivedNum }} </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="usedNum" label="已使用" align="center" />
      <el-table-column prop="state" label="是否启用" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.state === 1" type="success">已启用</el-tag>
          <el-tag v-else type="danger">已禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="swt" label="提醒管理" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openRemindDialog(scope.row)">管理</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="updateUserName" label="数据统计" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="dataStatis(scope.row)">数据统计</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center" width="180px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="editCoupon(scope.row)">编辑</el-button>
            <el-button type="text" @click="copy(scope.row)">复制链接</el-button>
            <el-button type="text" @click="editStatus(scope.row)">{{ scope.row.state === 1?'禁用':"启用" }}</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 发放张数 弹框 -->
    <common-dialog
      :show-footer="true"
      width="300px"
      :title="stockTitle"
      :visible.sync='stockShow'
      @confirm="submitStock"
      @close='closeStock'
    >
      <div class="dialog-main">
        <el-form
          ref="stockForm"
          class="form"
          size='mini'
          :model="stockForm"
          :rules="stockRules"
        >
          <el-form-item prop='num'>
            <input-number :value.sync="stockForm.num" />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>

    <!-- 新增/编辑 弹窗 -->
    <update-dialog
      :id='currentEditId'
      :title="updateDialogTitle"
      :visible.sync="udVisible"
      @refresh-list="getTableList"
    />

    <!-- 领取详情 弹窗 -->
    <receive-dialog
      :id='currentReId'
      :type="reType"
      :visible.sync="reVisible"
      @refresh-list="getTableList"
    />

    <!-- 派券管理 弹窗 -->
    <coupon-manage-dialog
      :id='couponManId'
      :title="cmTitle"
      :visible.sync="cmVisible"
      @refresh-list="getTableList"
    />

    <!-- 数据统计 弹窗 -->
    <data-statistics
      :id='currentDsId'
      :title="dsTitle"
      :visible.sync="dsVisible"
      @refresh-list="getTableList"
    />

    <remind-dialog :visible.sync="remindShow" :row="currentRow" />

    <!-- copy 专用 -->
    <div ref="copyElem" class="hidden">{{ copyText }}</div>
  </div>
</template>

<script>
import { validate } from '@/utils/validate';
import { handleDateControl, formatTimeStamp } from '@/utils';
import updateDialog from './update-dialog';
import receiveDialog from './receive-dialog';
import dataStatistics from './data-statistics';
import couponManageDialog from './send-coupon/coupon-manage-dialog';
import { zmcUrl } from '@/config/request';
import { CopyElemText } from '@/common';
import remindDialog from './remind-dialog';
export default {
  components: {
    updateDialog,
    receiveDialog,
    dataStatistics,
    couponManageDialog,
    remindDialog
  },
  filters: {
    changEffectTime(row) {
      if (row.effectiveType === 1) {
        return formatTimeStamp(row.startTime, 'YYYY-MM-DD HH:mm:ss') + formatTimeStamp(row.endTime, 'YYYY-MM-DD HH:mm:ss');
      } else if (row.effectiveType === 2) {
        let dynamicTime = row.dynamicTime;
        const day = parseInt(dynamicTime / (60 * 60 * 24));
        dynamicTime -= day * 60 * 60 * 24;
        const hours = parseInt(dynamicTime / (60 * 60));
        dynamicTime -= hours * 60 * 60;
        const minutes = parseInt(dynamicTime / 60);
        return `领券后${day}天${hours}时${minutes}分有效`;
      }
    },
    useRange(val) {
      if (!val) return '';
      const obj = {
        '1': '全部课程可用',
        '2': '指定课程可用',
        '3': '指定课程不可用'
      };
      return obj[val];
    },
    handRange(val) {
      if (!val) return '';
      const obj = {
        '1': '私密券',
        '2': '公开券'
      };
      return obj[val];
    },
    couponType(val) {
      if (!val) return '';
      const obj = {
        '1': '满减券',
        '2': '抵扣券'
      };
      return obj[val];
    }
  },

  data() {
    const checkNum = (rule, value, callback) => {
      if (value === '') {
        callback('请输入');
      } else if (value > 1000000000) {
        callback('数值过大');
      } else if (!validate('positiveInteger', value)) {
        callback('请输入正整数');
      } else {
        callback();
      }
    };
    return {
      // 弹窗
      udVisible: false,
      reVisible: false, // 领取详情
      cmVisible: false, // 派券管理
      dsVisible: false, // 数据统计
      stockShow: false,
      remindShow: false,
      currentEditId: null,
      currentReId: null,
      currentDsId: null,
      couponManId: null,
      updateDialogTitle: '新增',
      reType: '1',
      cmTitle: '派券管理',
      dsTitle: '',
      stockTitle: '',
      stockForm: {
        num: undefined
      },
      stockRules: {
        num: [{ required: true, validator: checkNum, trigger: 'blur' }]
      },
      form: {
        couponName: '',
        couponType: '',
        state: '',
        limitTime: '',
        limitStartTime: '',
        limitEndTime: '',
        couponId: undefined
        // Range: '' // 分发范围

      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tableLoading: false,
      tableData: [],
      copyText: '',
      currentRow: null
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    open(type, title, row) {
      this.stockShow = true;
      this.stockTitle = title;
      this.stockForm.num = undefined;
      this.currentItem = { type, row };
    },
    openRemindDialog(row) {
      this.currentRow = row;
      this.remindShow = true;
    },
    // 发放张数提交
    submitStock() {
      this.$refs['stockForm'].validate((valid) => {
        if (valid) {
          const data = {
            couponId: this.currentItem.row.couponId,
            num: this.stockForm.num + '',
            type: this.currentItem.type + ''
          };
          this.$post('updateCouponStock', data, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
              this.stockShow = false;
            }
          });
        }
      });
    },
    closeStock() {
      this.stockShow = false;
    },
    getTableList() {
      this.tableLoading = true;
      const formData = JSON.parse(JSON.stringify(this.form));
      formData.limitTime = handleDateControl(formData.limitTime);
      formData.limitStartTime = formData.limitTime[0];
      formData.limitEndTime = formData.limitTime[1];
      formData.couponId = formData.couponId === undefined ? '' : formData.couponId;

      delete formData.limitTime;

      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('getCouponList', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 创建优惠券
    addCoupon() {
      this.currentEditId = null;
      this.updateDialogTitle = '新增';
      this.udVisible = true;
    },
    // 编辑优惠券
    editCoupon(row) {
      this.currentEditId = row.couponId;
      this.updateDialogTitle = '编辑';
      this.udVisible = true;
    },
    // 启用禁用
    editStatus(row) {
      const data = {
        state: row.state === 1 ? 0 : 1,
        couponId: row.couponId
      };
      this.$post('updateCouponState', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.getTableList();
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      });
    },
    // 领取明细
    receiveDetails(row) {
      console.log('執行了');
      this.currentReId = row.couponId;
      this.reVisible = true;
    },
    // 派券管理
    couponManage() {
      this.couponManId = '1';
      this.cmVisible = true;
    },
    // 复制链接
    copy(row) {
      this.copyText = zmcUrl + 'aspirantUniversity/voucher/share?couponId=' + row.couponId + '&receiveChannel=1&regOrigin=95&regChannel=3';

      // 等待元素更新
      this.$nextTick(() => {
        CopyElemText(this.$refs.copyElem);
        this.$message({
          message: '复制成功',
          type: 'success'
        });
      });
    },
    // 数据统计
    dataStatis(row) {
      if (row) {
        this.currentDsId = row.couponId;
        this.dsTitle = row.couponName;
      } else {
        this.currentDsId = '';
        this.dsTitle = '';
      }
      this.dsVisible = true;
    }
  }
};
</script>

<style lang = "scss" scoped>
.yz-table-btnbox {
  margin-top: 20px;
}

.hidden {
  position: absolute;
  top:0;
  opacity: 0 !important;
}
</style>

