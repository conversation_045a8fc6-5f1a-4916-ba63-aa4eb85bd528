<template>
  <common-dialog
    class="common-dialog"
    width="80%"
    title="选择用户"
    :visible.sync="show"
    :show-footer="true"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-wrap">
      <!-- 左边 -->
      <div class="content-wrap">
        <p>待选列表</p>
        <el-form
          ref="searchForm"
          :inline="true"
          :model="form"
          size="mini"
          label-width="75px"
          label-suffix=":"
          class="wrap-search-form mt10"
          @submit.native.prevent='search'
        >
          <el-form-item label="用户姓名" prop="realName">
            <el-input v-model="form.realName" placeholder="请输入用户姓名" clearable />
          </el-form-item>
          <el-form-item label="远智编号" prop="yzCode">
            <el-input v-model="form.yzCode" placeholder="请输入远智编号" clearable />
          </el-form-item>
          <div class="search-reset-box">
            <el-button
              type="primary"
              native-type="submit"
              size="mini"
            >搜索</el-button>
            <el-button size="mini" @click="search(0)">重置</el-button>
          </div>
        </el-form>
        <el-table ref="tableRef" v-loading="tableLoading" size="mini" row-key="userId" :data="tableData" border style="width: 100%" @select-all="selectLeftAll" @select="selectLeft">
          <el-table-column type="selection" width="39" />
          <el-table-column prop="realName" label="用户姓名" align="center" />
          <el-table-column prop="yzCode" label="远智编号" align="center" />
        </el-table>
        <!-- 分页区 -->
        <div class="yz-table-pagination">
          <pagination
            size="mini"
            :total="pagination.total"
            :page.sync="pagination.page"
            :limit.sync="pagination.limit"
            @pagination="getTableList"
          />
        </div>
      </div>
      <!-- 右边 -->
      <div class="content-wrap">
        <p>已选列表</p>
        <div class="mt10">
          <el-button type="danger" size="small" @click="batchDelete">批量删除</el-button>
          <span class="ml10">已选择{{ tableRightData.length }}个用户</span>
        </div>
        <el-table ref="tableSelectedRef" max-height="650" size="mini" :data="tableRightData" border style="width: 100%;margin-top: 60px;">
          <el-table-column prop="realName" label="用户姓名" align="center" />
          <el-table-column prop="yzCode" label="远智编号" align="center" />
          <el-table-column label="操作" align="center" width="150">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="handleDelete(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tablePropData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      form: {
        realName: '',
        yzCode: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableData: [], // 左边表格数据
      tableRightData: [] // 右边表格已选择的数据
    };
  },
  watch: {
    visible(val) {
      this.show = val;
      this.tableRightData = JSON.parse(JSON.stringify(this.tablePropData));
    }
  },
  methods: {
    // 左边表格选择全部和全不选
    selectLeftAll(rows) {
      if (rows.length) {
        // 全选
        rows.forEach(row => {
          const findFlag = this.tableRightData.find(item => item.userId === row.userId);
          if (!findFlag) {
            this.tableRightData.push(row);
          }
        });
      } else {
        // 全不选
        this.tableRightData = this.tableRightData.filter(item => {
          const findFlag = this.tableData.find(row => row.userId === item.userId);
          return !findFlag;
        });
      }
    },
    // 表左边表格选择单个
    selectLeft(rows, row) {
      const selected = rows.length && rows.indexOf(row) !== -1; // true就是选中，0或者false是取消选中
      if (selected) {
        this.tableRightData.push(row);
      } else {
        const index = this.tableRightData.findIndex(item => item.userId === row.userId);
        if (index >= 0) {
          this.tableRightData.splice(index, 1);
        }
      }
    },
    // 单个删除
    handleDelete(index) {
      const row = this.tableRightData[index];
      this.$refs.tableRef.toggleRowSelection(row, false);
      this.tableRightData.splice(index, 1);
    },
    // 批量删除
    batchDelete() {
      this.$confirm('是否批量删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableRightData.forEach(item => {
          this.$refs.tableRef.toggleRowSelection(item);
        });
        this.tableRightData = [];
      }).catch(() => {});
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('getGiveUseInfo', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          const ids = this.tableRightData.map(item => item.userId);
          this.tableData.forEach(item => {
            if (ids.includes(item.userId)) {
              this.$nextTick(() => {
                this.$refs.tableRef.toggleRowSelection(item);
              });
            }
          });
        }
      });
    },
    // 打开弹框
    open() {
      this.getTableList();
    },
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.show = false;
      this.$emit('confirm', this.tableRightData);
    }
  }
};
</script>

<style lang = "scss" scoped>

::v-deep .el-table tr {
  height: 40px;
}
.dialog-wrap {
  padding: 10px 0;
  display: flex;
  .content-wrap {
    flex: 1;
    padding: 0 20px;
  }
}
.wrap-search-form {
  position: relative;
  padding-right: 120px;
  .search-reset-box {
    position: absolute;
    top: 0;
    right: 0;
  }
}
</style>
