<template>
  <common-dialog
    :visible.sync="visible"
    title="新建工单"
    width="600px"
    :show-footer="true"
    @confirm="submitForm"
    :confirm-loading="loading"
    @close="handleClose"
  >
    <div class="create-workorder">
      <el-form
        ref="workorderForm"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <!-- 工单标题 -->
        <el-form-item label="工单标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入工单标题"
            :maxlength="30"
            show-word-limit
          />
        </el-form-item>

        <!-- 工单类型 -->
        <el-form-item label="工单类型" prop="orderType2">
          <div class="flex">
            <common-select
              class="mr-10"
              v-model="form.workOrderType"
              type="orderType"
              disabled
            />
            <common-select
              v-if="orderType() !== '2'"
              v-model="form.orderType2"
              type="consultWorkOrderType"
              :extraParams="{ orderType: form.orderType }"
            />
          </div>
        </el-form-item>

        <!-- 关联学员 -->
        <el-form-item label="关联学员" prop="stuRecordName">
          <div class="add-item-container">
            <el-button
              v-if="!form.stuRecordName"
              type="text"
              icon="el-icon-plus"
              @click="openStudentDialog"
              >添加</el-button
            >
            <div v-if="form.stuRecordName" class="selected-students">
              <el-tag closable @close="removeStudent()">
                {{ form.stuRecordName }}
              </el-tag>
            </div>
          </div>
        </el-form-item>

        <!-- 咨询分类 / 投诉来源 -->
        <el-form-item
          :label="form.workOrderType === '2' ? '投诉来源' : '咨询分类'"
          prop="category1DictId"
        >
          <double-select
            :value1.sync="form.category1DictId"
            :value2.sync="form.category2DictId"
            :extraParams1="{
              parentId: form.workOrderType,
            }"
            type1="workOrderDict"
            type2="workOrderDict"
            :placeholder1="
              form.workOrderType === '2' ? '请选择一级来源' : '请选择一级分类'
            "
            :placeholder2="
              form.workOrderType === '2' ? '请选择二级来源' : '请选择二级分类'
            "
          />
        </el-form-item>

        <!-- 投诉原因 (仅投诉类工单显示) -->
        <el-form-item
          v-if="form.workOrderType === '2'"
          label="投诉原因"
          prop="reasonDictId"
        >
          <common-select
            v-model="form.reasonDictId"
            type="workOrderDict"
            :extraParams="{ parentId: 3 }"
            placeholder="请选择投诉原因"
          />
        </el-form-item>

        <!-- 工单/投诉内容 -->
        <el-form-item
          :label="form.workOrderType === '2' ? '投诉内容' : '工单内容'"
          prop="content"
        >
          <div class="content-wrapper">
            <el-input
              v-model="form.content"
              type="textarea"
              :rows="6"
              placeholder="请输入工单内容"
              :maxlength="500"
              show-word-limit
            />
            <upload-attachment v-model="form.filesList" />
          </div>
        </el-form-item>

        <!-- 工单优先级 -->
        <el-form-item label="工单优先级" prop="priority">
          <el-radio-group v-model="form.priority">
            <el-radio label="1">一般</el-radio>
            <el-radio label="2">紧急</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 受理人 -->
        <el-form-item label="受理人" prop="receiveEmpId">
          <div class="add-item-container">
            <el-button
              v-if="!form.receiveEmpName"
              type="text"
              icon="el-icon-plus"
              @click="openHandlerDialog"
              >添加</el-button
            >
            <div v-if="form.receiveEmpName" class="selected-handlers">
              <el-tag closable @close="removeHandler()">
                {{ form.receiveEmpName }}
              </el-tag>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <!-- 关联学员弹窗 -->
    <student-selector
      :visible.sync="studentDialogVisible"
      @confirm="handleStudentSelected"
    />

    <!-- 受理人弹窗 -->
    <handler-selector
      :visible.sync="handlerDialogVisible"
      :receiveEmpId.sync="form.receiveEmpId"
      :receiveEmpName.sync="form.receiveEmpName"
    />
  </common-dialog>
</template>

<script>
import { ossUri } from "@/config/request";

export default {
  name: "CreateWorkorder",
  components: {
    CommonDialog: () => import("@/components/common-dialog"),
    DoubleSelect: () => import("@/components/formTools/DoubleSelect"),
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
    StudentSelector: () => import("./student-selector"),
    HandlerSelector: () => import("./handler-selector"),
    UploadAttachment: () => import("../upload-attachment"),
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  inject: ["orderType"],
  data() {
    return {
      loading: false,
      form: {
        title: "", // 工单标题
        workOrderType: "1", // 新增工单类型：1咨询，2投诉
        priority: "1", // 1(一般)2(紧急)
        category1DictId: "", // 一级分类字典id
        category2DictId: "", // 二级分类字典id
        content: "", // 工单内容
        receiveEmpId: "", // 受理人ID
        receiveEmpName: "", // 受理人姓名
        filesList: [], // 附件文件列表
        stuXyCode: "", // 学业编码
        stuYzCode: "", // 远智编码
        stuName: "", // 学员名称
        stuPhone: "", // 手机号码
        stuRecordName: "", // 单纯用来学员记录名称

        // 以下是非接口参数，用于表单操作
        orderType2: "", // 工单类型详情
        reasonDictId: "", // 投诉原因
        attachments: [], // 附件，使用数组格式 [{name:'',url:''}]
        attachmentFileList: [], // 附件文件列表
      },
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        orderType: [
          { required: true, message: "请选择工单类型", trigger: "change" },
        ],
        orderType2: [
          {
            required: true,
            message: "请选择工单子类型",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.workOrderType === "2") {
                callback(); // 投诉类工单不需要子类型
              } else if (!value) {
                callback(new Error("请选择工单子类型"));
              } else {
                callback();
              }
            },
          },
        ],
        stuRecordName: [
          {
            required: true,
            message: "请选择关联学员",
            trigger: "change",
          },
        ],
        category1DictId: [
          { required: true, message: "请选择分类", trigger: "change" },
        ],
        category2DictId: [
          { required: true, message: "请选择子分类", trigger: "change" },
        ],
        reasonDictId: [
          {
            required: true,
            message: "请选择投诉原因",
            trigger: "change",
            validator: (rule, value, callback) => {
              if (this.form.workOrderType === "2" && !value) {
                callback(new Error("请选择投诉原因"));
              } else {
                callback();
              }
            },
          },
        ],
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        priority: [
          { required: true, message: "请选择工单优先级", trigger: "change" },
        ],
        receiveEmpId: [
          { required: true, message: "请选择受理人", trigger: "change" },
        ],
      },

      // 关联学员弹窗
      studentDialogVisible: false, // 关联学员弹窗可见性
      handlerDialogVisible: false, // 受理人弹窗可见性
    };
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.form.workOrderType = this.orderType();
        }
      },
      immediate: true,
    },
  },
  mounted() {},

  methods: {
    // 关闭弹窗
    handleClose() {
      this.$refs.workorderForm?.resetFields();
      this.form = this.$options.data().form;
      this.$emit("update:visible", false);
    },

    // 提交表单
    submitForm() {
      this.$refs.workorderForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 构建提交数据
          const formData = {
            title: this.form.title,
            workOrderType: this.form.workOrderType, // 1咨询，2投诉
            orderType: this.form.orderType2, //这里是工单子类型，只有咨询类有
            priority: Number(this.form.priority), // 1一般，2紧急
            category1DictId: this.form.category1DictId
              ? Number(this.form.category1DictId)
              : "",
            category2DictId: this.form.category2DictId
              ? Number(this.form.category2DictId)
              : "",
            content: this.form.content,
            reasonDictId: this.form.reasonDictId,
            receiveEmpId: this.form.receiveEmpId || "",
            filesList: this.form.filesList || [],
            stuXyCode: this.form.stuXyCode || "",
            stuYzCode: this.form.stuYzCode || "",
            stuName: this.form.stuName || "",
            stuPhone: this.form.stuPhone || "",
          };

          // 调用API提交工单
          this.$post("addWorkOrder", formData, { json: true })
            .then((res) => {
              const { ok, body } = res;
              if (ok) {
                this.$message.success("工单创建成功~");
                this.handleClose();
                this.$emit("success");
              }
            })
            .catch((err) => {
              console.error("创建工单失败", err);
              this.$message.error("创建工单失败，请重试");
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          return false;
        }
      });
    },

    // 从URL获取文件名
    getFileNameFromUrl(url) {
      if (!url) return "";
      return url.substring(url.lastIndexOf("/") + 1);
    },

    // 打开关联学员弹窗
    openStudentDialog() {
      this.studentDialogVisible = true;
    },

    // 移除关联学员
    removeStudent() {
      // 清空表单中的学员相关信息
      this.form.stuXyCode = "";
      this.form.stuYzCode = "";
      this.form.stuName = "";
      this.form.stuPhone = "";
      this.form.stuRecordName = "";
      this.$forceUpdate();
    },

    // 处理学员选择事件
    handleStudentSelected(studentObj) {
      if (!studentObj) return;

      // 先清空原有学员数据
      this.form.stuXyCode = "";
      this.form.stuYzCode = "";
      this.form.stuName = "";
      this.form.stuPhone = "";

      // 设置新的学员数据
      Object.assign(this.form, studentObj);
    },

    // 打开受理人弹窗
    openHandlerDialog() {
      this.handlerDialogVisible = true;
    },

    // 移除受理人
    removeHandler() {
      this.form.receiveEmpId = "";
      this.form.receiveEmpName = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.create-workorder {
  padding: 20px;

  .add-item-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .el-button {
      color: #409eff;
    }

    .selected-students {
      display: flex;
      flex-wrap: wrap;
      margin-top: 5px;
      width: 100%;

      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }

    .selected-handlers {
      display: flex;
      flex-wrap: wrap;
      margin-top: 5px;
      width: 100%;

      .el-tag {
        margin-right: 5px;
        margin-bottom: 5px;
      }
    }
  }

  .content-wrapper {
    .el-textarea {
      margin-bottom: 0;
    }
  }
}
</style>
