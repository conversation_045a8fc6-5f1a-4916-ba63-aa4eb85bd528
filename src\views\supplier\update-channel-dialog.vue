<template>
  <common-dialog
    show-footer
    :title="title"
    width="600px"
    :visible.sync='show'
    @open="init"
    @confirm='submit'
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='渠道名称' prop='channelName'>
          <el-input
            v-model="form.channelName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='分成比例' prop='proportion'>
          <input-number v-model="form.proportion">
            <span slot="suffix">%</span>
          </input-number>
        </el-form-item>

        <el-form-item label='关联角色' prop='roleCode'>
          <remote-search-selects
            v-model="form.roleCode"
            :props="{apiName: 'getRolesList', value: 'roleCode', label: 'roleName', query: 'sName'}"
            :param="{sName:''}"
            :default-option="defaultOption"
            @changeVal="handleSelectChange"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='allow'>
          <el-radio-group v-model="form.allow">
            <el-radio label="1">启用</el-radio>
            <el-radio label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    // 供应商id
    id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      defaultOption: null,
      form: {
        channelName: '',
        proportion: undefined,
        roleCode: '',
        roleName: '',
        allow: ''
      },
      roleOptions: [],
      roleOptionsTotal: 0,
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      rules: {
        channelName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        proportion: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        roleCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        allow: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleSelectChange({ value, label }) {
      this.form.roleName = label;
    },
    init() {
      if (this.id) {
        const data = {
          channelId: this.id
        };
        this.$post('getSupplierInfo', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.channelName = body.channelName;
            this.form.proportion = body.proportion;
            this.form.roleCode = body.roleCode;
            this.form.allow = body.allow;
            this.defaultOption = {
              roleCode: body.roleCode,
              roleName: body.roleName
            };
          }
        });
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKeyName = 'addSupplier';
          const formData = JSON.parse(JSON.stringify(this.form));
          // 编辑
          if (this.id) {
            apiKeyName = 'editSupplier';
            formData.channelId = this.id;
          }
          const data = {
            ...formData
          };
          this.$post(apiKeyName, data).then(res => {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.$emit('refresh');
            this.show = false;
          });
        } else {
          return false;
        }
      });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$refs['form'].resetFields();
      this.roleOptions = [];
      this.pagination.page = 1;
    }
  }
};
</script>
