<template>
  <common-dialog show-footer title="直播频道编辑" width="500px" :visible.sync='show' @open="init" @confirm="submit" @close='close'>
    <div v-if="show" class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >

        <el-form-item label='频道名称' prop='channelName'>
          <el-input v-model="form.channelName" disabled />
        </el-form-item>

        <el-form-item label='频道号' prop='channelId'>
          <el-input v-model="form.channelId" />
        </el-form-item>

        <el-form-item label='频道密码' prop='channelPassword'>
          <el-input v-model="form.channelPassword" />
        </el-form-item>

        <el-form-item label='直播分类' prop='categoryId'>
          <el-select
            v-model="form.categoryId"
            filterable
            clearable
            placeholder="请选择"
            @change="getSelectedName"
          >
            <el-option
              v-for="(item,index) in liveType"
              :key="index"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='关联课程' prop='courseId'>
          <infinite-selects
            v-model="form.courseId"
            disabled
            api-key="courseSelect"
            key-name="courseName"
            value-name='courseId'
            :default-option="courseDefaultOption"
            :param="{courseName:''}"
          />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    lcId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      show: false,
      courseDefaultOption: null,
      form: {
        channelName: '',
        channelId: '',
        channelPassword: '',
        courseId: '',
        categoryId: '',
        categoryName: ''
      },
      rules: {
        channelId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        channelPassword: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        courseId: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      liveType: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getSelectedName(value) {
      const obj = this.liveType.find(item => {
        return item.categoryId === value;
      });

      if (Object.prototype.toString.call(obj) === '[object Object]') {
        this.form.categoryName = obj.categoryName;
      } else {
        this.form.categoryName = '';
      }
    },
    init() {
      this.getLiveType();
      if (this.lcId) {
        const data = {
          lcId: this.lcId,
          courseSystem: 7
        };
        this.$post('getChannelInfo', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.courseDefaultOption = {
              courseId: body.courseId,
              courseName: body.courseName
            };
            this.form.channelName = body.channelName;
            this.form.channelId = body.channelId;
            this.form.channelPassword = body.channelPassword;
            this.form.courseId = body.courseId;
            this.form.categoryId = Number(body.categoryId);
          }
        });
      }
    },
    // 获取直播分类下拉
    getLiveType() {
      this.$post('getLiveChannelCategory')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.liveType = body;
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = {
            ...this.form,
            lcId: this.lcId
          };
          this.$post('editChannel', data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$parent.getTableList();
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      this.courseOption = [];
      this.$refs['form'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
