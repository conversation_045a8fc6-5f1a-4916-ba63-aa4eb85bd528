<template>
  <!-- 无限加载的下拉选择器组件 -->
  <el-select
    v-model="val"
    v-loadmore="loadmore"
    v-bind="$attrs"
    v-on="$listeners"
    :filterable="filterable"
    :remote="filterable"
    :remote-method="remoteMethod"
    :loading="loading"
    :multiple="multiple"
    placeholder="请选择"
    clearable
    @change="_change"
    @focus="handleFocus"
    @click.native="handleClick"
  >
    <el-option
      v-for="item in afterFilterOptions"
      :key="item[valueName] + generateKey()"
      :label="item[keyName]"
      :value="item[valueName]"
    />
  </el-select>
</template>
<script>
import { uuid } from "@/utils";
/**
 * 无限滚动加载的下拉选择器组件
 * 支持远程搜索、分页加载、自定义键值等功能
 */
export default {
  name: "InfiniteSelects",
  props: {
    /**
     * 组件绑定的值
     */
    value: {
      type: [String, Number, Boolean, Array],
      default: "",
    },
    /**
     * API接口的键名，用于数据请求
     */
    apiKey: {
      type: String,
      default: "",
    },
    /**
     * 选项显示的字段名
     */
    keyName: {
      type: String,
      default: "name",
    },
    /**
     * 选项值的字段名
     */
    valueName: {
      type: String,
      default: "id",
    },
    /**
     * 每次加载的数据条数
     */
    limit: {
      type: Number,
      default: 50,
    },
    /**
     * 请求参数对象
     */
    param: {
      type: Object,
      default: null,
    },
    /**
     * 默认选项
     */
    defaultOption: {
      type: Object,
      default: null,
    },
    /**
     * 筛选字段名，默认为name
     */
    filterField: {
      type: String,
      default: "name",
    },
    /**
     * 是否开启远程搜索筛选功能
     */
    filterable: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否开启多选模式
     */
    multiple: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否开启请求json格式
     */
    isRequestJson: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      page: 1, // 当前页码
      val: this.initValue(), // 组件内部值，多选时为数组
      options: [], // 选项数据
      loading: false, // 加载状态
      // keyword: "", // 搜索关键字
      isVisible: false, // 控制下拉框是否可见
      hasEmittedOptionsLoaded: false, // 是否已经触发过 options-loaded 事件
    };
  },
  computed: {
    afterFilterOptions() {
      let arr = this.options;
      if (this.defaultOption) {
        arr = this.options.filter((item) => {
          const keyName = this.keyName;
          const valueName = this.valueName;
          if (
            item[keyName] !== this.defaultOption[keyName] &&
            item[valueName] !== this.defaultOption[valueName]
          ) {
            return true;
          }
        });
        arr.push(this.defaultOption);
      }
      return arr;
    },
  },
  watch: {
    value: {
      handler(val) {
        // 多选模式下，确保val始终是数组
        if (this.multiple) {
          this.val = Array.isArray(val) ? val : [];
        } else {
          this.val = val || "";
        }
      },
      immediate: true,
    },
    defaultOption(val) {
      // if (val) {
      //   this.options.push(val);
      // }
    },
    // param(val) {
    //   this.query = val;
    // }
    /**
     * 监听筛选字段变化，重置并重新获取数据
     */
    filterField() {
      this.resetGetOptions();
    },
    /**
     * 监听参数变化，重置并重新获取数据
     */
    param: {
      handler() {
        this.resetGetOptions();
      },
      deep: true,
    },
  },
  mounted() {
    this.getList();
    if (this.defaultOption) {
      this.options.push(this.defaultOption);
    }
  },
  methods: {
    /**
     * 初始化组件内部值
     * @returns {string|array} 初始化的值
     */
    initValue() {
      // 如果是多选模式且value不是数组，则返回空数组
      if (this.multiple) {
        return Array.isArray(this.value) ? this.value : [];
      }
      // 单选模式直接返回value或空字符串
      return this.value || "";
    },

    /**
     * 生成唯一key值
     * @returns {string} 唯一ID
     */
    generateKey() {
      return uuid();
    },

    /**
     * 值变化事件处理
     * @param {any} value - 选中的值
     */
    _change(value) {
      // 多选模式下，如果value是空数组，则发送空数组
      if (this.multiple && Array.isArray(value) && value.length === 0) {
        this.$emit("input", []);
      } else {
        this.$emit("input", value);
      }
      this.$emit("change", value);
    },

    /**
     * 获取列表数据
     * 根据当前页码、关键字等参数请求数据
     */
    async getList(keyword) {
      this.loading = true;
      const data = {
        page: this.page,
        rows: this.limit,
        length: this.limit,
        start: (this.page - 1) * this.limit,
        ...this.param,
      };

      // 添加筛选条件
      if (keyword) {
        data[this.filterField] = keyword;
      }

      const { fail, body } = await this.$post(this.apiKey, data, {
        json: this.isRequestJson,
      });
      if (!fail) {
        console.log(body);
        const list = body.data || body || [];
        if (list && list.length > 0) {
          const ops = this.options.concat(list);
          this.options = [].concat(ops);

          // 首次加载且有数据时触发事件，并且只触发一次
          if (this.page === 1 && !this.hasEmittedOptionsLoaded) {
            this.$emit("options-loaded", list);
            this.hasEmittedOptionsLoaded = true;
          }
        } else {
          this.page -= 1;
        }
      }
      this.loading = false;
    },

    /**
     * 远程搜索方法
     * 当用户输入关键字时触发搜索
     * @param {string} query - 用户输入的关键字
     */
    remoteMethod(query) {
      // 设置为可见状态，确保下拉框显示
      this.isVisible = true;

      // 无论是否有输入，都清空当前列表并重置页码
      this.options = [];
      this.page = 1;

      // 防止请求中途取消导致的问题
      if (this.loading) {
        return;
      }

      // 设置搜索关键字并获取数据
      const keyword = query || "";

      this.$nextTick(() => {
        this.getList(keyword);
      });
    },

    /**
     * 加载更多数据
     * 当滚动到底部时触发
     */
    loadmore() {
      this.page += 1;
      this.getList();
    },

    /**
     * 重置选项并重新获取数据
     */
    resetGetOptions() {
      this.options = [];
      this.page = 1;
      this.getList();
    },

    /**
     * 处理组件获得焦点事件
     */
    handleFocus() {
      this.isVisible = true;
      // 使用nextTick确保DOM已更新

      this.resetGetOptions();
    },

    /**
     * 处理组件失去焦点事件
     */
    // handleBlur() {
    //   console.log(this.value);
    //   //如果没有绑定值，就清空关键词
    //   if (!this.value || this.value?.length === 0) {
    //     this.keyword = "";
    //   }
    // },

    /**
     * 处理组件点击事件
     * 强制显示下拉框
     */
    handleClick() {
      // 防止多次点击引起重复操作
      if (!this.isVisible) {
        this.isVisible = true;
        // 确保下拉框显示
        const selectEl = this.$el.querySelector(".el-input__inner");
        if (selectEl) {
          // 模拟获取焦点
          selectEl.focus();
          // 重新加载数据
          this.resetGetOptions();
        }
      }
    },
  },
};
</script>
