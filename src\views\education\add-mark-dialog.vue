<template>
  <common-dialog
    title="添加备注"
    :visible.sync="show"
    :showFooter="true"
    width="500px"
    @confirm="submit"
    @open="open"
    @close="close"
    :confirmLoading="loading"
  >
    <div class="dialog-main">
      <!-- 备注表单 -->
      <el-form
        ref="searchForm"
        :model="form"
        label-width="0"
        @submit.native.prevent="search"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="form.remark"
            type="textarea"
            :maxlength="200"
            :rows="8"
            :show-word-limit="true"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
      // 控制对话框是否显示
    },
    distributionOrderNo: {
      type: String,
      default: "",
      // 分销订单编号，用于标识当前操作的订单
    },
    remark: {
      type: String,
      default: "",
      // 订单备注内容，用于编辑时显示原有备注
    },
  },
  data() {
    return {
      aeDialogShow: false, // 高级编辑对话框显示状态（未使用）
      form: {
        remark: "", // 备注表单数据
      },
      tableLoading: false, // 表格加载状态（未使用）
      tableData: [], // 表格数据（未使用）
      pagination: {
        total: 0, // 总记录数（未使用）
        page: 1, // 当前页码（未使用）
        limit: 10, // 每页记录数（未使用）
      },
      show: false, // 当前对话框显示状态
      curEditRowData: null, // 当前编辑的行数据（未使用）
      loading: false, // 提交按钮加载状态
    };
  },
  watch: {
    // 监听外部传入的visible属性变化，同步到内部show状态
    visible(val) {
      this.show = val;
    },
    // 监听外部传入的distributionOrderNo属性变化
    distributionOrderNo(val) {
      this.distributionOrderNo = val;
    },
    // 监听外部传入的remark属性变化，同步到表单
    remark(val) {
      this.form.remark = val;
    },
  },
  methods: {
    /**
     * 提交备注信息
     * 将备注内容和订单编号发送到后端API
     * 成功后通知父组件刷新列表，并关闭对话框
     */
    submit() {
      // 防止重复提交
      if (this.loading) {
        return;
      }

      // 检测是否包含emoji
      const emojiRegex =
        /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;

      if (emojiRegex.test(this.form.remark)) {
        this.$message({
          message: "备注内容不可包含emoji表情",
          type: "warning",
        });
        return;
      }

      const params = {
        distributionOrderNo: this.distributionOrderNo,
        remark: this.form.remark,
      };
      this.loading = true;
      this.$post("updateEduStudentRemark", params, { json: true })
        .then((res) => {
          const { fail } = res;
          if (!fail) {
            this.$emit("getTableList"); // 通知父组件刷新列表
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.close(); // 关闭对话框
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    /**
     * 对话框打开时的钩子函数（预留，暂未实现具体逻辑）
     */
    open() {},
    /**
     * 关闭对话框
     * 重置组件数据到初始状态，并通知父组件更新visible状态
     */
    close() {
      this.$emit("update:visible", false); // 通知父组件更新visible
      this.$emit("close"); // 触发关闭事件
    },
  },
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
