<template>
  <div class="analysis">
    <div class="title">趋势图</div>
    <div class="form">
      <el-form
        :inline="true"
        :model="form"
        label-width="60px"
        size='mini'
        class="demo-form-inline"
      >
        <el-form-item>
          <el-radio-group v-model="form.accesstype">
            <el-radio-button label="pv">浏览量(PV)</el-radio-button>
            <el-radio-button label="uv">访客量(UV)</el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="时间" prop='time'>
          <el-date-picker
            v-model="form.time"
            v-width="240"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div ref='chart' class="chart" />
  </div>
</template>
<script>
import { getTodayToSomeday } from '@/common';
import { handleDateControl } from '@/utils';
const echarts = require('echarts');
export default {
  props: {
    targetType: {
      type: String,
      default: 'pu.member.act.browse',
      require: true
    },
    targetId: {
      type: Number,
      default: 30,
      require: true
    }
  },
  data() {
    return {
      form: {
        accesstype: 'pv',
        time: []
      },
      platform: ['Android', 'IOS', 'WECHAT'],
      date: [],
      options: {},
      chartTitle: '浏览量(PV)',
      chartData: {
        Android: {
          PV: []
        },
        IOS: [],
        WECHAT: []
      }
    };
  },
  mounted() {
    const date = getTodayToSomeday(-7);
    this.form.time = [date[1], date[0]];
    console.log(this.form.time);
    this.getChartData();
    this.initChart();
  },
  methods: {
    // 获取图表数据
    getChartData() {
      this.platform.forEach(item => {
        const formData = JSON.parse(JSON.stringify(this.form));
        const date = handleDateControl(formData.time);
        const data = {
          targetType: this.targetType, // 事件类型
          targetId: this.targetId, // 业务ID
          platform: item, // 发布平台
          type: 'day', // 统计类型 默认带 "day"
          startTime: date[0],
          endTime: date[1],
          start: 0,
          length: 9999
        };
        this.$post('getEventCountList', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.chartData[item] = body.data;
          }
        });
      });
    },
    initChart() {
      const self = this;
      const option = {
        tooltip: {
          trigger: 'axis',
          padding: 0,
          borderWidth: 0,
          borderColor: '#DADBDC',
          backgroundColor: '#fff',
          textStyle: {
            fontSize: 12,
            fontColor: '#BFC0C2'
          },
          formatter: function(params, ticket, callback) {
            const item = params[0];
            const listHtml = params.map(item => {
              return `
                <div>
                  <div style="width:8px;height:8px;display:inline-block;background:${item.color};border-radius: 50%;margin-right:8px;"></div>
                  <div style="display:inline-block;">${item.seriesName}</div>
                  <div style="float:right;">${item.data}</div>
                </div>
              `;
            });
            return `
              <div style="width:200px;font-size:12px;height:36px;padding:0 10px;border-bottom:1px solid #DADBDC;background-color:#F4F6F6;opacity: 0.8;">
                <div style="float:left;line-height: 36px;">${item.axisValue}</div>
                <div style="float:right;line-height: 36px;">${self.chartTitle}</div>
              </div>
              <div style="padding:14px 12px;background-color="#fff;">
                ${listHtml.join('')}
              </div>
            `;
          }
        },
        legend: {
          data: ['安卓', '苹果', '微信公众号']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            color: '#333'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E0E0E0'
            }
          },
          data: ['2020/12/1', '2020/12/2', '2020/12/3', '2020/12/4', '2020/12/5', '2020/12/6', '2020/12/7']
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#333'
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#E0E0E0'
            }
          }
        },
        series: [
          {
            name: '安卓',
            type: 'line',
            stack: '总量',
            data: [120, 132, 101, 134, 90, 230, 210],
            itemStyle: {
              color: '#5470C6'
            }
          },
          {
            name: '苹果',
            type: 'line',
            stack: '总量',
            data: [220, 182, 191, 234, 290, 330, 310],
            itemStyle: {
              color: '#FAC858'
            }
          },
          {
            name: '微信公众号',
            type: 'line',
            stack: '总量',
            data: [150, 232, 201, 154, 190, 330, 410],
            itemStyle: {
              color: '#3BA272'
            }
          }
        ]
      };
      const chart = echarts.init(this.$refs.chart);
      chart.setOption(option);
    }
  }
};
</script>
<style lang="scss" scoped>
.analysis {
  border:1px solid #e1e2e2;
  padding:20px;

  .title {
    padding-bottom:20px;
    font-size: 14px;
    color:#323437;
    font-weight: 700;
  }
  .chart {
    width: 800px;
    height: 400px;
  }
}
</style>
