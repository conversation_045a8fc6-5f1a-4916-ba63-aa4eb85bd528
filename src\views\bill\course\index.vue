<template>
  <div>
    <open-packup>
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='用户姓名' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>

        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder='请输入手机号' />
        </el-form-item>

        <el-form-item label='缴费名称' prop='goodsShelfId'>
          <remote-search-selects
            v-model="form.goodsShelfId"
            :props="{
              apiName: 'findShelfSelectList',
              value: 'goodsShelfId',
              label: 'goodsShelfName',
              query: 'goodsShelfName'
            }"
          />
        </el-form-item>

        <el-form-item label='订单渠道' prop='appType'>
          <el-select v-model="form.appType" placeholder="请选择订单渠道">
            <el-option label="微信" value="WECHAT" />
            <el-option label="APP" value="APP" />
          </el-select>
        </el-form-item>

        <el-form-item label='订单类型' prop='goodsShelfType'>
          <el-select
            v-model="form.goodsShelfType"
            clearable
            placeholder="请选择订单类型"
          >
            <el-option label="单课程套餐" value="1" />
            <el-option label="组合课程套餐" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label='对账类型' prop='tradeCode'>
          <el-select
            v-model="form.tradeCode"
            clearable
            placeholder="请选择对账类型"
          >
            <el-option label="套餐课程" value="course" />
            <el-option label="读书计划课程" value="readPlan" />
            <el-option label="学霸卡" value="studycard" />
            <el-option label="训练营课程" value="training" />
          </el-select>
        </el-form-item>

        <el-form-item label='收费方式' prop='paymentType'>
          <el-select
            v-model="form.paymentType"
            clearable
            placeholder="请选择收费方式"
          >
            <el-option
              v-for="item in $dictJson['paymentType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='支付状态' prop='payStatus'>
          <el-select v-model="form.payStatus" placeholder="请选择" clearable>
            <el-option label="已取消" value="-1" />
            <el-option label="待支付" value="1" />
            <el-option label="已支付" value="2" />
            <el-option label="已退款" value="3" />
            <el-option label="支付中" value="4" />
            <el-option label="退款中" value="5" />
          </el-select>
        </el-form-item>

        <el-form-item label='单据号' prop='payNo'>
          <el-input v-model="form.payNo" placeholder='请输入单据号' />
        </el-form-item>

        <el-form-item label='第三方单号' prop='outNo'>
          <el-input v-model="form.outNo" placeholder='请输入第三方单号' />
        </el-form-item>

        <el-form-item label="缴费起止时间" prop="time">
          <el-date-picker
            v-model="form.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='应付金额区间' prop='amountsPay'>
          <el-input-number
            v-model="form.startAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最低"
          />
          -
          <el-input-number
            v-model="form.endAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最高"
          />
        </el-form-item>
        <el-form-item label='实缴金额区间' prop='orderAmount'>
          <el-input-number
            v-model="form.payStartAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最低"
          />
          -
          <el-input-number
            v-model="form.payEndAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最高"
          />
        </el-form-item>

        <el-form-item label='订单来源' prop='orderChannel'>
          <el-select
            v-model="form.orderChannel"
            clearable
            placeholder="请选择订单来源"
          >
            <el-option label="零一裂变" value="lingYi" />
            <el-option label="远智" value="YZ" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <div class="left">
        套餐累计付费{{ tiredTotalPrice }}元，累计付费{{ payNumber }}人；累计退费{{ refundPrice }}元，累计退费{{ refundNumber }}人；累计净收入{{ totalPrice }}元
      </div>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportSetMeal">导出对账</el-button>
      <!-- <el-button type="success" size="small" icon="el-icon-upload2" @click="exportCourseListExcel">导出商品明细</el-button> -->
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
    >
      <el-table-column label="订单来源" align="center" prop="orderChannel">
        <template slot-scope="scope">
          {{ scope.row.orderChannel | orderSource }}
        </template>
      </el-table-column>
      <el-table-column label="订单渠道" align="center" prop="appType">
        <template slot-scope="scope">
          {{ scope.row.appType | appType }}
        </template>
      </el-table-column>
      <el-table-column label="订单类型" align="center" prop="goodsShelfType">
        <template slot-scope="scope">
          {{ scope.row.goodsShelfType | orderType }}
        </template>
      </el-table-column>
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column label="姓名" align="center" prop="userName" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="mobile" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="缴费名称" align="center" prop="goodsShelfName" />
      <el-table-column label="市场价" align="center" prop="marketPrice" />
      <el-table-column label="应付金额" align="center" prop="totalPrice">
        <template slot-scope="scope">
          {{ scope.row.totalPrice+ "元" }}
        </template>
      </el-table-column>
      <el-table-column label="实缴金额" align="center" prop="payAmount">
        <template slot-scope="scope">
          {{ scope.row.payAmount+ "元" }}
        </template>
      </el-table-column>
      <el-table-column label="智米抵扣" align="center" prop="zmScale">
        <template slot-scope="scope">
          {{ scope.row.zmScale+ "元" }}
        </template>
      </el-table-column>
      <el-table-column label="滞留抵扣" align="center" prop="demurrageScale" />
      <el-table-column label="优惠券抵扣(元)" align="center" prop="couponScale">
        <template slot-scope="scope">
          <el-link v-if="scope.row.couponScale !== 0" type="success" @click="couponDetails(scope.row)">{{ scope.row.couponScale }} </el-link>
          <el-link v-else-if="scope.row.couponScale === 0" type="success">{{ scope.row.couponScale }} </el-link>
        </template>
      </el-table-column>
      <el-table-column label="缴费日期" align="center" prop="payTime" width="150px" />
      <el-table-column label="单据号" align="center" prop="payNo" />
      <el-table-column label="第三方单据号" align="center" prop="outNo" />
      <el-table-column label="收款方式" align="center" prop="paymentType">
        <template slot-scope="scope">
          {{ scope.row.paymentType | tansformPayMethod }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="payStatus">
        <template slot-scope="scope">
          {{ scope.row.payStatus | payStatus }}
        </template>
      </el-table-column>
      <el-table-column label="分销人" align="center" prop="distributionUserName" />
      <el-table-column label="订单绑定" align="center" prop="payStatus">
        <template slot-scope="scope">
          <el-button type="text" @click="orderBind(scope.row)">绑定</el-button>
        </template>
      </el-table-column>
      <el-table-column label="是否审核" align="center" prop="checkStatus">
        <template slot-scope="scope">
          {{ scope.row.checkStatus | tansformApprovalStatus }}
        </template>
      </el-table-column>
      <el-table-column label="审核人" align="center" prop="checkUser" />
      <el-table-column label="备注" align="center" prop="checkRemark">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" icon="el-icon-edit-outline" @click="handleEdit(scope.row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 备注 弹窗 -->
    <remark-dialog :visible.sync="rdVisible" :remark="currentRemark" :order-no="currentOrderNo" />

    <!-- 优惠券 弹窗 -->
    <coupon-dialog :row='currentRow' :visible.sync="cpVisible" @refresh-list="getTableList" />

    <!-- 订单绑定 -->
    <order-bind :visible.sync="obVisible" :row="currentRow" orderIdentify="goods" />
  </div>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
import remarkDialog from './remark-dialog';
import couponDialog from './coupon-dialog';
import orderBind from '../components/order-bind.vue';
import { handleDateControl, exportExcel } from '@/utils';
import LookMobile from '@/mixins/LookMobile';

export default {
  components: {
    remarkDialog,
    couponDialog,
    orderBind
  },
  filters: {
    orderType(val) {
      if (!val) return '';
      const data = {
        1: '单课程套餐',
        2: '组合课程套餐'
      };
      return data[val];
    },
    appType(val) {
      if (!val) return '';
      const data = {
        WECHAT: '微信',
        APP: 'APP'
      };
      return data[val];
    },
    payStatus(val) {
      if (!val) return '';
      const data = {
        '-1': '已取消',
        '1': '待支付',
        '2': '已支付',
        '3': '已退款',
        '4': '支付中',
        '5': '退款中'
      };
      return data[val];
    }
  },
  mixins: [LookMobile],
  data() {
    return {
      tableLoading: false,
      cpVisible: false,
      currentcpId: false,
      obVisible: false,
      currentRow: null,
      form: {
        goodsShelfId: '',
        time: '',
        userName: '',
        mobile: '',
        payStatus: '',
        goodsShelfName: '',
        goodsShelfType: '',
        appType: '',
        paymentType: '',
        payNo: '',
        outNo: '',
        createStartTime: '',
        createEndTime: '',
        startAmount: undefined,
        endAmount: undefined,
        payStartAmount: undefined,
        payEndAmount: undefined,
        tradeCode: '',
        orderChannel: '',
        yzCode: ''
      },
      currentRemark: '',
      currentOrderNo: null,
      table_height: TABLE_HEIGHT,
      rdVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tiredTotalPrice: 0, // 套餐累计付费
      payNumber: 0, // 累计付费人数
      refundPrice: 0, // 累计退费
      refundNumber: 0, // 累计退费人数
      totalPrice: 0// totalPrice
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 优惠券详情
    couponDetails(row) {
      this.currentRow = row;
      this.cpVisible = true;
    },
    getSetMealBillInfo() {
      const data = this.handleQueryParams();
      this.$post('getSetMealBillInfo', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tiredTotalPrice = body.tiredTotalPrice || 0;
          this.payNumber = body.payNumber || 0;
          this.refundPrice = body.refundPrice || 0;
          this.refundNumber = body.refundNumber || 0;
          this.totalPrice = body.totalPrice || 0;
        }
      });
    },
    exportSetMeal() {
      const data = this.handleQueryParams();
      exportExcel('exportSetMeal', data);
    },
    exportCourseListExcel() {
      const data = this.handleQueryParams();
      exportExcel('exportCourseListExcel', data);
    },
    handleEdit(row) {
      this.currentOrderNo = row.orderNo;
      this.currentRemark = row.checkRemark;
      this.rdVisible = true;
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.createStartTime = date[0];
      formData.createEndTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    async getTableList() {
      this.tableLoading = true;
      this.getSetMealBillInfo();

      const data = this.handleQueryParams();
      const { fail, body } = await this.$post('getGoodsBillList', data);
      if (!fail) {
        this.tableLoading = false;
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
      }
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
        this.form.startAmount = undefined;
        this.form.endAmount = undefined;
        this.form.payStartAmount = undefined;
        this.form.payEndAmount = undefined;
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    orderBind(row) {
      this.currentRow = row;
      this.obVisible = true;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  .left{
    color:red;
    float: left;
  }
}
</style>
