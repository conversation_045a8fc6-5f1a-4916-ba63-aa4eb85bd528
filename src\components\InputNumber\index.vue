<template>
  <div class="yz-input-number">
    <el-input-number
      v-model="val"
      v-bind="$attrs"
      class="input-number"
      :controls="controls"
      v-on="$listeners"
      @change="_change"
      @blur="_blur"
    />
    <div v-if="$slots.suffix" class="input__suffix">
      <div class="el-input__icon">
        <slot name="suffix" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'InputNumber',
  props: {
    value: {
      type: [Number, String],
      default: undefined
    },
    controls: {
      type: Boolean,
      default: false
    },
    length: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      val: undefined
    };
  },
  watch: {
    value(val) {
      this.val = val;
    }
  },
  mounted() {
    this.val = this.value;
  },
  methods: {
    _blur(event) {
      if (this.length !== null) {
        // let value = this.val.toString();
        let value = String(this.val);
        if (value.length > this.length) {
          value = Number(value.slice(0, this.length));
          this.val = value;
          this.$emit('update:value', value);
        }
      } else {
        this.$emit('update:value', this.val);
      }
    },
    _change(currentValue, oldValue) {
      this.$emit('update:value', currentValue);
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-input-number{
  position: relative;
  .input-number{
    width: 100% !important;

    ::v-deep .el-input__inner{
      text-align: left;
      padding-left:15px;
      padding-right:30px;
    }
  }

  .input__suffix {
      position: absolute;
      height: 100%;
      right: 5px;
      top: 0;
      text-align: center;
      color: #c0c4cc;
      transition: all .3s;
      pointer-events: none;

      ::v-deep .el-input__icon{
        line-height: unset;
      }
  }
}
</style>
