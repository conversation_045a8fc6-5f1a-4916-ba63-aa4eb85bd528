<template>
  <common-dialog
    title="选择课程"
    width="650px"
    :visible.sync='visible'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!--      <el-form-->
      <!--        ref='searchForm'-->
      <!--        size='mini'-->
      <!--        :model='searchForm'-->
      <!--        label-width='80px'-->
      <!--      >-->
      <!--        <el-form-item label='课程名称' prop='allow'>-->
      <!--          <el-input v-model="searchForm.name" placeholder="请输入内容" />-->
      <!--        </el-form-item>-->
      <!--      </el-form>-->

      <div>
        <el-table
          v-loading="tableLoading"
          border
          size="small"
          style="width: 100%"
          height="400px"
          header-cell-class-name='table-cell-header'
          :data="tableData"
        >
          <el-table-column prop="title" label="课程" align="center" />
          <el-table-column label="操作" align="center" width="150px">
            <template slot-scope="scope">
              <div class="yz-button-area">
                <el-button type="text" @click="selectionCourse(scope.row)">选择</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          layout='prev, pager, next, total'
          @pagination="getKsbCourseList"
        />
      </div>

    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      show: false,
      searchForm: {
        name: ''
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.getKsbCourseList();
    },
    getKsbCourseList() {
      this.tableLoading = true;
      const data = {
        pageNo: this.pagination.page,
        pageSize: this.pagination.limit
      };
      this.$post('getKSBCourseList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.lists;
          this.pagination.total = body.total;
          this.tableLoading = false;
        }
      });
    },
    // 选择课程
    selectionCourse(row) {
      this.$emit('selected', row);
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped>

</style>
