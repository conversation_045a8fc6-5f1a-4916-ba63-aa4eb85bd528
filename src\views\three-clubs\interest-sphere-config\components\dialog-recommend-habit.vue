<template>
  <common-dialog class="common-dialog" width="80%" title="习惯推荐列表" :visible.sync="show" @close="close">
    <!-- 表单 -->
    <el-form
      ref="form"
      size="mini"
      label-width="100px"
      class="yz-search-form"
      :model="form"
    >

      <el-form-item label="打卡类型" prop="markTaskType">
        <el-select v-model="form.markTaskType" placeholder="请选择" clearable>
          <el-option v-for="item in DMarkTaskType" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label="打卡名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="书名" prop="subName">
        <el-input v-model="form.subName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="发起人" prop="createName">
        <el-input v-model="form.createName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="打卡时间起" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="打卡时间止" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="是否启用" prop="isAllow">
        <el-select v-model="form.isAllow" placeholder="请选择" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="是否推荐" prop="isRecommend">
        <el-select v-model="form.isRecommend" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="渠道" prop="irrigationDitch">
        <el-select v-model="form.irrigationDitch" placeholder="请选择" clearable>
          <el-option v-for="item in DIrrigationDitch" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

    </el-form>

    <div v-loading="tableLoading">

      <!-- 按钮区 -->
      <div class="m-t-10 m-l-10 m-b-10">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click="search('reset')">重置</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-circle-plus-outline" @click="onAddExpert">添加选中</el-button>
        <el-button size="mini" type="danger" plain icon="el-icon-remove-outline" @click="onRemoveExpert">移除选中</el-button>
      </div>

      <div class="m-l-10 m-r-10">
        <!-- 表格 -->
        <el-table
          ref="table"
          border
          size="small"
          style="width: 100%;"
          header-cell-class-name="table-cell-header"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column label="是否选中" align="center" width="70">
            <template #default="scope">
              <el-tag v-if="scope.row.ifSelected == 1" size="mini"> 是</el-tag>
              <el-tag v-else size="mini" type="info"> 否 </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="markTaskType" label="打卡类型" align="center" :formatter="handleMarkTaskType" width="90" />
          <el-table-column label="渠道" align="center" :formatter="handleIrrigationDitch" width="90" />
          <el-table-column prop="name" label="打卡名称" align="center" />
          <el-table-column prop="subName" label="书名" align="center" />
          <el-table-column prop="createName" label="创建人" align="center" />
          <el-table-column prop="initiatorUserName" label="发起人" align="center" />
          <el-table-column prop="createTime" label="创建时间" align="center" width="135" />
          <el-table-column prop="enrollEndTime" label="报名结束时间" align="center" width="135" />
          <el-table-column prop="startTime" label="打卡开始时间" align="center" width="135" />
          <el-table-column prop="endTime" label="打卡结束时间" align="center" width="135" />
          <el-table-column prop="amount" label="报名费" align="center" />
          <el-table-column prop="isAllow" label="状态" align="center" width="70">
            <template #default="scope">
              {{ scope.row.isAllow == 1 ? "启用" : "禁用" }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="推荐状态" align="center" width="70">
            <template #default="scope">
              {{ scope.row.isRecommend == 1 ? "是" : "否" }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页区 -->
      <div class="yz-table-pagination m-b-10">
        <pagination
          :total='pager.total'
          :page.sync="pager.pageNum"
          :limit.sync="pager.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>

import { DMarkTaskType, DIrrigationDitch } from '@/dict';
/** 操作类型: 1: 添加选中 0: 清除选中 */
const OPERATE_TYPE = {
  ADD: 1,
  REMOVE: 0
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      DMarkTaskType,
      DIrrigationDitch,
      show: false,
      form: {
        markTaskType: undefined, // 打卡类型 [2:读书 3:跑步 4:其他 5:公益] 对应字典表的 markTaskType
        name: undefined, // 用户名
        subName: undefined, // 书名
        createName: undefined, // 发起人
        startTime: undefined, // 打卡时间起
        endTime: undefined, // 打卡时间止
        isAllow: undefined, // 是否显示 [1：启用, 2：禁用]
        isRecommend: undefined, // 是否推荐 1: 是 2: 否
        irrigationDitch: undefined // 渠道 1:远智教育APP  2:上进青年网
      },
      tableLoading: false,
      tableData: [],
      selectionList: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
        if (val) {
          this.search();
        }
      },
      immediate: true
    }
  },
  methods: {
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    async getData() {
      try {
        this.tableLoading = true;
        const params = {
          ...this.pager,
          ...this.form,
          configId: this.row.id
        };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/circleConfig/markTaskRecommendList',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    search(type) {
      if (type === 'reset') {
        this.$refs.form.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    handleMarkTaskType(row) {
      return this.DMarkTaskType.find(v => v.dictValue === Number(row.markTaskType))?.dictName;
    },
    handleIrrigationDitch(row) {
      return this.DIrrigationDitch.find(v => v.dictValue === Number(row.irrigationDitch))?.dictName ?? '缺失';
    },
    // 选中用户
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    // 操作成员状态 新增|删除 达人
    async handleClubActMember(type) {
      if (this.tableLoading) return;
      try {
        this.tableLoading = true;
        const params = {
          configId: this.row.id,
          businessType: 1, // 业务类型，1:习惯 2:活动 3:话题
          operateType: type,
          mappingList: this.selectionList.map(s => s.id)
        };
        const { code } = await this.$http({
          method: 'post',
          url: '/circleConfig/addOrClearSelected',
          data: params,
          json: true
        });
        if (code !== '00') {
          this.tableLoading = false;
          return;
        }
        this.$message.success(type === OPERATE_TYPE.ADD ? '添加成功' : '移除成功');
        this.getData();
      } catch (error) {
        console.log(error);
        this.tableLoading = false;
      }
    },
    // btn 添加
    async onAddExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加为选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.ADD);
        }).catch(() => { });
    },
    // btn 移除
    onRemoveExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.REMOVE);
        }).catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.m-r-10 {
  margin-right: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}

.yz-search-form{
  padding-right: 0;
  @extend .m-t-10;
}

::v-deep .el-table__header-wrapper .el-checkbox{
display:none
}

</style>
