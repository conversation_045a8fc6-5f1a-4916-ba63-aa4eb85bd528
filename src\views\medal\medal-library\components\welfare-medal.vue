<template>
  <el-radio-group v-model="conditionType" :disabled="disabled" class="vertical-radio-group" @change="handleConditionTypeChange">
    <div class="radio-item">
      <el-radio :label="1">首次公益发帖</el-radio>
    </div>

    <div class="radio-item">
      <el-radio :label="2">公益笔记≥</el-radio>
      <el-input-number
        v-if="conditionType === 2"
        v-model="welfareData.sum"
        :disabled="disabled"
        :precision="0"
        :min="1"
        :controls="false"
        placeholder="请输入公益笔记数"
        style="width: 180px;"
      />
      <span v-if="conditionType === 2">条</span>
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'WelfareMedal',
  props: {
    welfareData: {
      type: Object,
      default: () => ({
        first: undefined, // 首次公益发帖
        sum: undefined // 公益笔记条
      })
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      conditionType: null
    };
  },
  watch: {
    welfareData: {
      handler(val) {
      },
      deep: true
    }
  },
  mounted() {
    // 根据welfareData的值初始化conditionType
    if (this.welfareData.first) {
      this.conditionType = 1;
    } else if (this.welfareData.sum) {
      this.conditionType = 2;
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.conditionType) {
        return { valid: false, message: '请选择获取条件' };
      } else if (this.conditionType === 2 && !this.welfareData.sum) {
        return { valid: false, message: '请输入公益笔记数' };
      }
      return { valid: true };
    },
    // 处理条件类型变化
    handleConditionTypeChange(val) {
      // 重置所有属性为undefined
      Object.keys(this.welfareData).forEach(key => {
        this.welfareData[key] = undefined;
      });

      // 如果选择了首次公益发帖，设置first为true
      if (val === 1) {
        this.welfareData.first = true;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }

    span {
      margin-left: 5px;
      font-size: 14px;
    }
  }
}
</style>
