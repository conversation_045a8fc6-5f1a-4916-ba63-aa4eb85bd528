// 指令 v-width 设置高度
export function width(el, binding, vnode) {
  if (typeof binding.value === "number") {
    el.style["width"] = binding.value + "px";
  } else {
    el.style["width"] = binding.value;
  }
}

export function preventReClick(el, binding, vnode) {
  // 防止重复点击
  el.addEventListener("click", () => {
    if (!el.disabled) {
      el.disabled = true;
      setTimeout(() => {
        el.disabled = false;
      }, binding.value || 1000);
    }
  });
}

// 下拉加载
export const loadmore = {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap"
    );

    // 添加防抖功能
    let timer = null;
    const debounce = (fn, delay = 200) => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        fn();
      }, delay);
    };

    SELECTWRAP_DOM.addEventListener("scroll", function () {
      /**
       * scrollHeight 元素内容高度
       * scrollTop 元素滚动的偏移量
       * clientHeight 元素的可见高度
       */
      // 如果元素滚动到底, 下面等式返回true, 没有则返回false, 这里的3是给予一定的误差，以防止检测不到滚动到底部
      const CONDITION =
        this.scrollHeight - this.scrollTop - 3 <= this.clientHeight; // 说明滚动到底
      if (CONDITION) {
        // 使用防抖执行 v-loadmore='loadMore' 传入的函数
        debounce(() => {
          binding.value();
        });
      }
    });
  },
};

/**
 * 权限指令 v-auth
 * 用法：v-auth="'workOrder:add'"
 * 默认隐藏元素，有权限时才显示，并添加淡入动画
 */
export const auth = {
  bind: function (el, binding) {
    // 创建一个包装元素
    const wrapper = document.createElement("div");
    wrapper.style.display = "contents"; // 不影响布局

    // 保存原始元素的引用
    const originalDisplay = el.style.display;

    // 添加过渡类
    el.classList.add("auth-checking");
    // 默认隐藏
    el.style.opacity = "0";
    el.style.transition = "opacity 0.5s ease-in-out";

    // 将原始元素的属性保存在wrapper上
    wrapper._originalDisplay = originalDisplay;
    wrapper._originalElement = el;

    // 用wrapper替换原始元素
    if (el.parentNode) {
      el.parentNode.insertBefore(wrapper, el);
      wrapper.appendChild(el);
    }
  },
  inserted: async function (el, binding, vnode) {
    // 由于我们包装了元素，需要获取原始元素
    const wrapper = el.parentNode;
    const originalEl = wrapper._originalElement || el;

    // 获取Vue实例
    const vm = vnode.context;
    const permissionCode = binding.value;

    try {
      // 使用全局权限检查方法
      const hasPermission = await vm.$checkPermission(permissionCode);

      if (hasPermission) {
        // 如果有权限，显示元素并添加淡入效果
        originalEl.classList.remove("auth-checking");
        originalEl.classList.add("auth-permitted");

        // 淡入显示
        setTimeout(() => {
          originalEl.style.opacity = "1";
        }, 50);
      } else {
        // 如果没有权限，将包装元素设为none
        wrapper.style.display = "none";
      }
    } catch (error) {
      console.error("权限检查失败:", error);
      // 发生错误时隐藏包装元素
      wrapper.style.display = "none";
    }
  },
  unbind: function (el) {
    // 尝试恢复原始DOM结构
    const wrapper = el.parentNode;
    if (wrapper && wrapper._originalElement) {
      if (wrapper.parentNode) {
        wrapper.parentNode.insertBefore(el, wrapper);
        wrapper.parentNode.removeChild(wrapper);
      }

      // 清除添加的类和样式
      el.classList.remove("auth-checking", "auth-permitted");
      el.style.transition = "";
      el.style.opacity = "";
    }
  },
};
