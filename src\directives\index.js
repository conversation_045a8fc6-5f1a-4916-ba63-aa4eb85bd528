// 指令 v-width 设置高度
export function width(el, binding, vnode) {
  if (typeof binding.value === "number") {
    el.style["width"] = binding.value + "px";
  } else {
    el.style["width"] = binding.value;
  }
}

export function preventReClick(el, binding, vnode) {
  // 防止重复点击
  el.addEventListener("click", () => {
    if (!el.disabled) {
      el.disabled = true;
      setTimeout(() => {
        el.disabled = false;
      }, binding.value || 1000);
    }
  });
}

// 下拉加载
export const loadmore = {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap"
    );

    // 添加防抖功能
    let timer = null;
    const debounce = (fn, delay = 200) => {
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(() => {
        fn();
      }, delay);
    };

    SELECTWRAP_DOM.addEventListener("scroll", function () {
      /**
       * scrollHeight 元素内容高度
       * scrollTop 元素滚动的偏移量
       * clientHeight 元素的可见高度
       */
      // 如果元素滚动到底, 下面等式返回true, 没有则返回false, 这里的3是给予一定的误差，以防止检测不到滚动到底部
      const CONDITION =
        this.scrollHeight - this.scrollTop - 3 <= this.clientHeight; // 说明滚动到底
      if (CONDITION) {
        // 使用防抖执行 v-loadmore='loadMore' 传入的函数
        debounce(() => {
          binding.value();
        });
      }
    });
  },
};
