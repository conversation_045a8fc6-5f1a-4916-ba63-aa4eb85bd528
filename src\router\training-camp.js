import Layout from '@/layout';

// 自考训练营
export default [
  {
    path: '/trainingCamp',
    component: Layout,
    meta: {
      title: '训练营管理',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/zkxly/zk-trainingCamp/index'),
        meta: {
          title: '训练营管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    path: '/levelManagement',
    component: Layout,
    meta: {
      title: '关卡管理',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/zkxly/zk-level-management/index'),
        meta: {
          title: '关卡管理'
        }
      }
    ]
  },
  {
    path: '/customsClearanceGift',
    component: Layout,
    meta: {
      title: '通关礼品',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () =>
          import('@/views/zkxly/zk-customs-clearance-gift/index'),
        meta: {
          title: '通关礼品'
        }
      }
    ]
  },
  {
    path: '/addStudent',
    component: Layout,
    meta: {
      title: '添加学员',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/zkxly/zk-add-student/index'),
        meta: {
          title: '添加学员'
        }
      }
    ]
  },
  {
    path: '/viewTargetStudents',
    component: Layout,
    meta: {
      title: '查看目标学员',
      icon: 'el-icon-s-check',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        component: () => import('@/views/zkxly/zk-view-target-students/index'),
        meta: {
          title: '查看目标学员'
        }
      },
      {
        path: '/students-info',
        component: () =>
          import('@/views/zkxly/zk-view-target-students/students-info'),
        meta: {
          title: '查看目标学员详情'
        }
      }
    ]
  }
];
