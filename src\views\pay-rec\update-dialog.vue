<template>
  <common-dialog
    :show-footer="true"
    width="600px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="80px"
        :rules="rules"
      >
        <el-form-item label='推荐类型' prop='recommendType'>
          <div class="group">
            <el-select
              v-model="form.recommendType"
              v-width="'20%'"
              class="inline-block group-select"
              @change="handleChangeType"
            >
              <el-option label="商品" :value="1" />
              <el-option label="推荐链接" :value="2" />
            </el-select>

            <remote-search-selects
              v-show="form.recommendType === 1"
              ref="remoteSearch"
              v-model="form.goodsShelfId"
              v-width="'77%'"
              placeholder="请选择商品"
              class="inline-block"
              :props="{
                apiName: 'getSetMealList',
                value: 'goodsShelfId',
                label: 'goodsShelfName',
                query: 'goodsShelfName'
              }"
              :param="{
                goodsShelfName:'',
                shelfStatus: 1
              }"
              :default-option="defaultOption"
              @changeVal="handleChangeGoods"
            />

            <el-input
              v-show="form.recommendType === 2"
              v-model="form.url"
              v-width="'77%'"
              class="inline-block"
              placeholder="请输入链接地址"
            />
          </div>
        </el-form-item>

        <el-form-item label='推荐位置' prop='positionCode'>
          <el-select v-model="form.positionCode" placeholder="请选择">
            <el-option
              v-for="(item,index) in positionCode"
              :key="index"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='推荐排序' prop='sort'>
          <el-input-number
            v-model="form.sort"
            placeholder='请输入排序'
            :precision="0"
            class="yz-input-number"
            :min="0"
            :max="1000000000"
            :controls="false"
          />
        </el-form-item>

        <el-form-item label='推荐标题' prop='recommendTitle'>
          <el-input
            v-model="form.recommendTitle"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='缩略图' prop='recommendPic'>
          <upload-file
            v-model="form.recommendPic"
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='推荐简介' prop='recommendIntroduction'>
          <el-input v-model="form.recommendIntroduction" />
        </el-form-item>

        <el-form-item label='是否启用' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number, Object],
      default: null
    }
  },
  data() {
    const checkRecommendType = (rule, value, callback) => {
      if (value === 1) {
        if (!this.form.goodsShelfId) {
          return callback(new Error('请选择推荐商品'));
        }
      }

      if (value === 2) {
        if (!this.form.url) {
          return callback(new Error('请输入推荐链接地址'));
        }
      }

      callback();
    };
    return {
      checked: false,
      show: false,
      defaultOption: null,
      fileList: [], // 图片list
      goodsOption: [], // 商品
      goodsSource: {}, // 商品来源
      positionCode: [], // 商品推荐位置
      form: {
        goodsSource: 'PU', // 商品来源，遗留字段，写死
        goodsShelfId: '',
        positionCode: null,
        sort: undefined,
        recommendPic: '',
        recommendTitle: '',
        recommendIntroduction: '',
        status: 0,
        goodsRecommendPicFile: {
          fileUrl: '',
          isAdd: 0
        },
        recommendType: 1,
        url: '' // 推荐链接
        // actPrice: 0, // 活动价
        // marketPrice: 0, // 市场价
        // vipPrice: 0 // 会员价
      },
      rules: {
        recommendType: [
          { validator: checkRecommendType, trigger: 'change' }
        ],
        goodsShelfId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        positionCode: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        recommendPic: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        recommendTitle: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      selectedGoods: null // 选中商品
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
    // 'form.goodsShelfId'(value) {
    //   if (!value) {
    //     this.selectedGoods = null;
    //     // this.form.actPrice = 0;
    //     // this.form.marketPrice = 0;
    //     // this.form.vipPrice = 0;
    //     this.form.recommendTitle = '';
    //     console.log('jjjjjj1');
    //   }
    // }
  },
  methods: {
    handleChangeType() {
      this.form.recommendTitle = '';
      this.form.url = '';
      this.form.goodsShelfId = null;
    },
    handleChangeGoods({ value, label, source }) {
      this.form.recommendTitle = label;
      // console.log(value, label);
      // const params = {
      //   goodsShelfId: value
      // };
      // this.$post('getPutCommodityInfo', params)
      //   .then(res => {
      //     const { fail, body } = res;
      //     if (!fail) {
      //       this.selectedGoods = body;
      //       // this.form.actPrice = body.actPrice;
      //       // this.form.marketPrice = body.marketPrice;
      //       // this.form.vipPrice = body.vipPrice;
      //       this.form.recommendTitle = body.goodsShelfName;
      //     }
      //   });
    },
    // // 获取商品来源
    // getGoodsSource() {
    //   this.$post('getGoodsSource')
    //     .then(res => {
    //       const { fail, body } = res;
    //       if (!fail) {
    //         this.goodsSource = body;
    //       }
    //     });
    // },
    // 获取商品推荐位置
    getPositionCode() {
      // 取字典数据
      this.positionCode = this.$dictJson['positionCode'];
    },
    open() {
      // this.getGoodsSource();
      this.getPositionCode();

      if (this.id) {
        this.$http.post(`/puGoodsRecommend/getById/${this.id}`)
          .then(res => {
            const { fail, body } = res;
            if (!fail) {
              // this.form.goodsSource = body.goodsSource;
              this.form.goodsShelfId = body.goodsShelfId;
              this.form.positionCode = body.positionCode;
              this.form.sort = body.sort;
              this.form.recommendPic = body.recommendPic;
              this.form.recommendTitle = body.recommendTitle;
              this.form.recommendIntroduction = body.recommendIntroduction;
              this.form.status = body.status;
              this.form.recommendType = body.recommendType;
              this.form.url = body.url;
              // this.form.actPrice = body.actPrice;
              // this.form.marketPrice = body.marketPrice;
              this.fileList.push({ url: ossUri + body.recommendPic });
              this.defaultOption = {
                goodsShelfId: body.goodsShelfId,
                goodsShelfName: body.goodsShelfName
              };
            }
          });
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addProductRec';
          const formData = JSON.parse(JSON.stringify(this.form));
          // delete formData.actPrice;
          // delete formData.marketPrice;
          const params = {
            ...formData
          };

          if (this.id) {
            apiKey = 'editProductRec';
            params.goodsRecommendId = this.id;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.show = false;
                this.$parent.getTableList();
              }
            });
        }
      });
    },
    handleRemoveImg({ file, fileList }) {
      this.form.recommendPic = '';
      this.form.goodsRecommendPicFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.recommendPic = response;
      this.form.goodsRecommendPicFile.fileUrl = response;
      this.form.goodsRecommendPicFile.isAdd = 1;
      this.$refs['form'].clearValidate(['recommendPic']);
    },
    close() {
      // 这里必须 call 指向当前的实例
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.show = false;
    }
  }
};
</script>

<style lang="scss">

.inline-block {
  display: inline-block;
}

.group-select {
  margin-right: 14px;
}

</style>
