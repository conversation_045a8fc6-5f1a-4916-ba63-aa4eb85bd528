<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    title="新增/编辑"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='选择分值区间' prop='reviewUserName'>
          <div class="ipt-box">
            <el-input
              v-model="form.scoreMin"
              show-word-limit
            /> ——
            <el-input
              v-model="form.scoreMax"
              show-word-limit
            />
          </div>
        </el-form-item>

        <el-form-item label='点评人' prop='reviewUserId'>
          <remote-search-selects
            v-model="form.reviewUserId"
            :default-option="reviewDefaultOption"
            :props="{
              apiName: 'getLedReadAllowList',
              value: 'ledReadId',
              label: 'ledReadName',
              query: 'ledReadName'
            }"
            @changeVal='changeReviewUser'
          />
        </el-form-item>
        <el-form-item label='点评内容' prop='reviewContent'>
          <el-input
            v-model="form.reviewContent"
            type="textarea"
            :rows="2"
            :maxlength="500"
            show-word-limit
            placeholder="请输入内容"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='enable'>
          <el-radio-group v-model="form.enable">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="2">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
// import { splitChar } from '@/utils';
import { validate } from '@/utils/validate';
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    paperId: {
      type: String,
      default: ''
    },
    trainingId: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateScore = (rule, value, callback) => {
      if (!String(this.form.scoreMin) || !String(this.form.scoreMax)) {
        callback(new Error('请填写分值区间'));
      } else if (!validate('positiveInteger', this.form.scoreMin) || !validate('positiveInteger', this.form.scoreMax)) {
        callback(new Error('请填写正整数的分值'));
      } else if (+this.form.scoreMin > +this.form.scoreMax || +this.form.scoreMax > 150) {
        callback(new Error('请填写合理的分值区间'));
      } else {
        callback();
      }
    };

    return {
      show: false,
      fileList: [],
      qrCode: [],
      reviewDefaultOption: null,
      form: {
        reviewId: '',
        reviewUserId: '',
        reviewUserName: '',
        reviewContent: '',
        scoreMin: '',
        scoreMax: '',
        enable: 1,
        detailsPicFileList: []
      },
      rules: {
        reviewUserName: [
          { required: true, validator: validateScore, trigger: 'change' }
        ],
        reviewUserId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        reviewContent: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    changeReviewUser(item) {
      this.form.reviewUserId = item.value;
      this.form.reviewUserName = item.label;
    },
    open() {
      if (this.row) {
        this.getTableList();
      }
    },
    getTableList() {
      const params = {
        reviewId: this.row.reviewId
      };
      this.$post('getTrainReviewFindSingle', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.reviewUserName = body.reviewUserName;
            this.form.scoreMax = body.scoreMax;
            this.form.scoreMin = body.scoreMin;
            this.form.reviewContent = body.reviewContent;
            this.form.reviewId = body.reviewId;
            this.form.enable = body.enable;

            // 下拉回显
            this.form.reviewUserId = body.reviewUserId;
            this.reviewDefaultOption = {
              ledReadId: body.reviewUserId,
              ledReadName: body.reviewUserName
            };
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addTrainReview';
          this.form.scoreMin = +this.form.scoreMin;
          this.form.scoreMax = +this.form.scoreMax;
          const params = {
            ...this.form,
            'paperId': this.paperId,
            'trainingId': this.trainingId
          };

          if (this.row) {
            apiKey = 'editTrainReview';
            params.review_id = this.row.review_id;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$emit('getTableList');
              }
            });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.ipt-box{
  ::v-deep .el-input{
    width: 100px;
  }
}
</style>
