<template>
  <common-dialog
    is-full
    width="1000px"
    title="课时管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='转存状态' prop='txyStatus'>
          <el-select
            v-model="form.txyStatus"
            clearable
            placeholder="选择转存状态"
          >
            <el-option
              v-for="item in $dictJson['txyStatus']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="success" size="small" @click="addCourseHour">新增</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column label="来源" align="center">
          <template slot-scope="scope">
            {{ scope.row.type | source }}
          </template>
        </el-table-column>
        <el-table-column label="上课类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.attendClassVO.content | type }}
          </template>
        </el-table-column>
        <el-table-column prop="classHourName" label="课程名称" align="center" />
        <el-table-column prop="test" label="保利威vids" align="center">
          <template slot-scope="scope">
            {{ scope.row.attendClassVO.content | changeVidsUrl }}
          </template>
        </el-table-column>
        <el-table-column prop="txyStatus" label="腾讯云转存状态" align="center">
          <template slot-scope="scope">
            {{ format(scope.row.txyStatus.toString(), 'txyStatus') }}
          </template>
        </el-table-column>
        <el-table-column prop="txyFileId" label="腾讯云ID" align="center" />
        <el-table-column prop="test" label="关联试卷" align="center">
          <template slot-scope="scope">
            {{ scope.row.paperName }}
          </template>
        </el-table-column>
        <el-table-column label="试卷点评配置" align="center" prop="tradeType">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="toComment(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="关卡文案" align="center" prop="tradeType">
          <template slot-scope="scope">
            <div class="yz-button-area">
              {{ scope.row.copySetting }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1? 'success':'danger'">
              {{ scope.row.status === 1 ? '启用':'禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="排序" align="center">
          <template slot-scope="scope">
            <el-button
              icon="el-icon-top"
              circle
              size="mini"
              title="上移"
              @click="updateSort(scope.row, 'up')"
            />
            <el-button
              icon="el-icon-bottom"
              circle
              size="mini"
              title="下移"
              @click="updateSort(scope.row, 'down')"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === 0"
              type="text"
              @click="updateStatus(scope.row, 1)"
            >启用</el-button>
            <el-button
              v-else
              type="text"
              @click="updateStatus(scope.row, 0)"
            >禁用</el-button>
            <el-button type="text" @click="editCourseHour(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

      <chapter-add-edit
        :visible.sync="aeVisible"
        :row="currentRow"
        :trainingId='trainingId'
        :directoryId="currdirectoryId"
        @refreshList="getTableList"
      />
      <!-- 试卷点评 -->
      <test-paper-comment
        :visible.sync="commentVisible"
        :row="currentRow"
        :trainingId='trainingId'
        @refreshList="getTableList"
      />
    </div>
  </common-dialog>
</template>

<script>
import chapterAddEdit from './chapter-add-edit';
import testPaperComment from './test-paper-comment';
import { getTextFromDict } from '@/utils';
export default {
  components: {
    chapterAddEdit,
    testPaperComment
  },
  filters: {
    source(value) {
      if (!value) return;
      const data = {
        'BAOLI': '保利',
        'YZ': '远智',
        'TXY': '腾讯云'
      };
      return data[value];
    },
    type(value) {
      if (!value) return;
      value = JSON.parse(value);
      const data = {
        'playback': '录播',
        'toUrl': '外部链接'
      };
      return data[value.type];
    },
    changeVidsUrl(value) {
      if (!value) return;
      value = JSON.parse(value);
      return value.vidsUrl;
    }
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      addEdit: false,
      tableData: [],
      aeVisible: false,
      trainingId: '',
      commentVisible: false, // 试卷点评
      tableLoading: false,
      currentRow: {},
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      currdirectoryId: null,
      classHourId: null,
      form: {
        txyStatus: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    format(valueKey, key) {
      return getTextFromDict(valueKey, key);
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    open() {
      this.trainingId = this.row.trainingId;
      this.currdirectoryId = this.row.directoryId;
      this.getTableList();
    },
    // 更新状态
    updateStatus(row, status) {
      const params = {
        classHourId: row.classHourId,
        status: status
      };
      this.$post('updateSingleBookClassStatus', params)
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
    },
    updateSort(row, type) {
      const params = {
        classHourId: row.classHourId,
        directoryId: this.currdirectoryId,
        operationType: type
      };
      this.$post('operationClassHourSort', params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.getTableList();
          }
        });
    },
    addCourseHour() {
      this.currentRow = null;
      this.aeVisible = true;
    },
    editCourseHour(row) {
      this.currentRow = row;
      this.aeVisible = true;
    },
    // 试卷点评
    toComment(row) {
      this.currentRow = row;
      this.commentVisible = true;
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        courseId: this.row.courseId
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getTrainHourList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
