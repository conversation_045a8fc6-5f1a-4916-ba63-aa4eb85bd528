<template>
  <el-dialog
    title="关联学员"
    :visible.sync="dialogVisible"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="studentForm"
      :model="studentForm"
      label-width="100px"
      size="small"
    >
      <!-- 关联方式 -->
      <el-form-item label="关联方式" prop="linkType">
        <common-select
          v-model="studentForm.linkType"
          type="linkType"
          @change="handleLinkTypeChange"
        />
      </el-form-item>

      <!-- 远智学业编码 -->
      <template v-if="studentForm.linkType === 'stuXyCode'">
        <el-form-item label="学员" prop="stuXyCode">
          <common-select
            v-model="studentForm.stuXyCode"
            type="stuXyCode"
            emptyWhenNoSearch
            placeholder="请选择学员"
            @change="handleAcademicStudentChange"
          />
        </el-form-item>
      </template>

      <!-- 远智编码 -->
      <template v-if="studentForm.linkType === 'yzCode'">
        <el-form-item label="学员" prop="yzCode">
          <common-select
            v-model="studentForm.yzCode"
            type="yzCode"
            placeholder="请选择学员"
            @change="handleyzCodeChange"
          />
        </el-form-item>
      </template>

      <!-- 新建学员 -->
      <template v-if="studentForm.linkType === 'new'">
        <el-form-item
          label="姓名"
          prop="newName"
          :rules="[{ required: true, message: '请输入姓名', trigger: 'blur' }]"
        >
          <el-input
            v-model="studentForm.newName"
            placeholder="请输入姓名"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="手机号码"
          prop="newPhone"
          :rules="[
            { required: true, message: '请输入手机号码', trigger: 'blur' },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号码',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="studentForm.newPhone"
            placeholder="请输入手机号码"
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button
        type="primary"
        @click="confirmAddStudent"
        :loading="studentLoading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { request } from "@/api";

export default {
  name: "StudentSelector",
  components: {
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      studentLoading: false,
      studentForm: {
        linkType: "stuXyCode", // 关联方式：academic(远智学业编码)、code(远智编码)、new(新建学员)
        stuXyCode: "", // 学业学员
        academicStudentObj: null, // 学业学员对象
        yzCode: "", // 远智编码对应学员
        yzCodeObj: null, // 远智学员对象
        newName: "", // 新建学员姓名
        newPhone: "", // 新建学员手机号
      },
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.resetForm();
      }
    },
  },
  methods: {
    // 重置表单
    resetForm() {
      this.studentForm = {
        linkType: "stuXyCode",
        stuXyCode: "",
        academicStudentObj: null,
        yzCode: "",
        yzCodeObj: null,
        newName: "",
        newPhone: "",
      };
    },

    // 处理关联方式变更
    handleLinkTypeChange() {
      Object.assign(this.studentForm, {
        stuXyCode: "",
        academicStudentObj: null,
        yzCode: "",
        yzCodeObj: null,
        newName: "",
        newPhone: "",
      });
    },

    // 处理学业学员选择变更
    handleAcademicStudentChange(val, option) {
      console.log("学业学员选择:", val, option);
      this.studentForm.academicStudentObj = option;
    },

    // 处理远智学员选择变更
    handleyzCodeChange(val, option) {
      console.log("远智学员选择:", val, option);
      this.studentForm.yzCodeObj = option;
    },

    // 确认添加关联学员
    confirmAddStudent() {
      // 根据关联方式验证不同的表单
      let validateForm = true;
      let studentObj = null;

      switch (this.studentForm.linkType) {
        case "stuXyCode":
          validateForm = !!this.studentForm.stuXyCode;
          if (!validateForm) {
            this.$message.warning("请选择学员");
            return;
          }
          studentObj = {
            stuXyCode: this.studentForm.stuXyCode,
            stuRecordName: this.studentForm.academicStudentObj?.label,
          };
          break;
        case "yzCode":
          validateForm = !!this.studentForm.yzCode;
          if (!validateForm) {
            this.$message.warning("请选择学员");
            return;
          }
          studentObj = {
            stuYzCode: this.studentForm.yzCode,
            stuRecordName: this.studentForm.yzCodeObj?.label,
          };
          break;
        case "new":
          this.$refs.studentForm.validate((valid) => {
            validateForm = valid;
          });
          if (!validateForm) {
            return;
          }
          // 检查学员是否已存在
          if (this.studentForm.newPhone) {
            this.studentLoading = true;
            request(
              "isStudentExist",
              { stuPhone: this.studentForm.newPhone },
              { json: true }
            )
              .then((res) => {
                this.studentLoading = false;
                if (res.body === true) {
                  this.$message.warning(
                    "学员系统已存在学员，请通过远智编码/学业编码进行绑定学员"
                  );
                  return;
                }
                // 学员不存在，可以创建
                studentObj = {
                  stuRecordName: this.studentForm.newName,
                  stuName: this.studentForm.newName,
                  stuPhone: this.studentForm.newPhone,
                };
                // 发送学员数据到父组件
                this.$emit("confirm", studentObj);
                this.handleClose();
              })
              .catch((err) => {
                this.studentLoading = false;
                console.error("检查学员是否存在出错：", err);
              });
            return; // 异步验证，提前返回
          }
          studentObj = {
            stuRecordName: this.studentForm.newName,
            stuName: this.studentForm.newName,
            stuPhone: this.studentForm.newPhone,
          };
          break;
      }

      // 发送学员数据到父组件
      this.$emit("confirm", studentObj);
      this.handleClose();
    },

    // 取消选择
    handleCancel() {
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("update:visible", false);
      this.resetForm();
    },
  },
};
</script>

<style lang="scss" scoped></style>
