<template>
  <common-dialog
    is-full
    width="800px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="sort" label="关卡序号" width="100" align="center" />
        <el-table-column label="提交时间" align="center" prop="submitTime">
          <template slot-scope="scope">
            {{ scope.row.submitTime | transformTimeStamp }}
          </template>
        </el-table-column>
        <el-table-column prop="paperName" label="试卷名称" align="center" />
        <el-table-column prop="userScore" label="得分" align="center" />
      </el-table>
    </div>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
  </common-dialog>
</template>

<script>
import { handleDateControl } from '@/utils';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    },
    trainingId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      title: '已完成关卡',
      tableLoading: false,
      isEditLock: false,
      lockStatus: '1',
      form: {
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.startTime = date[0];
      formData.endTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        id: this.row.id,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      console.log(this.row);
      const data = this.handleQueryParams();
      this.$post('trainUserSignFindSingle', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    open() {
      console.log(this.trainingId, 'trainingId');
      this.title = this.row.userName + '已完成关卡';
      this.getTableList();
    },
    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
