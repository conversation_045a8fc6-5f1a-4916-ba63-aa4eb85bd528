<template>
  <common-dialog class="common-dialog" width="80%" title="话题推荐列表" :visible.sync="show" @close="close">
    <!-- 表单 -->
    <el-form
      ref="form"
      size="mini"
      label-width="100px"
      class="yz-search-form"
      :model="form"
    >

      <el-form-item label="类型" prop="topicType">
        <el-select v-model="form.topicType" placeholder="请选择" clearable>
          <el-option v-for="item in DTopicType" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label="话题名称" prop="topicName">
        <el-input v-model="form.topicName" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="创建时间起" prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="创建时间止" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm"
          type="datetime"
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item label="是否显示" prop="isAllow">
        <el-select v-model="form.isAllow" placeholder="请选择" clearable>
          <el-option label="显示" :value="1" />
          <el-option label="隐藏" :value="2" />
        </el-select>
      </el-form-item>

    </el-form>

    <div v-loading="tableLoading">

      <!-- 按钮区 -->
      <div class="m-t-10 m-l-10 m-b-10">
        <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
        <el-button size="mini" icon="el-icon-refresh" @click="search('reset')">重置</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-circle-plus-outline" @click="onAddExpert">添加选中</el-button>
        <el-button size="mini" type="danger" plain icon="el-icon-remove-outline" @click="onRemoveExpert">移除选中</el-button>
      </div>

      <div class="m-l-10 m-r-10">
        <!-- 表格 -->
        <el-table
          ref="table"
          border
          size="small"
          style="width: 100%;"
          header-cell-class-name="table-cell-header"
          :data="tableData"
          @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="50" />
          <el-table-column label="是否选中" align="center">
            <template #default="scope">
              <el-tag v-if="scope.row.ifSelected == 1" size="mini"> 是</el-tag>
              <el-tag v-else size="mini" type="info"> 否 </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="类型" align="center" :formatter="handleTypeFormatter" />
          <el-table-column prop="userName" label="创建人" align="center" />
          <el-table-column prop="officialSort" label="话题排序" align="center" />
          <el-table-column prop="topicName" label="话题名称" align="center" />
          <el-table-column prop="createTime" label="创建时间" align="center" width="150" />
          <el-table-column prop="createTime" label="是否显示" align="center">
            <template #default="scope">
              {{ scope.row.isAllow == 1 ? "显示" : "隐藏" }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 分页区 -->
      <div class="yz-table-pagination m-b-10">
        <pagination
          :total='pager.total'
          :page.sync="pager.pageNum"
          :limit.sync="pager.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>

import { DTopicType } from '@/dict';
/** 操作类型: 1: 添加选中 0: 清除选中 */
const OPERATE_TYPE = {
  ADD: 1,
  REMOVE: 0
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      DTopicType,
      show: false,
      form: {
        topicType: undefined, // 话题类型 [1:官方 2:学员自建 3:自考话题 4:成教话题 5:国开话题 6:全日制话题 7:研究生话题 8:新年活动话题] 与字典表的 topicType 关联
        topicName: undefined, // 话题名称
        startTime: undefined, // 创建时间起
        endTime: undefined, // 创建时间止
        isAllow: undefined // 是否显示 [1：启用, 2：禁用]
      },
      tableLoading: false,
      tableData: [],
      selectionList: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  watch: {
    visible: {
      handler(val) {
        this.show = val;
        if (val) {
          this.search();
        }
      },
      immediate: true
    }
  },
  methods: {
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    async getData() {
      try {
        this.tableLoading = true;
        const params = {
          ...this.pager,
          ...this.form,
          configId: this.row.id
        };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/circleConfig/topicRecommendList',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    search(type) {
      if (type === 'reset') {
        this.$refs.form.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    handleTypeFormatter(row) {
      return this.DTopicType.find(v => v.dictValue === Number(row.topicType))?.dictName;
    },
    // 选中用户
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    // 操作成员状态 新增|删除 达人
    async handleClubActMember(type) {
      if (this.tableLoading) return;
      try {
        this.tableLoading = true;
        const params = {
          configId: this.row.id,
          businessType: 3, // 业务类型，1:习惯 2:活动 3:话题
          operateType: type,
          mappingList: this.selectionList.map(s => s.id)
        };
        const { code } = await this.$http({
          method: 'post',
          url: '/circleConfig/addOrClearSelected',
          data: params,
          json: true
        });
        if (code !== '00') {
          this.tableLoading = false;
          return;
        }
        this.$message.success(type === OPERATE_TYPE.ADD ? '添加成功' : '移除成功');
        this.getData();
      } catch (error) {
        console.log(error);
        this.tableLoading = false;
      }
    },
    // btn 添加
    async onAddExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加为选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.ADD);
        }).catch(() => { });
    },
    // btn 移除
    onRemoveExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除选中吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(OPERATE_TYPE.REMOVE);
        }).catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.m-r-10 {
  margin-right: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}

.yz-search-form{
  padding-right: 0;
  @extend .m-t-10;
}

::v-deep .el-table__header-wrapper .el-checkbox{
display:none
}

</style>
