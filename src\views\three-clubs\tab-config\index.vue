<template>
  <div class="yz-base-container">
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="clubsType" label="类型" align="center" :formatter="handleClubsType" />
      <el-table-column prop="clubsName" label="展示名称" align="center" />
      <el-table-column label="总成员" align="center">
        <template #default="{ row }">
          {{ row.totalMemberCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="总数" align="center">
        <template #default="{ row }">
          {{ row.totalCount || 0 }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <!-- <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div> -->

    <!-- 编辑弹框 -->
    <dialog-edit :visible.sync="editVisible" :row="editRow" @confirm="getData" />
  </div>
</template>

<script>
import DialogEdit from './components/dialog-edit.vue';
import { ClubType } from '@/dict';

export default {
  components: { DialogEdit },
  data() {
    return {
      tableLoading: true,
      tableData: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      editVisible: false,
      editRow: null
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/clubsInfo/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 类型名称显示
    handleClubsType(row) {
      return ClubType.find((v) => v.dictValue === row.clubsType)?.dictName;
    },
    // 编辑操作
    onEdit(row) {
      this.editRow = row;
      this.editVisible = true;
    }
  }
};
</script>
