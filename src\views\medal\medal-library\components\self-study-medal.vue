<template>
  <el-radio-group v-model="value" :disabled="disabled" class="vertical-radio-group">
    <div v-for="(item, index) in selfStudyOptions" :key="index" class="radio-item">
      <el-radio :label="item.value">{{ item.label }}</el-radio>
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'SelfStudyMedal',
  props: {
    selfStudyType: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      value: this.selfStudyType,
      selfStudyOptions: [
        { value: 1, label: '自考新星（自考通过1门）' },
        { value: 2, label: '自考达人（自考通过4门）' },
        { value: 3, label: '自考精英（自考通过8门）' },
        { value: 4, label: '自考王者（自考通过10门）' }
      ]
    };
  },
  watch: {
    selfStudyType(val) {
      this.value = val;
    },
    value(val) {
      this.$emit('update:selfStudyType', val);
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.value) {
        return { valid: false, message: '请选择获取条件' };
      }
      return { valid: true };
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>
