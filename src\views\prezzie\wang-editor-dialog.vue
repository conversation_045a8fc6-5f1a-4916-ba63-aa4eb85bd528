<template>
  <common-dialog
    :show-footer="true"
    width="1000px"
    title="富文本"
    :visible.sync="show"
    @open="open"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size="mini"
        :model="form"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="富文本标题" prop="title">
          <el-input
            v-model="form.title"
            type="text"
            placeholder="请输入"
            maxlength="8"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="富文本内容" prop="content">
          <wang-editor
            ref="RichTextContent"
            v-model="form.content"
            :height="500"
            :content.sync="form.content"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { api } from '@/api';

export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      RichTextContent: undefined,
      form: {
        title: '',
        content: '',
        id: ''
      },
      rules: {
        title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
        content: [
          { required: true, message: '请输入富文本内容', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      const url = api['giftGivingOrderConfig'];
      this.$http.get(url).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.form.title = body?.title;
          this.form.id = body?.id;
          this.$nextTick(() => {
            this.form.content = body?.content || '';
            // 如果wang-editor有专门的设置内容方法，可以这样调用
            if (this.$refs.RichTextContent && this.$refs.RichTextContent.setContent) {
              this.$refs.RichTextContent.setContent(body?.content || '');
            }
          });
        }
      });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form));
          const params = {
            ...formData
          };
          const url = api['updateGiftGivingOrderConfig'].replace('{id}', formData.id);
          this.$http.post(url, params, { json: true }).then((res) => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
            }
          });
        }
      });
    },
    close() {
      // 这里必须 call 指向当前的实例
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
      this.show = false;
    }
  }
};
</script>

<style lang="scss">
.inline-block {
  display: inline-block;
}

.group-select {
  margin-right: 14px;
}
</style>
