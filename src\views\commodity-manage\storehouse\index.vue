<template>
  <div>
    <!-- 表单 -->
    <open-packup>
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='商品编码' prop='goodsBaseId'>
          <el-input v-model="form.goodsBaseId" placeholder='请输入商品编码' />
        </el-form-item>

        <el-form-item label='商品名称' prop='goodsName'>
          <el-input v-model="form.goodsName" placeholder='请输入商品名称' />
        </el-form-item>

        <el-form-item label='一级分类' prop='goodsClass'>
          <infinite-selects
            v-model="form.goodsClass"
            placeholder='请选择'
            api-key="getSelectCfList"
            key-name="goodsTypeName"
            value-name='goodsTypeId'
            :param="classParams"
          />
        </el-form-item>

        <el-form-item label='二级分类' prop='goodsTypeId'>
          <infinite-selects
            v-model="form.goodsTypeId"
            placeholder='请选择'
            api-key="getSelectCfList"
            key-name="goodsTypeName"
            value-name='goodsTypeId'
            :param="{
              goodsTypeLevel: '2',
              goodsTypeName: ''
            }"
          />
        </el-form-item>

        <el-form-item label='是否启用' prop='status'>
          <el-select v-model="form.status" clearable placeholder="请选择">
            <el-option
              v-for="item in $localDict['status']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="上传时间" prop="time">
          <el-date-picker
            v-model="form.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label='供应渠道' prop='supplyChannel'>
          <infinite-selects
            v-model="form.supplyChannel"
            placeholder='请选择'
            api-key="getSupplierSelects"
            key-name="channelName"
            value-name='channelId'
            :param="{sName:''}"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" @click="cdVisible=true">二级分类管理</el-button>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <!-- <el-button type="danger" size="small" icon="el-icon-delete" plain>批量删除</el-button> -->
      <el-button type="success" size="small" icon="el-icon-open" plain @click="updateStatus(1)">批量启用</el-button>
      <el-button type="danger" size="small" icon="el-icon-turn-off" plain @click="updateStatus(2)">批量禁用</el-button>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="一级分类" align="center" prop="goodsClassName" />
      <el-table-column label="二级分类" align="center" prop="goodsTypeName" />
      <el-table-column label="供应渠道" align="center" prop="channelName" />
      <!-- <el-table-column label="商品排序" align="center" prop="sort" /> -->
      <el-table-column label="商品编码" align="center" prop="goodsBaseId" />
      <el-table-column label="名称" align="center" prop="goodsName" />
      <el-table-column label="商品图片" align="center" prop="goodsPic" width="200px">
        <template slot-scope="scope">
          <img :src="scope.row.goodsPic | splitOssUrl" style="object-fit: contain;width: 100%;">
        </template>
      </el-table-column>
      <el-table-column label="购买人数" align="center" prop="purchaseNum" />
      <el-table-column label="操作时间" align="center" prop="updateTime" width="150" />
      <el-table-column label="操作人" align="center" prop="updateUserName" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status===1?'success':'danger'">
            {{ scope.row.status | tansformStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <update-dialog
      :id='currentEditId'
      :title="updateDialogTitle"
      :visible.sync="udVisible"
      @refresh-list="getTableList"
    />
    <classification-dialog :visible.sync="cdVisible" />

  </div>
</template>
<script>
import { pagination, TABLE_HEIGHT } from '@/config/constant';
import { ossUri } from '@/config/request';
import openPackup from '@/components/open-packup';
// import updateDialog from './update-dialog';
// import classificationDialog from './classification-dialog';
import { handleDateControl, exportExcel } from '@/utils';
export default {
  components: {
    openPackup,
    updateDialog: resolve => require(['./update-dialog.vue'], resolve),
    classificationDialog: resolve => require(['./classification-dialog'], resolve)
  },
  data() {
    return {
      ossUri: null,
      classParams: {
        goodsTypeLevel: '1',
        goodsTypeName: ''
      },
      selects: [],
      tableLoading: false,
      currentEditId: null,
      form: {
        goodsBaseId: '',
        goodsName: '',
        goodsClass: '',
        goodsTypeId: '',
        status: '',
        time: '',
        supplyChannel: ''
      },
      classification: [],
      selectPage: 0,
      table_height: TABLE_HEIGHT,
      updateDialogTitle: '新增',
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        ...pagination
      },
      udVisible: false,
      cdVisible: false
    };
  },
  mounted() {
    this.ossUri = ossUri;
    this.getTableList();
  },
  methods: {
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportFindBaseList', data);
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      delete formData.time;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        createStartTime: date[0],
        createEndTime: date[1],
        ...this.form
      };
      return data;
    },
    updateStatus(status) {
      if (this.selects.length > 0) {
        const ids = this.selects.map(item => {
          return item.goodsBaseId;
        });
        const data = {
          idStr: ids.join(),
          status: status
        };
        this.$post('updateCommodityStatus', data).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
      } else {
        this.$message.error('请勾选数据');
      }
    },
    handleAdd() {
      this.currentEditId = null;
      this.updateDialogTitle = '新增';
      this.udVisible = true;
    },
    handleEdit(row) {
      this.currentEditId = row.goodsBaseId;
      this.updateDialogTitle = '编辑';
      this.udVisible = true;
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getCommodityLibraryList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          // body.data.forEach(e => {
          //   e.goodsPic = ossUri + e.goodsPic;
          // });
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
