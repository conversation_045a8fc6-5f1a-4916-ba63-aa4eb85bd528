<!-- 测评配置-题目配置 -->
<template>
  <common-dialog is-full title="测评题目配置" :visible.sync="visible" @open="getSubjectList" @close="closeAllBtn">
    <div class="subject">
      <!-- 表单区 -->
      <el-form size="mini" :model="subjects" label-width="100px" class="yz-search-form" @submit.native.prevent="getSubjectList">
        <el-form-item label="题目id:">
          <el-input v-model="subjects.id" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="题干:">
          <el-input v-model="subjects.title" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="启用状态:">
          <el-select v-model="subjects.isEnable" filterable clearable placeholder="请选择">
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox">
        <el-button type="primary" size="small" icon="el-icon-plus" @click="opeStemsBtn">新增测评题目</el-button>
      </div>
      <!-- 表格区 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
      >
        <el-table-column label="题目id" prop="id" align="center" />
        <el-table-column label="题干" prop="title" align="center" />
        <el-table-column label="选项数" prop="optionCount" align="center" />
        <el-table-column label="题目状态" prop="isEnable" align="center">
          <template slot-scope="scope">
            {{ scope.row.isEnable?'启用':'禁用' }}
          </template>
        </el-table-column>
        <el-table-column label="最近修改人" prop="updatesUser" align="center" />
        <el-table-column label="最近修改时间" prop="updatesTime" align="center" />
        <el-table-column label="操作" align="center" width="240">
          <template slot-scope="scope">
            <el-button :type="scope.row.isEnable?'info':'success'" size="small" @click="seeDetails(scope.row)">
              {{ scope.row.isEnable?'禁用':'启用' }}
            </el-button>
            <!-- 回显当前id的配置信息，全部项可进行更改，调用新增接口 -->
            <el-button type="primary" size="small" @click="opeStemsBtn(scope.row)">复制配置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination :total="subjects.total" :page.sync="subjects.page" :limit.sync="subjects.rows" @pagination="getSubjectList" />
      </div>
      <!-- 新增测评题目 -->
      <common-dialog width="55%" :visible.sync="showStems" :showFooter="true" @close="closeStemsBtn" @confirm="confirmStemsBtn">
        <el-form ref="addStemsFrom" class="stem_stems-main" size="mini" :model="addStemsFrom">
          <div class="main-title">
            <el-form-item label="题目标题" label-width="100px" prop="title" class="main-input" :rules="addStemsRule.content">
              <el-input v-model="addStemsFrom.title" maxlength="50" show-word-limit placeholder="请输入" clearable />
            </el-form-item>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addTargetBtn">新增选项</el-button>
          </div>
          <div v-for="(item,index) in addStemsFrom.optionList" :key="index" class="main-items">
            <el-form-item class="main-input" :label="`选项${index+1}`" label-width="100px" :prop="`optionList.${index}.content`" :rules="addStemsRule.content">
              <el-input v-model="item.content" maxlength="30" show-word-limit placeholder="请输入" clearable />
            </el-form-item>
            <el-button type="danger" size="small" icon="el-icon-delete" @click="delTargetBtn(index)">删除按钮</el-button>
          </div>
          <p class="main-desc">备注：测评题创建后，无法修改，请谨慎填写!</p>
        </el-form>
      </common-dialog>
    </div>
  </common-dialog>
</template>

<script>
import { getCutDay } from '@/utils';

export default {
  props: {
    visible: { type: Boolean, default: false },
    isstemsAuthority: { type: Boolean, default: false }
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      subjects: {
        id: '',
        title: '',
        isEnable: '',
        total: 10,
        page: 1,
        rows: 10
      },
      showStems: false,
      addStemsFrom: {
        type: '2',
        title: '',
        optionList: [{ content: '' }, { content: '' }]
      },
      addStemsRule: {
        title: [{ required: true, message: '请输入', trigger: 'blur' }],
        content: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    };
  },
  methods: {
    // 打开新增-修改结果弹窗
    opeStemsBtn(row) {
      // 判断是否有操作权限
      // if (!this.isstemsAuthority) {
      //   this.$message.error('您当前没有操作权限');
      //   return false;
      // }
      console.log('打开新增-row', row);
      this.addStemsFrom = {
        type: '2',
        title: row.title || '',
        optionList: row?.optionList ? row.optionList : [{ content: '' }, { content: '' }]
      };
      console.log('打开新增-addStemsFrom', this.addStemsFrom);
      this.showStems = true;
    },
    // 新增题目选项
    addTargetBtn() {
      if (this.addStemsFrom.optionList.length >= 25) {
        this.$message.warning('新增失败，最多配置25个选项！');
        return;
      }
      this.addStemsFrom.optionList.push({ content: '' });
    },
    // 删除题目选项
    delTargetBtn(ins) {
      if (this.addStemsFrom.optionList.length <= 2) {
        this.$message.error('删除失败，最少配置2个选项！');
        return false;
      }
      this.addStemsFrom.optionList.splice(ins, 1);
    },
    // 确认-新增测评题目
    confirmStemsBtn() {
      this.$refs['addStemsFrom'].validate((valid) => {
        if (valid) {
          console.log('新增测评题目-news', this.addStemsFrom);
          this.$post('saveTopic', this.addStemsFrom, { json: true }).then((res) => {
            console.log('禁用-启用-res', res);
            if (res.code === '00') {
              this.$message({ message: '新增成功', type: 'success' });
              this.closeStemsBtn();
              this.getSubjectList();
            } else {
              this.$message.error('新增失败');
            }
            this.tableLoading = false;
          }).catch(() => {
            this.$message.error('新增失败');
            this.tableLoading = false;
          });
        }
      });
    },
    // 关闭-新增测评题目
    closeStemsBtn() {
      console.log('关闭-新增测评题目', this.addStemsFrom);
      this.showStems = false;
      this.addStemsFrom = {
        type: '2',
        title: '',
        optionList: [{ content: '' }, { content: '' }]
      };
      this.$refs['addStemsFrom'].resetFields();
    },
    // 禁用-启用
    seeDetails(rows) {
      // 判断是否有操作权限
      // if (!this.isstemsAuthority) {
      //   this.$message.error('您当前没有操作权限');
      //   return false;
      // }
      // 当前状态是禁用，可以直接启用
      if (!rows.isEnable) {
        this.showBoxTips(rows);
        return false;
      }
      // 当前状态是启用，需要确认才可变为禁用
      this.$confirm('请确认禁用后，测评配置将无法选取该题目（历史已配置的无影响）', '请确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'none'
      }).then(() => {
        this.tableLoading = true;
        this.showBoxTips(rows);
      }).catch(() => {
        this.$message({ type: 'info', message: '已取消删除!' });
      });
    },
    showBoxTips({ id, isEnable }) {
      this.$post('disableTopicById', { id, isEnable: isEnable ? 0 : 1 }).then((res) => {
        console.log('禁用-启用-res', res);
        if (res.code === '00') {
          this.$message({ message: '操作成功', type: 'success' });
          this.getSubjectList();
        } else {
          this.$message.error('操作失败');
        }
        this.tableLoading = false;
      }).catch(() => {
        this.$message.error('操作失败');
        this.tableLoading = false;
      });
    },
    // 接口获取测评结果配置列表
    getSubjectList() {
      this.tableLoading = true;
      this.$post('getTopicList', this.subjects).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          this.tableData = body?.data?.map(item => {
            item.isEnable = Number(item?.isEnable || 0);
            item.updatesUser = item.createUser || item.updateUser || '';
            item.updateTime = item.updateTime && getCutDay(item.updateTime);
            item.createTime = item.createTime && getCutDay(item.createTime);
            item.updatesTime = item.createTime || item.updateTime || '';
            item.optionList = null;
            if (item?.options?.length) {
              item.optionList = item.options.map(opem => {
                return { content: opem.content || '' };
              });
            }
            return item;
          });
          this.subjects.total = body.recordsTotal;
        }
        this.tableLoading = false;
      }).catch(() => {
        this.tableLoading = false;
      });
    },
    // 关闭当前弹窗
    closeAllBtn() {
      this.$emit('on-close');
    }
  }
};
</script>

<style lang="scss" scoped>
.subject {
  margin: 30px;
  // height: calc(100% - 60px);
  .search-item {
    margin-right: 40px;
  }
  .subject-img {
    width: 120px;
    height: 60px;
  }
  .subject-tag {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    .subject-li {
      margin: 0 4px 10px;
      padding: 0 4px;
      min-width: 40px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      border: 1px solid #cecece;
      border-radius: 4px;
    }
  }
  .yz-table-btnbox {
    margin: 20px 0;
  }
}
.subject::-webkit-scrollbar {
  display: none;
}
.el-message-box__btns {
  text-align: center;
  .el-button--default {
    width: 30%;
  }
}
.stem_stems-main {
  margin: 30px;
  height: 480px;
  overflow: hidden;
  overflow-y: scroll;
  .main-title {
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .main-input {
      width: 70%;
      margin-bottom: 0px;
    }
  }
  .el-form-item {
    margin-bottom: 0px;
  }
  .main-items {
    margin-bottom: 20px;
    width: 70%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .main-input {
      width: 80%;
      margin-bottom: 0px;
    }
  }
  .main-desc {
    margin: 40px 30px 0;
    color: #fd8383;
    font-size: 14px;
  }
}
.yz-common-dialog__footer {
  text-align: center !important;
}
.eval-inputs {
  width: 100%;
  .el-input__inner {
    text-align: left;
  }
}
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
  background-color: transparent;
}
</style>
