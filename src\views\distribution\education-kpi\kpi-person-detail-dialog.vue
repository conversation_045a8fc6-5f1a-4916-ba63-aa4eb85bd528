<template>
  <common-dialog
    isFull
    :title="empName + '职业教育月度绩效明细' + month"
    width="1000px"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <list-component :month="month" :empId="empId" />
  </common-dialog>
</template>

<script>
import listComponent from './list-component';
export default {
  components: {
    listComponent
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    month: {
      type: String,
      default: ''
    },
    empName: {
      type: String,
      default: ''
    },
    empId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
