
import axios from '@/utils/axios';
import { Message } from 'element-ui';
import moment from 'moment';

/**
 * 下载文件到本地
 *
 * @param {Object} params - 参数对象
 * @param {Object} params.response - HTTP响应对象，用于从响应头获取文件名
 * @param {string} [params.name] - 可选的文件名，如果提供将覆盖从HTTP响应头获取的文件名
 * @throws {Error} 当既没有提供文件名也没有有效的HTTP响应数据时抛出错误
 */
export function downFile({ response, name }) {
  let fileName = '';
  if (name) {
    // 如果传入了文件名，则使用传入的文件名
    const formattedTime = moment().format('YYYYMMDD_HH.mm.ss');
    fileName = `${name}${formattedTime}.xlsx`;
  } else {
    // 如果没有传入文件名，则从响应头获取文件名
    const disposition = response.headers['content-disposition'];
    fileName = '导出.xlsx'; // 默认文件名

    if (disposition && disposition.indexOf('attachment') !== -1) {
    // 使用正则提取文件名
      const matches = disposition.match(/filename\*?=[\s]*utf-8''([^;]+)/);
      if (matches && matches[1]) {
      // 解码文件名并去掉URL编码
        fileName = decodeURIComponent(matches[1]);
      }
    }
  }

  const paths = window.URL.createObjectURL(new Blob([response.data]));
  const a = document.createElement('a');
  a.href = paths;
  a.download = `${fileName}`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(paths);
  Message.success('下载完成!');
}

/**
 * 使用 HTTP GET 请求下载文件
 *
 * @param {Object} params - 参数对象
 * @param {string} params.url - 文件的 URL 地址
 * @param {string} [params.name] - 可选的文件名，默认为 undefined
 */
export function httpGetDownFile({ url, name }) {
  axios.get(url, { responseType: 'blob', isBinary: 1, json: true }).then(res => {
    const { fail, response } = res;
    if (fail) {
      downFile({ response, name });
    } else {
      Message.error('下载错误!');
    }
  });
}

/**
 * 使用 HTTP POST 请求下载文件
 *
 * @param {Object} params - 参数对象
 * @param {string} params.urs - 文件的 URL 地址
 * @param {Object} params.params - POST 请求的参数
 * @param {string} [params.name] - 可选的文件名，默认为 undefined
 */
export function httpPostDownFile({ url, params, name }) {
  axios.post(url, params, { responseType: 'arraybuffer', isBinary: 1, json: true }).then(res => {
    const { fail, response } = res;
    if (fail) {
      downFile({ response, name });
    } else {
      Message.error('下载错误!');
    }
  });
}

