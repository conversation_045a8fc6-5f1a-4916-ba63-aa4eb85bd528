// 本地字典
export default {
  // 会员卡类型
  mebFreeType: [
    { dictName: '免费体验卡', dictValue: 'free' },
    { dictName: '付费卡', dictValue: 'pay' }
  ],
  // 会员卡种类
  mebPayType: [
    { dictName: '年卡', dictValue: 'year' },
    { dictName: '季卡', dictValue: 'season' },
    { dictName: '月卡', dictValue: 'mouth' },
    { dictName: '周卡', dictValue: 'week' }
  ],
  // 抵扣方式
  deductType: [
    { dictName: '智米', dictValue: '1' },
    { dictName: '滞留金', dictValue: '2' }
  ],
  // 抵扣方式
  refundType: [
    { dictName: '现金', dictValue: '1' },
    { dictName: '智米', dictValue: '2' },
    { dictName: '滞留金', dictValue: '3' }
  ],
  // 数据状态
  status: [
    { dictName: '启用', dictValue: '1' },
    { dictName: '禁用', dictValue: '2' }
  ],
  // 上课方式
  courseType: [
    { dictName: '直播', dictValue: '0' },
    { dictName: '录播', dictValue: '1' },
    { dictName: '硬盘推流', dictValue: '2' },
    { dictName: '回放', dictValue: '3' }
  ],
  // 上架类型
  ShelfType: [
    { dictName: '单个套餐', dictValue: '1' },
    { dictName: '组合套餐', dictValue: '2' }
  ],
  // 购买渠道
  platform: [
    { dictName: '微信', dictValue: 'WECHAT' },
    { dictName: 'APP', dictValue: 'APP' }
  ],
  // 赠送类型
  giveType: [
    { dictName: '课程', dictValue: 'goods' },
    { dictName: '会员卡', dictValue: 'vip' },
    { dictName: '优惠券', dictValue: 'coupon' }
  ],
  // 课程渠道
  courseChannelCode: [
    { dictName: '远智教育', dictValue: 'YZ' },
    { dictName: '课火网', dictValue: 'KHW' },
    { dictName: '课师宝', dictValue: 'KSB' },
    { dictName: '易智教育', dictValue: 'YIZHI' }
  ],
  // 广告投放渠道
  adChannel: [
    { dictName: '字节渠道', dictValue: 'BYTE_DANCE' },
    { dictName: '微信渠道', dictValue: 'WECHAT' }
  ]
};

/**
 * 三大社团
 */
// 社团类型映射 [1:跑步 2:读书 3:公益]
export const ClubTypeMap = { run: 1, read: 2, public: 3 };

export const ClubType = [
  { dictName: '跑步', dictValue: ClubTypeMap.run },
  { dictName: '读书', dictValue: ClubTypeMap.read },
  { dictName: '公益', dictValue: ClubTypeMap.public }
];

// 社团类型
export const ClubsActType = [
  { dictName: '跑团', dictValue: 1 },
  { dictName: '读书会', dictValue: 2 }
];

// 组建类型
export const ClubsActBuildType = [
  { dictName: '官方', dictValue: 1 },
  { dictName: '老师组建', dictValue: 2 },
  { dictName: '学员自建', dictValue: 3 }
];

// 标签分类
export const TagCategory = [
  { dictName: '习惯', dictValue: 'HABIT' },
  { dictName: '上进活动', dictValue: 'UP_ACTIVITY' },
  { dictName: '公益帖子', dictValue: 'PUBLIC_BENEFIT_ARTICLE' }
];

/** 业务类型，1:今日热议 2:上进导师，对应字典：moduleType */
export const DModuleType = [
  { dictName: '今日热议', dictValue: 1 },
  { dictName: '上进导师', dictValue: 2 }
];

/** 话题类型 [1:官方 2:学员自建 3:自考话题 4:成教话题 5:国开话题 6:全日制话题 7:研究生话题 8:新年活动话题] 与字典表的 topicType 关联 */
export const DTopicType = [
  { dictName: '官方', dictValue: 1 },
  { dictName: '学员自建', dictValue: 2 },
  { dictName: '自考话题', dictValue: 3 },
  { dictName: '成教话题', dictValue: 4 },
  { dictName: '国开话题', dictValue: 5 },
  { dictName: '全日制话题', dictValue: 6 },
  { dictName: '研究生话题', dictValue: 7 },
  { dictName: '新年活动话题', dictValue: 8 }
];

/** 打卡类型 [2:读书 3:跑步 4:其他 5:公益] 对应字典表的 markTaskType */
export const DMarkTaskType = [
  { dictName: '读书', dictValue: 2 },
  { dictName: '跑步', dictValue: 3 },
  { dictName: '其他', dictValue: 4 },
  { dictName: '公益', dictValue: 5 }
];

/**  渠道 1:远智教育APP  2:上进青年网 */
export const DIrrigationDitch = [
  { dictName: '远智教育APP', dictValue: 1 },
  { dictName: '上进青年网', dictValue: 2 }
];

/** 社团审核状态映射 */
export const DClubAuditStatusMap = { approval: 0, agree: 1, reject: 2 };

/** 社团审核状态：0:待审批 1:已同意2:已拒绝 */
export const DClubAuditStatus = [
  { dictName: '待审批', dictValue: DClubAuditStatusMap.approval },
  { dictName: '已同意', dictValue: DClubAuditStatusMap.agree },
  { dictName: '已拒绝', dictValue: DClubAuditStatusMap.reject }
];

/* 申请人身份 (1:学生  2:老师) */
export const DApplyIdentity = [
  { dictName: '学生', dictValue: '1' },
  { dictName: '老师', dictValue: '2' }
];
