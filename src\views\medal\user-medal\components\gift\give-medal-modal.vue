<template>
  <common-dialog
    class="common-dialog"
    width="60%"
    title="新增勋章赠送"
    :visible.sync="show"
    :show-footer="true"
    :confirmLoading="confirmLoading"
    @open="open"
    @close="close"
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        :rules="rules"
        label-width="170px"
        size="small"
        label-suffix=":"
      >
        <el-form-item
          label='赠送勋章'
          prop='medalTimeId'
        >
          <yz-select
            v-model="form.medalTimeId"
            url="/bmsAdmin/medalTimeExportList.do"
            method="post"
            :props="{
              label: 'name',
              value: 'medalTimeId',
              query: 'name'
            }"
            @change="medalSelectChange"
          />
        </el-form-item>
        <template v-if="form.medalTimeId">
          <el-form-item label="勋章类型">
            <span>{{ medalObj.medalType | medalTypeEnum }}</span>
          </el-form-item>
          <el-form-item label="勋章id">
            <span>{{ medalObj.medalTimeId }}</span>
          </el-form-item>
          <el-form-item label="勋章名称">
            <span>{{ medalObj.name }}</span>
          </el-form-item>
        </template>
        <el-form-item label="赠送用户" prop="sendType">
          <el-radio-group v-model="form.sendType">
            <el-radio :label="1">筛选用户</el-radio>
            <el-radio :label="2">导入用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.sendType === 1" label="筛选用户" prop="userSelectList">
          <el-button type="primary" size="mini" @click="selectUserVisible = true">选择用户</el-button>
          <span class="ml10">已选择{{ form.userSelectList.length }}个用户</span>
        </el-form-item>
        <template v-if="form.sendType === 2">
          <el-form-item label="导入模板">
            <a :href="templateUrl" download>
              <el-button type="primary" plain>下载模板</el-button>
            </a>
          </el-form-item>
          <el-form-item
            label="选择文件"
            prop="fileList"
          >
            <el-upload
              ref="upload"
              class="upload-demo"
              accept=".xlsx,.xls,.csv"
              drag
              :action="action"
              :data="extraData"
              :on-change="handleChange"
              :on-exceed="handleExceed"
              :on-success="uploadSuccess"
              :on-remove="handleRemove"
              :name="field"
              :file-list="form.fileList"
              multiple
              :limit="1"
              :auto-upload="false"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
            </el-upload>
          </el-form-item>
        </template>
        <el-form-item v-if="form.sendType === 1" label="勋章获得日期" prop="gainTime">
          <el-date-picker
            v-model="form.gainTime"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择年月日"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item label="赠送勋章备注">
          <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入赠送勋章备注" maxlength="100" show-word-limit clearable />
        </el-form-item>
      </el-form>
      <selectUserModal :visible.sync="selectUserVisible" :tablePropData="form.userSelectList" @confirm="updateTableData" />
    </div>
  </common-dialog>
</template>

<script>
import { arrToEnum } from '@/utils';
import { medalTypeList } from './../../../type.js';
import selectUserModal from './select-user-modal.vue';
const medalTypeEnum = arrToEnum(medalTypeList, 'value', 'name');
export default {
  components: {
    selectUserModal
  },
  filters: {
    medalTypeEnum(val) {
      return medalTypeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      templateUrl: 'https://static.yzou.cn/excel/template/UserMedalTimeExportUrl.xlsx',
      field: 'file',
      confirmLoading: false,
      selectUserVisible: false,
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() > Date.now();
        }
      },
      action: '/bmsAdmin/medalTimeExcelExport.do',
      fileList: [], // 选择的文件
      medalObj: {}, // 选择的勋章
      show: false,
      extraData: {
        remark: undefined,
        medalTimeId: undefined
      },
      form: {
        medalTimeId: '',
        gainTime: undefined,
        fileList: [],
        userSelectList: []
      },
      rules: {
        medalTimeId: [{ required: true, message: '请选择赠送勋章', trigger: 'change' }],
        sendType: [{ required: true, message: '请选择赠送用户', trigger: 'change' }],
        userSelectList: [{ required: true, message: '请选择用户', trigger: 'change' }],
        gainTime: [{ required: true, message: '请选择勋章获得日期', trigger: 'change' }],
        fileList: [{ required: true, message: '请选择文件', trigger: 'change' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 更新表格数据
    updateTableData(data) {
      this.form.userSelectList = data;
    },
    // 勋章改变
    medalSelectChange({ value, label, source }) {
      this.medalObj = source;
    },
    // 上传文件
    handleChange(file, fileList) {
      this.form.fileList = fileList;
    },
    // 移除文件
    handleRemove(file, fileList) {
      this.form.fileList = fileList;
    },
    // 上传成功
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        this.confirmLoading = false;
        this.show = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('refresh');
      } else {
        this.confirmLoading = false;
        const h = this.$createElement;
        this.$alert(h('div', { style: 'max-height: 60vh;overflow-y: auto;' }, [
          h('p', { domProps: { innerHTML: response.body }})
        ]), response.msg, {
          dangerouslyUseHTMLString: true, // 允许渲染 HTML
          showClose: false
        });
      }
    },
    // 上传文件超出限制
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    // 打开弹框
    open() {},
    // 关闭弹框
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 提交
    submit() {
      this.$refs.formModal.validate((valid) => {
        if (!valid) return false;

        this.$confirm('确定导入？导入后不可取消！请谨慎操作。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.confirmLoading = true;

          if (this.form.sendType === 1) {
            const form = {
              medalTimeId: this.form.medalTimeId,
              gainTime: this.form.gainTime,
              remark: this.form.remark,
              infoList: this.form.userSelectList.map(item => {
                return {
                  realName: item.realName,
                  yzCode: item.yzCode
                };
              })
            };
            this.$post('medalTimeExport', form, { json: true }).then(res => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '导入成功',
                  type: 'success'
                });
                this.$parent.getTableList();
              }
            });
          } else {
            this.extraData.remark = this.form.remark;
            this.extraData.medalTimeId = this.form.medalTimeId;
            this.$refs.upload.submit();
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
