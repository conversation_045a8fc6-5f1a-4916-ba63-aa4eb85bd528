<template>
  <div>
    <!-- 表单 -->
    <open-packup default-open>
      <el-form
        ref="searchForm"
        class="yz-search-form"
        size="mini"
        :model="form"
        label-width="120px"
        @submit.native.prevent="search"
      >
        <el-form-item label="购买人姓名" prop="buyUserName">
          <el-input v-model="form.buyUserName" placeholder="请输入购买人姓名" />
        </el-form-item>
        <el-form-item label="远智编码" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label="手机号" prop="buyUserMobile">
          <el-input v-model="form.buyUserMobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="商品名称" prop="distributionGoodsName">
          <el-input
            v-model="form.distributionGoodsName"
            placeholder="请输入商品名称"
          />

          <!-- <infinite-selects
            v-model="form.distributionGoodsName"
            apiKey="getEduDistributionList"
            key-name="distributionGoodName"
            value-name="distributionGoodName"
            filterable
            filter-field="distributionGoodsName"
            multiple
            ref="goodsSelect"
          /> -->
        </el-form-item>
        <el-form-item label="订单状态" prop="orderState">
          <el-select v-model="form.orderState" clearable>
            <el-option label="已支付" value="pay" />
            <el-option label="已退款" value="refund" />
          </el-select>
        </el-form-item>
        <el-form-item label="付费时间" prop="payTime">
          <el-date-picker
            v-model="form.payTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <el-form-item label="退费时间" prop="refundTime">
          <el-date-picker
            v-model="form.refundTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <el-form-item
          v-for="item in selectOptions"
          :key="item.label"
          :label="item.label"
          :prop="item.prop"
        >
          <infinite-selects
            v-model="form[item.prop]"
            :apiKey="item.apiKey"
            value-name="name"
            filterable
            is-request-json
            ref="userSelects"
            @options-loaded="handleOptionsLoaded($event, item.prop)"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <div class="search-reset-box">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
    </open-packup>
    <!-- 按钮区 -->
    <!-- <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain :style="{ opacity: isBtn ? 1 : 0 }" @click="exportData">导出</el-button>
    </div> -->
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
      :data="tableData"
    >
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="buyUserName" label="购买人姓名" align="center" />
      <el-table-column prop="buyUserMobile" label="手机号码" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.buyUserMobile }}</div>
          <el-button
            size="mini"
            v-if="scope.row.myStudent"
            type="primary"
            plain
            @click="getCompleteMobile(scope.row.buyUserId, 'getEduUserMobile')"
            >查看</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="distributionGoodsName"
        label="商品名称"
        align="center"
      />
      <el-table-column
        prop="distributionGoodsId"
        label="商品id"
        align="center"
      />

      <el-table-column prop="orderNo" label="订单号" align="center" />
      <el-table-column prop="orderState" label="订单状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.orderState === "pay" ? "已支付" : "已退款" }}
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="付费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.payTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="refundTime" label="退费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" label="应付金额" align="center" />
      <el-table-column prop="payAmount" label="实缴金额" align="center" />
      <el-table-column prop="test" label="抵扣" width="120px" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.couponScale">
            优惠券抵扣: {{ scope.row.couponScale }}
          </p>
          <p v-if="scope.row.demurrageScale">
            滞留金抵扣: {{ scope.row.demurrageScale }}
          </p>
          <p v-if="scope.row.zmScale">智米抵扣: {{ scope.row.zmScale }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="invitationEmpName" label="邀约人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.invitationEmpName }}</span>
          <el-tag
            v-if="scope.row.invitationEmpStatus === '2'"
            type="info"
            size="mini"
            >离</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column prop="distributionEmpName" label="成交人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.distributionEmpName }}</span>
          <el-tag
            v-if="scope.row.distributionEmpStatus === '2'"
            type="info"
            size="mini"
            >离</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center">
        <template slot-scope="scope">
          <p class="remark-text">{{ scope.row.remark }}</p>
          <p>{{ scope.row.remarkTime | transformTimeStamp }}</p>
          <el-button type="text" @click="editMark(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <add-mark-dialog
      :visible.sync="markVisible"
      :distributionOrderNo="distributionOrderNo"
      :remark="remark"
      @getTableList="getTableList"
    />
  </div>
</template>
<script>
import { exportExcel, handleDateControl } from "@/utils";
// import batchImportDialog from './batch-import-dialog';
import LookMobile from "@/mixins/LookMobile";
import addMarkDialog from "./add-mark-dialog";
import InfiniteSelects from "@/components/InfiniteSelects";
import { mapGetters } from "vuex";

export default {
  components: {
    // batchImportDialog
    addMarkDialog, // 添加备注对话框组件
    InfiniteSelects,
  },
  filters: {},
  mixins: [LookMobile], // 引入查看手机号码混入
  props: {
    orderParams: {
      type: String,
      default: "",
      // 来自父组件的订单参数，用于筛选特定商品名称
    },
    empId: {
      type: String,
      default: "",
      // 员工ID，用于按照特定员工筛选订单
    },
    isBtn: {
      type: Boolean,
      default: true,
      // 控制导出按钮是否显示
    },
  },
  computed: {},
  data() {
    return {
      tableLoading: false, // 表格加载状态
      markVisible: false, // 备注弹窗显示状态
      selectOptions: [
        {
          label: "邀约人",
          prop: "invitationUserName",
          apiKey: "getEduUserList",
        },
        {
          label: "邀约人部门",
          prop: "invitationUserDepartment",
          apiKey: "getEduDepartmentList",
        },
        {
          label: "成交人",
          prop: "distributionUserName",
          apiKey: "getEduUserList",
        },
        {
          label: "成交人部门",
          prop: "distributionUserDepartment",
          apiKey: "getEduDepartmentList",
        },
      ],
      form: {
        distributionGoodsName: "", // 商品名称
        orderState: "", // 订单状态（已支付/已退款）
        distributionUserName: "", // 成交人姓名
        buyUserName: "", // 购买人姓名
        distributionMobile: "", // 成交人手机号
        buyUserMobile: "", // 购买人手机号
        payTime: "", // 付费时间范围
        yzCode: "", // 远智编码
        invitationUserName: "", // 邀约人姓名
        refundTime: "", // 退费时间范围
        invitationMobile: "", // 邀约人手机号
      },
      distributionOrderNo: "", // 当前操作的订单编号
      remark: "", // 订单备注
      tableData: [], // 表格数据
      pagination: {
        page: 1, // 当前页码
        total: 0, // 总记录数
        limit: 10, // 每页记录数
      },
    };
  },
  watch: {
    // 监听订单参数变化，更新表单中的商品名称并重新获取列表
    orderParams(val) {
      console.log(val);
      this.form.distributionGoodsName = val || "";
      this.getTableList();
    },
    // 监听员工ID变化，重新获取列表
    empId() {
      this.getTableList();
    },
    immediate: true,
  },
  mounted() {},
  methods: {
    /**
     * 处理选择器选项加载完成事件
     * @param {Array} options - 加载的选项数据
     * @param {String} prop - 当前选择器的属性名
     */
    handleOptionsLoaded(options, prop) {
      // 如果是成交人选择器且有数据
      if (prop === "distributionUserName" && options && options.length > 0) {
        // 默认选择第一个选项
        this.form[prop] = options[0].name;
        this.getTableList();
      }
    },

    /**
     * 处理查询参数
     * 将表单数据转换为API所需的格式，处理日期范围等
     * @returns {Object} 处理后的查询参数对象
     */
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      // 处理付费时间范围
      const date = handleDateControl(formData.payTime);
      formData.payStartDate = date[0];
      formData.payEndDate = date[1];
      // 处理退费时间范围
      const refundTime = handleDateControl(formData.refundTime);
      formData.refundStartDate = refundTime[0];
      formData.refundEndDate = refundTime[1];
      // 删除原始日期范围字段
      delete formData.payTime;
      delete formData.refundTime;
      // 添加员工ID筛选条件（如果存在）
      if (this.empId) {
        formData.empId = this.empId;
      }
      // 构建最终请求参数，添加分页信息
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
      };
      return data;
    },

    /**
     * 导出数据到Excel
     * 调用exportExcel工具函数将当前查询结果导出
     */
    exportData() {
      const data = this.handleQueryParams();
      exportExcel("exportOrder", data);
    },

    /**
     * 获取列表数据
     * 设置加载状态，获取查询参数，发送API请求，更新表格数据
     */
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post("getEduStudentList", data, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data; // 更新表格数据
          this.pagination.total = body.recordsTotal; // 更新总记录数
        }
      });
    },

    /**
     * 搜索/重置查询
     * @param {number} type - 0表示重置，其他值表示搜索
     */
    search(type) {
      if (type === 0) {
        // 重置表单
        this.$refs["searchForm"].resetFields();
        console.log(this.$data);
        this.form = Object.assign({}, this.$options.data.form);
      } else {
        // 搜索操作，重置页码并获取列表
        this.pagination.page = 1;
        this.getTableList();
      }
    },

    /**
     * 编辑订单备注
     * @param {Object} rowData - 当前行数据
     * 打开备注编辑弹窗，设置当前订单号和备注内容
     */
    editMark(rowData) {
      console.log(rowData);
      const { remark, distributionOrderNo } = rowData;
      this.distributionOrderNo = distributionOrderNo;
      this.remark = remark;
      this.markVisible = !this.markVisible;
    },
  },
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top: 16px;
}
.remark-text {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
