<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>

      <el-form-item label='姓名' prop='userName'>
        <el-input v-model="form.userName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='手机' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='赠送类型' prop='giveType'>
        <el-select v-model="form.giveType" filterable placeholder="请选择">
          <el-option
            v-for="item in $localDict['giveType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='赠送状态' prop='giveStatus'>
        <el-select v-model="form.giveStatus" filterable placeholder="请选择">
          <el-option label="未赠送" :value="1" />
          <el-option label="已赠送" :value="2" />
          <el-option label="赠送异常" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label='赠送名称' prop='mappingName'>
        <el-input v-model="form.mappingName" placeholder="请输入" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button
        type="success"
        size="small"
        plain
        @click="bidVisibel = true"
      >批量导入赠送</el-button>
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="adVisibel = true"
      >新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="userName" label="赠送对象" align="center" />
      <el-table-column prop="mobile" width="150px" label="手机" align="center" />
      <el-table-column prop="giveType" width="100px" label="赠送类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.giveType | giveType }}
        </template>
      </el-table-column>
      <el-table-column prop="mappingId" label="赠送商品ID" align="center" />
      <el-table-column prop="mappingName" label="赠送名称" align="center" />
      <el-table-column prop="giveStatus" label="赠送状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.giveStatus | giveStatus }}
        </template>
      </el-table-column>
      <el-table-column
        prop="describe"
        label="异常信息"
        show-overflow-tooltip
        align="center"
      />
      <el-table-column prop="giveTime" label="赠送时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.giveTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" />

      <el-table-column prop="createUserName" width="100px" label="操作人" align="center" />
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEditRemark(scope.row)">修改备注</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <addDialog :visible.sync="adVisibel" @refresh="getTableList" />
    <batch-import-dialog :visible.sync="bidVisibel" @refresh="getTableList" />

    <el-dialog
      title="修改备注"
      :visible.sync="dialogVisible"
      width="500px"
    >
      <el-input
        v-model="textarea"
        type="textarea"
        :rows="4"
        maxlength="100"
        show-word-limit
        placeholder="请输入内容"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRemark">确 定</el-button>
      </span>
    </el-dialog>

  </div>
</template>
<script>
import addDialog from './add-dialog';
import batchImportDialog from './batch-import-dialog';
export default {
  components: {
    addDialog,
    batchImportDialog
  },
  filters: {
    giveType(val) {
      if (!val) return;
      const data = {
        goods: '课程',
        vip: '会员卡',
        coupon: '优惠券'
      };
      return data[val];
    },
    giveStatus(val) {
      if (!val) return;
      const data = {
        1: '未赠送',
        2: '已赠送',
        3: '赠送异常'
      };
      return data[val];
    }
  },
  data() {
    return {
      tableLoading: false,
      adVisibel: false,
      bidVisibel: false,
      dialogVisible: false,
      form: {
        mappingName: '',
        mobile: '',
        userName: '',
        giveType: '',
        giveStatus: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      textarea: '',
      currentRow: null
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    handleEditRemark(row) {
      this.currentRow = row;
      this.textarea = this.currentRow.remark;
      this.dialogVisible = true;
    },
    submitRemark() {
      const parmas = {
        giveId: this.currentRow.giveId,
        remark: this.textarea
      };
      this.$post('updateGiveRemark', parmas)
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.dialogVisible = false;
            this.getTableList();
          }
        });
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getGiveList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:20px;
}
</style>
