<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='90px'
      @submit.native.prevent='onSearch'
    >

      <el-form-item label='内容大类' prop='tagCategory'>
        <el-select v-model="form.tagCategory" placeholder="请选择内容大类" clearable>
          <el-option v-for="item in tagOpts" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
        </el-select>
      </el-form-item>

      <el-form-item label='内容分类' prop='tagName'>
        <el-input v-model="form.tagName" placeholder="请输入内容分类" clearable />
      </el-form-item>

      <el-form-item label='状态' prop='status'>
        <el-select v-model="form.status" placeholder="请选择状态" clearable>
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch("reset")' />
      </div>

    </el-form>

    <div class="table-tools">
      <el-button type="primary" size="mini" @click="onEdit()">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="tagCategory" label="内容大类" align="center" :formatter="handleTagCategory" />

      <el-table-column prop="tagName" label="内容分类" align="center" />
      <el-table-column prop="status" label="状态" align="center" :formatter="handleStatus" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)"> 编辑 </el-button>
          <el-button type="text" size="small" @click="onStatus(row)"> {{ row.status === 1 ? '禁用' : '启用' }} </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div>
    <!-- 新增|编辑弹框 -->
    <dialog-operation :visible.sync="operationVisible" :row="editRow" @confirm="getData" />
  </div>
</template>

<script>
import DialogOperation from './components/dialog-operation.vue';
import { TagCategory } from '@/dict';

export default {
  components: { DialogOperation },
  data() {
    return {
      tagOpts: TagCategory,
      tableLoading: true,
      operationVisible: false,
      editRow: null,
      tableData: [],
      form: {
        tagCategory: '',
        tagName: '',
        status: undefined
      },
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/businessTagCategoryConfig/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 处理内容大类显示
    handleTagCategory(row) {
      return this.tagOpts.find((v) => v.dictValue === row.tagCategory)?.dictName;
    },
    // 处理状态显示
    handleStatus(row) {
      return row.status === 1 ? '启用' : '禁用';
    },
    // btn 搜索 | 重置
    onSearch(type) {
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 新增|编辑
    onEdit(row) {
      this.editRow = row;
      this.operationVisible = true;
    },
    // btn 修改状态
    onStatus(row) {
      this.$confirm(`您确定${row.status === 1 ? '禁用' : '启用'}此内容分类？`, '提示', {
        type: 'warning',
        confirmButtonText: '是',
        cancelButtonText: '否'
      }).then(async(e) => {
        if (e !== 'confirm') return;
        this.tableLoading = true;
        const { code } = await this.$http({
          method: 'post',
          url: `/businessTagCategoryConfig/updateStatus/${row.id}/${row.status === 1 ? 0 : 1}`
        });
        this.tableLoading = false;
        if (code !== '00') return;
        this.$message.success(`${row.status === 1 ? '禁用' : '启用'}成功`);
        this.getData();
      }).catch(() => {
        this.tableLoading = false;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}
</style>
