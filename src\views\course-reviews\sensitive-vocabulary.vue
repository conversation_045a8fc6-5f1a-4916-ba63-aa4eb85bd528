<template>
  <common-dialog
    width="800px"
    title="敏感词"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        :inline="true"
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='敏感词' prop='queryWord'>
          <el-input v-model="form.queryWord" placeholder="请输入" />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </el-form-item>

      </el-form>

      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" @click="childDialog=true">添加</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        class="dialog-table"
        border
        size="small"
        style="width: 100%"
        height="480px"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="word" label="敏感词" />
        <el-table-column label="操作" width="150" align="center">
          <template slot-scope="scope">
            <el-button size="mini" type="danger" @click="addOrDel(2,scope.row.word)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加弹窗 -->
      <common-dialog
        width="500px"
        title="添加"
        show-footer
        :visible.sync='childDialog'
        @confirm="submit"
        @close='childDialog=false'
      >
        <div class="dialog-main">
          <el-form
            ref="sensitiveForm"
            class="form"
            size='mini'
            :model="sensitiveForm"
            label-width="100px"
            :rules="rules"
          >
            <el-form-item label='敏感词' prop='word'>
              <el-input
                v-model="sensitiveForm.word"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </div>
      </common-dialog>
      <!-- 添加弹窗END -->
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tableLoading: false,
      form: {
        queryWord: ''
      },
      sensitiveForm: {
        word: ''
      },
      page: 0,
      show: false,
      tableData: [],
      sensitiveWords: [],
      childDialog: false,
      rules: {
        word: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    /**
     * 新增或删除敏感词
     * @param type 1新增，2删除
     * @param word 敏感词
     */
    addOrDel(type, word) {
      const data = {
        handleType: type,
        delWithSensitiveWord: word
      };
      this.$post('addOrDelSensitive', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          if (type === 1) {
            this.childDialog = false;
            this.$refs.sensitiveForm.resetFields();
          }
          this.page = 0;
          this.getTableList();
        }
      });
    },
    submit() {
      this.addOrDel(1, this.sensitiveForm.word);
    },
    open() {
      this.$nextTick(() => {
        this.infiniteScroll();
      });
      this.getTableList();
    },
    // 表格数据赖加载处理
    infiniteScroll() {
      const elem = document.querySelector('.dialog-table .el-table__body-wrapper');
      const totalPages = this.sensitiveForm / 10;
      elem.addEventListener('scroll', () => {
        const scrollDistance = elem.scrollHeight - elem.scrollTop - elem.clientHeight; // 滚动距离
        if (scrollDistance <= 0) { // 到底了，可以继续加载数据
          this.page += 1;
          if (this.page > totalPages) {
            return;
          }
          const data = this.sensitiveWords.splice(this.page * 10, 10);
          this.tableData = this.tableData.concat(data);
        }
      });
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        queryWord: this.form.queryWord
      };
      this.$post('getSensitiveVocabulary', data).then(res => {
        const { fail } = res;
        let body = res.body;
        if (!fail) {
          if (Array.isArray(body)) {
            // 转成符合表格的数据
            body = body.map(item => {
              return {
                word: item
              };
            });
            this.sensitiveWords = body;
            this.tableData = body.slice(0, 10);
            this.tableLoading = false;
          }
        }
      });
    },
    // 提交敏感词
    submitSensitive() {},
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.getTableList();
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:15px;
}
.table {
  margin-bottom: 15px;
}
</style>
