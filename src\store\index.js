import Vue from "vue";
import Vuex from "vuex";
import getters from "./getters";
import app from "./modules/app";
import settings from "./modules/settings";
import readPlan from "./modules/readPlan";
import permission from "./modules/permission";

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    readPlan,
    permission,
  },
  getters,
});

export default store;
