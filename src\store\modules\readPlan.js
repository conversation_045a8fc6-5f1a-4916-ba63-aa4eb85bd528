export default {
  namespaced: true, //	命名空间，为true时，可以在store中把当前模块文件夹名称（add），当作模块
  state: {
    menuDirectoryVos: [],
    PuMenuBookVOS: []
  },
  getters: {
    dbnum(state) {
      return state.num * 2;
    }
  },
  mutations: {
    toChangeNum(state, payload) {
      state.num += payload.age;
    }
  },
  actions: {
    asyncToChangeNum(state, payload) {
      setTimeout(() => {
        state.commit('toChangeNum', payload);
      }, 1000);
    }
  }
};
