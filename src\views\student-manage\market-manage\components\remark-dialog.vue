<template>
  <common-dialog
    :show-footer="true"
    width="400px"
    title="修改备注"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="80px"
      >
        <el-form-item label='备注' prop='remark'>
          <el-input
            v-model="form.remark"
            rows="3"
            type="textarea"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    challengeId: {
      type: [String, Number],
      default: null
    },
    remarkType: {
      type: String,
      default: null
    },
    remark: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      show: false,
      urlStr: '',
      type: '',
      form: {
        remark: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    remarkType: {
      immediate: true,
      handler(val) {
        console.log(val, 'remarkType');
        this.type = val;
      }
    }
    // remarkType(val) {
    //   console.log(val, '111111111');
    //   this.type = val;
    // }
  },
  methods: {
    init() {
      this.form.remark = this.remark;
    },
    submit() {
      console.log(this.type, 'this.remarkType');
      let data = {};
      if (this.remarkType === 'adminRemark') {
        data = {
          adminRemark: this.form.remark,
          challengeId: this.challengeId
        };
        this.urlStr = 'updateAdminRemark2';
      } else {
        data = {
          teachRemark: this.form.remark,
          challengeId: this.challengeId
        };
        this.urlStr = 'updateTeachRemark2';
      }
      this.$post(this.urlStr, data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.show = false;
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.$parent.getTableList();
        }
      });
    },
    close() {
      this.$refs['form'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
