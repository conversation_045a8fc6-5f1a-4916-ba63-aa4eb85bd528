<template>
  <common-dialog
    :show-footer="true"
    width="900px"
    :visible.sync="show"
    @open="open"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-wrap">
      <div class="dialog-wrap--item dialog-wrap--item__left">
        <el-input v-model="filterText" clearable placeholder="输入关键字进行过滤" />
        <el-tree
          ref="tree"
          class="mt10"
          :data="mTagList"
          show-checkbox
          node-key="id"
          :props="defaultProps"
          :filter-node-method="filterNode"
          @check-change="handleCheckChange"
        />
      </div>
      <div class="dialog-wrap--item">
        <p>
          <span>已选择{{ tagSelectedList.length }}个标签</span>
          <el-button class="ml10" type="primary" size="mini" @click="tagClear">清空</el-button>
        </p>
        <ul class="selected-ul">
          <li v-for="(item, index) in tagSelectedList" :key="item.id" class="selected-li">
            <span>{{ item.name }}</span>
            <i class="el-icon-error del" :style="{ 'font-size': '20px' }" @click="tagDel(index)" />
          </li>
        </ul>
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'tag',
        label: 'name'
      },
      show: false,
      isEdit: false,
      filterText: '',
      tagData: [],
      tagSelectedList: [] // 已经选择的标签
    };
  },
  inject: ['tagList'],
  computed: {
    mTagList() {
      return this.tagList();
    }
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  mounted() {},
  methods: {
    // 处理tree改变
    handleCheckChange() {
      this.tagSelectedList = this.$refs.tree.getCheckedNodes(true, false);
    },
    // 删除标签
    tagDel(index) {
      console.log(index);
      console.log(this.tagSelectedList);
      this.$refs.tree.setChecked(this.tagSelectedList[index].id, false);
      this.tagSelectedList.splice(index, 1);
    },
    // 清空标签
    tagClear() {
      this.$refs.tree.setCheckedKeys([]);
      this.tagSelectedList = [];
    },
    open() {
      if (this.row) {
        this.$nextTick(() => {
          this.$refs.tree.setCheckedNodes(this.row);
        });
      }
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    submit() {
      this.$emit('confirm', this.tagSelectedList);
      this.close();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.yz-common-dialog__content.fullWithFooter {
  .dialog-wrap {
    height: inherit;
    .dialog-wrap--item {
      height: inherit;
    }
  }
}
.dialog-wrap {
  max-height: inherit;
  .dialog-wrap--item {
    padding: 20px;
    display: inline-block;
    vertical-align: top;
    width: 50%;
    max-height: inherit;
    overflow-y: auto;
  }
  .dialog-wrap--item__left {
    border-right: 0.5px solid #ddd;
  }
}
.selected-ul {
  .selected-li {
    line-height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:hover {
      background: #f1f1f1;
    }
    .del {
      cursor: pointer;
    }
  }
}
</style>
