<template>
  <div>
    <common-dialog
      show-footer
      width="500px"
      :title="row ? '编辑' : '新增'"
      :visible.sync="visible"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size="mini"
          :model="form"
          label-width="100px"
          :rules="rules"
        >
          <el-form-item label="工具名称：" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入工具名称"
              show-word-limit
              maxlength="15"
              clearable
            />
          </el-form-item>

          <el-form-item label="按钮名称：" prop="buttonName">
            <el-input
              v-model="form.buttonName"
              placeholder="请输入按钮名称"
              show-word-limit
              maxlength="8"
              clearable
            />
          </el-form-item>

          <el-form-item label="工具概括：" prop="tabName">
            <el-input
              v-model="form.tabName"
              placeholder="请输入工具概括"
              show-word-limit
              maxlength="15"
              clearable
            />
          </el-form-item>

          <el-form-item label="工具简介：" prop="context">
            <el-input
              v-model="form.context"
              placeholder="请输入工具简介"
              clearable
            />
          </el-form-item>

          <el-form-item label="跳转路由：" prop="jumpUrl">
            <el-input v-model="form.jumpUrl" placeholder="请输入跳转路由" />
          </el-form-item>

          <el-form-item label="封面头像：" prop="logoUrl">
            <el-upload
              class="avatar-uploader"
              action="/file/uploadFile.do"
              :on-success="uploadSuccess"
              :show-file-list="false"
              accept="image/*"
              :before-upload="beforeAvatarUpload"
            >
              <img
                v-if="form.logoUrl"
                :src="`${ossUri}${form.logoUrl}`"
                class="avatar"
              >
              <i v-else class="el-icon-plus avatar-uploader-icon" />
            </el-upload>
            <p>支持图片格式：jpg, jpeg, bmp, png，图片尺寸144X144，大小10M以内</p>
          </el-form-item>

          <el-form-item label="排序：" prop="sort">
            <el-input-number
              v-model="form.sort"
              :controls="false"
              :min="0"
              placeholder="数字越大的排前面"
            />
          </el-form-item>

          <el-form-item label="状态：" prop="isEnable">
            <el-radio-group v-model="form.isEnable">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { ossUri } from '@/config/request';

const formInit = {
  title: '',
  tabName: '',
  context: '',
  jumpUrl: '',
  logoUrl: '',
  sort: undefined,
  isEnable: 1
};
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      loading: false,
      ossUri,
      form: { ...formInit },
      rules: {
        title: [{ required: true, message: '请输入工具名称', trigger: 'blur' }],
        buttonName: [{ required: true, message: '请输入按钮名称', trigger: 'blur' }],
        tabName: [
          { required: true, message: '请输入工具概括', trigger: 'blur' }
        ],
        jumpUrl: [
          { required: true, message: '请输入跳转路由', trigger: 'blur' }
        ],
        logoUrl: [
          { required: true, message: '请上传封面头像', trigger: 'blur' }
        ],
        sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        isEnable: [{ required: true, message: '请选择状态', trigger: 'blur' }]
      }
    };
  },
  computed: {
    visible: {
      get() {
        return this.show;
      },
      set(val) {
        this.$emit('update:show', val);
      }
    }
  },
  methods: {
    open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({ ...this.row }));
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row?.id,
              title: this.form.title,
              tabName: this.form.tabName,
              buttonName: this.form.buttonName,
              context: this.form.context,
              jumpUrl: this.form.jumpUrl,
              logoUrl: this.form.logoUrl,
              sort: this.form.sort,
              isEnable: this.form.isEnable
            };
            let url = '/bmsAdmin/addMarketTool';
            if (this.row) {
              url = '/bmsAdmin/updateMarketTool';
            }
            const { code } = await this.$http({
              method: 'post',
              url,
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.form = { ...formInit };
    },
    beforeAvatarUpload(file) {
      const isJPG = [
        'image/bmp',
        'image/png',
        'image/jpg',
        'image/jpeg'
      ].includes(file.type);
      if (!isJPG) {
        this.$message.error('请按格式要求上传图片！');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 3;
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 3M！');
        return false;
      }

      return true;
    },
    // 图片上传成功
    uploadSuccess(response) {
      if (response.code !== '00') return;
      this.form.logoUrl = response.body;
      this.$refs.form.validateField('logoUrl');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-main ::v-deep .el-input-number {
  width: 100%;

  .el-input__inner {
    text-align: left;
    position: relative;
  }
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &.el-upload:hover {
    border-color: #409eff;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 130px;
  height: 130px;
  line-height: 130px;
  text-align: center;
}
.avatar {
  width: 130px;
  height: 130px;
  display: block;
}
</style>
