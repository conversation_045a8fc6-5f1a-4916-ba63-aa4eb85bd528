<template>
  <common-dialog class="common-dialog" width="60%" title="配置社团人员" :visible.sync="show" @close="close">
    <!-- 表单 -->
    <el-form
      ref="form"
      size="mini"
      label-width="100px"
      class="yz-search-form m-t-10"
      :model="form"
      @submit.native.prevent="search"
    >
      <el-form-item label="远智编码" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="是否成员" prop="ifMember">
        <el-select v-model="form.ifMember" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="手机号码" prop="mobile">
        <el-input v-model.number="form.mobile" maxlength="11" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item label="跟进人姓名" prop="empName">
        <el-select
          v-model="form.empName"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="请输入"
          :remote-method="empNameRemoteMethod"
          :loading="empNameLoading"
        >
          <el-option v-for="item in empNameOpts" :key="item.empId" :label="item.empName" :value="item.empName" />
        </el-select>
      </el-form-item>

      <el-form-item label="是否达人" prop="ifExpert">
        <el-select v-model="form.ifExpert" placeholder="请选择" clearable>
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box m-r-10">
        <el-button type="primary" icon="el-icon-search" native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="search('reset')" />
      </div>
    </el-form>

    <div v-loading="tableLoading">

      <!-- 按钮区 -->
      <div class="m-t-10 m-l-10 m-b-10">
        <el-button type="primary" size="mini" @click="onAddMemberAExpert">添加成员及达人</el-button>
        <el-button type="primary" size="mini" @click="onAddMember">仅添加为成员</el-button>
        <el-button type="primary" size="mini" @click="onAddExpert">添加为达人(已是成员)</el-button>
        <el-button type="danger" size="mini" plain @click="onRemoveExpert">仅移除达人</el-button>
        <el-button type="danger" size="mini" plain @click="onRemoveMember">移除成员及达人</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        ref="table"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" type="selection" width="50" />
        <el-table-column prop="ifMember" label="是否成员" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.ifMember == '1'" size="mini"> 是</el-tag>
            <el-tag v-else size="mini" type="info"> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ifExpert" label="是否达人" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.ifExpert == '1'" size="mini"> 是</el-tag>
            <el-tag v-else size="mini" type="info"> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="realName" label="姓名" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="empName" label="跟进人" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination m-b-10">
        <pagination
          :total='pager.total'
          :page.sync="pager.pageNum"
          :limit.sync="pager.pageSize"
          @pagination="getData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
/**
 * 修改用户状态类型
 * 1: 添加为达人及成员 2: 仅添加为成员 3：移除达人 4: 移除成员及达人 5: 添加为达人[已是成员]
 */
const DICT_CHANGE_TYPE = {
  MEMBER_EXPERT: 1,
  MEMBER: 2,
  REMOVE_EXPERT: 3,
  REMOVE_MEMBER: 4,
  EXPERT: 5
};
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      form: {
        yzCode: '',
        ifMember: '',
        mobile: '',
        empName: '',
        ifExpert: ''
      },
      tableLoading: false,
      tableData: [],
      empNameLoading: false,
      empNameOpts: [],
      selectionList: [],
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form, clubsActId: this.row.id };
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/clubsActivityInfo/getClubActMember',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    search(type) {
      if (type === 'reset') {
        this.$refs.form.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // 选中用户
    handleSelectionChange(val) {
      this.selectionList = val;
    },
    // 获取跟进人
    async empNameRemoteMethod(query) {
      if (!query) return;
      try {
        this.empNameLoading = true;
        const { code, body } = await this.$http({
          method: 'get',
          url: '/employ/getEmpList.do',
          params: { sName: query, page: 1, rows: 7 }
        });
        if (code !== '00') return;
        this.empNameOpts = body.data ?? [];
      } finally {
        this.empNameLoading = false;
      }
    },
    // 操作成员状态 新增|删除 成员|达人
    async handleClubActMember(type) {
      try {
        this.tableLoading = true;
        const params = {
          id: this.row.id,
          type,
          userIdList: this.selectionList.map(s => s.userId),
          learnIdList: this.selectionList.map(s => s.learnId)
        };
        const { code } = await this.$http({
          method: 'post',
          url: '/clubsActivityInfo/changeClubActMember',
          data: params,
          json: true
        });
        if (code !== '00') {
          this.tableLoading = false;
          return;
        }
        switch (type) {
          case DICT_CHANGE_TYPE.MEMBER_EXPERT:
          case DICT_CHANGE_TYPE.EXPERT:
          case DICT_CHANGE_TYPE.MEMBER:
            this.$message.success('添加成功');
            break;
          case DICT_CHANGE_TYPE.REMOVE_EXPERT:
          case DICT_CHANGE_TYPE.REMOVE_MEMBER:
            this.$message.success('移除成功');
        }
        this.getData();
      } catch (error) {
        console.log(error);
        this.tableLoading = false;
      }
    },
    // btn 添加为达人
    async onAddExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加为达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(DICT_CHANGE_TYPE.EXPERT);
        }).catch(() => { });
    },
    // btn 添加成员及达人
    onAddMemberAExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认添加成员及达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(DICT_CHANGE_TYPE.MEMBER_EXPERT);
        }).catch(() => { });
    },
    // btn 添加为成员
    onAddMember() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要添加的数据!');
      }
      this.$confirm('确认仅添加为成员吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(DICT_CHANGE_TYPE.MEMBER);
        }).catch(() => { });
    },
    // btn 移除达人
    onRemoveExpert() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(DICT_CHANGE_TYPE.REMOVE_EXPERT);
        }).catch(() => { });
    },
    // btn 移除成员
    onRemoveMember() {
      if (this.selectionList.length === 0) {
        return this.$message.warning('请选择要移除的数据!');
      }
      this.$confirm('确认移除成员及达人吗？', '提示', { type: 'warning' })
        .then(() => {
          this.handleClubActMember(DICT_CHANGE_TYPE.REMOVE_MEMBER);
        }).catch(() => { });
    }
  }
};
</script>

<style lang="scss" scoped>
.m-r-10 {
  margin-right: 10px;
}

.m-b-10 {
  margin-bottom: 10px;
}

.m-t-10 {
  margin-top: 10px;
}

.m-l-10 {
  margin-left: 10px;
}
</style>
