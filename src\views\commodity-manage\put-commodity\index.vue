<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='类型' prop='goodsShelfType'>
        <el-select v-model="form.goodsShelfType" clearable filterable placeholder="请选择类型">
          <el-option
            v-for="item in $localDict['ShelfType']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='名称' prop='goodsShelfName'>
        <el-input v-model="form.goodsShelfName" placeholder='请输入名称' />
      </el-form-item>

      <el-form-item label='上架状态' prop='shelfStatus'>
        <el-select v-model="form.shelfStatus" clearable filterable placeholder="请选择上架状态">
          <el-option label="已上架" :value="1" />
          <el-option label="已下架" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='推荐状态' prop='recommendStatus'>
        <el-select v-model="form.recommendStatus" clearable filterable placeholder="请选择推荐状态">
          <el-option label="推荐" :value="1" />
          <el-option label="不推荐" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="上架时间" prop="time">
        <el-date-picker
          v-model="form.time"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      <el-button type="primary" size="small" plain @click="batchUptateStatus(1)">批量上架</el-button>
      <el-button type="primary" size="small" plain @click="batchUptateStatus(2)">批量下架</el-button>
      <!-- <el-button type="danger" size="small" icon="el-icon-turn-off">批量禁用</el-button> -->
      <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      @selection-change="handleSelectionChange"
    >
      <!-- :height="table_height" -->
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="列表顺序" align="center" prop="sort" />
      <el-table-column label="套餐id" align="center" prop="goodsShelfId" />
      <el-table-column label="类型" align="center" prop="goodsShelfType">
        <template slot-scope="scope">
          {{ scope.row.goodsShelfType | type }}
        </template>
      </el-table-column>
      <el-table-column label="名称" align="center" prop="goodsShelfName" />
      <el-table-column label="关联商品数" align="center" prop="goodsNum" />
      <el-table-column label="库存" align="center" prop="stock" width="200">
        <template slot-scope="scope">
          <div>
            <el-button
              size="mini"
              style="float:left;"
              type="danger"
              plain
              @click="open(2,'库存减少',scope.row)"
            >-</el-button>
            <span style="margin:0 10px">{{ scope.row.stock }}</span>
            <el-button
              size="mini"
              type="success"
              style="float:right;"
              plain
              @click="open(1,'库存增加',scope.row)"
            >+</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="会员价" align="center" prop="vipPrice" />
      <el-table-column label="活动价" align="center" prop="actPrice" />
      <el-table-column label="市场价" align="center" prop="marketPrice" />
      <!-- <el-table-column label="员工价" align="center" prop="empPrice" /> -->
      <el-table-column label="购买人数" align="center" prop="purchaseNum">
        <template slot-scope="scope">
          <el-link type="primary" @click="seeDetails(scope.row)">{{ scope.row.purchaseNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="最后编辑者" align="center" prop="updateUserName" />
      <el-table-column label="上架时间" align="center" prop="onShelfTime" width="150px" />
      <el-table-column label="上架状态" align="center" prop="shelfStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.shelfStatus===1?'success':'danger'">{{ scope.row.shelfStatus | shelfStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推荐状态" align="center" prop="recommendStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.recommendStatus==1?'success':'danger'"> {{ scope.row.recommendStatus | recommendStatus }} </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 编辑弹窗 -->
    <update-dialog
      :title="udTitle"
      :visible.sync="udVisible"
      :goods-id="currentEditGoodsId"
      @close="closeEdit"
    />
    <purchase-details :visible.sync="pdVisible" :goods-id="currentLookGoodsId" />

    <common-dialog
      :show-footer="true"
      width="300px"
      :title="stockTitle"
      :visible.sync='stockShow'
      @confirm="submit"
      @close='close'
    >
      <div class="dialog-main">
        <el-form
          ref="stockForm"
          class="form"
          size='mini'
          :model="stockForm"
          :rules="stockRules"
        >
          <el-form-item prop='num'>
            <input-number :value.sync="stockForm.num" />
          </el-form-item>

        </el-form>
      </div>
    </common-dialog>
  </div>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
import updateDialog from './update-dialog';
import purchaseDetails from './purchase-details';
import { handleDateControl, exportExcel } from '@/utils';
export default {
  components: {
    updateDialog,
    purchaseDetails
  },
  filters: {
    type(val) {
      if (!val) return '';
      const data = {
        '1': '单个套餐',
        '2': '组合套餐'
      };
      return data[String(val)];
    },
    shelfStatus(val) {
      const value = String(val);
      if (!value) return '';
      const data = {
        '0': '待上架',
        '1': '已上架',
        '2': '已下架'
      };
      return data[value];
    },
    recommendStatus(val) {
      if (!val) return '';
      const data = {
        '1': '推荐',
        '2': '不推荐'
      };
      return data[String(val)];
    }
  },
  data() {
    return {
      currentEditGoodsId: null,
      currentLookGoodsId: null,
      pdVisible: false,
      stockShow: false,
      checkList: [],
      stockTitle: '',
      form: {
        goodsShelfType: '',
        goodsShelfName: '',
        shelfStatus: '',
        recommendStatus: '',
        time: ''
      },
      stockForm: {
        num: undefined
      },
      stockRules: {
        num: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      tableLoading: false,
      table_height: TABLE_HEIGHT,
      udVisible: false,
      udTitle: '新增',
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      currentItem: {}
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    open(type, title, row) {
      this.stockShow = true;
      this.stockTitle = title;
      this.currentItem = { type, row };
    },
    submit() {
      this.$refs['stockForm'].validate((valid) => {
        if (valid) {
          const data = {
            goodsShelfId: this.currentItem.row.goodsShelfId,
            num: this.stockForm.num,
            type: this.currentItem.type
          };
          this.$post('updateStock', data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getTableList();
              this.stockShow = false;
            }
          });
        }
      });
    },
    close() {
      this.stockShow = false;
      this.$refs['stockForm'].resetFields();
    },
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportFindGoodsShelfList', data);
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      delete formData.time;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        onShelfStartTime: date[0],
        onShelfEndTime: date[1],
        ...formData
      };
      return data;
    },
    batchUptateStatus(status) {
      if (this.checkList.length > 0) {
        const ids = this.checkList.map(item => {
          return item.goodsShelfId;
        });
        const data = {
          goodsShelfIdStr: ids.join(),
          status: status
        };
        this.$post('updatePutCommodityStatus', data).then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
      } else {
        this.$message.error('请勾选数据');
      }
    },
    seeDetails(row) {
      this.currentLookGoodsId = row.goodsShelfId;
      this.pdVisible = true;
    },
    handleAdd() {
      this.currentEditGoodsId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    handleEdit(row) {
      this.currentEditGoodsId = row.goodsShelfId;
      this.udTitle = '编辑';
      this.udVisible = true;
    },
    closeEdit() {
      this.udVisible = false;
    },
    handleSelectionChange(selections) {
      this.checkList = selections;
    },
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getPutCommodityList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
