<template>
  <div class="history-list border border-gray-200 rounded p-4">
    <div class="text-base font-medium mb-4">• 工单记录</div>
    <div v-if="displayList && displayList.length > 0">
      <div v-for="(item, index) in displayList" :key="index" class="mb-4">
        <!-- 标题行 - 灰色底色 -->
        <div
          class="flex items-center p-3 bg-gray-100 rounded-t border border-gray-200"
        >
          <div class="w-20 text-gray-600 text-sm flex-shrink-0">
            <el-tag size="mini" :type="getStatusType(item.logType)">{{
              getStatusText(item.logType)
            }}</el-tag>
          </div>
          <div class="w-24 text-gray-600 text-sm flex-shrink-0">
            {{ item.createEmpIdText }}
          </div>
          <div class="text-gray-600 text-sm mr-6">
            {{ formatDateTime(item.createTime) }}
          </div>
          <div class="w-40 text-gray-600 text-sm flex-1">
            {{ getActionText(item) }}
          </div>
          <div class="w-40 text-gray-600 text-sm flex-1">
            停留时长: {{ item.duration }}
          </div>
        </div>

        <!-- 内容行 - 白色底色 -->
        <div class="p-3 bg-white border border-gray-200 border-t-0 rounded-b">
          <!-- 回复内容 -->
          <div
            v-if="item.comment"
            class="mb-3 whitespace-pre-wrap leading-normal"
          >
            {{ item.comment }}
          </div>

          <!-- 转交信息 -->
          <div
            v-if="item.logType === 3 && item.toEmpIdText"
            class="mb-3 whitespace-pre-wrap leading-normal"
          >
            转交给: {{ item.toEmpIdText }}
          </div>

          <!-- 转交原因 -->
          <div
            v-if="item.logType === 3 && item.circulationReasonText"
            class="mb-3 whitespace-pre-wrap leading-normal"
          >
            转交原因: {{ item.circulationReasonText }}
          </div>

          <!-- 退费详细信息 -->
          <div v-if="item.logType === 4" class="mb-3">
            <!-- 退费提出时间 -->
            <div
              v-if="item.refundCreateTime"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              退费提出时间: {{ formatDateTime(item.refundCreateTime) }}
            </div>
            <!-- 退费原因 -->
            <div
              v-if="item.reason"
              class="mb-3 whitespace-pre-wrap leading-normal"
            >
              退费原因: {{ item.reason }}
            </div>

            <!-- 是否三七天退费 -->
            <div
              v-if="item.isThreeDayText"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              是否3.7天退费: {{ item.isThreeDayText }}
            </div>

            <!-- 应退金额 -->
            <div
              v-if="item.shouldRefundMoney"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              应退金额: {{ item.shouldRefundMoney }}
            </div>

            <!-- 实退金额 -->
            <div
              v-if="item.actualRefundMoney"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              实退金额: {{ item.actualRefundMoney }}
            </div>
          </div>

          <!-- 完结处理结果 -->
          <!-- <div
            v-if="item.logType === 1 && item.handleStatus"
            class="mb-3 whitespace-pre-wrap leading-normal"
          >
            处理结果: {{ item.handleStatus }}
          </div> -->

          <!-- 完结详细信息 -->
          <div v-if="item.logType === 1" class="mb-3">
            <!-- 处理结果 -->
            <div
              v-if="item.handleStatusText"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              处理结果: {{ item.handleStatusText }}
            </div>

            <!-- 是否需要回复 -->
            <div
              v-if="item.needReplyText"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              是否需要回复: {{ item.needReplyText }}
            </div>

            <!-- 是否给予礼品 -->
            <div
              v-if="item.needGiftText"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              是否给予礼品: {{ item.needGiftText }}
            </div>

            <!-- 礼品名称 -->
            <div
              v-if="item.needGiftName"
              class="mb-2 whitespace-pre-wrap leading-normal"
            >
              礼品名称: {{ item.needGiftName }}
            </div>
          </div>

          <!-- 备注 -->
          <div
            v-if="item.handleRemark"
            class="mb-3 whitespace-pre-wrap leading-normal"
          >
            备注: {{ item.handleRemark }}
          </div>

          <!-- 附件展示 -->
          <div v-if="item.files && item.files.length > 0" class="mt-3">
            <div class="text-sm text-gray-600 mb-2">
              <span
                class="text-red-500 cursor-pointer"
                @click="downloadAllFiles(item.files)"
                >下载全部附件</span
              >
            </div>
            <div class="flex flex-wrap gap-4">
              <div
                v-for="(file, fileIndex) in item.files"
                :key="fileIndex"
                class="w-25 cursor-pointer"
                @click="handleFileClick(file)"
              >
                <!-- <div v-if="isVideoFile(file.name)" class="relative w-25 h-25 rounded overflow-hidden border border-gray-200">
                  <video
                    :src="file.localPath || file.url"
                    class="w-full h-full object-cover"
                    controls
                    muted
                  ></video>
                </div> -->
                <!-- 图片文件 -->
                <template v-if="isImageFile(file.name)">
                  <div
                    class="relative w-25 h-25 rounded overflow-hidden border border-gray-200"
                  >
                    <el-image
                      :src="file.url"
                      fit="cover"
                      class="w-full h-full object-cover"
                    >
                      <div
                        slot="error"
                        class="w-full h-full flex justify-center items-center bg-gray-100 text-gray-400"
                      >
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                    <div
                      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center opacity-0 hover:opacity-100 transition-opacity"
                    >
                      <i
                        class="el-icon-view text-white text-xl mx-2 cursor-pointer hover:transform hover:scale-110"
                        @click.stop="previewImage(file, item.files)"
                      ></i>
                      <i
                        class="el-icon-download text-white text-xl mx-2 cursor-pointer hover:transform hover:scale-110"
                        @click.stop="downloadFile(file)"
                      ></i>
                    </div>
                  </div>
                </template>
                <!-- 非图片文件 -->
                <template v-else>
                  <div
                    class="relative w-25 h-25 flex justify-center items-center bg-gray-100 rounded border border-gray-200"
                  >
                    <i
                      :class="getFileIcon(file.name)"
                      class="text-4xl text-gray-400"
                    ></i>
                    <div
                      class="absolute inset-0 bg-black bg-opacity-50 flex justify-center items-center opacity-0 hover:opacity-100 transition-opacity"
                    >
                      <i
                        class="el-icon-download text-white text-xl cursor-pointer hover:transform hover:scale-110"
                        @click.stop="downloadFile(file)"
                      ></i>
                    </div>
                  </div>
                </template>
                <div
                  class="mt-1 text-xs text-gray-600 text-center truncate"
                  :title="file.name"
                >
                  {{ file.name }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center text-gray-500 py-5">暂无工单记录</div>

    <!-- 图片预览 -->
    <media-viewer
      v-if="previewVisible"
      :url-list="previewUrlList"
      :initial-index="previewIndex"
      :z-index="2000"
      @close="previewVisible = false"
    />
  </div>
</template>

<script>
import { imageData } from "@/components/formTools/MediaViewer/utils";
import MediaViewer from "@/components/formTools/MediaViewer";

export default {
  name: "WorkorderHistory",
  components: {
    MediaViewer,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      previewVisible: false,
      previewUrlList: [],
      previewIndex: 0,
    };
  },
  computed: {
    displayList() {
      // 处理后端返回的数据
      if (this.list && this.list.length > 0) {
        return this.list.map((item) => {
          // 检查是否有data字段且为字符串
          if (item.data && typeof item.data === "string") {
            try {
              // 尝试解析data字段中的JSON数据
              const parsedData = JSON.parse(item.data);

              // 将解析后的数据合并到item中
              return {
                ...item,
                ...parsedData,
              };
            } catch (e) {
              console.error("解析data字段失败:", e);
            }
          }
          return item;
        });
      }
      return [];
    },
  },
  methods: {
    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return "";

      // 将日期字符串转换为Date对象
      const dateTime = new Date(dateTimeStr);

      // 当前日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 昨天日期
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      // 日期部分
      let dateStr = "";

      // 检查是否是今天
      if (dateTime >= today) {
        dateStr = "今天";
      }
      // 检查是否是昨天
      else if (dateTime >= yesterday && dateTime < today) {
        dateStr = "昨天";
      }
      // 其他日期
      else {
        const year = dateTime.getFullYear();
        const month = (dateTime.getMonth() + 1).toString().padStart(2, "0");
        const day = dateTime.getDate().toString().padStart(2, "0");
        dateStr = `${year}-${month}-${day}`;
      }

      // 时间部分
      const hours = dateTime.getHours().toString().padStart(2, "0");
      const minutes = dateTime.getMinutes().toString().padStart(2, "0");
      const seconds = dateTime.getSeconds().toString().padStart(2, "0");
      const timeStr = `${hours}:${minutes}:${seconds}`;

      // 返回格式化后的日期时间
      return `${dateStr} ${timeStr}`;
    },

    // 获取操作文本
    getActionText(item) {
      const actionMap = {
        1: "完结了工单",
        2: "回复了工单",
        3: `将工单转交给了${item.toEmpIdText}`,
        4: "对工单进行了退费记录",
        5: "挂起了工单",
      };
      return actionMap[item.logType] || "未知操作";
    },

    // 获取状态文本
    getStatusText(action) {
      if (action === 1) return "完结";
      if (action === 2) return "回复";
      if (action === 3) return "转交";
      if (action === 4) return "退费";
      if (action === 5) return "挂起";
      return "处理中";
    },

    // 获取状态类型
    getStatusType(action) {
      if (action === 1) return "success";
      if (action === 2) return "primary";
      if (action === 3) return "warning";
      if (action === 4) return "danger";
      if (action === 5) return "info";
      return "primary";
    },

    // 判断文件是否为图片
    isImageFile(fileName) {
      if (!fileName) return false;
      const extension = fileName.split(".").pop().toLowerCase();
      return imageData.includes(extension);
    },

    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();

      if (["jpg", "jpeg", "png", "gif", "bmp"].includes(extension)) {
        return "el-icon-picture-outline";
      } else if (["doc", "docx"].includes(extension)) {
        return "fa fa-file-word-o";
      } else if (["xls", "xlsx"].includes(extension)) {
        return "fa fa-file-excel-o";
      } else if (["ppt", "pptx"].includes(extension)) {
        return "fa fa-file-powerpoint-o";
      } else if (extension === "pdf") {
        return "fa fa-file-pdf-o";
      } else if (["zip", "rar", "7z"].includes(extension)) {
        return "fa fa-file-archive-o";
      } else if (["mp4", "avi", "mov", "wmv", "mkv"].includes(extension)) {
        return "fa fa-file-video-o";
      } else {
        return "el-icon-document";
      }
    },

    // 判断文件是否为视频
    isVideoFile(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();
      return ["mp4", "avi", "mov", "wmv", "mkv"].includes(extension);
    },

    // 处理文件点击
    handleFileClick(file) {
      if (!this.isImageFile(file.name)) {
        this.downloadFile(file);
      }
    },

    // 预览图片
    previewImage(file, fileList) {
      // 获取所有图片文件
      const imageFiles = fileList.filter((item) => this.isImageFile(item.name));

      // 获取所有图片URL
      this.previewUrlList = imageFiles.map((item) => item.url);

      // 找到当前点击图片的索引
      this.previewIndex = imageFiles.findIndex(
        (item) => item.name === file.name
      );
      if (this.previewIndex === -1) this.previewIndex = 0;

      // 显示预览
      this.previewVisible = true;
    },

    // 下载所有文件
    downloadAllFiles(fileList) {
      if (!fileList || fileList.length === 0) return;

      fileList.forEach((file) => {
        this.downloadFile(file);
      });

      this.$message({
        message: "开始下载全部附件",
        type: "success",
        duration: 2000,
      });
    },

    // 下载文件
    downloadFile(file) {
      // 判断是否为图片文件
      if (this.isImageFile(file.name)) {
        this.downloadImage(file);
      } else {
        this.downloadNormalFile(file);
      }
    },

    // 下载图片文件
    downloadImage(file) {
      const image = new Image();
      image.crossOrigin = "Anonymous"; // 解决跨域问题

      image.onload = () => {
        // 创建canvas
        const canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;

        // 绘制图片到canvas
        const ctx = canvas.getContext("2d");
        ctx.drawImage(image, 0, 0);

        // 将canvas转换为blob
        canvas.toBlob((blob) => {
          // 创建下载链接
          const url = URL.createObjectURL(blob);
          const link = document.createElement("a");
          link.href = url;
          link.download = file.name;
          link.style.display = "none";

          // 添加到body并点击
          document.body.appendChild(link);
          link.click();

          // 清理
          setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }, 100);
        });
      };

      image.onerror = () => {
        // 图片加载失败，使用备用方法
        this.fallbackDownload(file);
      };

      // 设置图片源
      image.src = file.url;
    },

    // 下载普通文件
    downloadNormalFile(file) {
      const link = document.createElement("a");
      link.href = file.url;
      link.download = file.name;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 备用下载方法
    fallbackDownload(file) {
      try {
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.src = file.url;
        document.body.appendChild(iframe);

        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);

        this.$message({
          message: `${file.name} 开始下载，如果没有自动下载，请右键图片选择"另存为"`,
          type: "info",
          duration: 5000,
        });
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error('下载失败，请右键点击图片选择"图片另存为"');
      }
    },
  },
};
</script>

<style scoped>
.history-list {
  margin-top: 20px;
}
</style>
