<template>
  <common-dialog
    width="650px"
    :show-footer="true"
    title="设置老师企微"
    :visible.sync='show'
    @open="open"
    @close='close'
    @confirm="submit"
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='跳转链接' prop='linkUrl'>
          <el-input v-model="form.linkUrl" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { validate } from '@/utils/validate';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const validateLinkUrl = (rule, value, callback) => {
      if (!validate('url', value)) {
        return callback(new Error('请输入合法的链接'));
      }
      callback();
    };
    return {
      show: false,
      form: {
        linkUrl: ''
      },
      rules: {
        linkUrl: [
          { validator: validateLinkUrl, trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = {
            openLink: 1, // 链接开关 1-> 开 0-> 关
            linkUrl: this.form.linkUrl
          };
          this.$post('setSuperPlusWeChatLink', params, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.close();
            }
          });
        }
      });
    },
    open() {
      this.$post('getSuperPlusWeChatLink').then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.form.linkUrl = body.linkUrl;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">

</style>
