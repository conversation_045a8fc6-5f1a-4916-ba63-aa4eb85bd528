<template>
  <common-dialog
    is-full
    width="1000px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        :data="tableData"
        height="calc(100vh - 135px)"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
      >
        <el-table-column type="index" label="序号" width="50" align="center" />
        <el-table-column prop="clockClass" label="打卡课时" align="center" />
        <el-table-column
          prop="status"
          label="打卡状态"
          align="center"
          width="200px"
        >
          <template slot-scope="scope">
            <span :class="{grey: scope.row.clockState === -2 }">{{ scope.row.clockState | status }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="readPlanName" label="留言内容" align="left">
          <template slot-scope="scope">
            <ul v-if="scope.row.readCommentVoList">
              <li v-for="(item, index) in scope.row.readCommentVoList" :key="index" class="comment-li">
                <div class="comment"><strong>留言内容：</strong>{{ item.commentContent }}</div>
                <div class="imgs">
                  <el-image
                    v-for="(imgUrl, i) in item.imgs"
                    :key="i"
                    class="comment-img"
                    :src="imgUrl"
                    :preview-src-list="item.imgs"
                  />
                  <div class="comment-time">
                    <strong>留言时间：</strong>{{ item.createTime | transformTimeStamp }}
                  </div>
                </div></li>
            </ul>
          </template>
        </el-table-column>
      </el-table>

      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>

</template>

<script>
import { ossUri } from '@/config/request';

export default {
  filters: {
    status(val) {
      if (!String(val)) return;
      const data = {
        '-2': '未到打卡时间',
        '-1': '缺卡',
        '0': '未打卡',
        '1': '已打卡'
      };
      return data[String(val)];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {
        return {
          enroId: '', // 报读id
          readPlanId: '', // 读书计划id
          semesterId: '' // 读书计划期数id
        };
      }
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      goodsShelfId: null,
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.getTableList();
    },
    // 获取用户读书计划打卡记录
    getTableList() {
      this.tableLoading = true;
      const params = {
        userId: this.row.userId,
        semesterId: this.row.semesterId, // 读书计划期数id
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getUserClockDetails', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            body.data.forEach(item => {
              if (Array.isArray(item.readCommentVoList)) {
                item.readCommentVoList.forEach(comment => {
                  if (typeof comment.pictureUrl === 'string' && comment.pictureUrl !== '') {
                    comment.imgs = comment.pictureUrl.split(',');
                    comment.imgs = comment.imgs.map(imgUrl => {
                      return ossUri + imgUrl;
                    });
                  } else {
                    comment.imgs = [];
                  }
                });
              }
            });
            this.tableData = body.data;
            console.log(this.tableData, 'tableData');
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang = "scss" scoped>
.comment-img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  border: 1px solid #ccc;
  padding: 2px;

  ::v-deep .el-image__error {
    font-size: 12px;
    text-align: center;
  }
}

.grey {
  color: #c8c9cc;
}

.comment-li {
  margin-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

</style>
