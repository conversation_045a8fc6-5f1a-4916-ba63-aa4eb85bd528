import Layout from '@/layout';

/** 小程序配置 routes */
export default [
  {
    // 测评小程序配置
    path: '/evaluation',
    name: 'evaluation',
    component: Layout,
    meta: {
      title: '测评小程序配置',
      icon: 'el-icon-connection'
    },
    children: [
      {
        path: 'index',
        name: 'evaluation.index',
        component: () => import('@/views/evaluation/index'),
        meta: {
          title: '测评小程序配置',
          breadcrumb: false
        }
      },
      {
        path: 'keyTagConfig',
        name: 'evaluation.keyTagConfig',
        component: () => import('@/views/evaluation/keyTagConfig/index'),
        meta: {
          title: '关键标签配置',
          breadcrumb: false
        }
      },
      {
        path: 'exploringHorsesPush',
        name: 'evaluation.horses',
        component: () => import('@/views/evaluation/exploringHorsesPush'),
        meta: {
          title: '探马推送设置',
          breadcrumb: false
        }
      }
    ]
  },
  {
    // 小程序浮标配置
    path: '/buoyAllocation',
    name: 'buoyAllocation',
    component: Layout,
    meta: {
      title: '小程序浮标配置',
      icon: 'el-icon-connection'
    },
    children: [
      {
        path: 'index',
        name: 'buoyAllocation.index',
        component: () => import('@/views/buoyAllocation/index'),
        meta: {
          title: '小程序浮标配置',
          breadcrumb: false
        }
      }
    ]
  }
];

