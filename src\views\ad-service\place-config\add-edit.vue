<template>
  <div>
    <common-dialog
      :show-footer="true"
      width="60%"
      :title="row ? '修改' : '新建'"
      :visible.sync="show"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size="mini"
          :model="form"
          label-width="150px"
          :rules="rules"
        >
          <el-form-item label="投放渠道" prop="putInChannel">
            <el-select
              v-model="form.putInChannel"
              :disabled="isEdit"
              clearable
              placeholder="请选择投放渠道"
            >
              <el-option
                v-for="item in putInChannel"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="商品名称" prop="productName">
            <el-input
              v-model="form.productName"
              maxlength="25"
              show-word-limit
              placeholder="请输入商品名称"
            />
          </el-form-item>

          <el-form-item label="页面标题(对外显示)" prop="productTitle">
            <el-input
              v-model="form.productTitle"
              placeholder="请输入页面标题"
              maxlength="10"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="头图" prop="productBannerImg">
            <upload-file
              :max-limit="1"
              exts="jpg|png"
              :file-list="fileList"
              @remove="onFileRemove"
              @success="onFileSuccess"
            />
          </el-form-item>

          <el-form-item label="详情页图" prop="productDetailImgList">
            <p>最多支持上传5张图片，按上传先后顺序依次展示</p>
            <span
              style="font-size: 12px"
            >建议：切成多张图片进行上传，页面加载速度更快</span>
            <upload-file
              :max-limit="5"
              exts="jpg|png"
              :file-list="mulFileList"
              @remove="onMulFileRemove"
              @success="onMulFileSuccess"
            />
          </el-form-item>

          <el-form-item label="链路" prop="linkConf">
            <el-radio-group
              v-model="form.linkConf"
              :disabled="isEdit"
            >
              <el-radio :label="1">领课链路（无需注册）</el-radio>
              <el-radio :label="2">购课链路（需注册登录）</el-radio>
            </el-radio-group>
          </el-form-item>

          <template v-if="form.linkConf == 1">
            <el-form-item label="吸底按钮" prop="bottomButtonImg">
              <p>支持上传PNG/JPEG/GIF；建议尺寸686*80；大小不超过500KB</p>
              <upload-file
                :max-limit="1"
                exts="jpg|png|jpeg|gif"
                :size="0.5"
                :file-list="btnFileList"
                @remove="onBtnFileRemove"
                @success="onBtnFileSuccess"
              />
            </el-form-item>

            <el-form-item label="按钮跳转链接" prop="bottomButtonUrl">
              <el-input
                v-model="form.bottomButtonUrl"
                show-word-limi
                placeholder="请输入按钮跳转链接"
              />
            </el-form-item>
          </template>

          <template v-if="form.linkConf == 2">
            <el-form-item label="价格" prop="amount">
              <el-input
                v-model="form.amount"
                :disabled="isEdit"
                placeholder="请输入价格"
                @input="handleLimitInput"
              />
            </el-form-item>

            <el-form-item label="对应的获客链接" prop="contactType">
              <el-select
                v-model="form.contactType"
                :disabled="isEdit"
                filterable
                clearable
                placeholder="请选择获客链接"
              >
                <el-option
                  v-for="item in huoKeLinkOptions"
                  :key="item.productId"
                  :label="item.contactName"
                  :value="item.contactType"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="对应的标签" prop="workWechatTagVOS">
              <el-button
                type="primary"
                :disabled="isEdit"
                @click="handleTagShow"
              >请选择</el-button>
              <div>
                <span>已选择{{ tagSelectedList.length }}个标签:</span>
                <span v-for="(item, index) in tagSelectedList" :key="item.id">
                  {{ item.name
                  }}{{ index == tagSelectedList.length - 1 ? "" : "、" }}
                </span>
              </div>
            </el-form-item>
          </template>
        </el-form>
      </div>
    </common-dialog>
    <tag-dialog
      :visible.sync="tagVisible"
      :row="form.workWechatTagVOS"
      @confirm="tagDialogConfirm"
    />
  </div>
</template>

<script>
import tagDialog from './tag-dialog.vue';
import { ossUri } from '@/config/request';
import { putInChannel } from './type';
export default {
  components: {
    tagDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 获客链接下拉数据
    huoKeLinkOptions: {
      type: Array,
      default: () => []
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      putInChannel: putInChannel,
      tagSelectedList: [], // 已选择的标签列表
      tagVisible: false, // 选择标签弹框
      show: false, // 修改弹框
      isEdit: false, // 是否修改
      form: {
        workWechatTagVOS: [], // 选择的标签
        productBannerImg: '', // 头图
        productDetailImgList: [], // 详情图
        bottomButtonImg: [] // 吸底按钮图
      },
      rules: {
        putInChannel: [
          { required: true, message: '请选择投放渠道', trigger: 'change' }
        ],
        productName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' }
        ],
        productTitle: [
          { required: true, message: '请输入页面标题', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入价格', trigger: 'blur' },
          {
            validator(rule, value, callback) {
              const reg = /^\d+(\.\d{1,2})?$/; // 正则表达式，匹配金额规范，最多2位小数
              if (!reg.test(value)) {
                callback(new Error('请输入正确的金额'));
              } else {
                callback();
              }
            }
          }
        ],
        contactType: [
          {
            required: true,
            message: '请选择对应的获客链接',
            trigger: 'change'
          }
        ],
        workWechatTagVOS: [
          { required: true, message: '请选择标签', trigger: 'change' }
        ],
        productBannerImg: [
          { required: true, message: '请上传头图', trigger: 'change' }
        ],
        productDetailImgList: [
          { required: true, message: '请上传详情图', trigger: 'change' }
        ],
        linkConf: [
          { required: true, message: '请选择链路', trigger: 'change' }
        ],
        bottomButtonImg: [
          { required: true, message: '请上传吸底按钮图', trigger: 'change' }
        ],
        bottomButtonUrl: [
          { required: true, message: '请输入按钮跳转链接', trigger: 'blur' }
        ]
      },
      fileList: [], // 头图
      mulFileList: [], // 详情图片
      btnFileList: [] // 吸底按钮图
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    // 吸底按钮图上传成功
    onBtnFileSuccess({ response, file, fileList }) {
      this.form.bottomButtonImg = response;
    },
    // 吸底按钮图删除
    onBtnFileRemove({ file, fileList }) {
      this.form.bottomButtonImg = '';
    },
    // 标签弹框确定事件
    tagDialogConfirm(list) {
      this.tagSelectedList = list;
      this.form.workWechatTagVOS = list;
    },
    // 显示选择标签弹框
    handleTagShow() {
      this.tagVisible = true;
    },
    // 头图上传成功
    onFileSuccess({ response, file, fileList }) {
      this.form.productBannerImg = response;
    },
    // 头图删除
    onFileRemove({ file, fileList }) {
      this.form.productBannerImg = '';
    },
    // 详情图片上传成功
    onMulFileSuccess({ response, file, fileList }) {
      this.form.productDetailImgList.push({ fileUrl: file.response });
    },
    // 详情图片删除
    onMulFileRemove({ file, fileList }) {
      let index = 0;
      for (let i = 0; i < this.form.productDetailImgList.length; i++) {
        const item = this.form.productDetailImgList[i];
        if (item.fileUrl === file.response) {
          index = i;
          break;
        }
      }

      this.form.productDetailImgList.splice(index, 1);
    },
    // 限制只能输入数字和小数点
    handleLimitInput(value) {
      // this.form.amount = value.replace(/[^\d.]/g, '');

      // 只允许输入数字和小数点
      this.form.amount = value.replace(/[^\d.]/g, '');

      // 限制小数点后最多两位
      const parts = this.form.amount.split('.');
      if (parts.length > 1) {
        parts[1] = parts[1].slice(0, 2); // 限制小数点后最多两位
        this.form.amount = parts.join('.');
      }
    },
    open() {
      if (this.row) {
        const {
          putInChannel,
          productName,
          productTitle,
          amount,
          contactType,
          productBannerImg,
          productDetailImgList,
          workWechatTagVOS,
          linkConf,
          bottomButtonImg,
          bottomButtonUrl
        } = this.row;
        this.form = {
          ...this.form,
          putInChannel,
          productName,
          productTitle,
          amount,
          contactType,
          linkConf,
          bottomButtonImg,
          bottomButtonUrl
        };

        if (this.form.linkConf) {
          this.btnFileList.push({ url: ossUri + bottomButtonImg });
        }

        this.form.productBannerImg = productBannerImg;
        this.fileList.push({ url: ossUri + productBannerImg });
        productDetailImgList.forEach((item) => {
          this.mulFileList.push({ url: ossUri + item });
          this.form.productDetailImgList.push({ fileUrl: item });
        });
        const list = workWechatTagVOS.map((item) => {
          return {
            id: item.workWechatTagId,
            name: item.workWechatTagName
          };
        });
        this.tagSelectedList = list;
        this.form.workWechatTagVOS = list;
        this.isEdit = true;
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const form = JSON.parse(JSON.stringify(this.form));
          form.productDetailImgList = form.productDetailImgList.map(item => item.fileUrl);
          if (form.linkConf === 1) {
            // 免费
            delete form.amount;
            delete form.contactType;
            delete form.workWechatTagVOS;
          } else {
            // 购买
            form.amount = Number(form.amount);
            form.workWechatTagVOS = form.workWechatTagVOS.map((item) => {
              return {
                workWechatTagId: item.id,
                workWechatTagName: item.name
              };
            });

            delete form.bottomButtonUrl;
            delete form.bottomButtonImg;
          }
          let apiKey = 'addAdPutInConf';
          if (this.row) {
            apiKey = 'updateAdPutInConf';
            form.productId = this.row.productId;
          }
          this.$post(apiKey, form, { json: true }).then(
            (res) => {
              const { fail } = res;
              if (!fail) {
                this.show = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.$parent.loadNextPage();
                this.$parent.getProductOptions();
              }
            }
          );
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped></style>
