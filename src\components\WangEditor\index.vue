<template>
  <div ref="editor" v-loading="loading" class="editorStyle" />
</template>

<script>
import E from 'wangeditor';
import { ossUri } from '@/config/request';
export default {
  name: '<PERSON>Editor',
  props: {
    value: {
      type: [String, Object],
      default: ''
    },
    zIndex: {
      type: Number,
      default: 500
    },
    height: {
      type: Number,
      default: 300
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    uploadUrl: {
      type: String,
      default: '/file/uploadFile.do'
    }
  },
  data() {
    return {
      editor: null,
      uploadUrlChild: '',
      loading: false
    };
  },
  watch: {
    uploadUrl(val) {
      console.log(val, 'uploadUrlChild11111');
      this.uploadUrlChild = val;
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const elem = this.$refs['editor'];
      this.editor = new E(elem);
      this.editor.config.menus = [
        'head',
        'bold',
        'fontSize',
        'fontName',
        'italic',
        'underline',
        'strikeThrough',
        'indent',
        'lineHeight',
        'foreColor',
        'backColor',
        'link',
        'list',
        'justify',
        // 'emoticon',
        'image',
        'table',
        'undo',
        'redo',
        'video'
      ];
      // 配置字体大小
      this.editor.config.fontSizes = {
        'x-small': { name: '10px', value: '1' },
        'small': { name: '13px', value: '2' },
        'normal': { name: '16px', value: '3' },
        'large': { name: '18px', value: '4' },
        'x-large': { name: '24px', value: '5' },
        'xx-large': { name: '32px', value: '6' },
        'xxx-large': { name: '48px', value: '7' }
      };

      this.editor.config.zIndex = this.zIndex;
      this.editor.config.height = this.height;
      this.editor.config.pasteFilterStyle = false;
      this.editor.config.placeholder = this.placeholder;
      // 配置 onchange 回调函数
      this.editor.config.onchange = (newHtml) => {
        // 处理font标签转换
        newHtml = this.convertFontToSpan(newHtml);
        this.$emit('input', newHtml);
        this.$emit('change', newHtml);
      };
      // 配置图片上传
      this.editor.config.customUploadImg = (resultFiles, insertImgFn) => {
        const formData = new FormData();
        formData.set('file', resultFiles[0]);
        this.$http.post(this.uploadUrl, formData, { uploadFile: true })
          .then(res => {
            console.log(ossUri + res.body, 'ossUri + res.body');
            const imgUrl = ossUri + res.body;
            insertImgFn(imgUrl);
            // insertImgFn('https://static.ijiaolian.com/coach/A156010A6991491A9854B287EA4BAC28.jpg');
          });
      };
      this.editor.config.customUploadVideo = (resultFiles, insertVideoFn) => {
        this.loading = true;
        const formData = new FormData();
        formData.set('file', resultFiles[0]);
        this.$http.post(this.uploadUrl, formData, { uploadFile: true })
          .then(res => {
            const videoUrl = ossUri + res.body;
            // this.$message({
            //   message: '视频上传成功',
            //   type: 'success'
            // });
            this.loading = false;
            insertVideoFn(videoUrl);
          });
      };

      this.editor.config.pasteFilterStyle = false;
      this.editor.config.pasteText = false;
      this.editor.config.pasteTextHandle = function(content) {
        if (content === '' && !content) return '';
        return removeFormatWrd(content);
      };
      this.editor.config.colors = [
        '#000000',
        '#ffffff',
        '#eeece0',
        '#1c487f',
        '#4d80bf',
        '#c24f4a',
        '#8baa4a',
        '#7b5ba1',
        '#46acc8',
        '#f9963b',
        '#FF0000',
        '#00FF00',
        '#0000FF',
        '#FF00FF',
        '#00FFFF',
        '#FFFF00',
        '#000000',
        '#70DB93',
        '#5C3317',
        '#9F5F9F',
        '#B5A642',
        '#D9D919',
        '#A67D3D',
        '#8C7853',
        '#A67D3D',
        '#5F9F9F',
        '#D98719',
        '#B87333'
      ];
      // 样式过滤
      function removeFormatWrd(html) {
        console.log(html);
        // html = html.replace(/<xml>[\s\S]*?<\/xml>/ig, '');
        // html = html.replace(/<style>[\s\S]*?<\/style>/ig, '');
        // html = html.replace(/<\/?[^>]*>/g, '');
        // html = html.replace(/[ | ]*\n/g, '\n');
        // html = html.replace(/&nbsp;/ig, '');
        // 上面代码是不要任何样式，纯文本

        // 下面代码，保留从相关文档中的粘贴过来时的样式
        // html = html.replace(/<\/?SPANYES[^>]*>/gi, "");//  Remove  all  SPAN  tags
        // html = html.replace(/<(\w[^>]*) class=([^|>]*)([^>]*)/gi, '<$1$3'); //  Remove  Class  attributes
        html = html.replace(/<(\w[^>]*) style="([^"]*)"([^>]*)/gi, '<$1$3'); //  Remove  Style  attributes
        // html = html.replace(/<(\w[^>]*)  lang=([^|>]*)([^>]*)/gi, "<$1$3");//  Remove  Lang  attributes
        // html = html.replace(/<?xml[^>]*>/gi, "");//  Remove  XML  elements  and  declarations
        html = html.replace(/<\/?\w+:[^>]*>/gi, '');//  Remove  Tags  with  XML  namespace  declarations:  <o:p></o:p>
        // html = html.replace(/ /, "");//  Replace  the
        // html = html.replace(/<xml>[\s\S]*?<\/xml>/ig, '');
        // html = html.replace(/<html>[\s\S]*?<\/html>/ig, '');
        // html = html.replace(/<head>[\s\S]*?<\/head>/ig, '');
        html = html.replace(/<style>[\s\S]*?<\/style>/ig, '');
        // html = html.replace(/<html<body/ig, '<html><body');
        // html = html.replace(/<\/html<body>/ig, '</body></html>');
        // html = html.replace(/\n(\n)*( )*(\n)*\n/gi, '\n');
        return html;
      }
      this.editor.create();
    },
    // 添加转换方法
    convertFontToSpan(html) {
      if (!html) return html;

      // 创建临时div来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // 查找所有font标签
      const fontElements = tempDiv.getElementsByTagName('font');
      while (fontElements.length > 0) {
        const fontElement = fontElements[0];
        const size = fontElement.getAttribute('size');
        const color = fontElement.getAttribute('color');
        const face = fontElement.getAttribute('face');

        // 创建新的span标签
        const spanElement = document.createElement('span');

        // 设置样式
        if (size) {
          spanElement.style.fontSize = this.editor.config.fontSizes[Object.keys(this.editor.config.fontSizes).find(key =>
            this.editor.config.fontSizes[key].value === size
          )].name;
        }
        if (color) {
          spanElement.style.color = color;
        }
        if (face) {
          spanElement.style.fontFamily = face;
        }

        // 移动内容
        while (fontElement.firstChild) {
          spanElement.appendChild(fontElement.firstChild);
        }

        // 替换标签
        fontElement.parentNode.replaceChild(spanElement, fontElement);
      }

      return tempDiv.innerHTML;
    },
    getContent(type = 'html') {
      if (type === 'html') {
        return this.editor.txt.html();
      }

      if (type === 'text') {
        return this.editor.txt.text();
      }

      if (type === 'json') {
        return this.editor.txt.getJSON();
      }
    },
    setContent(content) {
      if (this.editor) {
        this.editor.txt.html(content);
      }
    }
  }
};
</script>

<style lang='scss' scoped>
.editorStyle {
  ::v-deep .w-e-text-container .w-e-text>ol>li {
    list-style: auto;
  }

  ::v-deep .w-e-text-container .w-e-text>ul>li {
    list-style: disc;
  }

  // /deep/ .w-e-text-placeholder{
  //   top:7px;
  // }

  // /* table 样式 */
  // table {border-top: 1px solid #ccc;border-left: 1px solid #ccc;
  // }
  // table td,
  // table th {border-bottom: 1px solid #ccc;border-right: 1px solid #ccc;padding: 3px 5px;
  // }
  // table th {border-bottom: 2px solid #ccc;text-align: center;
  // }/* blockquote 样式 */
  // blockquote {display: block;border-left: 8px solid #d0e5f2;padding: 5px 10px;margin: 10px 0;line-height: 1.4;font-size: 100%;background-color: #f1f1f1;
  // }/* code 样式 */
  // code {display: inline-block;*display: inline;*zoom: 1;background-color: #f1f1f1;border-radius: 3px;padding: 3px 5px;margin: 0 3px;
  // }
  // pre code {display: block;
  // }/* ul ol 样式 */
  // ul, ol {margin: 10px 0 10px 20px;
  // }

  // 添加字体大小样式
  ::v-deep .w-e-text-container {
    font[size="1"] { font-size: 10px; }
    font[size="2"] { font-size: 13px; }
    font[size="3"] { font-size: 16px; }
    font[size="4"] { font-size: 18px; }
    font[size="5"] { font-size: 24px; }
    font[size="6"] { font-size: 32px; }
    font[size="7"] { font-size: 48px; }
  }
}
</style>
