<template>
  <common-dialog
    :show-footer="true"
    width="1000px"
    title="绑定题库"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <h1 class="title">课程名称：<span class="grey">{{ row && row.courseName }}</span></h1>
      <div class="library">
        <h1 class="title" required>选择练题库：</h1>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="left">
              <el-input
                v-model="filterText"
                size="small"
                class="filterInput"
                placeholder="输入关键字进行过滤"
              />
              <el-tree
                ref="tree"
                class="filter-tree"
                :data="treeData"
                check-strictly
                show-checkbox
                node-key="practiceQuestionBankId"
                :props="defaultProps"
                default-expand-all
                check-on-click-node
                :filter-node-method="filterNode"
                @check-change="treeCheckChange"
              />
            </div>
          </el-col>
          <el-col :span="12">
            <div class="right">
              <el-table
                ref="table"
                v-loading="tableLoading"
                border
                row-key="subId"
                size="small"
                :height="500"
                style="width: 100%"
                header-cell-class-name='table-cell-header'
                :data="tableData"
                @selection-change="tableSelectionChange"
              >
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column prop="subCode" label="课程编码" align="center" />
                <el-table-column prop="subName" label="课程名称" align="center" />
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      defaultProps: {
        children: 'practiceQuestionBankResultVOList',
        label: 'bankTitle'
      },
      tableData: [],
      tableLoading: false,
      treeData: [],
      filterText: '',
      pagination: {
        page: 1,
        limit: 99999,
        total: 0
      },
      currentNode: null, // 当前点击的 tree node对象数据
      tableSelection: [],
      search: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    // 获取编辑状态下，绑定的课程数据
    getRightCourseList() {
      const params = {
        courseQuestionBankId: this.row.courseQuestionBankId
      };
      this.$post('getCourseQuestionBankVO', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableSelection = body;
            this.getNodeCourseList();
          }
        });
    },
    tableSelectionChange(selection) {
      this.tableSelection = selection;
      // console.log(this.tableSelection, 'tableSelection');
    },
    tableSelect(selection, row) {
      const selected = selection.length && row.indexOf(row) !== -1;
      if (selected) {
        this.tableSelection.push(row);
      }
      // console.log(selection, 'selection');
      // console.log(row);
    },
    // 获取题库树形结构数据
    getQuestionBankList() {
      // 用于给父节点加disabled
      function recursion(arr) {
        arr.forEach(item => {
          // ps: 这字段名真真真长。。。
          const children = item.practiceQuestionBankResultVOList;
          if (children && children.length > 0) {
            item.disabled = true;
            recursion(children);
          } else {
            delete item.practiceQuestionBankResultVOList;
          }
        });
      }

      this.$post('getQuestionBankList')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            recursion(body);
            this.treeData = body;
            // 如果是编辑状态下，回显数据
            if (this.row && this.row.practiceQuestionBankId) {
              this.$refs.tree.setCheckedKeys([this.row.practiceQuestionBankId]);
            }
          }
        });
    },
    // 获取该题库节点
    getNodeCourseList() {
      this.tableLoading = true;
      const params = {
        practiceQuestionBankId: this.currentNode.practiceQuestionBankId, // 题库id
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getExamSubjectByBankId', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.$nextTick(() => {
              // 将之前选中的数据回显勾上
              this.tableSelection.forEach(selected => {
                this.tableData.forEach(row => {
                  if (selected.subId === row.subId) {
                    this.$refs.table.toggleRowSelection(row, true);
                  }
                });
              });

              this.pagination.total = body.recordsTotal;
              this.tableLoading = false;
            });
          }
        });
    },
    // treeChange
    treeCheckChange(data, checked) {
      if (checked) {
        this.tableSelection = [];
        this.currentNode = data;
        this.$refs.tree.setCheckedKeys([]);
        this.$refs.tree.setCheckedKeys([data.practiceQuestionBankId]);
        this.$refs.table.clearSelection(); // 这里需要清空 reserve-selection 属性为我们保留的勾选数据
        this.getNodeCourseList();
      } else {
        this.tableSelection = [];
        this.tableData = [];
        this.pagination.total = 0;
      }
    },
    // tree 查询过滤
    filterNode(value, data) {
      if (!value) return true;
      return data.bankTitle.indexOf(value) !== -1;
    },
    open() {
      this.getQuestionBankList();
      // 编辑状态下
      if (this.row && this.row.practiceQuestionBankId) {
        this.currentNode = {
          practiceQuestionBankId: this.row.practiceQuestionBankId,
          bankTitle: this.row.bankTitle
        };
        this.getRightCourseList();
      }
    },
    submit() {
      const params = {
        bankInfoResultVOS: this.tableSelection,
        bankTitle: this.currentNode.bankTitle,
        courseId: this.row.courseId, // 课程id
        courseQuestionBankId: this.row ? this.row.courseQuestionBankId : '', // 已绑定的课程题库id, 新增则没有
        practiceQuestionBankId: this.currentNode.practiceQuestionBankId
      };
      this.$post('addOrUpdateCourseQuestionBank', params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.close();
            this.$parent.getTableList();
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.title {
  font-size: 17px;
  margin-bottom: 20px;
}

[required] {
  position: relative;
  padding-left: 8px;
  &:before {
    content: '*';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    color: red;
  }
}

.left {
  height: 500px;
  padding: 10px;
  border: 1px solid #EBEEF5;
}

.grey {
  color: #909399;
}

.filterInput {
  margin-bottom: 10px;
}

::v-deep .el-tree {
  height: 440px;
  overflow: auto;
}

::v-deep .el-tree-node__content:hover {
  background-color: oldlace;
}

::v-deep .el-table--border::after,
  .el-table--group::after,
  .el-table::before {
  background-color: #fff;
}

.el-table--border {
  border-right: 1px solid #EBEEF5;
}

.yz-table-pagination {
  border: 1px solid #EBEEF5;
  border-top: none;
  margin: 0;
  padding-bottom: 7px;
}

</style>
