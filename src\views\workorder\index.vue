<template>
  <div class="main">
    <!-- 顶部标签页 -->
    <el-tabs v-model="activeTab" type="card" class="px-4 py-2 bg-white">
      <el-tab-pane label="咨询类" name="1"></el-tab-pane>
      <el-tab-pane label="投诉类" name="2"></el-tab-pane>
      <el-tab-pane label="退款类" name="3"></el-tab-pane>
    </el-tabs>
    <div class="p-4 m-2 bg-white">
      <!-- 表单 -->
      <open-packup>
        <el-form
          ref="searchForm"
          class="yz-search-form"
          size="mini"
          :model="form"
          label-width="128px"
          @submit.native.prevent="search"
        >
          <template v-if="activeTab === '1' || activeTab === '2'">
            <el-form-item label="优先级" prop="priority">
              <common-select v-model="form.priority" type="orderPriority" />
            </el-form-item>
            <el-form-item
              v-if="activeTab === '1'"
              label="咨询分类"
              prop="category1DictId"
            >
              <double-select
                :value1.sync="form.category1DictId"
                :value2.sync="form.category2DictId"
                :extraParams1="{
                  parentId: 1,
                }"
                type1="workOrderDict"
                type2="workOrderDict"
              />
            </el-form-item>
            <el-form-item
              v-else-if="activeTab === '2'"
              label="投诉来源"
              prop="category1DictId"
            >
              <double-select
                :value1.sync="form.category1DictId"
                :value2.sync="form.category2DictId"
                type1="workOrderDict"
                type2="workOrderDict"
                :extraParams1="{
                  parentId: 2,
                }"
              />
            </el-form-item>
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="工单内容" prop="content">
              <el-input v-model="form.content" placeholder="请输入" />
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="退费原因" prop="refundReason">
              <common-select
                v-model="form.refundReason"
                type="workOrderDict"
                :extraParams="{
                  parentId: 5,
                }"
              />
            </el-form-item>
            <el-form-item label="工单号" prop="orderId">
              <el-input v-model="form.orderId" placeholder="请输入" />
            </el-form-item>
          </template>
          <el-form-item label="学员姓名" prop="stuName">
            <el-input v-model="form.stuName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="手机号" prop="stuPhone">
            <el-input v-model="form.stuPhone" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="学业编码" prop="stuXyCode">
            <el-input v-model="form.stuXyCode" placeholder="请输入" />
          </el-form-item>

          <template v-if="activeTab === '1' || activeTab === '2'">
            <el-form-item label="发起人" prop="createEmpName">
              <el-input v-model="form.createEmpName" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="受理人" prop="receiveEmpId">
              <common-select v-model="form.receiveEmpId" type="receiveEmp" />
            </el-form-item>
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                placement="bottom-start"
              />
            </el-form-item>

            <el-form-item label="完结时间" prop="finishTime">
              <el-date-picker
                v-model="form.finishTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                placement="bottom-start"
              />
            </el-form-item>
            <el-form-item label="远智编码" prop="stuYzCode">
              <el-input v-model="form.stuYzCode" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="工单状态" prop="consultStatus">
              <common-select
                v-if="activeTab === '1'"
                v-model="form.consultStatus"
                type="workOrderStatus"
              />
              <common-select
                v-else
                v-model="form.complaintStatus"
                type="workOrderStatus"
              />
            </el-form-item>
            <el-form-item label="流转原因" prop="circulationReason">
              <common-select
                v-model="form.circulationReason"
                :type="activeTab === '1' ? 'workOrderDict' : 'workOrderDict'"
                :extraParams="{
                  parentId: activeTab === '1' ? 4 : 6,
                }"
              />
            </el-form-item>
          </template>
          <template v-else>
            <el-form-item label="发起人" prop="createEmpId">
              <common-select v-model="form.createEmpId" type="createEmp" />
            </el-form-item>
            <el-form-item label="受理人" prop="receiveEmpId">
              <common-select v-model="form.receiveEmpId" type="receiveEmp" />
            </el-form-item>
            <el-form-item label="提出退费时间" prop="refundTime">
              <el-date-picker
                v-model="form.refundTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                placement="bottom-start"
              />
            </el-form-item>

            <el-form-item label="系统退费申请时间" prop="systemRefundTime">
              <el-date-picker
                v-model="form.systemRefundTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                placement="bottom-start"
              />
            </el-form-item>
          </template>
          <div class="search-reset-box">
            <el-button
              type="primary"
              icon="el-icon-search"
              native-type="submit"
              size="mini"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
          </div>
        </el-form>
      </open-packup>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox">
        <el-button
          type="primary"
          v-if="
            hasBatchTransferPermission &&
            (activeTab === '1' || activeTab === '2')
          "
          size="small"
          plain
          :disabled="disableTransferBtn"
          @click="openTransferDialog"
          >批量转交</el-button
        >
        <el-button
          type="success"
          size="small"
          icon="el-icon-upload2"
          @click="exportData"
          >导出</el-button
        >
        <el-button
          v-if="hasAddPermission && (activeTab === '1' || activeTab === '2')"
          type="primary"
          size="small"
          icon="el-icon-plus"
          @click="handleAdd"
          >新建工单</el-button
        >
      </div>
      <!-- 替换原表格为子组件 -->
      <consult-table
        v-if="activeTab === '1'"
        :loading="tableLoading"
        :tableData="tableData"
        @selection-change="handleSelectionChange"
        @go-to-detail="goToDetail"
        @del-order="delOrder"
        ref="multipleTable"
      />
      <complaint-table
        v-else-if="activeTab === '2'"
        :loading="tableLoading"
        :tableData="tableData"
        @selection-change="handleSelectionChange"
        @go-to-detail="goToDetail"
        @del-order="delOrder"
        ref="multipleTable"
      />
      <refund-table
        v-else
        :loading="tableLoading"
        :tableData="tableData"
        @selection-change="handleSelectionChange"
        @go-to-detail="goToDetail"
        @del-order="delOrder"
        ref="multipleTable"
      />
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>

    <!-- 转交弹窗 -->
    <transfer-dialog
      :visible.sync="transferDialogVisible"
      :selected-work-orders="checkRows"
      @success="handleTransferSuccess"
    />

    <!-- 新建工单弹窗 -->
    <create-workorder
      :visible.sync="createWorkorderVisible"
      @success="handleCreateSuccess"
    />

    <!-- 工单详情弹窗 -->
    <workorder-detail
      :visible.sync="workorderDetailVisible"
      :work-order-id="
        currentWorkOrder &&
        (currentWorkOrder.consultId ||
          currentWorkOrder.complaintId ||
          currentWorkOrder.workOrderId)
      "
      :workOrderType="activeTab === '3' ? fromType : activeTab"
      @refresh="getTableList"
    />
  </div>
</template>
<script>
import { exportExcel, handleDateControl } from "@/utils";
import { request } from "@/api";
import Cookies from 'js-cookie';

export default {
  components: {
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
    DoubleSelect: () => import("@/components/formTools/DoubleSelect"),
    TransferDialog: () => import("./components/transfer-dialog"),
    CreateWorkorder: () => import("./components/create-workorder"),
    WorkorderDetail: () => import("./components/workorder-detail"),
    ConsultTable: () => import("./components/consult-table"),
    ComplaintTable: () => import("./components/complaint-table"),
    RefundTable: () => import("./components/refund-table"),
  },
  mixins: [], // 引入查看手机号码混入
  computed: {
    // 禁用批量转交按钮
    disableTransferBtn() {
      return this.checkRows.length === 0;
    },
  },
  data() {
    return {
      hasAddPermission: false, // 是否有添加工单权限
      hasBatchTransferPermission: false, // 是否有批量转交权限
      activeTab: "1", // 当前激活的标签页，默认为咨询类
      tableLoading: false, // 表格加载状态
      markVisible: false, // 备注弹窗显示状态
      checkRows: [], // 当前选中的行
      transferDialogVisible: false, // 转交弹窗显示状态
      createWorkorderVisible: false, // 新建工单弹窗显示状态
      currentWorkOrder: null, // 当前工单
      fromType: "", // 工单类型
      workorderDetailVisible: false, // 工单详情弹窗显示状态
      form: {
        priority: "", // 优先级
        category1DictId: "", // 一级分类字典id
        category2DictId: "", // 二级分类字典id
        title: "", // 标题
        content: "", // 工单内容
        stuName: "", // 学员姓名
        stuPhone: "", // 手机号
        createEmpName: "", // 发起人
        stuXyCode: "", // 学业编码
        receiveEmpId: Cookies.get('COOKIE_USER_EMP') ? Cookies.get('COOKIE_USER_EMP') : "", // 受理人
        createTime: "", // 创建时间
        stuYzCode: "", // 远智编码
        finishTime: "", // 完结时间
        consultStatus: "", // 工单状态
        circulationReason: "", // 流转原因
        // 退款类表单字段保持原样
        refundReason: "",
        consultNo: "",
        refundTime: "",
        systemRefundTime: "",
      },
      lastQueryParamsStr: null, // 用于存储上一次查询参数的字符串形式
      distributionOrderNo: "", // 当前操作的订单编号
      remark: "", // 订单备注
      tableData: [], // 表格数据
      pagination: {
        page: 1, // 当前页码
        total: 0, // 总记录数
        limit: 10, // 每页记录数
      },
    };
  },
  provide() {
    return {
      orderType: () => this.orderType(),
    };
  },
  watch: {
    // 监听标签页变化，重新获取列表
    activeTab() {
      this.search(0);
      // 默认添加受理人为当前用户
      this.form = {
        receiveEmpId: Cookies.get('COOKIE_USER_EMP') ? Cookies.get('COOKIE_USER_EMP') : "", // 受理人
      }
      this.getTableList();
    },
  },
  async created() {
    this.getTableList();
    // 检查是否有添加权限和批量转交权限，如果有operateAll权限，则必定有添加权限和批量转交权限`
    this.hasAddPermission =
      (await this.$checkPermission("workOrder:add")) ||
      (await this.$checkPermission("workOrder:operateAll"));
    this.hasBatchTransferPermission =
      (await this.$checkPermission("workOrder:batchTransfer")) ||
      (await this.$checkPermission("workOrder:operateAll"));
  },
  methods: {
    /**
     * 提供当前工单类型
     */
    orderType() {
      return this.activeTab === "3" ? this.fromType : this.activeTab;
    },

    /**
     * 处理查询参数
     * 将表单数据转换为API所需的格式，处理日期范围等
     * @returns {Object} 处理后的查询参数对象
     */
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      console.log('formData:',formData);


      // 使用循环处理所有日期范围
      const dateFields = [
        {
          source: "createTime",
          startTarget: "createTimeStart",
          endTarget: "createTimeEnd",
        },
        {
          source: "finishTime",
          startTarget: "finishTimeStart",
          endTarget: "finishTimeEnd",
        },
        {
          source: "refundTime",
          startTarget: "startCreateTime",
          endTarget: "endCreateTime",
        },
        {
          source: "systemRefundTime",
          startTarget: "startRefundTime",
          endTarget: "endRefundTime",
        },
      ];

      dateFields.forEach((field) => {
        const dateRange = handleDateControl(formData[field.source]);
        formData[field.startTarget] = dateRange[0];
        formData[field.endTarget] = dateRange[1];
      });

      // 构建最终请求参数，添加分页信息
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
      };
      return data;
    },

    /**
     * 导出数据到Excel
     * 调用exportExcel工具函数将当前查询结果导出
     */
    exportData() {
      const data = this.handleQueryParams();
      exportExcel(
        this.activeTab === "1"
          ? "exportConsult"
          : this.activeTab === "2"
          ? "exportComplaint"
          : "exportRefund",
        data
      );
    },

    /**
     * 获取列表数据
     * 设置加载状态，获取查询参数，发送API请求，更新表格数据
     */
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      console.log("data:", data);


      // 根据标签页选择不同的API
      const apiList = ["getConsultList", "getComplaintList", "getRefundList"];
      this.$post(apiList[Number(this.activeTab) - 1], data, {
        json: true,
      }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data; // 更新表格数据
          this.pagination.total = body.recordsTotal; // 更新总记录数

          // 清空选择项
          this.checkRows = [];
        }
      });
    },

    /**
     * 搜索/重置查询
     * @param {number} type - 0表示重置，其他值表示搜索
     */
    search(type) {
      if (type === 0) {
        // 重置表单
        this.$refs["searchForm"].resetFields();
        this.form = Object.assign({}, this.$options.data.form);
      } else {
        // 搜索操作，重置页码并获取列表
        this.pagination.page = 1;
        this.getTableList();
      }
    },

    /**
     * 打开工单详情弹窗
     * @param {Object} rowData - 当前行数据
     */
    goToDetail(rowData) {
      this.currentWorkOrder = rowData;
      if (this.activeTab === "3") {
        this.fromType = String(rowData.fromType);
      }
      console.log(rowData, this.fromType);

      this.workorderDetailVisible = true;
    },

    // 删除工单
    delOrder(row) {
      const id = this.activeTab === "1" ? row.consultId : row.complaintId;
      request(
        "deleteWorkOrder",
        { id, orderType: this.activeTab },
        { json: true }
      ).then((res) => {
        if (res.ok) {
          this.$message.success(`${row.title}工单删除成功`);
          // 重新获取数据
          this.getTableList();
        }
      });
    },

    /**
     * 处理表格选择变化
     * @param {Array} val - 当前选中的行数据
     */
    handleSelectionChange(val) {
      this.checkRows = val;
      console.log(this.checkRows);
    },
    /**
     * 打开转交弹窗
     */
    openTransferDialog() {
      if (this.checkRows.length === 0) {
        this.$message.warning("请至少选择一条工单");
        return;
      }
      this.transferDialogVisible = true;
    },

    /**
     * 转交成功回调
     */
    handleTransferSuccess() {
      // 清空选择项
      this.$refs.multipleTable.clearSelection();
      // 重新获取数据
      this.getTableList();
    },

    /**
     * 新建工单
     * 打开新建工单弹窗
     */
    handleAdd() {
      this.createWorkorderVisible = true;
    },

    /**
     * 处理工单创建成功
     * 重新获取列表数据
     */
    handleCreateSuccess() {
      this.getTableList();
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  ::v-deep .el-tabs {
    .el-tabs__header {
      margin-bottom: 0;
      .el-tabs__item {
        background-color: #f2f2f2;
        &.is-active {
          background-color: #d7d7d7;
          color: #000;
        }
      }
    }
  }
}

.link-text {
  color: #409eff;
  text-decoration: underline;
  cursor: pointer;
}
</style>
