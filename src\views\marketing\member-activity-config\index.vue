<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item label='活动名称' prop='actName'>
        <el-input v-model="form.actName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='会员卡名称' prop='mebName'>
        <el-input v-model="form.mebName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='状态' prop='status'>
        <el-select v-model="form.status" clearable placeholder="请选择">
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="openActConfigDialog">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="actName" label="活动名称" align="center" />
      <el-table-column prop="mebName" label="会员卡名称" align="center" />
      <el-table-column prop="commonPrice" label="单价（元）" align="center" />
      <el-table-column prop="limitMax" label="库存" align="center" />
      <el-table-column prop="limitGet" label="限制领取人数" align="center" />
      <el-table-column prop="buyTimes" label="购买次数" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="lookbuyDetails(scope.row)">{{ scope.row.buyTimes }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="totalMoney" label="购买总金额" align="center" />
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status == 1? 'success':'danger' ">
            {{ scope.row.status == 1?'启用':'禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="推广" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="openShareDialog(scope.row)">分享</el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="editActConfig(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 活动配置弹窗 -->
    <active-config :id="mebDeployId" :title="acTitle" :visible.sync="acVisible" />
    <share-link title="分享" :visible.sync="slVisible" :mappingid="mappingid" />
    <buyDetails :visible.sync="detailsVisible" :mebId="mebId" />

  </div>
</template>
<script>
import activeConfig from './active-config.vue';
import shareLink from './share-link.vue';
import buyDetails from './member-details-dialog';
export default {
  components: {
    activeConfig,
    shareLink,
    buyDetails
  },
  data() {
    return {
      acVisible: false,
      slVisible: false,
      detailsVisible: false,
      tableLoading: false,
      mappingid: '',
      mebDeployId: '', // 活动配置id
      mebId: null,
      acTitle: '新增会员卡活动页配置',
      form: {
        actName: '',
        mebName: '',
        status: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    lookbuyDetails(row) {
      this.mebId = row.mebId;
      this.detailsVisible = true;
    },
    openActConfigDialog() {
      this.mebDeployId = null;
      this.acTitle = '新增会员卡活动页配置';
      this.acVisible = true;
    },
    editActConfig(row) {
      this.acTitle = '编辑会员卡活动页配置';
      this.mebDeployId = row.mebDeployId;
      this.acVisible = true;
    },
    openShareDialog(row) {
      this.mappingid = row.mebDeployId;
      this.slVisible = true;
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getActPageConfigList', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }

};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:20px;
}
</style>
