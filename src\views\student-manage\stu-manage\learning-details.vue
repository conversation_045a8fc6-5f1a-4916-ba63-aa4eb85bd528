<template>
  <common-dialog
    v-if="show"
    is-full
    width="1000px"
    :title="row.readPlanName + ' — ' + row.semesterName"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        :data="tableData"
        height="calc(100vh - 130px)"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :row-class-name="tableRowClassName"
      >
        <el-table-column label="打卡日期" align="center" prop="clockDay" />
        <el-table-column label="打卡课时" align="center" prop="clockClass" />
        <el-table-column label="应打卡" align="center" prop="shouldClockInTotal" />
        <el-table-column label="已打卡" align="center" prop="clockedTotal">
          <template slot-scope="scope">
            <el-link
              class="bold"
              type="success"
              @click="openClockDetails(scope.row, 0)"
            >{{ scope.row.clockedTotal }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="未打卡" align="center" prop="missingClockTotal">
          <template slot-scope="scope">
            <el-link
              class="bold"
              type="warning"
              @click="openClockDetails(scope.row, 1)"
            >{{ scope.row.missingClockTotal }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="从未打卡" align="center" prop="neverClockTotal">
          <template slot-scope="scope">
            <div v-if="scope.row.clockedTotal > 0">
              <el-link
                class="bold"
                type="danger"
                @click="openClockDetails(scope.row, 2)"
              >{{ scope.row.neverClockTotal }}</el-link>
            </div>
            <div v-else>
              <span class="mr-5">未统计</span>
              <el-tooltip class="item" effect="dark" content="当天无打卡未统计，请参照上一天" placement="top">
                <i class="el-icon-question" />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="当天打卡率" align="center" prop="thatDayRate">
          <template slot-scope="scope">
            {{ (scope.row.thatDayRate * 100).toFixed(2) + '%' }}
          </template>
        </el-table-column>
        <el-table-column label="打卡完成率" align="center" prop="finishRate">
          <template slot-scope="scope">
            {{ (scope.row.finishRate * 100).toFixed(2) + '%' }}
          </template>
        </el-table-column>
        <el-table-column label="发布心得数" align="center" prop="readCount">
          <template slot-scope="scope">
            <el-link
              class="bold"
              type="primary"
              @click="openClockDetails(scope.row, 3)"
            >{{ scope.row.readCount }}</el-link>
          </template>
        </el-table-column>
        <el-table-column label="发布心得人数" align="center" prop="userReadCount" />
        <el-table-column label="发布率" align="center" prop="readCountRate">
          <template slot-scope="scope">
            {{ (scope.row.readCountRate * 100).toFixed(2) + '%' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getLearningDetails"
        />
      </div>

    </div>

    <clock-details
      ref="clockDialog"
      :visible.sync="cdVisible"
      :tabIndex="tabIndex"
    />

  </common-dialog>
</template>

<script>
import clockDetails from './clock-details';
export default {
  components: {
    clockDetails
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      cdVisible: false,
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tabIndex: 0
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    tableRowClassName({ row, rowIndex }) {
      const clockDate = new Date(row.clockDay).getTime();
      const now = new Date().getTime();
      if (now < clockDate) {
        return 'disable';
      }
    },
    // 打开打卡详细弹窗
    openClockDetails(row, index) {
      this.tabIndex = index;
      this.$refs.clockDialog.semesterId = row.semesterId;
      this.$refs.clockDialog.arrangeId = row.arrangeId;
      this.cdVisible = true;
    },
    open() {
      this.getLearningDetails();
    },
    // 获取该学期的打卡心得数据
    getLearningDetails() {
      const params = {
        semesterId: this.row.semesterId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('getClockClassDetailList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.bold {
  font-weight: 600;
}

::v-deep .disable {
  //background: #c0c4cc;
  color: #c0c4cc !important;

  .el-link {
    color: #c0c4cc !important;
  }

}

.mr-5 {
  display: inline-block;
  margin-right: 5px;
}

</style>
