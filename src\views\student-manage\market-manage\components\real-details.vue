<template>
  <common-dialog
    width="800px"
    :title="title"
    :visible.sync="show"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <ul v-loading="tableLoading">
        <li v-for="(value, key) in titleObj" :key="key" class="real-li">
          <div class="real-li-title">{{ value }}</div>
          <div class="real-li-value">{{ tableData[key] }}</div>
        </li>
      </ul>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      titleObj: {
        stdName: '姓名：',
        mobile: '电话号码：',
        unvsName: '院校：',
        pfsnName: '专业：',
        grade: '年级：',
        tuitionTime: '缴费时间：',
        recruitName: '助学老师：',
        dpName: '助学部门：',
        followName: '跟进人：',
        followDpName: '跟进人部门：'
      },
      tableData:
        {
          /* 模拟数据 */
          // stdName: '张学友',
          // mobile: '2021-12-15 14:20:32',
          // unvsName: '进行中',
          // pfsnName: '19天',
          // grade: '2次',
          // tuitionTime: '5次',
          // recruitName: '进行中',
          // dpName: '20次',
          // followName: '9天',
          // followDpName: 'hhhhh'
        }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    open() {
      this.tableLoading = true;
      const { userId, scholarship } = this.row;
      this.$http({
        method: 'get',
        url: '/scholarshipChallenge/getStuInfo',
        params: { userId, scholarship }
      })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body;
            this.tableLoading = false;
          }
        });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.real-li {
  display: flex;
  line-height: 36px;
  .real-li-title {
    flex: none;
    min-width: 120px;
    text-align: right;
  }
  .real-li-value {
    flex: 4;
    text-align: left;
    color: #303133;
  }
}
</style>
