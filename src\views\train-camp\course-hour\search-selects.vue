<template>
  <el-select
    v-model="val"
    v-loadmore="loadmore"
    filterable
    remote
    clearable
    :remote-method="remoteMethod"
    v-bind="$attrs"
    v-on="$listeners"
    @change="_change"
    @clear="_clear"
    @visible-change="visibleChange"
  >
    <el-option
      v-for="item in filterOptions"
      :key="item[props.value] + generateKey()"
      :label="item[props.label]"
      :value="item[props.value]"
    />
  </el-select>
</template>
<script>
import { uuid } from '@/utils';
export default {
  name: 'RemoteSearchSelects',
  props: {
    value: {
      type: [String, Number, Boolean, Object],
      default: null
    },
    props: {
      type: Object,
      default: function() {
        /**
         * apiName '接口key值'
         * value option的value字段名
         * label option的label字段名
         * query 远程查询的参数名
         */
        return { apiName: '', value: '', label: '', query: '' };
      }
    },
    param: {
      type: Object,
      default: function() {
        return {};
      }
    },
    defaultOption: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      val: null,
      options: [],
      queryParam: null,
      defaultSelection: null,
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    };
  },
  computed: {
    filterOptions() {
      let arr = this.options;
      if (this.defaultSelection) {
        arr = this.options.filter(item => {
          const keyName = this.props.label;
          const valueName = this.props.value;
          if (item[keyName] !== this.defaultSelection[keyName] && item[valueName] !== this.defaultSelection[valueName]) {
            return true;
          }
        });
        arr.push(this.defaultSelection);
        console.log(this.defaultSelection, 'this.defaultSelection');
      }
      return arr;
    }
  },
  watch: {
    value(val) {
      this.val = val;
    },
    param(val) {
      this.queryParam = val;
    },
    defaultOption(value) {
      this.defaultSelection = value;
    }
  },
  mounted() {
    this.getOptions();
  },
  methods: {
    visibleChange(status) {
      if (!status) {
        this.resetGetOptions();
      }
    },
    loadmore() {
      if (this.pagination.total === this.options.length) {
        return;
      }
      this.pagination.page += 1;
      this.getOptions();
    },
    getOptions() {
      const data = {
        page: this.pagination.page,
        rows: this.pagination.limit,
        ...this.param
      };
      this.$post(this.props.apiName, data).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.options = this.options.concat(body);
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    resetGetOptions() {
      this.options = [];
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.param[this.props.query] = null;
      this.getOptions();
    },
    remoteMethod(query) {
      console.log(this.queryParam, 'cess');
      this.options = [];
      this.param[this.props.query] = query;
      this.pagination.page = 1;
      this.getOptions();
    },
    _clear() {
      // console.log(this.queryParam, 'param');
      this.options = [];
      this.pagination.page = 1;
      this.getOptions();
    },
    _change(value) {
      if (!value) {
        this.$emit('changeVal', { value, label: '' });
        return;
      }
      let obj = {};
      obj = this.filterOptions.find((item) => {
        return item[this.props.value] === value;
      });
      this.$emit('changeVal', { value, label: obj[this.props.label], source: obj });
      // this.resetGetOptions();
    },
    generateKey() {
      return uuid();
    }
  }
};
</script>
