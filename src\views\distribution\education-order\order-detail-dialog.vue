<template>
  <common-dialog
    is-full
    title="分销订单"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <open-packup>
        <el-form
          ref='searchForm'
          class='yz-search-form'
          size='mini'
          :model='form'
          label-width='120px'
          @submit.native.prevent='search'
        >
          <el-form-item label='远智编码' prop='yzCode'>
            <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
          </el-form-item>

          <el-form-item label='商品名称' prop='distributionGoodsName'>
            <el-input v-model="form.distributionGoodsName" placeholder="请输入商品名称" />
          </el-form-item>

          <el-form-item label='订单状态' prop='orderState'>
            <el-select v-model="form.orderState" clearable>
              <el-option label="已支付" value="pay" />
              <el-option label="已退款" value="refund" />
            </el-select>
          </el-form-item>

          <el-form-item label='分销人姓名' prop='distributionUserName'>
            <el-input v-model="form.distributionUserName" placeholder="请输入分销人姓名" />
          </el-form-item>

          <el-form-item label='购买人姓名' prop='buyUserName'>
            <el-input v-model="form.buyUserName" placeholder="请输入购买人姓名" />
          </el-form-item>

          <el-form-item label='分销人手机号' prop='distributionMobile'>
            <el-input v-model="form.distributionMobile" placeholder="请输入分销人手机号" />
          </el-form-item>

          <el-form-item label='购买人手机号' prop='buyUserMobile'>
            <el-input v-model="form.buyUserMobile" placeholder="请输入购买人手机号" />
          </el-form-item>

          <!-- <el-form-item label='结算时间' prop='settleTime'>
            <el-date-picker
              v-model="form.settleTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placement="bottom-start"
            />
          </el-form-item> -->

          <el-form-item label='付费时间' prop='payTime'>
            <el-date-picker
              v-model="form.payTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placement="bottom-start"
            />
          </el-form-item>

          <el-form-item label='结算状态' prop='distributionState'>
            <el-select v-model="form.distributionState" clearable>
              <el-option label="已结算" value="settled" />
              <el-option label="未结算" value="unsettle" />
              <el-option label="不结算" value="notSettle" />
              <el-option label="已回收" value="recycled" />
            </el-select>
          </el-form-item>

          <el-form-item label='邀约人' prop='buyUserMobile'>
            <el-input v-model="form.buyUserMobile" placeholder="请输入邀约人" />
          </el-form-item>

          <el-form-item label='邀约人手机号码' prop='buyUserMobile'>
            <el-input v-model="form.buyUserMobile" placeholder="请输入邀约人手机号" />
          </el-form-item>

          <el-form-item label='退费时间' prop='payTime'>
            <el-date-picker
              v-model="form.payTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              placement="bottom-start"
            />
          </el-form-item>

          <el-form-item label='是否有备注' prop='distributionState'>
            <el-select v-model="form.distributionState" clearable>
              <el-option label="是" value="settled" />
              <el-option label="否" value="unsettle" />
            </el-select>
          </el-form-item>

          <div class="search-reset-box">
            <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
          </div>

        </el-form>
      </open-packup>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="distributionGoodsName" label="商品信息" align="center" />
        <el-table-column prop="distributionGoodsType" label="商品类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.distributionGoodsType | tradeCode }}
          </template>
        </el-table-column>
        <el-table-column prop="buyUserName" label="购买人" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="buyUserMobile" label="购买人手机号" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.buyUserMobile }}</div>
            <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.buyUserId)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="orderState" label="订单状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.orderState === 'pay' ? '已支付' : '已退款' }}
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="付费时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.payTime | transformTimeStamp }}
          </template>
        </el-table-column>
        <el-table-column prop="payTime" label="退费时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.payTime | transformTimeStamp }}
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="应付金额" align="center" />
        <el-table-column prop="payAmount" label="实缴金额" align="center" />
        <el-table-column prop="test" label="抵扣" width="120px" align="center">
          <template slot-scope="scope">
            <p v-if="scope.row.couponScale">优惠券抵扣: {{ scope.row.couponScale }}</p>
            <p v-if="scope.row.demurrageScale">滞留金抵扣: {{ scope.row.demurrageScale }}</p>
            <p v-if="scope.row.zmScale">智米抵扣: {{ scope.row.zmScale }}</p>
          </template>
        </el-table-column>
        <el-table-column prop="actualRewardAmount" label="实际计佣金额" align="center" />
        <el-table-column prop="rewardAmount" label="邀约人" align="center" />
        <el-table-column prop="rewardAmount" label="邀约分成比例" align="center" />
        <el-table-column prop="rewardAmount" label="邀约佣金" align="center" />
        <el-table-column prop="distributionUserName" label="分销人" align="center" />
        <el-table-column prop="rewardAmount" label="分销分成比例" align="center" />
        <el-table-column prop="rewardAmount" label="分销佣金" align="center" />
        <el-table-column prop="rewardAmount" label="备注" align="center">
          <template slot-scope="scope">
            {{ scope.row.rewardAmount }}
            <el-button type="text" @click="editMark(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
    <add-mark-dialog :visible.sync="markVisible" />
  </common-dialog>
</template>

<script>
import { handleDateControl } from '@/utils';
import addMarkDialog from '../distribution-order/add-mark-dialog';
export default {
  components: {
    addMarkDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      markVisible: false,
      tableLoading: false,
      form: {
        distributionGoodsName: '',
        distributionState: '',
        orderState: '',
        distributionUserName: '',
        buyUserName: '',
        distributionMobile: '',
        buyUserMobile: '',
        settleTime: '',
        payTime: '',
        yzCode: ''
      },
      tableData: [],
      selects: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.payTime);
      formData.payStartDate = date[0];
      formData.payEndDate = date[1];
      const settleDate = handleDateControl(formData.settleTime);
      formData.settleStartDate = settleDate[0];
      formData.settleEndDate = settleDate[1];
      delete formData.payTime;
      delete formData.settleTime;
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('distributionOrderList', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleSelected(selects) {
      this.selects = selects;
    },
    batchSettle(status) {

    },
    // importData() {
    //   this.bidVisibel = true;
    // }
    exportData() {
      this.markVisible = !this.markVisible;
    },
    handleKickback() {
      this.orderDetailVisible = !this.orderDetailVisible;
    },
    open() {
      this.getTableList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    editMark() {
      this.markVisible = !this.markVisible;
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
