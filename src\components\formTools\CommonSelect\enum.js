//请求API引入
// import { getCertificateList } from "@/request/calendar";
// import { apiGetBankMajorList } from "@/request/creditBank.js";
// import { getUniversityList } from "@/request/college.js";
//选项列表
export const optionEnumList = [
  {
    type: "isOrNot",
    label: "是/否", // 标签
    defaultOptions: [
      { label: "否", value: 0 },
      { label: "是", value: 1 },
    ],
  },
  {
    type: "isOrNot_2",
    label: "是/否", // 标签
    defaultOptions: [
      { label: "否", value: 2 },
      { label: "是", value: 1 },
    ],
  },
  {
    type: "consultWorkOrderType",
    label: "工单类型",
    defaultOptions: [
      { label: "在线咨询单", value: 2 },
      { label: "电话咨询单", value: 1 },
      { label: "非报读学员咨询", value: 3 },
    ],
  },
  {
    type: "workOrderDict",
    label: "工单字典",
    api: "getWorkOrderDict",
    // defaultValueField: "name",
  },
  {
    type: "workOrderStatus",
    label: "工单状态",
    defaultOptions: [
      { label: "已完结", value: "1" },
      { label: "受理中", value: "2" },
      { label: "已挂起", value: "3" },
      { label: "已退费", value: "4" },
    ],
  },
  {
    type: "stuXyCode",
    label: "学业学员",
    api: "getStdIdList",
    remoteField: "stdId",
    nameRender: (item) => {
      return `${item.id || ""} ${item.name || ""}`.trim();
    },
  },
  {
    type: "yzCode",
    label: "远智学员",
    api: "getyzCodeList",
    remoteField: "yzCode",
    nameRender: (item) => {
      return `${item.id || ""} ${item.name || ""}`.trim();
    },
  },
  {
    type: "linkType",
    label: "关联方式",
    defaultOptions: [
      { label: "学业编码", value: "stuXyCode" },
      { label: "远智编码", value: "yzCode" },
      { label: "新建学员", value: "new" },
    ],
  },
  {
    type: "handler",
    label: "受理人",
    api: "getHandlerList",
    remoteField: "sname",
    defaultValueField: "empId",
    nameRender: (item) => {
      return `${item.empName || ""} ${item.dpName || ""}`.trim();
    },
  },
  {
    type: "createEmp",
    label: "已有发起人",
    api: "getCreateEmpList",
  },
  {
    type: "receiveEmp",
    label: "已有受理人",
    api: "getReceiveEmpList",
  },
  {
    type: "orderPriority",
    label: "优先级",
    defaultOptions: [
      { label: "紧急", value: 2 },
      { label: "一般", value: 1 },
    ],
  },
  {
    type: "orderStatus",
    label: "工单状态",
    defaultOptions: [
      { label: "已完结", value: 1 },
      { label: "受理中", value: 2 },
      { label: "已挂起", value: 3 },
    ],
  },
  // {
  //   type: "complaintCirculationReason",
  //   label: "投诉流转原因",
  //   defaultOptions: [{ label: "转交投退组", value: "转交投退组" }],
  // },
  {
    type: "workOrderNeedReply",
    label: "是否需要回复",
    defaultOptions: [
      { label: "短信回复", value: 1 },
      { label: "电话回复", value: 2 },
      { label: "无需回复", value: 3 },
    ],
  },
  {
    type: "isThreeSeven",
    label: "退费天数",
    defaultOptions: [
      { label: "3天内", value: "1" },
      { label: "7天内", value: "2" },
      { label: "7天外", value: "3" },
    ],
  },
  {
    type: "complaintReason",
    label: "投诉原因",
    defaultOptions: [
      { label: "服务态度", value: "serviceAttitude" },
      { label: "教学质量", value: "teachingQuality" },
      { label: "收费问题", value: "chargingIssue" },
      { label: "设施设备", value: "facilities" },
      { label: "其他原因", value: "other" },
    ],
  },
  /*——————————————————分割线——下面是还未对接的——————————————————*/
  {
    type: "orderType",
    label: "工单类型",
    defaultOptions: [
      { label: "咨询类", value: "1" },
      { label: "投诉类", value: "2" },
      { label: "退款类", value: "3" },
    ],
  },
  {
    type: "orderTypeDetail",
    label: "工单子类型",
    defaultOptions: [
      { label: "学习咨询", value: "study" },
      { label: "考试咨询", value: "exam" },
      { label: "证书咨询", value: "certificate" },
      { label: "其他咨询", value: "other" },
    ],
  },
  {
    type: "consultType1",
    label: "咨询一级分类",
    defaultOptions: [
      { label: "学习问题", value: "study" },
      { label: "考试问题", value: "exam" },
      { label: "证书问题", value: "certificate" },
      { label: "其他问题", value: "other" },
    ],
  },
  {
    type: "consultType2",
    label: "咨询二级分类",
    defaultOptions: [
      { label: "课程学习", value: "course" },
      { label: "作业提交", value: "homework" },
      { label: "考试安排", value: "examSchedule" },
      { label: "证书申请", value: "certificateApply" },
      { label: "其他", value: "other" },
    ],
  },
  {
    type: "complaintSource1",
    label: "投诉一级来源",
    defaultOptions: [
      { label: "电话投诉", value: "phone" },
      { label: "邮件投诉", value: "email" },
      { label: "现场投诉", value: "onsite" },
      { label: "其他渠道", value: "other" },
    ],
  },
  {
    type: "complaintSource2",
    label: "投诉二级来源",
    defaultOptions: [
      { label: "客服热线", value: "hotline" },
      { label: "投诉专线", value: "complaintLine" },
      { label: "官方邮箱", value: "officialEmail" },
      { label: "个人邮箱", value: "personalEmail" },
      { label: "校区前台", value: "campusReception" },
      { label: "其他", value: "other" },
    ],
  },
  {
    type: "flowReasonComplaint",
    label: "投诉流转原因",
    defaultOptions: [
      { label: "投诉原因", value: "serviceAttitude" },
      { label: "教学质量", value: "teachingQuality" },
      { label: "收费问题", value: "chargingIssue" },
      { label: "设施设备", value: "facilities" },
      { label: "其他原因", value: "other" },
    ],
  },
  {
    type: "flowReasonConsulting",
    label: "咨询流转原因",
    defaultOptions: [
      { label: "咨询原因", value: "serviceAttitude" },
      { label: "教学质量", value: "teachingQuality" },
      { label: "收费问题", value: "chargingIssue" },
      { label: "设施设备", value: "facilities" },
      { label: "其他原因", value: "other" },
    ],
  },
  {
    type: "refundReason",
    label: "退费原因",
    defaultOptions: [
      { label: "个人原因", value: "personal" },
      { label: "课程质量", value: "courseQuality" },
      { label: "教学服务", value: "teachingService" },
      { label: "招生宣传与实际不符", value: "falseAdvertising" },
      { label: "经济困难", value: "financial" },
      { label: "其他", value: "other" },
    ],
  },
  {
    type: "consultHandleStatus",
    label: "咨询处理情况",
    defaultOptions: [
      { label: "已达成一致", value: "1" },
      { label: "未达成一致", value: "2" },
      { label: "跟进中", value: "3" },
    ],
  },
  {
    type: "complaintHandleStatus",
    label: "投诉处理情况",
    defaultOptions: [
      { label: "已达成一致", value: "1" },
      { label: "未达成一致", value: "2" },
      { label: "跟进中", value: "3" },
      { label: "挽单成功", value: "4" },
      { label: "联系不上", value: "5" },
    ],
  },
];

/**
 * 根据给定的type和value从optionEnumList中获取对应的label。
 *
 * @param type 类型标识符
 * @param value 可选的值参数，用于从defaultOptions中匹配
 * @returns 对应的label，如果没有找到匹配项，则返回undefined
 */
export function getLabelByTypeValue(type, value) {
  // 查找与提供的type匹配的条目
  const option = optionEnumList.find((option) => option.type === type);

  if (!option) {
    return undefined; // 如果没有找到type匹配项，返回undefined
  }

  if (option.defaultOptions && value !== undefined) {
    // 如果存在defaultOptions并且提供了value，查找与value匹配的选项
    const match = option.defaultOptions.find(
      (opt) => opt[option.defaultValueField || "value"] === value
    );
    return match ? match.label : undefined; // 如果找到匹配项，返回其label；否则返回undefined
  }

  return ""; // 如果没有defaultOptions或没有提供value，直接返回空字符
}
