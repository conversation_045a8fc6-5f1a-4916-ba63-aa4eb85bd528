<template>
  <common-dialog
    show-footer
    title="直播频道新增"
    width="500px"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='请关联课程' prop='courseId'>
          <remote-search-selects
            v-model="form.courseId"
            :props="{apiName: 'courseSelect', value: 'courseId', label: 'courseName', query: 'courseName'}"
            :param="{courseName:''}"
          />
        </el-form-item>

        <el-form-item label='频道名称' prop='channelName'>
          <el-input
            v-model="form.channelName"
            placeholder="请输入"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='直播分类' prop='categoryId'>
          <el-select
            v-model="form.categoryId"
            filterable
            clearable
            placeholder="请选择"
            @change="getSelectedName"
          >
            <el-option
              v-for="(item,index) in liveType"
              :key="index"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      courseName: '',
      courseOptions: [],
      total: 0,
      loading: false,
      coursePagination: {
        page: 1,
        rows: 10
      },
      form: {
        courseId: '',
        channelName: '',
        categoryId: '',
        categoryName: ''
      },
      rules: {
        courseId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        categoryId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        channelName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
      },
      liveType: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      this.getLiveType();
    },
    getSelectedName(value) {
      const obj = this.liveType.find(item => {
        return item.categoryId === value;
      });

      if (Object.prototype.toString.call(obj) === '[object Object]') {
        this.form.categoryName = obj.categoryName;
      } else {
        this.form.categoryName = '';
      }
    },
    // 获取直播分类下拉
    getLiveType() {
      this.$post('getLiveChannelCategory')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.liveType = body;
          }
        });
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = {
            ...this.form,
            courseSystem: 7
          };
          this.$post('addChannel', data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$parent.getTableList();
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      // this.$refs['form'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
