<template>
  <common-dialog
    show-footer
    title="选择商品"
    :visible.sync='show'
    width="1200px"
    @open="init"
    @confirm="confirm"
    @close='close'
  >
    <el-row>
      <!-- left -->
      <el-col :span="12">
        <div class="left">
          <p class="title">待选列表</p>

          <!-- 表单 -->
          <el-form
            ref='searchForm'
            class="form"
            :inline="true"
            size='mini'
            :model='form'
            label-width='80px'
            @submit.native.prevent='search'
          >
            <el-form-item label='一级分类' prop='goodsClass'>
              <infinite-selects
                v-model="form.goodsClass"
                v-width="100"
                api-key="getSelectCfList"
                key-name="goodsTypeName"
                value-name='goodsTypeId'
                :param="{
                  goodsTypeLevel: '1',
                  goodsTypeName: ''
                }"
              />
            </el-form-item>

            <el-form-item label='二级分类' prop='goodsTypeId'>
              <infinite-selects
                v-model="form.goodsTypeId"
                v-width="100"
                api-key="getSelectCfList"
                key-name="goodsTypeName"
                value-name='goodsTypeId'
                :param="{
                  goodsTypeLevel: '2',
                  goodsTypeName: ''
                }"
              />
            </el-form-item>

            <el-form-item label='供应渠道' prop='supplyChannel'>
              <infinite-selects
                v-model="form.supplyChannel"
                v-width="100"
                api-key="getSupplierSelects"
                key-name="channelName"
                value-name='channelId'
                :param="{sName:''}"
              />
            </el-form-item>

            <el-form-item label='商品' prop='goodsName'>
              <el-input v-model="form.goodsName" v-width="100" placeholder="商品名称" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini" />
              <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
              <el-button type="primary" size="mini" @click="batchPushRightTable">添加</el-button>
            </el-form-item>

          </el-form>

          <el-table
            ref="notSelectedTable"
            v-loading='tableLoading'
            border
            size="small"
            height="400px"
            :data="tableData"
            style="width: 100%"
            header-cell-class-name='table-cell-header'
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" :selectable="isDisabled" width="55" align="center" />

            <el-table-column label="商品" align="center" prop="goodsName" />
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="pushRightTable(scope.row)">选择</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="yz-table-pagination">
            <pagination
              :total='pagination.total'
              :page.sync="pagination.page"
              :limit.sync="pagination.limit"
              @pagination="getTableList"
            />
          </div>

        </div>
      </el-col>

      <!-- right -->
      <el-col :span="12">
        <div class="right">
          <p class="title">已选列表</p>
          <div class="button-area">
            <el-button type="danger" size="mini" @click="batchDelete">批量删除</el-button>
          </div>
          <el-table
            ref="table"
            border
            size="small"
            height="400px"
            :data="selectedTableData"
            style="width: 100%"
            header-cell-class-name='table-cell-header'
            @selection-change="handleSelected"
          >
            <el-table-column type="selection" width="55" align="center" />
            <!-- <el-table-column type="index" index="1" prop="sort" width="50" label="排序" align="center" /> -->
            <el-table-column label="排序" align="center" prop="sort" width="160">
              <template slot-scope="scope">
                <!-- <input-number :value="scope.row.sort" :length='10' /> -->
                <!-- <el-input v-model.number="scope.row.sort" type="number" /> -->
                <el-input-number v-model="scope.row.sort" :controls="false" :min="0" :precision="0" size="small" />
              </template>
            </el-table-column>
            <el-table-column label="商品" align="center" prop="goodsName" />
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="deleteSelected(scope.row,scope.$index)">删除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>

    </el-row>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: null
    },
    type: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      show: false,
      selects: [],
      rightTableSelects: [],
      form: {
        goodsName: '',
        goodsClass: '',
        goodsTypeId: '',
        supplyChannel: '',
        status: 1
      },
      tableData: [],
      selectedTableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    },
    list(val) {
      this.selectedTableData = val;
    }
  },
  methods: {
    init() {
      this.getTableList();
    },
    confirm() {
      this.$emit('updateTable', this.selectedTableData);
      this.show = false;
    },
    isDisabled(row, index) {
      let status = 1;
      this.selectedTableData.find(item => {
        if (item.goodsBaseId === row.goodsBaseId) {
          status = 0;
          return true;
        }
      });
      return status;
    },
    deleteSelected(row, index) {
      this.selectedTableData.splice(index, 1);
    },
    pushRightTable(row) {
      if (this.type === '1') {
        if (this.selectedTableData.length >= 1) {
          this.$message({
            message: '单套餐只能选择一个课程',
            type: 'warning'
          });
          return false;
        }
      } else if (this.type === null) {
        this.$message({
          message: '请选择套餐类型',
          type: 'warning'
        });
        return false;
      }
      let isAdd = true;
      this.selectedTableData.find(item => {
        if (item.goodsBaseId === row.goodsBaseId) {
          isAdd = false;
          return true;
        }
      });
      if (isAdd) {
        // row.sort = null;
        this.selectedTableData.unshift(row);
      }
    },
    batchPushRightTable() {
      // 判断是否单课程
      if (this.type === '1') {
        if (this.selectedTableData.length >= 1) {
          this.$message({
            message: '单套餐只能选择一个课程',
            type: 'warning'
          });
          return;
        }
        if (this.selects.length > 1) {
          this.$message({
            message: '单套餐只能选择一个课程',
            type: 'warning'
          });
          return;
        }
      } else if (this.type === null) {
        this.$message({
          message: '请选择套餐类型',
          type: 'warning'
        });
        return false;
      }

      this.selects.forEach(row => {
        this.$refs['notSelectedTable'].clearSelection(row);
        this.selectedTableData.unshift(row);
      });
    },
    batchDelete() {
      const rowIndexs = [];
      this.rightTableSelects.forEach(row => {
        this.selectedTableData.find((item, index) => {
          if (row.goodsBaseId === item.goodsBaseId) {
            rowIndexs.push(index);
          }
        });
        rowIndexs.forEach(e => {
          this.selectedTableData.splice(e, 1);
        });
      });
    },
    handleSelected(selects) {
      this.rightTableSelects = selects;
    },
    handleSelectionChange(selects) {
      this.selects = selects;
    },
    getTableList() {
      this.tableLoading = true;
      const formData = JSON.parse(JSON.stringify(this.form));
      delete formData.time;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getGoodsList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.left,.right{
  padding:12px;
  box-sizing: border-box;
  .title{
    font-size:16px;
    font-weight: 600;
  }
  .form{
    margin-top:10px;
  }
  .button-area{
    margin-top:10px;
    height:94px;

  }
}

</style>
