<template>
  <common-dialog
    :show-footer="true"
    :title="title"
    :visible.sync='show'
    @open="init"
    @confirm='submit'
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="140px"
        :rules="rules"
      >
        <el-form-item label='类型' prop='marketingType'>
          <el-select
            v-model="form.marketingType"
            :disabled="!!id"
            @change="changeMapping"
          >
            <el-option label="会员卡" value="meb" />
            <el-option label="课程" value="course" />
            <!-- marketingType 类型-->
            <el-option label="读书计划" value="readPlan" />
          </el-select>
        </el-form-item>

        <el-form-item label='名称' prop='actName'>
          <el-input
            v-model="form.actName"
            maxlength="30"
            show-word-limit
            placeholder="请输入"
          />
        </el-form-item>

        <el-form-item label='关联会员卡/课程/读书计划' prop='mappingId'>
          <SearchSelect
            v-if="!!!id"
            v-model="form.mappingId"
            :disabled="!!id"
            clearable
            :options="mappingOptions"
            :props="{label:'itemName',value:'mappingId'}"
            @loadmore='loadmore'
            @search="selectSearch"
            @select="mappingChange"
            @open="openSelect"
          />
          <el-input
            v-else
            v-model="form.mappingName"
            :disabled="!!id"
          />
        </el-form-item>

        <el-form-item label='二维码图片' prop='fileUrl'>
          <upload-file
            :max-limit="1"
            :file-list='fileList'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='是否触发营销消息' prop='triggerStatus'>
          <el-radio-group v-model="form.triggerStatus" :disabled="id ? true : false">
            <el-radio label="1">触发</el-radio>
            <el-radio label="0">不触发</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="扫描进群页文案" prop="actExplain">
          <el-input
            v-model="form.actExplain"
            rows="5"
            maxlength="200"
            show-word-limit
            type="textarea"
          />
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const verificationSpace = (rule, value, callback) => {
      if (value.trim() !== '') {
        callback();
      } else {
        callback(new Error('请输入'));
      }
    };
    return {
      show: false,
      relatedOptions: [],
      defaultSelected: null,
      mappingOptions: [],
      form: {
        actName: '',
        mappingId: '',
        mappingName: '',
        marketingType: '',
        fileUrl: '',
        triggerStatus: '0',
        isAdd: 0,
        actExplain: ''
      },
      rules: {
        mappingId: [
          { required: true, trigger: 'change', message: '请选择' }
        ],
        actName: [
          { required: true, validator: verificationSpace, trigger: 'blur', message: '请输入' }
        ],
        actExplain: [
          { required: true, validator: verificationSpace, trigger: 'blur', message: '请输入' }
        ],
        marketingType: [
          { required: true, trigger: 'change', message: '请选择' }
        ],
        fileUrl: [
          { required: true, trigger: 'change', message: '请上传二维码图片' }
        ],
        triggerStatus: [
          { required: true, trigger: 'change', message: '请选择' }
        ]
      },
      fileList: [],
      selector: {
        page: 0,
        rows: 10,
        total: 0
      },
      timer: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    openSelect() {
      this.selector.page = 1;
      this.mappingOptions = [];
      this.getRelatedOptions();
    },
    loadmore() {
      if (this.mappingOptions.length === this.selector.total) {
        return;
      }
      this.selector.page += 1;
      this.getRelatedOptions();
    },
    selectSearch(value) {
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.selector.page = 1;
        this.mappingOptions = [];
        this.getRelatedOptions(value);
      }, 300);
    },
    mappingChange(obj) {
      if (obj) {
        this.form.mappingName = obj.itemName;
      }
    },
    init() {
      if (this.id) {
        const data = {
          actId: this.id
        };
        this.$post('getActivityInfo', data).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.marketingType = body.marketingType;
            this.form.actName = body.actName;
            this.form.mappingId = body.mappingId;
            this.form.mappingName = body.mappingName;
            this.form.fileUrl = body.imgUrl;
            this.form.triggerStatus = body.triggerStatus;
            this.form.actExplain = body.actExplain;
            this.fileList.push({
              url: ossUri + body.imgUrl
            });
            this.getRelatedOptions();
          }
        });
      }
    },
    changeMapping() {
      this.form.mappingId = '';
      this.mappingOptions = [];
    },
    getRelatedOptions(keyword) {
      const data = {
        itemType: this.form.marketingType,
        itemName: keyword,
        type: this.form.marketingType === 'readPlan' ? 'readPlan' : '',
        page: this.selector.page,
        rows: this.selector.rows
      };
      this.$post('getMebCardOrCommodity', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          if (Array.isArray(body)) {
            this.mappingOptions = this.mappingOptions.concat(body);
          }
        }
      });
    },
    handleRemoveImg({ file, fileList }) {
      this.form.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.fileUrl = response;
      this.form.isAdd = 1;
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const data = {
            ...this.form
          };
          let apiKey = 'newAct';
          if (this.id) {
            apiKey = 'editAct';
            data.actId = this.id;
          }

          this.$post(apiKey, data).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$parent.getTableList();
              this.show = false;
            }
          });
        }
      });
    },
    controlShow(value) {
      this.form.triggerStatus = '0';
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
