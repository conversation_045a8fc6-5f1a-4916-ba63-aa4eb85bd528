<template>
  <common-dialog
    title="佣金结算导出"
    :visible.sync='show'
    :showFooter="true"
    width="400px"
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='选择结算月份' prop='name'>
          <el-date-picker
            v-model="form.payTime"
            type="month"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      aeDialogShow: false,
      form: {
        time: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false,
      curEditRowData: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleDeleteMaterial(rowData) {
      const params = { materialId: rowData.materialId };
      this.$post('deleteGoodsShareMaterial', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    open() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
