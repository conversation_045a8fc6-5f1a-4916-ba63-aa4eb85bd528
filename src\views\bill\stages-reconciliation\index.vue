<template>
  <div>
    <open-packup>
      <el-form
        ref="searchForm"
        class="yz-search-form"
        size="mini"
        :model="form"
        label-width="120px"
        @submit.native.prevent="search"
      >
        <el-form-item label="远智编码" prop="yzCode">
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>

        <el-form-item label="手机号" prop="mobile">
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="缴费名称" prop="goodsShelfId">
          <remote-search-selects
            v-model="form.goodsShelfId"
            :props="{
              apiName: 'findShelfSelectList',
              value: 'goodsShelfId',
              label: 'goodsShelfName',
              query: 'goodsShelfName',
            }"
          />
        </el-form-item>

        <el-form-item label="订单渠道" prop="appType">
          <el-select v-model="form.appType" placeholder="请选择订单渠道">
            <el-option label="微信" value="WECHAT" />
            <el-option label="APP" value="APP" />
          </el-select>
        </el-form-item>

        <el-form-item label="订单类型" prop="goodsShelfType">
          <el-select
            v-model="form.goodsShelfType"
            clearable
            placeholder="请选择订单类型"
          >
            <el-option label="单课程套餐" value="1" />
            <el-option label="组合课程套餐" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="对账类型" prop="tradeCode">
          <el-select
            v-model="form.tradeCode"
            clearable
            placeholder="请选择对账类型"
          >
            <el-option label="套餐课程" value="course" />
            <el-option label="读书计划课程" value="readPlan" />
            <el-option label="学霸卡" value="studycard" />
            <el-option label="训练营课程" value="training" />
          </el-select>
        </el-form-item>

        <el-form-item label="收费方式" prop="paymentType">
          <el-select
            v-model="form.paymentType"
            clearable
            placeholder="请选择收费方式"
          >
            <el-option
              v-for="item in $dictJson['paymentType']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="支付状态" prop="paymentStatus">
          <el-select v-model="form.paymentStatus" placeholder="请选择" clearable>
            <el-option label="待支付" value="0" />
            <el-option label="已支付" value="1" />
            <el-option label="已退款" value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="单据号" prop="payNo">
          <el-input v-model="form.payNo" placeholder="请输入单据号" />
        </el-form-item>

        <el-form-item label="第三方单号" prop="outNo">
          <el-input v-model="form.outNo" placeholder="请输入第三方单号" />
        </el-form-item>

        <el-form-item label="缴费起止时间" prop="time">
          <el-date-picker
            v-model="form.time"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <el-form-item label="应付金额区间" prop="amountsPay">
          <el-input-number
            v-model="form.startAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最低"
          />
          -
          <el-input-number
            v-model="form.endAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最高"
          />
        </el-form-item>
        <el-form-item label="实缴金额区间" prop="orderAmount">
          <el-input-number
            v-model="form.payStartAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最低"
          />
          -
          <el-input-number
            v-model="form.payEndAmount"
            style="width: 48%"
            :controls="false"
            :precision="2"
            placeholder="最高"
          />
        </el-form-item>

        <el-form-item label="订单来源" prop="orderChannel">
          <el-select
            v-model="form.orderChannel"
            clearable
            placeholder="请选择订单来源"
          >
            <el-option label="零一裂变" value="lingYi" />
            <el-option label="远智" value="YZ" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button
            type="primary"
            icon="el-icon-search"
            native-type="submit"
            size="mini"
          >搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
        </div>
      </el-form>
    </open-packup>

    <!-- 按钮区 -->
    <div class="sr-btnbox">
      <div class="left">
        累计首付支付{{ tiredTotalPrice }}元，累计尾款支付{{ payNumber }}元；
      </div>
      <el-button
        type="success"
        size="small"
        icon="el-icon-upload2"
        @click="exportSetMeal"
      >导出对账</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column label="订单来源" align="center" prop="orderChannel">
        <template slot-scope="scope">
          {{ scope.row.orderChannel || '' }}
        </template>
      </el-table-column>
      <el-table-column label="订单渠道" align="center" prop="appType">
        <template slot-scope="scope">
          {{ scope.row.appType || '' }}
        </template>
      </el-table-column>
      <el-table-column label="订单类型" align="center" prop="goodsShelfType">
        <template slot-scope="scope">
          {{ scope.row.goodsShelfType || '' }}
        </template>
      </el-table-column>
      <el-table-column label="远智编码" prop="yzCode" align="center" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="昵称" align="center" prop="nickName" />
      <el-table-column label="手机号" align="center" prop="mobile" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile }}</span>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column label="缴费名称" align="center" prop="goodsShelfName" />
      <el-table-column label="市场价" align="center" prop="marketPrice" />
      <el-table-column label="应付金额" align="center" prop="totalPrice">
        <template slot-scope="scope">
          {{ scope.row.totalPrice + "元" }}
        </template>
      </el-table-column>
      <el-table-column label="实缴金额" align="center" prop="payAmount">
        <template slot-scope="scope">
          {{ scope.row.payAmount?`${scope.row.payAmount}元`:0 }}
        </template>
      </el-table-column>
      <el-table-column label="智米抵扣" align="center" prop="zmScale">
        <template slot-scope="scope">
          {{ scope.row.zmScale?`${scope.row.zmScale}元`:0 }}
        </template>
      </el-table-column>
      <el-table-column label="滞留抵扣" align="center" prop="demurrageScale" />
      <el-table-column label="优惠券抵扣(元)" align="center" prop="couponScale">
        <template slot-scope="scope">
          <el-link
            v-if="scope.row.couponScale !== 0"
            type="success"
            @click="couponDetails(scope.row)"
          >{{ scope.row.couponScale }}
          </el-link>
          <el-link
            v-else-if="scope.row.couponScale === 0"
            type="success"
          >{{ scope.row.couponScale }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="缴费日期" align="center" prop="paymentTime" width="150px" />
      <el-table-column label="费用项" align="center" prop="installmentTexts" width="150px" />
      <el-table-column label="单据号" align="center" prop="payNo" />
      <el-table-column label="第三方单据号" align="center" prop="outNo" />
      <el-table-column label="收款方式" align="center" prop="paymentType">
        <template slot-scope="scope">
          {{ scope.row.paymentType }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" align="center" prop="paymentStatus">
        <template slot-scope="scope">
          {{ scope.row.paymentStatus }}
        </template>
      </el-table-column>
      <el-table-column label="分期状态" align="center" prop="installmentStatus" />
      <el-table-column label="退款金额" align="center" prop="refundAmount" />
      <el-table-column label="邀约人" align="center" prop="invitationEmpName" />
      <el-table-column label="分销人" align="center" prop="distributionEmpName" />
      <el-table-column label="备注" align="center" prop="remark" width="100px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <p class="yz-button-ptext">{{ scope.row.remark }}</p>
            <el-button type="text" icon="el-icon-edit-outline" @click="openRemark(scope.row)" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="170px">
        <template slot-scope="scope">
          <div v-if="scope.row.installmentType === 1" class="sr-options">
            <el-button v-if="scope.row.refundStatus === 0 || scope.row.refundStatus === 3" class="sr-options-btn1" type="text" @click="openRefund(1,scope.row)">申请退首付</el-button>
            <el-button v-if="scope.row.refundStatus === 1" class="sr-options-btn2" type="text" @click="openRefund(2,scope.row)">审批退款</el-button>
            <el-button v-if="scope.row.refundStatus" class="sr-options-btn3" type="text" @click="openRefund(3,scope.row)">操作记录</el-button>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total="pagination.total" :page.sync="pagination.page" :limit.sync="pagination.limit" @pagination="getTableList" />
    </div>

    <!-- 弹窗：备注 -->
    <remark-dialog :visible.sync="rdVisible" :remark="currentRemark" :order-id="currentOrderId" @close="closeRemark" />

    <!-- 弹窗：申请退款、审核退款、退款记录 -->
    <refund-dialog :showRefund.sync="showRefund" :order-id="currentOrderId" :rudType="refundType" :title="refundTitle" @close="closeRefund" />
  </div>
</template>
<script>
import remarkDialog from './remark-dialog';
import refundDialog from './refund-dialog';
import { TABLE_HEIGHT } from '@/config/constant';
import openPackup from '@/components/open-packup';
import { handleDateControl, exportExcel } from '@/utils';
import LookMobile from '@/mixins/LookMobile';
export default {
  components: { openPackup, remarkDialog, refundDialog },
  mixins: [LookMobile],
  data() {
    return {
      haveExportPre: false,
      haveDetailPre: false,
      haveApprovalPre: false,
      haveAbortPre: false,
      refundTitle: '首付退款',
      refundType: 1,
      showRefund: false,
      tableLoading: false,
      cpVisible: false,
      currentcpId: false,
      obVisible: false,
      currentRow: null,
      form: {
        goodsShelfId: '',
        time: '',
        userName: '',
        mobile: '',
        paymentStatus: '',
        goodsShelfName: '',
        goodsShelfType: '',
        appType: '',
        paymentType: '',
        payNo: '',
        outNo: '',
        createStartTime: '',
        createEndTime: '',
        startAmount: undefined,
        endAmount: undefined,
        payStartAmount: undefined,
        payEndAmount: undefined,
        tradeCode: '',
        orderChannel: '',
        yzCode: ''
      },
      currentRemark: '',
      currentOrderId: null,
      table_height: TABLE_HEIGHT,
      rdVisible: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tiredTotalPrice: 0, // 套餐累计付费
      payNumber: 0 // 累计付费人数
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 优惠券详情
    couponDetails(row) {
      this.currentRow = row;
      this.cpVisible = true;
    },
    getSetMealBillInfo() {
      const data = this.handleQueryParams();
      this.$post('getOrderSummary', data).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          this.tiredTotalPrice = body.downPaymentAmount || 0;
          this.payNumber = body.balancePaymentAmount || 0;
        }
      });
    },
    exportSetMeal() {
      if (!this.haveExportPre) {
        this.$message({ message: '您的权限不足，无法使用当前功能', type: 'error' });
        return false;
      }
      const data = this.handleQueryParams();
      exportExcel('exportPuGoodsInstall', { ...data });
    },
    openRemark(row) {
      this.currentOrderId = row.id;
      this.currentRemark = row.remark;
      this.rdVisible = true;
    },
    closeRemark() {
      this.rdVisible = false;
      this.currentOrderId = '';
      this.currentRemark = '';
    },
    openRefund(type, row) {
      if (type === 1) {
        this.refundTitle = '申请退款';
        if (!this.haveApprovalPre) {
          this.$message({ message: '您的权限不足，无法使用当前功能', type: 'error' });
          return false;
        }
      } else if (type === 2) {
        this.refundTitle = '审核退款';
        if (!this.haveApprovalPre) {
          this.$message({ message: '您的权限不足，无法使用当前功能', type: 'error' });
          return false;
        }
      } else {
        this.refundTitle = '退款记录';
        if (!this.haveDetailPre) {
          this.$message({ message: '您的权限不足，无法使用当前功能', type: 'error' });
          return false;
        }
      }
      this.refundType = type;
      this.currentOrderId = row.id;
      this.showRefund = true;
    },
    closeRefund() {
      this.showRefund = false;
      this.currentOrderId = '';
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.time);
      formData.createStartTime = date[0];
      formData.createEndTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        page: this.pagination.page,
        rows: this.pagination.limit
      };
      return data;
    },
    async getTableList() {
      this.tableLoading = true;
      this.getSetMealBillInfo();
      // 校验用户权限
      const res = await this.$post('getPermission', {}, { json: 'application/json' });
      this.todoPermission(res);
      // 获取数据列表
      const data = this.handleQueryParams();
      this.$post('getPuGoodsInstall', data, { json: 'application/json' }).then((res) => {
        const { code, body } = res;
        if (code === '00') {
          this.tableLoading = false;
          body?.data.map(item => {
            // 缴费日期
            item.paymentTime = item.paymentTime?.replace('.0', '');
            item.refundStatus = Number(item.refundStatus);
            // 费用项：
            item.installmentTexts = '';
            item.installmentType = Number(item.installmentType);
            switch (item.installmentType) {
              case 0:
                item.installmentTexts = '抵扣项';
                item.installmentStatus = '无';
                break;
              case 1:
                item.installmentTexts = '首付';
                item.installmentStatus = '无';
                break;
              case 2:
                item.installmentTexts = '尾款';
                break;
              case 3:
                item.installmentTexts = '常规退费';
                break;
              case 4:
                item.installmentTexts = '利息补贴';
                break;
              default:
                item.installmentTexts = '';
                break;
            }
          });
          this.tableData = body?.data;
          this.pagination.total = body?.recordsTotal;
        }
      });
    },
    todoPermission(res) {
      const { code: _codes, body: _bodys } = res;
      this.haveExportPre = false;
      this.haveDetailPre = false;
      this.haveApprovalPre = false;
      this.haveAbortPre = false;
      if (_codes === '00') {
        _bodys.forEach(item => {
          if (item === 'installment:export') this.haveExportPre = true;
          if (item === 'installment:detail') this.haveDetailPre = true;
          if (item === 'installment:approval') this.haveApprovalPre = true;
          if (item === 'installment:abort') this.haveAbortPre = true;
        });
      }
      console.log('haveExportPre----close', this.haveExportPre);
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
        this.form.startAmount = undefined;
        this.form.endAmount = undefined;
        this.form.payStartAmount = undefined;
        this.form.payEndAmount = undefined;
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    orderBind(row) {
      this.currentRow = row;
      this.obVisible = true;
    }
  }
};
</script>
<style lang="scss">
.sr-btnbox {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  .left {
    color: red;
    float: left;
  }
}
.sr-options {
  display: flex;
  align-items: center;
  &-btn1 {
    color: rgb(109, 171, 241);
  }
  &-btn2 {
    color: rgb(115, 190, 108);
  }
  &-btn3 {
    color: rgb(50, 51, 51);
  }
}
.yz-button-ptext {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  box-orient: vertical;
  -webkit-box-orient: vertical;
  line-clamp: 3;
  -webkit-line-clamp: 3;
}
</style>
