<template>
  <common-dialog
    :show-footer="true"
    width="60%"
    title="新手任务配置页"
    :visible.sync='show'
    :confirmLoading="confirmLoading"
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="formModal"
        :model="form"
        label-width="130x"
        label-suffix="："
        size="small"
      >
        <el-form-item label="新手任务明细" required>
          <p>展示启用状态下的全部任务</p>
        </el-form-item>
        <el-table ref="tableRef" size="small" border :data="tableData">
          <el-table-column prop="id" label="任务id" align="center" />
          <el-table-column prop="taskMode" label="任务模式" align="center">
            <template slot-scope="scope">
              {{ scope.row.taskMode | taskModeEnum }}
            </template>
          </el-table-column>
          <el-table-column prop="taskTypeName" label="任务类型" align="center" />
          <el-table-column prop="taskTitle" label="任务标题" align="center" />
          <el-table-column prop="taskDesc" label="任务明细" align="center" />
          <el-table-column prop="taskZhimi" label="单次奖励智米数" align="center" />
          <el-table-column prop="taskCompleteTimes" label="每日任务上限" align="center" />
          <el-table-column
            label="操作"
            align="center"
            width="100"
            fixed="right"
          >
            <template slot-scope="scope">
              <div style="display: flex;">
                <el-button v-if="scope.$index !== 0" style="margin-right: auto;" type="primary" size="mini" circle icon="el-icon-top" @click="handleUpAndDown(scope.$index, scope.$index - 1)" />
                <el-button v-if="scope.$index !== tableData.length - 1" style="margin-left: auto;" type="primary" size="mini" circle icon="el-icon-bottom" @click="handleUpAndDown(scope.$index, scope.$index + 1)" />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { taskMode } from './../../type';
import { arrToEnum } from '@/utils';
const taskModeEnum = arrToEnum(taskMode);
export default {
  filters: {
    taskModeEnum(val) {
      return taskModeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      confirmLoading: false,
      tableLoading: false,
      show: false,
      tableData: [],
      form: {}
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    // 上移和下移
    handleUpAndDown(oldIndex, newIndex) {
      // 表格
      const tableData = this.tableData;
      const page = tableData[oldIndex];
      tableData.splice(oldIndex, 1);
      tableData.splice(newIndex, 0, page);
    },
    open() {
      this.getTableList();
    },
    // 获取列表数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const params = {
        pageNum: 0,
        pageSize: 100,
        status: 1
      };
      this.$post('getNewbieTaskConfigList', params, { json: true }).then((res) => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body?.data.sort((a, b) => a.sort - b.sort);
        }
      });
    },
    submit() {
      this.confirmLoading = true;
      const params = this.tableData.map((item, index) => {
        return {
          id: item.id,
          sort: index + 1
        };
      });
      this.$post('sortEditNewbieTask', JSON.stringify(params), { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.show = false;
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.$parent.getTableList();
        }
      }).finally(() => {
        this.confirmLoading = false;
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
</style>
