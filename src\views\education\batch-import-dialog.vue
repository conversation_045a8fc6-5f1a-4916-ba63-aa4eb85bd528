<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="导入佣金结算名单"
    :visible.sync="show"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form ref="searchForm" size="mini" :model="form" label-width="120px">
        <el-form-item label="模板：">
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label="选择文件：">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击选择文件</em>
            </div>
            <div slot="tip" class="el-upload__tip">
              <p>说明：</p>
              <p>
                (商品名称, 普通用户佣金比例, 会员用户佣金比例,
                结算周期）为必填列
              </p>
              <p style="color: red">
                请严格按模板填写，否则可能导致无法准确导入。
              </p>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { downUri } from "@/config/request";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
      // 控制对话框是否显示
    },
  },
  data() {
    return {
      show: false, // 内部对话框显示状态
      form: {}, // 表单数据对象（当前未使用字段）
      fileList: [], // 已选择的文件列表
      field: "multipartFile", // 上传字段名
      templateUrl: downUri + "/excel/distributionOrderImport.xlsx", // 模板下载地址
      action: "/distributionOrder/importSettleMoney", // 文件上传接口地址
    };
  },
  watch: {
    // 监听外部传入的visible属性变化，同步到内部show状态
    visible(val) {
      this.show = val;
    },
  },
  mounted() {},
  methods: {
    /**
     * 提交导入操作
     * 检查是否已选择文件，弹出确认框后提交上传
     */
    submit() {
      if (this.fileList.length > 0) {
        this.$confirm("确定导入此订单？导入后不可取消！请谨慎操作。", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$refs.upload.submit(); // 触发文件上传
        });
      } else {
        this.$message.error("请选择需要导入的文件");
      }
    },

    /**
     * 文件选择变更处理
     * @param {Object} file - 当前选择的文件
     * @param {Array} fileList - 选择的文件列表
     * 更新组件内的fileList状态
     */
    handleChange(file, fileList) {
      this.fileList = fileList;
    },

    /**
     * 文件上传成功回调
     * @param {Object} response - 上传接口返回数据
     * @param {Object} file - 上传的文件
     * @param {Array} fileList - 文件列表
     * 处理上传结果，成功则关闭对话框并刷新父组件
     */
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles(); // 清空文件列表
      if (response.code === "00") {
        this.$alert("导入成功！", "提示", {
          dangerouslyUseHTMLString: true,
        }).then(() => {
          this.show = false;
          this.$emit("refresh"); // 通知父组件刷新数据
        });
      } else {
        if (response.msg) {
          this.$message.error(response.msg); // 显示错误信息
        }
      }
    },

    /**
     * 处理超出文件数量限制
     * 当选择文件超过限制数量时，显示错误提示
     */
    handleExceed() {
      this.$message.error("每次只能上传一个文件");
    },

    /**
     * 关闭对话框
     * 通知父组件更新visible状态
     */
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped></style>
