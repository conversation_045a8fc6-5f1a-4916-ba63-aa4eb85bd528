<template>
  <common-dialog title="阶段" width="800px" :visible.sync='show' @open="open" @close='close'>
    <div class="dialog-main">
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAddStage">新增阶段</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        :height="400"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="stageSort" label="阶段序号" align="center" />
        <el-table-column prop="stageName" label="阶段名称" align="center" />
        <el-table-column prop="courseTimeCount" label="关联课时数量" align="center" />
        <el-table-column prop="allow" label="状态" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.allow===1?'success':'danger'">
              {{ scope.row.allow | tansformStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="编辑阶段" align="center">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="handlEdit(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="date" label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button :icon="scope.row.allow===1?'el-icon-circle-close':'el-icon-circle-check'" type="text" @click="handleEnableStage(scope.row)" />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>

    <common-dialog
      width="400px"
      show-footer
      :title="udTitle"
      :visible.sync='udVisible'
      @open="udOpen"
      @confirm='submitStage'
      @close='closeUpDialog'
    >
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          label-width="120px"
          :rules="rules"
        >
          <el-form-item label='阶段名称' prop='stageName'>
            <el-input v-model="form.stageName" maxlength="10" show-word-limit placeholder="请输入阶段名称" />
          </el-form-item>

          <el-form-item label='阶段顺序' prop='stageSort'>
            <el-input-number
              v-model="form.stageSort"
              placeholder="请输入阶段顺序"
              :controls="false"
              class="yz-input-number"
              :min="0"
              :max="10000"
            />
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>

  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    courseId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableData: [],
      udVisible: false,
      udTitle: '新增',
      tableLoading: false,
      form: {
        stageName: '',
        stageSort: undefined
      },
      rules: {
        stageName: [
          { required: true, trigger: 'blur', message: '请输入阶段名称' }
        ],
        stageSort: [
          { required: true, trigger: 'blur', message: '请输入阶段顺序' }
        ]
      },
      stageId: null,
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleEnableStage(row) {
      const data = {
        stageId: row.stageId,
        allow: row.allow === 1 ? '2' : '1'
      };
      this.$post('enableStage', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.getTableList();
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      });
    },
    closeUpDialog() {
      this.udVisible = false;
      this.$refs['form'].resetFields();
    },
    handleAddStage() {
      this.stageId = null;
      this.udTitle = '新增';
      this.udVisible = true;
    },
    handlEdit(row) {
      this.udVisible = true;
      this.udTitle = '编辑';
      this.stageId = row.stageId;
    },
    udOpen() {
      if (this.stageId) {
        this.$post('getStageSingle', { stageId: this.stageId }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form = {
              ...body
            };
          }
        });
      }
    },
    editSubmitStage() {
      const data = {
        stageId: this.stageId,
        ...this.form
      };
      this.$post('editStage', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.udVisible = false;
          this.getTableList();
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      });
    },
    addSubmitStage() {
      const data = {
        courseId: this.courseId,
        ...this.form
      };
      this.$post('addStage', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.udVisible = false;
          this.getTableList();
          this.$message({
            message: '操作成功',
            type: 'success'
          });
        }
      });
    },
    // 提交阶段
    submitStage() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.stageId) {
            this.editSubmitStage();
          } else {
            this.addSubmitStage();
          }
        } else {
          return false;
        }
      });
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        courseId: this.courseId
      };
      this.$post('getCourseStageList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    open() {
      this.getTableList();
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.el-input-number{
  width:100%;
}
</style>
