<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >

      <el-form-item label='类型' prop='marketingType'>
        <el-select v-model="form.marketingType" filterable placeholder="请选择">
          <el-option label="会员卡" value="meb" />
          <el-option label="课程" value="course" />
          <el-option label="读书计划" value="readPlan" />
        </el-select>
      </el-form-item>

      <el-form-item label='名称' prop='actName'>
        <el-input v-model="form.actName" placeholder="请输入活动名称" />
      </el-form-item>

      <el-form-item label='状态' prop='triggerStatus'>
        <el-select
          v-model="form.triggerStatus"
          filterable
          clearable
          placeholder="请选择"
        >
          <el-option label="触发" value="1" />
          <el-option label="不触发" value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="marketingType" label="类型" align="center">
        <template slot-scope="scope">
          {{ scope.row.marketingType | marketingType }}
        </template>
      </el-table-column>
      <el-table-column prop="actName" label="名称" align="center" />
      <el-table-column prop="imgUrl" label="绑定二维码" align="center" width="200">
        <template slot-scope="scope">
          <!-- style="object-fit: contain;width: 100%;height:50px;cursor: pointer;" -->
          <img
            style="object-fit: contain;width: 100%;cursor: pointer;"
            :src="scope.row.imgUrl | splitOssUrl"
            @click="enlargeSee(scope.row.imgUrl)"
          >
        </template>
      </el-table-column>
      <el-table-column prop="triggerStatus" label="是否触发二维码进群消息" align="center">
        <template slot-scope="scope">
          {{ scope.row.triggerStatus | triggerStatus }}
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 查看图片 -->
    <el-dialog width="750px" :visible.sync="dialogVisible" append-to-body>
      <div style="height:450px">
        <img width="100%" style="object-fit: contain;height: 100%;" :src="dialogImageUrl" alt="">
      </div>
    </el-dialog>

    <update-dialog :id="actId" :visible.sync="udVisible" :title="udTitle" />

  </div>
</template>
<script>
import updateDialog from './update-dialog.vue';
import { ossUri } from '@/config/request';
export default {
  components: {
    updateDialog
  },
  filters: {
    marketingType(val) {
      if (!val) return '';
      const data = {
        meb: '会员卡',
        course: '课程',
        readPlan: '读书计划'
      };
      return data[val];
    },
    triggerStatus(val) {
      if (!val) return '';
      const data = {
        '0': '不触发',
        '1': '触发'
      };
      return data[val];
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      tableLoading: false,
      udVisible: false,
      udTitle: '新增',
      form: {
        marketingType: '',
        actName: '',
        triggerStatus: ''
      },
      actId: null,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    enlargeSee(url) {
      this.dialogVisible = true;
      this.dialogImageUrl = ossUri + url;
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getActivityList', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
          this.tableLoading = false;
        }
      });
    },
    handleAdd() {
      this.actId = null;
      this.udVisible = true;
      this.udTitle = '新增';
    },
    handleEdit(row) {
      this.udVisible = true;
      this.udTitle = '编辑';
      this.actId = row.actId;
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }

};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:20px;
}
</style>
