<template>
  <div class="yz-base-container">
    <el-tabs v-model="activeName" type="border-card">
      <el-tab-pane label="勋章管理" name="manage">
        <manage />
      </el-tab-pane>
      <el-tab-pane label="勋章赠送" name="gift" lazy>
        <gift />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import manage from './components/manage/index';
import gift from './components/gift/index';
export default {
  components: {
    manage,
    gift
  },
  data() {
    return {
      activeName: 'manage'
    };
  }
};
</script>

<style lang = "scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
