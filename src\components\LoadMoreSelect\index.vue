<template>
  <!-- inputSelect  -->
  <div>
    <el-select
      v-model="currentValue"
      v-loadmore='load'
      filterable
      reserve-keyword
      clearable
      :remote="selectType !== 1"
      placeholder="请输入关键词"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="changeValue"
      @focus="focusEvent"
    >
      <!-- remote -->
      <el-option
        v-for="item in list"
        :key="item[keyValue]"
        :label="item[keyName]"
        :value="item[selectVal==='' ? keyName : selectVal]"
      />
    </el-select>
  </div>
</template>

<script>
// import axios from 'axios';
export default {
  name: 'LoadMoreSelect',
  directives: {
    loadmore: {
      inserted: function(el, binding) {
        // 获取element-ui定义好的scroll盒子
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');

        SELECTWRAP_DOM.addEventListener('scroll', function() {
          /*
                    * scrollHeight 获取元素内容高度(只读)
                    * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
                    * clientHeight 读取元素的可见高度(只读)
                    * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
                    * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
                    */
          const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight;

          if (CONDITION) {
            binding.value();
          }
        });
      }
    }
  },
  // props: ['value', 'keyName', 'url', 'keyValue'],
  /**
   *
     @param  keyName：接口参数的键、label值
     @param  keyValue：接口参数的值
     @param  selectVal:下拉框绑定的值
     @param  url:接口地址
     @param  selectType:  // 新增select方式判断 1为默认， 2为开启远程搜索且传入页码字段名为start，条数字段名为length
  */
  props: {
    value: {
      type: String,
      default: ''
    },
    selectType: {
      type: Number,
      default: 1
    },
    keyName: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    keyValue: {
      type: String,
      default: ''
    },
    selectVal: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      loading: false,
      currentValue: this.value,
      currentPage: this.selectType !== 1 ? 0 : 1,
      rows: 10,
      queryword: '',
      canmore: true
    };
  },
  watch: {
    value(val) {
      this.currentValue = val;
    }
  },
  mounted() {
  },
  methods: {
    // 聚焦
    focusEvent() {
      this.queryword = '';
      this.currentValue = '';
      this.canmore = true;
      this.currentPage = this.selectType !== 1 ? 0 : 1;
      this.loading = true;

      const data = this.selectType !== 1 ? {
        start: this.currentPage,
        length: this.rows
      } : {
        page: this.currentPage,
        rows: this.rows
      };
      data[this.keyName] = this.keyValue;
      console.log(data);

      this.$post(this.url, data).then(res => {
        const { body } = res;
        this.list = body.data;
        this.$emit('selectAllList', body.data);
        this.loading = false;
      });
    },

    remoteMethod(query) {
      console.log('query');
      console.log(query);
      this.queryword = query;
      this.currentPage = this.selectType !== 1 ? 0 : 1;
      if (query !== '') {
        this.loading = true;
        // 请求数据
        const data = this.selectType !== 1 ? {
          start: this.currentPage,
          length: this.rows
        } : {
          page: this.currentPage,
          rows: this.rows
        };

        data[this.keyName] = query;
        this.$post(this.url, data).then(res => {
          const { body } = res;
          this.list = body.data;
          this.loading = false;
        });
      }
    },
    changeValue(val) {
      console.log(val);
      this.$emit('input', val);
      this.$emit('selectCurrent', val);
    },
    load() {
      if (!this.canmore) {
        return;
      }
      this.currentPage++;
      // 请求数据
      const data = this.selectType !== 1 ? {
        start: this.currentPage,
        length: this.rows
      } : {
        page: this.currentPage,
        rows: this.rows
      };

      data[this.keyName] = this.queryword;
      this.$post(this.url, data).then(res => {
        const { body } = res;
        if (body.data.length > 0) {
          this.list = this.list.concat(body.data);
        } else {
          this.canmore = false;
        }
      });
    }
  }
};
</script>

<style>

</style>
