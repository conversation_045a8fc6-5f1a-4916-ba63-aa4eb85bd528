<template>
  <common-dialog
    :show-footer="true"
    width="1000px"
    title="学霸卡课程管理"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <div class="course-main">
        <el-transfer
          v-model="courseList"
          filterable
          :titles="['选择课程', '已选课程']"
          :button-texts="['移除', '选中 ']"
          :format="{
            noChecked: '${total}',
            hasChecked: '${checked}/${total}'
          }"
          :data="courseData"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      courseData: [],
      courseList: [],
      goodsShelfFreeList: [], // 已选中的数组
      // 分页
      pagination: {
        page: 1,
        total: 0,
        limit: 10000000
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
  },
  methods: {
    open() {
      const data = {
        page: this.pagination.page,
        rows: this.pagination.limit,
        goodsShelfName: '',
        shelfStatus: 1,
        goodShelfTradeCode: 'course'
      };

      this.$post('getSetMealList', data).then(res => {
        res.body.data.forEach(item => {
          this.courseData.push({ key: item.goodsShelfId, label: item.goodsShelfName });
        });
      });
    },
    submit() {
      // 在全部课程中筛选出已选中的课程
      // 全部课程 courseData 已选中的课程courseList
      console.log(this.courseList);
      if (this.courseList.length === 0) {
        this.$message.error('请选中数据');
        return;
      }
      this.courseData.map(item => {
        this.courseList.map(valItem => {
          if (item.key === valItem) {
            this.goodsShelfFreeList.push({
              cardType: 1,
              goodsShelfId: item.key + '',
              goodsShelfName: item.label
            });
          }
        });
      });
      // const data = {
      //   goodsShelfFreeList: this.goodsShelfFreeList
      // };

      this.$post('addGoodsFree', this.goodsShelfFreeList, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.$emit('update:visible', false);
          this.$parent.getTableList();
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
::v-deep .el-transfer__buttons{
  width: 20%;
}
::v-deep .el-transfer__buttons .el-button{
    display: block;
    margin: 0;
    margin-bottom: 10px;
  }

::v-deep .el-transfer-panel{
  width: 39%;
  height: 500px;
}
::v-deep .el-transfer-panel__list.is-filterable{
  height: 400px;
}
.course-main{
  display: flex;
  padding: 20px;
  justify-content: center;
  // align-items: center;
}
.el-transfer{
  width: 800px;
}
.el-table{
  margin-top: 10px;
}
</style>
