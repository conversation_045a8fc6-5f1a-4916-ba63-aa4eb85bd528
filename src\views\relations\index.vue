<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>
      <el-form-item label='用户姓名' prop='userName'>
        <el-input v-model="form.userName" placeholder="请输入用户姓名" />
      </el-form-item>

      <el-form-item label='手机号码' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入手机号码" />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='table-btnbox'>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="left">
            <span class="font-14">数据将于半小时后自动更新</span>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="align-right">
            <el-button type="success" size="small" icon="el-icon-upload2" @click="exportData">EXCEL导出</el-button>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      :height="table_height"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="userName" label="用户姓名" align="center" />
      <el-table-column prop="mobile" label="手机号" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.mobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="vipBuyNum" label="购买会员卡" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="lookMemberDetails(scope.row)">{{ scope.row.vipBuyNum }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="goodsShelfBuyNum" label="已购买课程" align="center">
        <template slot-scope="scope">
          <el-link type="success" @click="lookCourseDetails(scope.row)">{{ scope.row.goodsShelfBuyNum }} </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="已购买读书计划" align="center">
        <template slot-scope="scope">
          <el-link
            type="success"
            @click="lookBookDetails(scope.row)"
          >
            {{ scope.row.readPlanCount }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center" />
      <el-table-column label="备注" align="center" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" icon="el-icon-edit-outline" @click="editNote(scope.row)" />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <remark-dialog :id="currentEditId" :remark="currentRemark" :visible.sync="rdVisible" />
    <member-details-dialog :visible.sync="mddVisible" :user-id="currentUserId" />
    <course-details-dialog :visible.sync="cddVisible" :user-id="currentLookUserID" />
    <book-details-dialog :visible.sync="bddVisible" :user-id="currentBookUserID" />
  </div>
</template>
<script>
import { TABLE_HEIGHT } from '@/config/constant';
import { exportExcel } from '@/utils';
import remarkDialog from './remark-dialog';
import memberDetailsDialog from './member-details-dialog';
import courseDetailsDialog from './course-details-dialog';
import bookDetailsDialog from './book-details-dialog';
import LookMobile from '@/mixins/LookMobile';

export default {
  components: {
    remarkDialog,
    memberDetailsDialog,
    courseDetailsDialog,
    bookDetailsDialog
  },
  mixins: [LookMobile],
  data() {
    return {
      tableLoading: false,
      table_height: TABLE_HEIGHT,
      rdVisible: false,
      mddVisible: false,
      cddVisible: false,
      bddVisible: false,
      currentLookUserID: null,
      currentBookUserID: null,
      currentEditId: null,
      currentRemark: null,
      currentUserId: null,
      form: {
        userName: '',
        mobile: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    exportData() {
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      exportExcel('exportUserRelationshipList', data);
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getRelationList', data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    lookMemberDetails(row) {
      this.currentUserId = row.userId;
      this.mddVisible = true;
    },
    lookCourseDetails(row) {
      this.currentLookUserID = row.userId;
      this.cddVisible = true;
    },
    lookBookDetails(row) {
      this.currentBookUserID = row.userId;
      this.bddVisible = true;
    },
    editNote(row) {
      this.currentEditId = row.id;
      this.currentRemark = row.remark;
      this.rdVisible = true;
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
    margin-top:16px;
}
.table-btnbox{
  margin:20px 0 10px 0;
  .left{
    line-height: 33px;
  }
  .align-right{
    text-align:right;
  }
}
.font-14{
  font-size: 14px;
}
</style>
