<template>
  <div>
    <!-- 搜索表单 -->
    <el-form
      ref="searchForm"
      class="yz-search-form"
      size="mini"
      :model="form"
      label-width="120px"
      label-suffix=":"
      @submit.native.prevent="search"
    >
      <el-form-item label="远智编号" prop="yzCode">
        <el-input v-model="form.yzCode" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="姓名" prop="realName">
        <el-input v-model="form.realName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="手机" prop="mobile">
        <el-input v-model="form.mobile" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="勋章名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="勋章id" prop="medalTimeId">
        <el-input v-model="form.medalTimeId" placeholder="请输入" clearable />
      </el-form-item>

      <div class="search-reset-box">
        <el-button
          type="primary"
          icon="el-icon-search"
          native-type="submit"
          size="mini"
          :disabled="tableLoading"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          :disabled="tableLoading"
          @click="search(0)"
        />
      </div>
    </el-form>

    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" @click="giveMedalVisible = true">新增</el-button>
    </div>

    <!-- 赠送勋章弹窗 -->
    <give-medal-modal
      :visible.sync="giveMedalVisible"
      @refresh="getTableList"
    />

    <!-- 表格 -->
    <el-table
      ref="table"
      v-loading="tableLoading"
      border
      size="small"
      :data="tableData"
      style="width: 100%"
      header-cell-class-name="table-cell-header"
    >
      <el-table-column label="赠送对象" width="180" align="center">
        <template slot-scope="{ row }">
          <div style="text-align: left;">
            <div>远智编号：{{ row.yzCode }}</div>
            <div>真实姓名：{{ row.realName }}</div>
            <div>昵称：{{ row.nickname }}</div>
            <div>手机号码：{{ row.mobile }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="medalType" label="勋章类型" align="center">
        <template slot-scope="{ row }">
          {{ row.medalType | medalTypeEnum }}
        </template>
      </el-table-column>
      <el-table-column prop="medalTimeId" label="勋章id" align="center" />
      <el-table-column prop="name" label="勋章名称" align="center" />
      <el-table-column label="勋章获得日期" align="center">
        <template slot-scope="{ row }">
          {{ Number(row.gainTime) | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="createUser" label="赠送人" align="center" />
      <el-table-column prop="createTime" label="操作时间" align="center" />
      <el-table-column prop="remark" label="赠送勋章备注" align="center">
        <template slot-scope="{ row }">
          {{ row.remark || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template slot-scope="{ row }">
          <el-button
            type="primary"
            size="mini"
            @click="handleRemark(row)"
          >备注</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 备注弹窗 -->
    <remark-modal
      :visible.sync="remarkModalVisible"
      :current-row="currentRow"
    />

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total="pagination.total"
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

  </div>
</template>

<script>
import RemarkModal from './remark-modal.vue';
import GiveMedalModal from './give-medal-modal.vue';
import { medalTypeList } from '../../../type';
import { arrToEnum } from '@/utils';

const medalTypeEnum = arrToEnum(medalTypeList, 'value', 'name');

export default {
  name: 'MedalGift',
  components: {
    RemarkModal,
    GiveMedalModal
  },
  filters: {
    medalTypeEnum(val) {
      return medalTypeEnum[val] || '/';
    }
  },
  data() {
    return {
      // 搜索表单
      form: {
        realName: '',
        yzCode: '',
        mobile: '',
        medalTimeId: '',
        name: ''
      },
      // 表格加载状态
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      // 备注弹窗显示状态
      remarkModalVisible: false,
      // 当前选中行数据
      currentRow: {},
      // 赠送勋章弹窗显示状态
      giveMedalVisible: false
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 搜索
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      }
      this.pagination.page = 1;
      this.getTableList();
    },

    // 处理查询参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit
      };
      return data;
    },

    // 获取表格数据
    getTableList() {
      if (this.tableLoading) return;
      this.tableLoading = true;

      const data = this.handleQueryParams();

      this.$post('getUserMedalTimeGiftList', data, { json: true })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body?.data || [];
            this.pagination.total = body?.recordsTotal || 0;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    // 处理备注
    handleRemark(row) {
      this.currentRow = row;
      this.remarkModalVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.search-reset-box {
  display: flex;
  justify-content: center;
}
</style>
