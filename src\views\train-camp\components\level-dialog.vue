<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    title="标题"
    confirmText="保存"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <!-- <el-table-column type="index" label="课时序号" width="50" align="center" /> -->
        <el-table-column prop="sort" label="课时序号" align="center" />
        <el-table-column prop="classHourName" label="课时名称" align="center" />
        <el-table-column label="解锁模式" align="center" prop="tradeType">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-select v-if="scope.row.isEditLock" v-model="scope.row.unlockType" size='mini' @change="((value)=>{selectLock(value, scope.row,scope.$index)})">
                <el-option label="不需要解锁" value="0" />
                <el-option label="按日解锁" value="1" />
                <el-option label="立即解锁" value="2" />
              </el-select>
              <span v-if="!scope.row.isEditLock" class="lock-text">{{ scope.row.unlockType | lockChange }}</span>
              <el-button type="text" @click="editLock(scope.row,scope.$index)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  filters: {
    lockChange(val) {
      if (!val) return '';
      const obj = {
        '0': '不需要解锁',
        '1': '按日解锁',
        '2': '立即解锁'
      };
      return obj[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      isEditLock: false,
      unlockType: 0,
      levelList: [],
      levelTempList: [],
      form: {
        demo1: '',
        demo2: '',
        time: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    open() {
      this.getTableList();
    },
    selectLock(val, item, index) {
      this.levelList[index].unlockType = val;
    },
    getAddLevelParams(bodyList) {
      this.levelList = [];
      bodyList.map(item => {
        const params = {
          courseId: this.row.courseId,
          trainingId: this.row.trainingId,
          paperId: item.paperId,
          classHourId: item.classHourId,
          sort: item.sort,
          unlockType: item.unlockType || ''
        };
        this.levelList.push(params);
      });
      // console.log(this.levelList, 'levelList');
    },
    editLock(item, index) {
      console.log(item, index);
      item.isEditLock = true;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = {
        courseId: this.row.courseId
        // paperId: this.row.paperId
      };
      this.$post('getTrainLevelList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          body.map(item => {
            item['isEditLock'] = false;
          });
          this.getAddLevelParams(body);
          this.tableData = body;
          // this.pagination.total = body.recordsTotal;
        }
      });
    },

    submit() {
      console.log(this.levelList);
      this.$post('addTrainLevel', this.levelList, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
          this.close();
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
::v-deep .el-select{
  width: 50%;
  margin-right: 20px;
}
.lock-text{
  margin-right: 20px;
}
</style>
