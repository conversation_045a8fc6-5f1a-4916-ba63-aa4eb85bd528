<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='课程ID' prop='courseId'>
        <el-input v-model="form.courseId" placeholder="请输入课程ID" />
      </el-form-item>

      <el-form-item label='课程名称' prop='courseName'>
        <el-input v-model="form.courseName" placeholder="请输入课程名称" />
      </el-form-item>

      <el-form-item label='上课方式' prop='courseChannelCode'>
        <el-select
          v-model="form.courseChannelCode"
          clearable
          placeholder="请选择"
        >
          <el-option
            v-for="(item, index) in $localDict['courseChannelCode']"
            :key="index"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='状态' prop='allow'>
        <el-select
          v-model="form.allow"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="启用" :value="1" />
          <el-option label="禁用" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label='供应渠道' prop='courseSupplyChannelId'>
        <infinite-selects
          v-model="form.courseSupplyChannelId"
          placeholder="请选择供应渠道"
          api-key="getSupplierSelects"
          key-name="channelName"
          value-name='channelId'
          :param="{sName:''}"
          clearable
        />
      </el-form-item>

      <el-form-item label='是否配置题库' prop='courseQuestion'>
        <el-select
          v-model="form.courseQuestion"
          placeholder="请选择状态"
          clearable
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      :height="table_height"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="courseChannelCode" label="上课方式" align="center">
        <template slot-scope="scope">
          {{ scope.row.courseChannelCode | courseSupplier }}
        </template>
      </el-table-column>
      <el-table-column prop="courseSupplyChannelName" label="供应渠道" align="center" />
      <el-table-column prop="courseId" label="课程ID" align="center" />
      <el-table-column prop="courseName" label="课程名称" align="center" />
      <el-table-column prop="courseSort" label="课程排序" align="center" />
      <el-table-column prop="date" label="阶段名称" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleStageEdit(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="allow" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.allow===1?'success':'danger'">
            {{ scope.row.allow | tansformStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="练题库设置" align="center" width="150px">
        <template slot-scope="scope">
          <div>{{ scope.row.bankTitle }}</div>
          <div class="yz-button-area">
            <el-button type="text" @click="openQbDialog(scope.row)">设置</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="handleEditRowData(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <!-- 弹窗 -->
    <update-dialog :visible.sync="udVisible" :title="udTitle" :course-id="courseId" />
    <stage-dialog :visible.sync="sdVisible" :course-id="stageCourseId" />
    <question-bank :visible.sync="qbShow" :row="currentRow" />
  </div>
</template>
<script>

import { TABLE_HEIGHT } from '@/config/constant';
import updateDialog from './update-dialog';
import stageDialog from './stage-dialog';
import questionBank from './question-bank';
export default {
  components: {
    updateDialog,
    stageDialog,
    questionBank
  },
  filters: {
    supplyChannel(val) {
      if (!val) return '';
      const data = {
        'KHW': '课火网',
        'YZ': '远智教育'
      };
      return data[val];
    }
  },
  data() {
    return {
      table_height: TABLE_HEIGHT,
      udVisible: false,
      sdVisible: false,
      qbShow: false,
      tableLoading: false,
      udTitle: '新增',
      courseId: null,
      stageCourseId: null,
      form: {
        courseId: '',
        courseName: '',
        allow: '',
        courseChannelCode: '',
        courseSupplyChannelId: '',
        courseQuestion: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      currentRow: null
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    openQbDialog(row) {
      this.currentRow = row;
      this.qbShow = true;
    },
    // 获取列表数据
    async getTableList() {
      this.tableLoading = true;
      const data = {
        ...this.form,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      const { fail, body } = await this.$post('getCourseList', data);
      if (!fail) {
        this.tableData = body.data;
        this.pagination.total = body.recordsTotal;
        this.tableLoading = false;
      }
    },
    handleStageEdit(row) {
      // this.stageCourseId = null;
      this.stageCourseId = row.courseId;
      this.sdVisible = true;
    },
    handleAdd() {
      this.udVisible = true;
      this.udTitle = '新增';
      this.courseId = null;
    },
    handleEditRowData(row) {
      this.courseId = row.courseId;
      this.udVisible = true;
      this.udTitle = '编辑';
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  padding-top:20px;
}
</style>
