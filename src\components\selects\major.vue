<template>
  <infinite-select
    v-model="val"
    placeholder="请选择专业名称"
    :options='ops'
    :disabled='disabled'
    :loading='loading'
    valName='courseCode'
    label='name'
    @change='change'
    @loadMore='loadMore'
    @search='search'
  />
</template>

<script>
import infiniteSelect from '@/components/selects/infinite-select';

export default {
  name: 'MajorSelect',
  components: {
    infiniteSelect
  },

  props: {
    value: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {}
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      page: 1,
      size: 10,
      ops: [],
      val: '',
      name: '',
      isAll: false,
      loading: false
    };
  },
  watch: {
    value(v) {
      this.val = v;
    }
  },
  mounted() {
    this.val = this.value;
  },
  methods: {
    change() {
      this.$emit('input', this.val);
    },
    search(val) {
      this.name = val;
      this.page = 1;
      this.getList();
    },
    loadMore() {
      if (!this.loading && !this.isAll) {
        this.getList();
      }
    },
    async getList() {
      this.loading = true;
      const { fail, body } = await this.$post('getMajor', {
        start: (this.page - 1) * this.size,
        length: this.size,
        isAllow: 1,
        sName: this.name,
        ...this.params
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);
      if (!fail) {
        const ops = this.page === 1 ? body.data : this.ops.concat(body.data);
        this.ops = [].concat(ops);
        this.isAll = !body || body.data.length === 0;
        if (this.page === 1 && body.data.length === 0) {
          this.$message.warning('暂无数据');
        }
        if (!this.isAll) {
          this.page += 1;
        }
      }
    }
  }
};
</script>
