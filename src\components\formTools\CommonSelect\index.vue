<template>
  <el-select
    :class="['common-select']"
    v-model="mapModelValue"
    :options="options"
    filterable
    :remote="!!typeData.api"
    reserve-keyword
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    @change="selectChange"
    @blur="selectBlur"
    @clear="selectClear"
    :disabled="disabled"
    :multiple="!isSingle"
    :loading="loading"
    v-bind="$attrs"
    clearable
  >
    <el-option
      v-for="(item, index) in options"
      :key="index"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script>
//请求API引入
// import { getCertificateList } from "@/api/certificate/certificateManage"

//组件引入

//功能js、hooks引入
import { optionEnumList } from "./enum.js";
import { throttle, cloneDeep } from "lodash";

export default {
  model: {
    event: "change", // 事件名
  },
  props: {
    // 选择类型，默认为 "certificate"
    type: {
      type: String,
      default: "certificate",
    },
    // 值字段，默认为空字符串
    valueField: {
      type: String,
      default: "",
    },
    // 额外参数，默认为空对象
    extraParams: {
      type: Object,
      default: () => ({}),
    },
    // 是否禁用，默认为 false
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否为单选模式，默认为 true
    isSingle: {
      type: Boolean,
      default: true,
    },
    // 模型值，可以是字符串、数字或数组，默认为 null
    value: {
      type: [String, Number, Array],
      default: null,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    // 当搜索参数为空时是否返回空数组
    emptyWhenNoSearch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mapModelValue: this.isSingle ? null : [],
      loading: false,
      options: [],
      optionsParams: {
        page: 1,
        rows: 100,
        length: 100,
        name: "",
        status: 1,
      },
      currentRequestId: null, // 添加当前请求标识
      searchQuery: "", // 存储当前搜索参数
    };
  },
  computed: {
    typeData() {
      return optionEnumList.find((item) => item.type === this.type) ?? {};
    },
    hasDefaultOptions() {
      return !!this.typeData.defaultOptions;
    },
    optionValueType() {
      return typeof this.options?.[0]?.value;
    },
  },
  watch: {
    extraParams: {
      handler(newVal, oldVal) {
        // 比较前后值是否相等
        // 对象类型比较JSON字符串
        const newValStr = JSON.stringify(newVal);
        const oldValStr = JSON.stringify(oldVal);

        // 如果前后值相等且不为假值，不执行后续逻辑
        if (newValStr === oldValStr && !!newValStr) {
          return;
        }

        this.init();
      },
      deep: true,
    },
    value: {
      handler(newVal) {
        if (this.isSingle) {
          // 单选模式：直接赋值
          this.mapModelValue = newVal;
        } else {
          // 多选模式：解析字符串为数组
          if (newVal) {
            this.mapModelValue = newVal.split(",");
            //如果选项原本是数字类型，还要再转一下
            if (this.optionValueType === "number") {
              this.mapModelValue = this.mapModelValue.map((item) =>
                Number(item)
              );
            }
          } else {
            this.mapModelValue = [];
          }
        }
      },
      immediate: true,
    },
    // 类型变化时，重新初始化
    type: {
      handler(newVal) {
        // 类型改变时，创建新的请求ID
        this.currentRequestId = Date.now();
        this.init();
      },
    },
  },
  methods: {
    async getOptions() {
      this.loading = true;
      // 保存当前请求ID，用于后面比对
      const requestId = Date.now();
      this.currentRequestId = requestId;

      // 如果设置了emptyWhenNoSearch且搜索参数为空，则返回空数组
      if (this.emptyWhenNoSearch && !this.searchQuery && this.typeData.api) {
        this.options = [];
        this.loading = false;
        return;
      }

      //如果已经配置默认
      if (this.hasDefaultOptions) {
        this.options = cloneDeep(this.typeData.defaultOptions);
        this.loading = false;
      } else {
        // 否则去掉接口
        try {
          // 检查是否在浏览器环境中及API是否存在
          if (typeof window === "undefined" || !window.location) {
            console.error("不在浏览器环境中，无法发起请求");
            this.loading = false;
            this.$emit("error", new Error("不在浏览器环境中"));
            return;
          }

          // 检查API是否存在
          if (!this.typeData.api) {
            console.error("未配置API接口路径");
            this.loading = false;
            this.options = [];
            return;
          }

          console.log(this.extraParams, "extraParams");
          const { fail, body } = await this.$post(
            this.typeData.api,
            {
              ...this.optionsParams,
              ...this.extraParams,
            },
            {
              json: this.typeData.isRequestJson || true,
            }
          );

          // 检查当前请求ID是否与发起请求时的ID一致
          // 如果不一致，说明已经发起了新的请求，应该忽略当前请求的结果
          if (this.currentRequestId !== requestId) {
            console.log("请求已过期，忽略结果");
            return;
          }

          if (fail) {
            this.$emit("error", new Error("请求失败"));
            return;
          }
          const listData = body.data || body || [];
          console.log(listData, "listData");
          this.options = listData.map((item) => {
            //如果是self类型，则label为空，值是item
            if (this.typeData.defaultValueField === "self") {
              return { label: "", value: item };
            }

            return {
              ...item,
              label: this.typeData.nameRender
                ? this.typeData.nameRender(item)
                : item[this.typeData.defaultNameField || "name"],
              value:
                item[
                  this.valueField || this.typeData.defaultValueField || "id"
                ],
            };
          });
        } catch (error) {
          // 同样检查请求ID是否还有效
          if (this.currentRequestId !== requestId) {
            return;
          }
          console.error("获取选项数据失败:", error);
          this.$emit("error", error);
        } finally {
          // 只有当请求ID仍然有效时，才更新loading状态
          if (this.currentRequestId === requestId) {
            this.loading = false;
          }
        }
      }
    },
    selectChange(val) {
      if (!this.isSingle) {
        this.$emit("change", val.join(","));
      } else {
        this.$emit(
          "change",
          val,
          this.options.find((item) => item.value === val)
        );
      }
    },

    selectBlur() {
      // 如果当前选择为空，并且配置了远程搜索字段和API接口，则重新获取数据
     if(!this.mapModelValue && this.typeData.api && this.options?.length === 0) {
      this.optionsParams[this.typeData.remoteField || "name"] = "";
      this.init();
     }
    },
    selectClear() {
      // 清空
      if(this.optionsParams[this.typeData.remoteField || "name"]) {
        this.optionsParams[this.typeData.remoteField || "name"] = "";
        this.init();
      }
    },
    //远程搜索方法,节流
    remoteMethod: throttle(function (query) {
      //存储搜索参数
      this.searchQuery = query;
      //添加搜索参数
      this.optionsParams[this.typeData.remoteField || "name"] = query;
      this.getOptions();
    }, 500),
    async init() {
      //如果禁用，但是又有额外参数的话，尽管是空，那么还是不掉接口。
      if (this.disabled && Object.keys(this.extraParams).length !== 0) return;

      // 初始化搜索参数为空
      this.searchQuery = "";

      // 检查typeData是否存在有效配置
      if (
        !this.typeData ||
        (!this.typeData.defaultOptions && !this.typeData.api)
      ) {
        console.warn(`未找到类型 "${this.type}" 的有效配置`);
        this.options = [];
        return;
      }

      await this.getOptions();
    },
  },
  async mounted() {
    try {
      await this.init();
    } catch (error) {
      console.error("CommonSelect初始化错误:", error);
    }
  },
};
</script>

<style lang="scss" scoped>
.common-select {
}
</style>
