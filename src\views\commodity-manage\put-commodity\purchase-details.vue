<template>
  <common-dialog
    width="80%"
    title="购买明细"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>

        <el-form-item label='姓名' prop='userName'>
          <el-input v-model="form.userName" />
        </el-form-item>

        <el-form-item label='手机号码' prop='mobile'>
          <el-input v-model="form.mobile" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <!-- <el-button type="success" size="small" icon="el-icon-upload2">EXCEL导出</el-button> -->
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 320px)"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="goodsShelfName" label="套餐名称" align="center" />
        <el-table-column width="100" prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="userName" label="姓名" align="center" />
        <el-table-column prop="mobile" label="手机号" width="120px" align="center" />
        <el-table-column prop="payAmount" label="实缴金额" align="center" />
        <el-table-column prop="zmScale" label="智米抵扣" align="center" />
        <el-table-column prop="demurrageScale" label="滞留抵扣" align="center" />
        <el-table-column prop="payTime" label="购买时间" align="center" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    goodsId: {
      type: [String, Number],
      default: null,
      require: true
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      form: {
        userName: '',
        mobile: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      this.getTableList();
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        goodsShelfId: this.goodsId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getGoodsBuyList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      // this.$refs['searchForm'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
