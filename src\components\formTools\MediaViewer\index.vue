<template>
  <transition name="viewer-fade">
    <div
      ref="wrapper"
      :tabindex="-1"
      class="el-image-viewer__wrapper"
      :style="{ zIndex }"
    >
      <div
        class="el-image-viewer__mask"
        @click.self="hideOnClickModal && hide()"
      ></div>
      <!-- CLOSE -->
      <span
        class="el-image-viewer__btn el-image-viewer__close cursor-pointer"
        @click="hide"
      >
        <i class="el-icon-close"></i>
      </span>
      <!-- ARROW -->
      <template v-if="!isSingle">
        <span
          class="el-image-viewer__btn el-image-viewer__prev cursor-pointer"
          :class="{ 'is-disabled': !infinite && isFirst }"
          @click="prev"
        >
          <i class="el-icon-arrow-left"></i>
        </span>
        <span
          class="el-image-viewer__btn el-image-viewer__next cursor-pointer"
          :class="{ 'is-disabled': !infinite && isLast }"
          @click="next"
        >
          <i class="el-icon-arrow-right"></i>
        </span>
      </template>
      <!-- ACTIONS -->
      <div class="el-image-viewer__btn el-image-viewer__actions cursor-pointer">
        <div class="el-image-viewer__actions__inner cursor-pointer">
          <i class="el-icon-zoom-out" @click="handleActions('zoomOut')"></i>
          <i class="el-icon-zoom-in" @click="handleActions('zoomIn')"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i :class="mode.icon" @click="toggleMode"></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i
            class="el-icon-refresh-left"
            @click="handleActions('anticlocelise')"
          ></i>
          <i
            class="el-icon-refresh-right"
            @click="handleActions('clocelise')"
          ></i>
          <i class="el-image-viewer__actions__divider"></i>
          <i class="el-icon-download" @click="downloadImage"></i>
        </div>
      </div>
      <!-- CANVAS -->
      <div class="el-image-viewer__canvas">
        <template v-for="(url, i) in urlList">
          <img
            v-show="i === index"
            :id="`media-id-${i}`"
            :src="url"
            :style="mediaStyle"
            class="el-image-viewer__img"
            @load="handleMediaLoad"
            @error="handleMediaError"
            @mousedown="handleMouseDown"
            ref="mediaRef"
          />
        </template>
      </div>
    </div>
  </transition>
</template>

<script>
import { videoData, imageData, getNameDitPart } from "./utils";

export default {
  name: "mediaViewer",
  props: {
    urlList: {
      type: Array,
      default: () => [],
    },
    zIndex: {
      type: Number,
      default: 2000,
    },
    initialIndex: {
      type: Number,
      default: 0,
    },
    infinite: {
      type: Boolean,
      default: true,
    },
    hideOnClickModal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: true,
      index: this.initialIndex,
      mode: {
        name: "contain",
        icon: "el-icon-full-screen",
      },
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
    };
  },
  computed: {
    isSingle() {
      const { urlList } = this;
      return urlList.length <= 1;
    },
    isFirst() {
      return this.index === 0;
    },
    isLast() {
      return this.index === this.urlList.length - 1;
    },
    currentMedia() {
      return this.urlList[this.index];
    },
    mediaStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        marginLeft: `${offsetX}px`,
        marginTop: `${offsetY}px`,
      };
      if (this.mode.name === "contain") {
        style.maxWidth = style.maxHeight = "100%";
      }
      return style;
    },
  },
  mounted() {
    this.deviceSupportInstall();
    // add tabindex then wrapper can be focusable via Javascript
    // focus wrapper so arrow key can't cause inner scroll behavior underneath
    this.$refs.wrapper?.focus?.();
  },
  beforeDestroy() {
    this.deviceSupportUninstall();
  },
  methods: {
    isVideo(url) {
      const currentUrl = url || this.urlList[this.index];
      const suffix = getNameDitPart(currentUrl)[1];
      return videoData.includes(suffix);
    },
    isImage(url) {
      const currentUrl = url || this.urlList[this.index];
      const suffix = getNameDitPart(currentUrl)[1];
      return imageData.includes(suffix);
    },
    hide() {
      this.deviceSupportUninstall();
      this.$emit("close");
    },
    deviceSupportInstall() {
      this._keyDownHandler = this.rafThrottle((e) => {
        const keyCode = e.keyCode;
        switch (keyCode) {
          // ESC
          case 27:
            this.hide();
            break;
          // SPACE
          case 32:
            this.toggleMode();
            break;
          // LEFT_ARROW
          case 37:
            this.prev();
            break;
          // UP_ARROW
          case 38:
            this.handleActions("zoomIn");
            break;
          // RIGHT_ARROW
          case 39:
            this.next();
            break;
          // DOWN_ARROW
          case 40:
            this.handleActions("zoomOut");
            break;
        }
      });

      this._mouseWheelHandler = this.rafThrottle((e) => {
        const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
        if (delta > 0) {
          this.handleActions("zoomIn", {
            zoomRate: 0.015,
            enableTransition: false,
          });
        } else {
          this.handleActions("zoomOut", {
            zoomRate: 0.015,
            enableTransition: false,
          });
        }
      });

      const mousewheelEventName = this.isFirefox()
        ? "DOMMouseScroll"
        : "mousewheel";
      document.addEventListener("keydown", this._keyDownHandler, false);
      document.addEventListener(
        mousewheelEventName,
        this._mouseWheelHandler,
        false
      );
    },
    deviceSupportUninstall() {
      const mousewheelEventName = this.isFirefox()
        ? "DOMMouseScroll"
        : "mousewheel";
      document.removeEventListener("keydown", this._keyDownHandler, false);
      document.removeEventListener(
        mousewheelEventName,
        this._mouseWheelHandler,
        false
      );
      this._keyDownHandler = null;
      this._mouseWheelHandler = null;
    },
    handleMediaLoad() {
      this.loading = false;
    },
    handleMediaError(e) {
      this.loading = false;
    },
    handleMouseDown(e) {
      if (this.loading || e.button !== 0) return;

      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;

      const divLeft = this.$refs.wrapper.clientLeft;
      const divRight =
        this.$refs.wrapper.clientLeft + this.$refs.wrapper.clientWidth;
      const divTop = this.$refs.wrapper.clientTop;
      const divBottom =
        this.$refs.wrapper.clientTop + this.$refs.wrapper.clientHeight;

      this._dragHandler = this.rafThrottle((ev) => {
        this.transform = {
          ...this.transform,
          offsetX: offsetX + ev.pageX - startX,
          offsetY: offsetY + ev.pageY - startY,
        };
      });

      document.addEventListener("mousemove", this._dragHandler, false);
      document.addEventListener(
        "mouseup",
        (e) => {
          const mouseX = e.pageX;
          const mouseY = e.pageY;
          if (
            mouseX < divLeft ||
            mouseX > divRight ||
            mouseY < divTop ||
            mouseY > divBottom
          ) {
            this.reset();
          }
          document.removeEventListener("mousemove", this._dragHandler, false);
        },
        false
      );

      e.preventDefault();
    },
    reset() {
      this.transform = {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      };
    },
    toggleMode() {
      if (this.loading) return;

      const modeNames = ["contain", "original"];
      const modeIcons = ["el-icon-full-screen", "el-icon-c-scale-to-original"];
      const currentMode = this.mode.name;
      const index = modeNames.indexOf(currentMode);
      const nextIndex = (index + 1) % modeNames.length;

      this.mode = {
        name: modeNames[nextIndex],
        icon: modeIcons[nextIndex],
      };
      this.reset();
    },
    prev() {
      if (this.isFirst && !this.infinite) return;
      let dom = document.querySelector(`#media-id-${this.index}`);
      //如果为视频，则暂停
      if (dom && dom.tagName === "VIDEO") {
        dom.pause();
      }
      const len = this.urlList.length;
      this.index = (this.index - 1 + len) % len;
      this.$emit("switch", this.index);
    },
    next() {
      if (this.isLast && !this.infinite) return;
      //如果为视频，则暂停
      let dom = document.querySelector(`#media-id-${this.index}`);
      if (dom && dom.tagName === "VIDEO") {
        dom.pause();
      }
      const len = this.urlList.length;
      this.index = (this.index + 1) % len;
      this.$emit("switch", this.index);
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const {
        zoomRate = 0.2,
        rotateDeg = 90,
        enableTransition = true,
      } = options;

      switch (action) {
        case "zoomOut":
          if (this.transform.scale > 0.2) {
            this.transform.scale = parseFloat(
              (this.transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          this.transform.scale = parseFloat(
            (this.transform.scale + zoomRate).toFixed(3)
          );
          break;
        case "clocelise":
          this.transform.deg += rotateDeg;
          break;
        case "anticlocelise":
          this.transform.deg -= rotateDeg;
          break;
      }
      this.transform.enableTransition = enableTransition;
    },
    isFirefox() {
      return !!window.navigator.userAgent.match(/firefox/i);
    },
    rafThrottle(fn) {
      let locked = false;
      return function (...args) {
        if (locked) return;
        locked = true;
        window.requestAnimationFrame(() => {
          fn.apply(this, args);
          locked = false;
        });
      };
    },
    downloadImage() {
      const currentUrl = this.urlList[this.index];
      if (!currentUrl) return;

      try {
        // 创建一个canvas元素
        const image = new Image();
        image.crossOrigin = "Anonymous"; // 解决跨域问题

        image.onload = () => {
          // 创建canvas
          const canvas = document.createElement("canvas");
          canvas.width = image.width;
          canvas.height = image.height;

          // 绘制图片到canvas
          const ctx = canvas.getContext("2d");
          ctx.drawImage(image, 0, 0);

          // 将canvas转换为blob
          canvas.toBlob((blob) => {
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = this.getFileName(currentUrl);
            link.style.display = "none";

            // 添加到body并点击
            document.body.appendChild(link);
            link.click();

            // 清理
            setTimeout(() => {
              document.body.removeChild(link);
              URL.revokeObjectURL(url);
            }, 100);
          });
        };

        image.onerror = () => {
          // 图片加载失败，使用备用方法
          this.fallbackDownload(currentUrl);
        };

        // 设置图片源
        image.src = currentUrl;
      } catch (error) {
        console.error("下载图片失败:", error);
        this.fallbackDownload(currentUrl);
      }
    },

    // 备用下载方法
    fallbackDownload(url) {
      try {
        // 使用iframe方式下载
        const iframe = document.createElement("iframe");
        iframe.style.display = "none";
        iframe.src = url;
        document.body.appendChild(iframe);

        setTimeout(() => {
          document.body.removeChild(iframe);
        }, 1000);

        console.log("使用备用方法下载图片");
      } catch (error) {
        console.error("下载失败:", error);
        // 最后的备用方法：直接打开链接
        window.open(url, "_blank");
      }
    },

    // 从URL中获取文件名
    getFileName(url) {
      if (!url) return "download.png";

      try {
        // 尝试从URL中提取文件名
        const urlParts = url.split("/");
        let fileName = urlParts[urlParts.length - 1];

        // 如果文件名包含查询参数，去除
        if (fileName.includes("?")) {
          fileName = fileName.split("?")[0];
        }

        // 如果没有扩展名，添加.png
        if (!fileName.includes(".")) {
          fileName += ".png";
        }

        return fileName || "image.png";
      } catch (e) {
        return "image.png";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-image-viewer__btn {
  cursor: pointer;
}
</style>
