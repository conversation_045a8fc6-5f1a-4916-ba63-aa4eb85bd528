<template>
  <div class="upload-attachment">
    <el-upload
      class="upload-demo"
      :action="uploadAction"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      multiple
      :limit="limit"
      :on-exceed="handleExceed"
      :show-file-list="false"
      :file-list="[]"
      :data="{ fileSrc: 'workOrder' }"
      :http-request="customUpload"
      :disabled="fileList.length >= limit"
    >
      <div
        class="upload-btn"
        v-loading="loading"
        :class="{ disabled: fileList.length >= limit }"
      >
        <i class="el-icon-upload2" style="color: #409EFF;"></i>
        <span style="color: #409EFF;">上传附件</span>
        <div class="upload-tip">
          附件数量上限{{ limit }}个，总大小不得超过{{ maxSize }}M，附件 (已上传{{
            fileList.length
          }}个)
        </div>
      </div>
    </el-upload>
    <div v-if="fileList.length > 0" v-loading="loading" class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <!-- <div v-if="isVideoFile(file.name)">
          <video :src="file.localPath || file.url" class="w-auto h-100px mr-10px" controls muted></video>
        </div> -->
        <div class="file-icon" v-if="!isImageFile(file.name)">
          <i :class="getFileIcon(file.name)"></i>
        </div>
        <div class="file-thumbnail" v-else @click="previewFile(file)">
          <el-image
            :src="file.localPath || file.url"
            fit="cover"
            :preview-src-list="[]"
          >
            <div slot="error" class="image-error">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
        <div class="file-name" :title="file.name" @click="previewFile(file)">
          {{ file.name }}
        </div>
        <i class="el-icon-close" @click="handleRemove(file)"></i>
      </div>
    </div>

    <!-- 图片预览 -->
    <media-viewer
      v-if="previewVisible"
      :url-list="previewUrlList"
      :initial-index="previewIndex"
      :z-index="2000"
      @close="previewVisible = false"
    />
  </div>
</template>

<script>
export default {
  name: "UploadAttachment",
  components: {
    MediaViewer: () => import("@/components/formTools/MediaViewer"),
  },
  props: {
    // 使用v-model绑定的值，格式为文件对象数组 [{name:'',url:''}]
    value: {
      type: Array,
      default: () => [],
    },
    // 上传地址
    uploadAction: {
      type: String,
      default: "/file/uploadFile/specifyName.do",
    },
    // 文件大小限制，单位MB
    maxSize: {
      type: Number,
      default: 100,
    },
    // 文件数量限制
    limit: {
      type: Number,
      default: 10,
    },
  },
  data() {
    return {
      fileList: [], // 上传文件列表
      previewVisible: false, // 图片预览可见性
      previewUrlList: [], // 预览图片URL列表
      previewIndex: 0, // 当前预览图片索引
      selectedFiles: [], // 当前批量选择的文件列表
      loading: false,
      // 总共文件大小
      maxSizeList: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal && Array.isArray(newVal)) {
          // 如果外部value变化，则更新内部状态
          this.initFileList(newVal);
        }
      },
    },
  },
  methods: {
    // 初始化文件列表
    initFileList(fileArray) {
      if (!fileArray || !Array.isArray(fileArray) || fileArray.length === 0) {
        this.fileList = [];
        this.maxSizeList = [];
        return;
      }

      // 根据传入的文件数组构建文件列表
      this.fileList = [...fileArray];
    },

    // 从URL中提取文件名
    getFileNameFromUrl(url) {
      if (!url) return "未知文件";
      const parts = url.split("/");
      return parts[parts.length - 1];
    },

    // 获取文件图标
    getFileIcon(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();

      if (["jpg", "jpeg", "png", "gif", "bmp"].includes(extension)) {
        return "el-icon-picture-outline";
      } else if (["doc", "docx"].includes(extension)) {
        return "fa fa-file-word-o";
      } else if (["xls", "xlsx"].includes(extension)) {
        return "fa fa-file-excel-o";
      } else if (["ppt", "pptx"].includes(extension)) {
        return "fa fa-file-powerpoint-o";
      } else if (extension === "pdf") {
        return "fa fa-file-pdf-o";
      } else if (["zip", "rar", "7z"].includes(extension)) {
        return "fa fa-file-archive-o";
      } else if (["mp4", "avi", "mov", "wmv", "mkv"].includes(extension)) {
        return "fa fa-file-video-o";
      } else {
        return "el-icon-document";
      }
    },

    // 判断文件是否为图片
    isImageFile(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();
      return ["jpg", "jpeg", "png", "gif", "bmp"].includes(extension);
    },
    // 判断文件是否为视频
    isVideoFile(fileName) {
      const extension = fileName.split(".").pop().toLowerCase();
      return ["mp4", "avi", "mov", "wmv", "mkv"].includes(extension);
    },

    // 自定义上传方法
    customUpload(options) {
      const { file, onSuccess, onError } = options;

      // 检查上传后是否会超出文件数量限制
      const totalFilesAfterUpload =
        this.fileList.length +
        (this.selectedFiles ? this.selectedFiles.length : 1);
      if (totalFilesAfterUpload > this.limit) {
        const remainingCount = this.limit - this.fileList.length;
        if (remainingCount <= 0) {
          this.$message.error(`最多只能上传${this.limit}个文件`);
        } else {
          this.$message.error(`超过上传限制，请删除部分文件后重新上传`);
        }
        onError(new Error(`超出文件上传限制`));
        // 清空已选择的文件
        this.selectedFiles = [];
        return;
      }

      const formData = new FormData();
      formData.append("file", file);
      this.loading = true;

      // 使用axios或者项目中的http请求库
      this.$http({
        url: this.uploadAction,
        method: "post",
        data: formData,
        headers: { "Content-Type": "multipart/form-data" },
      })
        .then((res) => {
          if (res.ok && res.body) {
            // 处理成功响应
            const response = { body: res.body };

            // 保存原始URL
            let fileUrl = response.body;

            // 添加到文件列表
            const fileObj = {
              name: file.name,
              url: fileUrl,
              response: fileUrl,
              fullUrl: fileUrl,
              localPath: URL.createObjectURL(file), // 使用file对象创建本地预览URL
              size: file.size / 1024 / 1024, // 保存文件大小（MB）
            };

            this.fileList.push(fileObj);

            // 更新v-model值
            this.updateValue();

            // 触发上传成功事件
            this.$emit("upload-success", fileObj);

            onSuccess(response, file);
          } else {
            onError(new Error("上传失败"));
            this.$message.error("上传失败，请重试");
          }
          this.loading = false;
        })
        .catch((err) => {
          onError(err);
          this.$message.error("上传失败，请重试");
        });
    },

    // 更新v-model值
    updateValue() {
      // 构建文件对象数组
      const fileArray = this.fileList.map((file) => ({
        name: file.name,
        url: file.url,
        localPath: file.localPath || null,
      }));

      // 发送更新事件
      this.$emit("input", fileArray);
    },

    // 文件上传成功
    handleSuccess(response, file) {
      console.log("上传成功:", file.name, response.body);
      // 上传成功后清空选择的文件计数和大小列表
      this.selectedFiles = [];
    },

    // 文件上传失败
    handleError(err) {
      console.error("上传失败", err);
      this.$message.error("上传失败，请重试");
      // 上传失败后也清空选择的文件计数
      this.selectedFiles = [];
    },

    // 移除文件
    handleRemove(file) {
      const index = this.fileList.findIndex(
        (item) => item.name === file.name && item.url === file.url
      );
      if (index !== -1) {
        this.maxSizeList.splice(index, 1);

        this.fileList.splice(index, 1);

        // 更新v-model值
        this.updateValue();
      }
    },

    // 超出文件数量限制
    handleExceed(files, fileList) {
      const remainingCount = this.limit - this.fileList.length;
      if (remainingCount <= 0) {
        this.$message.warning(`最多只能上传${this.limit}个文件`);
      } else {
        this.$message.warning(
          `当前已上传${this.fileList.length}个文件，还能上传${remainingCount}个文件，本次选择了${files.length}个文件，超出限制`
        );
      }
      // 清空已选择的文件计数
      this.selectedFiles = [];
    },

    // 上传前校验
    beforeUpload(file) {
      // 保存当前选择的文件，用于批量上传检查
      if (!this.selectedFiles) {
        this.selectedFiles = [];
      }
      this.selectedFiles.push(file);
      this.maxSizeList.push(file.size / 1024 / 1024);
      const totalSize = this.maxSizeList.reduce((sum, size) => sum + size, 0);
      console.log(this.maxSizeList, totalSize);

      // 检查总大小是否符合要求
      const isFitSize = totalSize < this.maxSize;

      if (!isFitSize) {
        this.$message.error("所有文件大小不能超过 " + this.maxSize + "MB");
        this.maxSizeList.splice(this.maxSizeList.length - 1, 1);
        return false;
      }

      // 检查当前已上传文件数量加上新选择的所有文件是否超过限制
      if (this.fileList.length + this.selectedFiles.length > this.limit) {
        const remainingCount = this.limit - this.fileList.length;
        if (remainingCount <= 0) {
          this.$message.error(`最多只能上传${this.limit}个文件`);
        } else {
          this.$message.error(`超过上传限制，请删除部分文件后重新上传`);
        }
        // 清空已选择的文件列表
        this.selectedFiles = [];
        return false;
      }

      return true;
    },

    // 预览文件
    previewFile(file) {
      if (this.isImageFile(file.name)) {
        // 如果是图片，打开预览
        // 获取所有图片文件的URL
        const imageFiles = this.fileList.filter((item) =>
          this.isImageFile(item.name)
        );

        // 优先使用本地路径，如果没有则使用服务器URL
        this.previewUrlList = imageFiles.map(
          (item) => item.localPath || item.url
        );

        // 找到当前点击图片的索引
        const currentUrl = file.localPath || file.url;
        this.previewIndex = this.previewUrlList.indexOf(currentUrl);

        if (this.previewIndex === -1) this.previewIndex = 0;

        // 确保预览列表不为空
        if (this.previewUrlList.length > 0) {
          this.previewVisible = true;
        } else {
          this.$message.warning("无法预览图片");
        }
      } else {
        // 如果是其他类型文件，可以尝试在新窗口打开
        if (file.url) {
          window.open(file.url);
        }
      }
    },

    // 清空文件列表
    clear() {
      this.fileList = [];
      this.updateValue();
    },

    // 获取当前文件列表
    getFileList() {
      return [...this.fileList];
    },
  },
};
</script>

<style lang="scss" scoped>
.upload-attachment {
  width: 100%;
  position: relative;

  .upload-btn {
    display: flex;
    align-items: center;
    background-color: #f5f7fa;
    padding: 8px 12px;
    border: 1px solid #dcdfe6;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    cursor: pointer;

    &.disabled {
      background-color: #f5f5f5;
      color: #c0c4cc;
      cursor: not-allowed;
    }

    i {
      margin-right: 5px;
      font-size: 16px;
    }

    .upload-tip {
      margin-left: auto;
      font-size: 12px;
      color: #909399;
    }
  }

  .file-list {
    margin-top: 10px;

    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 10px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-bottom: 8px;
      background-color: #fff;
      position: relative;

      .file-icon {
        margin-right: 8px;
        font-size: 20px;
        color: #909399;
      }

      .file-thumbnail {
        width: 30px;
        height: 30px;
        margin-right: 8px;
        overflow: hidden;
        border-radius: 3px;
        cursor: pointer;

        .el-image {
          width: 100%;
          height: 100%;
        }

        .image-error {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f7fa;
          color: #909399;
        }
      }

      .file-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }

      .el-icon-close {
        cursor: pointer;
        color: #909399;
        font-size: 16px;

        &:hover {
          color: #f56c6c;
        }
      }
    }
  }
}

::v-deep .el-upload {
  width: 100%;
}

::v-deep .el-upload-list {
  display: none;
}
</style>
