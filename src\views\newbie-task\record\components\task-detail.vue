<template>
  <common-dialog
    class="common-dialog"
    width="80%"
    :isFull="true"
    title="任务明细"
    :visible.sync="show"
    :show-footer="false"
    @open="open"
    @close="close"
  >
    <div class="dialog-main">
      <!-- 顶部筛选 -->
      <el-form
        ref="searchForm"
        :model="form"
        label-width="120px"
        class="yz-search-form"
        size="mini"
        label-suffix=":"
        @submit.native.prevent="search"
      >
        <el-form-item label="任务id" prop="taskConfigId">
          <el-input
            v-model="form.taskConfigId"
            placeholder="请输入任务id"
            clearable
          />
        </el-form-item>
        <el-form-item label="任务标题" prop="taskTitle">
          <el-input
            v-model="form.taskTitle"
            placeholder="请输入任务标题"
            clearable
          />
        </el-form-item>
        <el-form-item label='获取时间' prop='taskTime'>
          <el-date-picker
            v-model="form.taskTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button
            type="primary"
            native-type="submit"
            size="mini"
          >查询</el-button>
          <el-button size="mini" @click="search(0)">重置</el-button>
        </div>
      </el-form>

      <el-table
        ref="table"
        v-loading="tableLoading"
        size="small"
        :data="tableData"
        style="width: 100%; margin: 25px 0"
        header-cell-class-name="table-cell-header"
        border
      >
        <el-table-column prop="taskConfigId" label="任务id" align="center" />
        <el-table-column prop="taskMode" label="任务模式" align="center">
          <template slot-scope="{ row }">
            {{ row.taskMode | taskModeEnum }}
          </template>
        </el-table-column>
        <el-table-column prop="taskTypeName" label="任务类型" align="center" />
        <el-table-column prop="taskTitle" label="任务标题" align="center" />
        <el-table-column prop="taskDesc" label="任务明细" align="center" />
        <el-table-column prop="zhimi" label="获得智米" align="center" />
        <el-table-column prop="createTime" label="获取时间" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { taskMode } from './../../type';
import { arrToEnum } from '@/utils';
const taskModeEnum = arrToEnum(taskMode);
export default {
  filters: {
    taskModeEnum(val) {
      return taskModeEnum[val] || '/';
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      form: {
        taskConfigId: '',
        taskTitle: '',
        taskTime: [] // 时间范围
      },
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      tableLoading: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      if (this.currentRow.id) this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    // 查询和重置
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 处理筛选参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      formData.startTime = formData.taskTime[0] ?? '';
      formData.endTime = formData.taskTime[1] ?? '';
      delete formData.taskTime;
      return {
        ...formData,
        newbieTaskUserId: this.currentRow.id,
        pageNum: (this.pagination.page - 1) * this.pagination.limit,
        pageSize: this.pagination.limit
      };
    },
    // 请求表格数据
    getTableList() {
      this.tableLoading = true;
      const params = this.handleQueryParams();
      this.$post('newbieTaskUserdetail', params, { json: true }).then(
        (res) => {
          const { fail, body } = res;
          if (!fail) {
            this.tableLoading = false;
            this.tableData = body?.data;
            this.pagination.total = body.recordsTotal;
          }
        }
      );
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
