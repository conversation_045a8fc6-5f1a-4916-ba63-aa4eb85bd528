<template>
  <common-dialog
    :show-footer="true"
    width="400px"
    title="备注"
    :visible.sync='visible'
    @open="init"
    @confirm="submitBtn"
    @close='closeBtn'
  >
    <div class="dialog-main">
      <el-form
        ref="remarkForm"
        class="form"
        size='mini'
        :model="form"
        label-width="80px"
      >
        <el-form-item label='备注' prop='remark'>
          <el-input v-model="form.remark" rows="3" type="textarea" maxlength="50" show-word-limit />
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: [String, Number],
      default: null
    },
    remark: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      form: { remark: '' }
    };
  },
  methods: {
    init() {
      this.form.remark = this.remark;
    },
    submitBtn() {
      const data = { id: this.orderId, ...this.form };
      this.$post('updateGoodsRemark', data, { json: 'application/json' }).then(res => {
        const { code } = res;
        if (code === '00') {
          this.$emit('close');
          this.$message({ message: '备注成功', type: 'success' });
          this.$parent.getTableList();
        } else {
          this.$message({ message: '备注失败', type: 'error' });
        }
      });
    },
    closeBtn() {
      this.$refs['remarkForm'].resetFields();
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
