<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    :title="!editId ? '新增关卡' : '编辑关卡'"
    :visible.sync="show"
    @confirm="confirm"
    @close="show = false"
  >
    <el-form
      ref="dynamicValidateForm"
      v-loading="loading"
      :model="dynamicValidateForm"
      :rules="rules"
      label-width="150px"
      style="padding-top: 18px"
      size="small"
    >
      <el-form-item label="关卡序号：" prop="barrierSerialNum">
        <el-col :span="10">
          <el-input-number
            v-model="dynamicValidateForm.barrierSerialNum"
            size="small"
            :min="1"
            :precision="0"
            placeholder="请输入"
            style="width: 100%"
            :disabled="!!isStart"
          />
        </el-col>
      </el-form-item>

      <el-form-item label="关卡名称：" prop="barrierName">
        <el-col :span="10">
          <el-input
            v-model="dynamicValidateForm.barrierName"
            placeholder="请输入"
            maxlength="12"
            show-word-limit
            style="width: 100%"
          />
        </el-col>
      </el-form-item>

      <el-form-item v-if="!editId" label="关卡任务：">
        <el-button
          v-if="!isStart"
          type="primary"
          icon="el-icon-circle-plus-outline"
          size="small"
          @click="addTask"
        >
          新增任务
        </el-button>
      </el-form-item>

      <template v-for="(domain, index) in dynamicValidateForm.taskDtoList">
        <div :key="domain.key">
          <el-divider v-if="dynamicValidateForm.taskDtoList.length > 1">
            <el-tag
              :closable="!isStart"
              @close="dynamicValidateForm.taskDtoList.splice(index, 1)"
            >
              任务{{ index + 1 }}
            </el-tag>
          </el-divider>

          <el-form-item
            :key="domain.key"
            :label="'任务类型：'"
            :prop="'taskDtoList.' + index + '.taskType'"
            :rules="rules.taskType"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.taskType"
                clearable
                placeholder="请选择"
                @change="taskTypeChange(domain)"
              >
                <el-option
                  v-for="item in $dictJson['trainCampTaskType']"
                  :key="item.dictValue"
                  :label="item.dictName"
                  :value="item.dictValue"
                />
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item
            :key="domain.key"
            :label="'任务名称：'"
            :prop="'taskDtoList.' + index + '.templateId'"
            :rules="rules.templateId"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.templateId"
                clearable
                placeholder="请选择"
                @change="domain.businessId = ''"
              >
                <el-option
                  v-for="item in taskNameMap[domain.taskType] || []"
                  :key="item.templateId"
                  :label="item.taskName"
                  :value="item.templateId"
                />
              </el-select>
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="domain.templateId === DTaskName.attendLecture"
            :key="domain.key"
            label="课时ID"
            :prop="'taskDtoList.' + index + '.businessId'"
            :rules="rules.businessId"
          >
            <el-col :span="10">
              <el-input
                v-model="domain.businessId"
                placeholder="请输入"
                style="width: 100%"
                clearable
              />
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="
              domain.templateId === DTaskName.classroomTest ||
                domain.templateId === DTaskName.clockIn
            "
            :key="domain.key"
            label="关联课时ID"
            :prop="'taskDtoList.' + index + '.businessId'"
            :rules="rules.businessId"
          >
            <el-col :span="10">
              <el-input
                v-model="domain.businessId"
                placeholder="请输入"
                style="width: 100%"
                clearable
              />
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="domain.templateId === DTaskName.practice"
            :key="domain.key"
            label="练题ID"
            :prop="'taskDtoList.' + index + '.businessId'"
            :rules="rules.businessId"
          >
            <el-input v-model="domain.businessId" />
          </el-form-item>

          <el-form-item
            v-if="domain.taskType == DTaskType.duration"
            :key="domain.key"
            :label="'任务时长要求：'"
            :prop="'taskDtoList.' + index + '.ext'"
            :rules="rules.ext"
          >
            <el-col :span="10">
              <el-input-number
                v-model="domain.ext"
                size="small"
                :min="1"
                :precision="0"
                placeholder="请输入"
                style="width: 100%"
              />
            </el-col>
            <span style="margin-left: 10px">
              {{
                domain.templateId === DTaskName.attendLecture ? "分钟" : "秒"
              }}
            </span>
          </el-form-item>

          <el-form-item
            v-if="domain.templateId !== DTaskName.viewAgreement"
            :key="domain.key"
            label="跳转URL"
            :prop="'taskDtoList.' + index + '.jumpUrl'"
            :rules="rules.jumpUrl"
          >
            <el-col :span="10">
              <el-input
                v-model="domain.jumpUrl"
                placeholder="请输入"
                style="width: 100%"
                clearable
              />
            </el-col>
          </el-form-item>

          <el-form-item
            v-if="domain.templateId === DTaskName.postDynamic"
            :key="domain.key"
            label="绑定话题"
            :prop="'taskDtoList.' + index + '.businessId'"
            :rules="rules.topicName"
          >
            <el-col :span="10">
              <el-select
                v-model="domain.businessId"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteMethod"
                :loading="topicLoading"
                clearable
              >
                <el-option
                  v-for="item in topicNameList"
                  :key="item.id"
                  :label="item.topicName"
                  :value="item.id"
                />
              </el-select>
            </el-col>
          </el-form-item>
        </div>
      </template>

      <el-divider v-if="dynamicValidateForm.taskDtoList.length > 1" />
      <el-form-item label="关卡规则" prop="barrierRule">
        <el-col :span="20">
          <el-input
            v-model="dynamicValidateForm.barrierRule"
            type="textarea"
            :rows="2"
            placeholder="请输入"
            maxlength="100"
            style="width: 100%"
          />
        </el-col>
      </el-form-item>
      <el-form-item label="关卡指引：" prop="barrierGuide">
        <el-upload
          class="avatar-uploader"
          action="/file/uploadFile.do"
          :show-file-list="false"
          :on-success="uploadSuccess"
          :before-upload="beforeAvatarUpload"
          :data="{ fileSrc: 'trainCamp' }"
          accept="image/*"
        >
          <img v-if="imageUrl" :src="imageUrl" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
          <div slot="tip">支持图片格式：jpg, jpeg, bmp, png，大小 3M 以内</div>
        </el-upload>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
// 任务类型-字典
const DTaskType = {
  duration: '1'
};

// 任务名称-字典
const DTaskName = {
  attendLecture: 1, // 听课
  classroomTest: 2, // 随堂测试
  practice: 3, // 智能练题
  clockIn: 4, // 课后打卡
  postDynamic: 5, // 发布动态
  viewFile: 6, // 看文件
  viewAgreement: 7 // 看协议
};

const rules = {
  barrierSerialNum: [
    { required: true, message: '请输入关卡序号', trigger: 'blur' }
  ],
  barrierName: [{ required: true, message: '请输入关卡名称' }],
  templateId: [{ required: true, message: '请选择任务名称', trigger: 'blur' }],
  taskType: [{ required: true, message: '请选择任务类型' }],
  businessId: [{ required: true, message: '请输入课时ID' }],
  ext: [{ required: true, message: '请输入时长', trigger: 'blur' }],
  barrierRule: [{ required: true, message: '请输入关卡规则' }],
  jumpUrl: [{ required: true, message: '请输入URL' }],
  topicName: [{ required: true, message: '请输入绑定话题' }]
};

// 任务项属性
const DTaskDtoItem = {
  taskType: '',
  templateId: '',
  businessId: '',
  ext: undefined,
  jumpUrl: ''
};

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      rules,
      DTaskName,
      DTaskType,
      dynamicValidateForm: {
        taskDtoList: [{ ...DTaskDtoItem }],
        barrierSerialNum: undefined,
        barrierName: '',
        barrierRule: '',
        barrierGuide: ''
      },
      imageUrl: '',
      taskNameMap: {},
      topicNameList: [],
      topicLoading: false
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit('update:visible', v);
      }
    },
    // 是否已经有人闯关
    isStart() {
      return this.$route.query.on;
    }
  },
  watch: {
    show(val) {
      if (val) {
        if (this.editId) {
          this.getBarrierDetail();
        }
      } else {
        this.imageUrl = '';
        this.topicNameList = [];
        this.$refs['dynamicValidateForm'].resetFields();
        this.dynamicValidateForm.taskDtoList.splice(1);
      }
    }
  },
  methods: {
    addTask() {
      this.dynamicValidateForm.taskDtoList.push({ ...DTaskDtoItem });
    },
    // 提交
    confirm() {
      this.$refs.dynamicValidateForm.validate((valid) => {
        if (valid) {
          this.dynamicValidateForm.taskDtoList.forEach((item) => {
            if (item.taskType === DTaskType.duration) {
              item.extDto = {};
              item.extDto.duration = item.ext;
              item.extDto.unit =
                item.templateId === DTaskName.attendLecture
                  ? undefined
                  : 'second';
            }
          });
          const data = {
            id: this.editId,
            trainCampId: this.$route.query.id,
            ...this.dynamicValidateForm
          };
          this.$post(this.editId ? 'updateBarrier' : 'addBarrier', data, {
            json: true
          }).then((res) => {
            if (res.ok) {
              this.$message.success(this.editId ? '编辑成功' : '添加成功');
              this.show = false;
              this.$emit('refresh');
            }
          });
        } else {
          this.$message.warning('请检查填写项');
        }
      });
    },
    // 任务类型变化
    taskTypeChange(item, reset = true) {
      const key = item.taskType;
      if (reset) {
        item.templateId = ''; // 重置任务名称
      }
      if (this.taskNameMap?.[key] || !key) return;
      this.$http({
        method: 'GET',
        url: `/trainCampBarrier/barrierTaskInfo/${key}`
      }).then((res) => {
        if (res.ok) {
          this.taskNameMap = { ...this.taskNameMap, [key]: res.body };
        }
      });
    },
    // 获取话题列表
    getTopicList(data) {
      this.topicLoading = true;
      this.$http({
        method: 'post',
        url: `/trainCampBarrier/topicList`,
        data: {
          // id: 6, // 话题Id
          // topicName: '',// 话题名称
          ...data,
          pageNum: 1,
          pageSize: 7
        },
        json: true
      })
        .then((res) => {
          if (res.ok) {
            this.topicNameList = res.body.data;
          }
        })
        .finally(() => {
          this.topicLoading = false;
        });
    },
    // 过滤话题
    remoteMethod(k) {
      if (k !== '') {
        this.getTopicList({ topicName: k });
      }
    },
    // 图片上传成功
    uploadSuccess(response, file) {
      this.imageUrl = URL.createObjectURL(file.raw);
      this.dynamicValidateForm.barrierGuide = response.body;
      this.$refs.dynamicValidateForm.validateField('barrierGuide');
    },
    beforeAvatarUpload(file) {
      const isJPG = [
        'image/bmp',
        'image/png',
        'image/jpg',
        'image/jpeg'
      ].includes(file.type);
      if (!isJPG) {
        this.$message.error('请按格式要求上传图片！');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 3;
      if (!isLt2M) {
        this.$message.error('图片大小不能超过 3M！');
        return false;
      }

      return true;
    },
    // 获取详情
    getBarrierDetail() {
      this.loading = true;
      this.$http({
        method: 'get',
        url: `/trainCampBarrier/barrierDetail/${this.editId}`
      })
        .then((res) => {
          if (res.ok) {
            this.dynamicValidateForm = res.body;
            res.body.taskDtoList.forEach((item) => {
              item.taskType = String(item.taskType);
              if (item.templateId === DTaskName.postDynamic) {
                item.businessId = Number(item.businessId);
                this.topicNameList.push({
                  id: item.businessId,
                  topicName: item.topicName
                });
              }
              this.taskTypeChange(item, false);
            });
            this.dynamicValidateForm.taskDtoList = res.body.taskDtoList;
            this.imageUrl = res.body.barrierGuide;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-color-picker__icon,
.el-input,
.el-textarea {
  width: 40%;
}

::v-deep .avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &.el-upload:hover {
    border-color: #409eff;
  }
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
