<template>
  <common-dialog
    width="1050px"
    title="优惠卷提醒管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div v-if="row" class="dialog-main">
      <el-descriptions border :column="2" title="优惠卷信息" class="mb10">
        <template slot="extra">
          <el-button type="primary" size="small" @click="addDialogShow = true">新增</el-button>
        </template>
        <el-descriptions-item label="优惠卷名称">{{ row.couponName }}</el-descriptions-item>
        <el-descriptions-item label="有效范围时间">{{ handleDate() }}</el-descriptions-item>
        <el-descriptions-item label="优惠卷状态" :span="2">{{ row.state ? '已启用': '已禁用' }}</el-descriptions-item>
      </el-descriptions>
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        :height="500"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="couponName" label="提醒时间" align="center">
          <template slot="header">
            <div @click="lookRule"><span>提醒时间</span><i class="el-icon-question question" /></div>
          </template>
          <template slot-scope="scope">
            <span>距离失效前{{ scope.row.remindDatetime | timestampToSecond }}发送提醒</span>
          </template>
        </el-table-column>
        <el-table-column prop="couponName" label="启停" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.remindStatus"
              active-value="1"
              inactive-value="0"
              active-color="#13ce66"
              @change="handleEnable(scope.row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getReminderTaskList"
        />
      </div>
    </div>

    <!-- 新增弹窗 -->
    <common-dialog
      width="650px"
      title="新增提醒"
      :show-footer="true"
      :visible.sync='addDialogShow'
      @confirm="submit"
      @close='addDialogShow = false'
    >
      <div class="dialog-main">
        <h3>距离优惠卷失效前</h3>
        <div>
          <el-input-number v-model="addForm.day" :min="0" label="天" size="small" :precision="0" />
          <span class="m10">天</span>
          <el-input-number v-model="addForm.hour" :min="0" :max="23" label="时" size="small" :precision="0" />
          <span class="m10">时</span>
          <el-input-number v-model="addForm.minute" :min="0" :max="59" label="分" size="small" :precision="0" />
          <span class="m10">分</span>
          <span>提醒</span>
        </div>
      </div>
    </common-dialog>

  </common-dialog>
</template>

<script>
import { transformSecond } from '@/utils';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      tableData: [],
      tableLoading: false,
      addDialogShow: false,
      addForm: {
        day: 0,
        hour: 0,
        minute: 0
      },
      effectiveTime: 0, // 单位秒
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    lookRule() {
      const html = `
        <p>创建优惠券之后，默认创建2个提醒：</p>
        <p>&nbsp;&nbsp;1、距离优惠券失效24小时时提醒一次；<strong>若优惠券的有效期本身不足24小时的，则不提醒；</strong></p>
        <p>&nbsp;&nbsp;2、距离优惠券失效1小时时提醒一次；<strong>若优惠券的有效期本身不足1小时的，则不提醒；</strong></p>
        <br/>
        <p>注意事项：</p>
        <p>&nbsp;&nbsp;1、禁用优惠券后仍然会提醒；</p>
        <p>&nbsp;&nbsp;2、可以禁用默认条件1、条件2的提醒；</p>
      `;
      this.$alert(html, '小提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '我知道了'
      });
    },
    handleEnable(row) {
      const params = {
        remindId: row.remindId,
        remindStatus: row.remindStatus
      };
      this.$post('handleCouponReminEnable', params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getReminderTaskList();
          }
        });
    },
    handleDate() {
      if (this.row.effectiveType === 1) {
        const effective = this.row.endTime - this.row.startTime;
        this.effectiveTime = effective / 1000; // 毫秒转秒
      }

      if (this.row.effectiveType === 2) {
        this.effectiveTime = this.row.dynamicTime;
      }

      return transformSecond(this.effectiveTime);
    },
    // 获取优惠卷过期提醒列表
    getReminderTaskList() {
      this.tableLoading = true;
      const params = {
        couponId: this.row.couponId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getCouponRemindList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    open() {
      if (this.row) {
        this.getReminderTaskList();
      }
    },
    // 新增过期任务提醒
    submit() {
      let seconds = 0;
      seconds += (this.addForm.day * 86400);
      seconds += (this.addForm.hour * 3600);
      seconds += (this.addForm.minute * 60);

      if (seconds > this.effectiveTime) {
        this.$message.error('设置的过期提醒时间，不能大于优惠券有效时间范围喔！');
        return;
      }

      const params = {
        couponId: this.row.couponId,
        remindDatetime: seconds
      };
      this.$post('addCouponRemindTask', params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.addDialogShow = false;
            this.getReminderTaskList();
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
::v-deep .m10 {
  display: inline-block;
  margin: 0 10px;
}

::v-deep .mb10 {
  margin-bottom: 10px;
}

.question {
  margin-left: 5px;
  color: #20a0ff;
  cursor: pointer;
}

</style>
