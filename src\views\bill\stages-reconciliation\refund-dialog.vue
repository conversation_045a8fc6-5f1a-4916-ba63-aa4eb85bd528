<template>
  <div>
    <common-dialog
      v-loading="showLoading"
      :show-footer="false"
      :title="title"
      :visible.sync="showRefund"
      width="80%"
      @open="init"
      @close="closeDialog"
    >
      <el-form
        ref="refundRef"
        class="runform"
        size="mini"
        :model="refundForm"
        label-width="120px"
        :rules="refundRules"
      >
        <el-form-item class="reqitem" label="姓名：">
          <div class="reqinput">{{ refundForm.realName }}</div>
        </el-form-item>
        <el-form-item class="reqitem" label="身份证号：">
          <div class="reqinput">{{ refundForm.idCard }}</div>
        </el-form-item>
        <el-form-item class="reqitem" label="手机号码：">
          <div class="reqinput">{{ refundForm.mobile }}</div>
        </el-form-item>
        <el-form-item class="reqitem" label="缴费名称：">
          <div class="reqinput">{{ refundForm.goodsShelfName }}</div>
        </el-form-item>
        <el-form-item class="reqitem" label="邀约人：">
          <div class="reqinput">{{ refundForm.invitationEmpName }}</div>
        </el-form-item>
        <el-form-item class="reqitem" label="分销人：">
          <div class="reqinput">{{ refundForm.distributionEmpName }}</div>
        </el-form-item>
        <el-form-item label="退款项目：" :required="true">
          <el-table :data="refundData" border>
            <el-table-column label="费用项" prop="installmentTexts" />
            <el-table-column label="应缴金额" prop="totalPrice" />
            <el-table-column label="实缴金额" prop="payAmount" />
            <el-table-column label="智米抵扣" prop="zmScale" />
            <el-table-column label="滞留金抵扣" prop="demurrageScale" />
            <el-table-column label="实缴退费金额" prop="refundAmount">
              <template slot-scope="scope">
                <el-form-item label="" :prop="rudType == 1?'b_agwee':''" :rules="refundRules.b_agwee">
                  <el-input v-model="scope.row.refundAmount" :disabled="rudType !== 1" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="智米退费金额" prop="zmRefundAmount">
              <template slot-scope="scope">
                <el-form-item label="" :prop="rudType == 1?'b_agtre':''" :rules="refundRules.b_agtre">
                  <el-input v-model="scope.row.zmRefundAmount" :disabled="rudType !== 1" />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="滞留金退费金额" prop="demurrageRefundAmount">
              <template slot-scope="scope">
                <el-form-item label="" :prop="rudType == 1?'b_aytge':''" :rules="refundRules.b_aytge">
                  <el-input v-model="scope.row.demurrageRefundAmount" :disabled="rudType !== 1" />
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="退款方式：" :required="true" :prop="rudType == 1?'refundType':''" :rules="refundRules.refundType">
          <el-radio-group v-model="refundForm.refundType" :disabled="rudType !== 1">
            <el-radio :label="0">原路退回退款</el-radio>
            <el-radio :label="1">线下出纳打款退款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注：">
          <el-input v-model="refundForm.remark" type="textarea" :rows="4" :disabled="rudType !== 1" />
        </el-form-item>
        <el-form-item v-if="rudType !== 1" class="reqfgr" label="操作记录：">
          <el-steps :active="refundActive" align-center :process-status="refundsStatu" finish-status="success">
            <el-step v-for="item in refundStep" :key="item.title" :title="item.title" :description="item.desc" />
          </el-steps>
        </el-form-item>
        <el-form-item class="reqbtn">
          <el-popconfirm
            v-if="rudType == 1"
            confirm-button-text='确定'
            cancel-button-text='取消'
            cancel-button-type="Primary"
            icon="el-icon-info"
            icon-color="#409EFF"
            placement="top"
            popper-class="refund-maske"
            title="确认提交首付退款申请？"
            @confirm="submitBtn(1)"
          >
            <el-button slot="reference" size="medium" type="primary">提交申请</el-button>
          </el-popconfirm>
          <el-popconfirm
            v-if="rudType == 2"
            confirm-button-text='确定'
            cancel-button-text='取消'
            cancel-button-type="Primary"
            icon="el-icon-info"
            icon-color="#409EFF"
            placement="top"
            popper-class="refund-maske"
            title="确认审核通过？"
            @confirm="submitBtn(2)"
          >
            <el-button slot="reference" size="medium" class="btn" type="primary">审核通过</el-button>
          </el-popconfirm>
          <el-popconfirm
            v-if="rudType == 2"
            confirm-button-text='确定'
            cancel-button-text='取消'
            cancel-button-type="Primary"
            icon="el-icon-info"
            icon-color="#F56C6C"
            placement="top"
            popper-class="refund-maske"
            title="确认驳回？"
            @confirm="submitBtn(3)"
          >
            <el-button slot="reference" size="medium" class="btn" type="danger">驳回</el-button>
          </el-popconfirm>
          <el-button size="medium" class="btn" @click="closeDialog">
            {{ rudType == 3 ? '返回':'取消' }}
          </el-button>
        </el-form-item>
      </el-form>
    </common-dialog>
    <!-- 反馈提示弹窗 -->
    <el-dialog
      title=""
      width="36%"
      :visible="showMaske"
      destroy-on-close
      :append-to-body="true"
      :fullscreen='false'
      @close="closeMakse"
    >
      <div class="refund-tips">
        <div class="tips-title">
          <img
            class="tips-img"
            :src="`https://yzims.oss-cn-shenzhen.aliyuncs.com/self_exam/radio-${refundMaskeImgs}.png`"
            alt=""
          >
          <h2 class="tips-h2">{{ refundMaskeTips }} </h2>
          <p class="tips-p">{{ refundMaskeText }}</p>
        </div>
        <el-button size="medium" class="btn" type="primary" @click="closeMakse">我知道了</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { formatTimeStamp } from '@/utils';
export default {
  props: {
    showRefund: { type: Boolean, default: false },
    title: { type: String, default: '首付退款' },
    rudType: { type: [String, Number], default: null },
    orderId: { type: [String, Number], default: null }
  },
  data() {
    return {
      showLoading: false,
      showMaske: false,
      refundForm: {},
      refundData: [],
      refundStep: [],
      refundsStatu: '',
      refundActive: 1,
      refundMaskeImgs: '',
      refundMaskeTips: '',
      refundMaskeText: '',
      refundRules: {
        b_agwee: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              value = this.refundData[0].refundAmount;
              const olds = Number(this.refundForm?.payAmount || 0);
              if (value === '' || value === undefined) {
                callback(new Error('请输入'));
              } else if (value < 0) {
                callback(new Error('输入金额不能小于0！'));
              } else if (value > Number(olds)) {
                callback(new Error('输入金额不能大于实缴退费金额！'));
              } else {
                this.refundForm['refundAmount'] = value;
                callback();
              }
            }
          }
        ],
        b_agtre: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              value = this.refundData[0].zmRefundAmount;
              const olds = Number(this.refundForm?.zmScale || 0);
              if (value === '' || value === undefined) {
                callback(new Error('请输入'));
              } else if (value < 0) {
                callback(new Error('输入金额不能小于0！'));
              } else if (value > Number(olds)) {
                callback(new Error('输入金额不能大于智米抵扣金额！'));
              } else {
                this.refundForm['zmRefundAmount'] = value;
                callback();
              }
            }
          }
        ],
        b_aytge: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              value = this.refundData[0].demurrageRefundAmount;
              const olds = Number(this.refundForm?.demurrageScale || 0);
              if (value === '' || value === undefined) {
                callback(new Error('请输入'));
              } else if (value < 0) {
                callback(new Error('输入金额不能小于0！'));
              } else if (value > Number(olds)) {
                callback(new Error('输入金额不能大于滞留金抵扣金额！'));
              } else {
                this.refundForm['demurrageRefundAmount'] = value;
                callback();
              }
            }
          }
        ],
        refundType: {
          trigger: 'change',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请选择'));
            } else callback();
          }
        }
      }
    };
  },
  methods: {
    init() {
      this.refundForm = {};
      this.refundData = [];
      this.showLoading = true;
      // 1：申请退首付 2、3：审批、驳回
      const urls = this.rudType === 1 ? 'getSubRefundDetail' : 'getRefundDetail';
      this.$post(urls, { id: this.orderId }, { json: 'application/json' }).then(res => {
        const { code, body } = res;
        if (code === '00' && body) {
          switch (Number(body.installmentType)) {
            case 0:
              body.installmentTexts = '抵扣项';
              break;
            case 1:
              body.installmentTexts = '首付';
              break;
            case 2:
              body.installmentTexts = '尾款';
              break;
            case 3:
              body.installmentTexts = '常规退费';
              break;
            case 4:
              body.installmentTexts = '利息补贴';
              break;
            default:
              body.installmentTexts = '';
              break;
          }
          // 审批、驳回时，才会展示进度条
          if (this.rudType !== 1) {
            body.createTime = formatTimeStamp(body.createTime);
            body.reviewTime = formatTimeStamp(body.reviewTime);
            this.refundStep = [
              { title: '提交首付退款申请', desc: `${body?.applyEmpName} ${body?.createTime}` },
              { title: '负责人审核', desc: body?.reviewTime ? `${body?.reviewEmpName} ${body?.reviewTime}` : '' },
              { title: body?.status ? '完成' : '', desc: '' }
            ];
            this.refundActive = Number(body?.status) + 1;
            this.refundsStatu = body?.status === 2 ? 'error' : body?.status === 1 ? 'success' : '';
          }
          // 赋值
          if (!body?.refundAmount) body.refundAmount = 0;
          if (!body?.zmRefundAmount) body.zmRefundAmount = 0;
          if (!body?.demurrageRefundAmount) body.demurrageRefundAmount = 0;
          this.refundForm = body;
          this.refundData = [{
            ...body,
            installmentTexts: body?.installmentTexts || '',
            totalPrice: body?.totalPrice || 0,
            payAmount: body?.payAmount || 0,
            zmScale: body?.zmScale || 0,
            demurrageScale: body?.demurrageScale || 0,
            refundAmount: body?.refundAmount || 0,
            zmRefundAmount: body?.zmRefundAmount || 0,
            demurrageRefundAmount: body?.demurrageRefundAmount || 0
          }];
        } else {
          this.refundForm = {};
          this.refundData = [];
        }
        this.$refs['refundRef'].resetFields();
        this.showLoading = false;
      }).catch(() => {
        this.refundForm = {};
        this.refundData = [];
        this.showLoading = false;
      });
    },
    submitBtn(type) {
      console.log(type);
      if (type === 1) {
        this.$refs['refundRef'].validate((valid) => {
          if (valid) {
            console.log('success submit!!', this.refundForm);
            this.submitApi(1);
          } else {
            console.log('error submit!!');
            return false;
          }
        });
      } else if (type === 2) {
        this.submitApi(2);
      } else {
        this.submitApi(3);
      }
    },
    submitApi(pes) {
      this.showLoading = true;
      this.showMaske = false;
      this.refundMaskeImgs = '';
      this.refundMaskeTips = '';
      this.refundMaskeText = '';
      let obs = { id: this.refundForm?.id };
      if (pes === 1) {
        obs = { ...this.refundForm, id: this.orderId };
      }
      const urs = pes === 1 ? 'subGoodsRefund' : pes === 2 ? 'subGoodsApproval' : 'subGoodsAbort';
      this.$post(urs, obs, { json: 'application/json' }).then((res) => {
        const _cosd = res?.code === '00';
        this.showLoading = false;
        this.refundMaskeImgs = _cosd ? 'right' : 'error';
        if (pes === 1) {
          this.showMaske = true;
          this.refundMaskeTips = _cosd ? '提交成功' : '提交失败';
          this.refundMaskeText = _cosd ? '请等待负责人审核' : res?.msg || '';
        } else if (pes === 2) {
          this.showMaske = true;
          this.refundMaskeTips = _cosd ? '审核通过' : '审核未通过';
          this.refundMaskeText = _cosd ? '金额将原路退回/线下出纳打款给用户' : res?.msg || '';
        } else {
          this.closeMakse();
          this.$message({ message: _cosd ? '已驳回' : '驳回失败', type: _cosd ? 'right' : 'error' });
        }
      }).catch(() => {
        this.showLoading = false;
      });
    },
    closeDialog() {
      this.$refs['refundRef'].resetFields();
      this.$emit('close');
    },
    closeMakse() {
      this.showMaske = false;
      if (this.refundMaskeImgs === 'right') {
        this.$parent.getTableList();
      }
      this.closeDialog();
    }
  }
};
</script>
<style lang="scss">
.runform {
  margin: 30px 40px 50px 0;
  .el-form-item__error {
    position: relative;
    padding-top: 10px;
    line-height: 1.4;
  }
  .reqitem {
    width: 40%;
  }
  .reqinput{
    width: 100%;
    height: 28px;
    line-height: 28px;
    padding: 0 15px;
    border: 1px solid #333333;
    background-color: #F5F7FA;
    border-color: #E4E7ED;
    color: #C0C4CC;
    cursor: not-allowed;
  }
  .reqbtn {
    margin-top: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    .btn {
      margin-left: 10px;
    }
  }
  .reqfgr {
    // width: 90%;
    margin-top: 50px;
  }
}
.refund-maske .el-popconfirm__action{
  margin-top: 10px;
}
.refund-tips {
  min-height: 300px;
  text-align: center;
  .tips-title {
    width: 40%;
    margin: auto;
    margin-bottom: 60px;
    text-align: center;
    .tips-img {
      width: 80px;
      height: 80px;
    }
    .tips-h2 {
      width: 100%;
    }
    .tips-p {
      width: 100%;
      color: #999999;
    }
  }
  .el-button {
    width: 20%;
    margin: auto;
  }
}

</style>
