<template>
  <common-dialog
    is-full
    width="600px"
    title="领读人管理"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="success" size="small" @click="childDialog = true">新增</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        height="calc(100vh - 175px)"
        :data="tableData"
      >
        <el-table-column prop="ledReadName" label="领读人姓名" align="center" />
        <el-table-column prop="ledReadHeadPortrait" label="头像" align="center">
          <template slot-scope="scope">
            <el-image
              class="image"
              :src="scope.row.ledReadHeadPortrait | splitOssUrl"
              fit="头像"
            />
          </template>
        </el-table-column>
        <el-table-column prop="ledReadIntro" label="领读人简介" align="center" />
        <el-table-column prop="status" label="是否启用" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1? 'success':'danger' ">
              {{ scope.row.status === 1 ? '启用':'禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="编辑" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="updateStatus(scope.row)">
              {{ scope.row.status === 0 ? '启用': '禁用' }}
            </el-button>
            <el-button type="text" @click="edit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <common-dialog
      :show-footer="true"
      width="600px"
      title="新增/编辑"
      :visible.sync='childDialog'
      @open="childOpen"
      @confirm="submitTeacher"
      @close='childClose'
    >
      <div class="dialog-main">
        <el-form
          ref="form"
          class="form"
          size='mini'
          :model="form"
          :rules="rules"
          label-width="120px"
        >
          <el-form-item label='领读人姓名' prop='ledReadName'>
            <el-input
              v-model="form.ledReadName"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label='领读人简介' prop='ledReadIntro'>
            <el-input
              v-model="form.ledReadIntro"
              :rows="4"
              type="textarea"
              placeholder="请输入内容"
              maxlength="30"
              show-word-limit
            />
          </el-form-item>
          <el-form-item label='领读人头像' prop='ledReadHeadPortraiFile.fileUrl'>
            <upload-file
              :max-limit="1"
              :file-list='fileList'
              @remove="handleRemoveImg"
              @success="uploadSuccess"
            />
          </el-form-item>
          <el-form-item label='是否启用' prop='status'>
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

        </el-form>
      </div>
    </common-dialog>

  </common-dialog>
</template>

<script>
import { ossUri } from '@/config/request';
import { checkInput } from '@/common/vali';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      show: false,
      tableLoading: false,
      childDialog: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      rules: {
        // ledReadIntro: [
        //   { required: true, validator: checkInput, trigger: 'blur' }
        // ],
        ledReadName: [
          { required: true, validator: checkInput, trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        'ledReadHeadPortraiFile.fileUrl': [
          { required: true, message: '请上传', trigger: 'change' }
        ]
      },
      form: {
        ledReadIntro: '',
        ledReadName: '',
        status: 1,
        ledReadHeadPortraiFile: {
          fileUrl: '',
          isAdd: 0
        }
      },
      currentRow: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
  },
  methods: {
    // 更新领读人状态
    updateStatus(row) {
      const params = {
        ledReadId: row.ledReadId
      };
      this.$post('ledReadUpdateStatus', params)
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('getledReadList', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
            this.tableLoading = false;
          }
        });
    },
    open() {
      this.getTableList();
    },
    // 编辑领读人
    edit(row) {
      this.currentRow = row;
      this.childDialog = true;
    },
    // 获取单个领读人信息
    getTeacherInfo() {
      const parmas = {
        ledReadId: this.currentRow.ledReadId
      };
      this.$post('getLedReadInfo', parmas)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.form.ledReadName = body.ledReadName;
            this.fileList.push({ url: ossUri + body.ledReadHeadPortrait });
            this.form.ledReadHeadPortraiFile.fileUrl = body.ledReadHeadPortrait;
            this.form.ledReadIntro = body.ledReadIntro;
            this.form.status = body.status;
          }
        });
    },
    // 子弹窗open
    childOpen() {
      if (this.currentRow) {
        this.getTeacherInfo();
      }
    },
    submitTeacher() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apiKey = 'addTeacher';
          const params = {
            ...this.form
          };

          if (this.currentRow) {
            apiKey = 'editTeacher';
            params.ledReadId = this.currentRow.ledReadId;
          }

          this.$post(apiKey, params, { json: true })
            .then(res => {
              const { fail } = res;
              if (!fail) {
                this.childDialog = false;
                this.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.getTableList();
              }
            });
        }
      });
    },
    handleRemoveImg({ file, fileList }) {
      this.form.ledReadHeadPortraiFile.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.ledReadHeadPortraiFile.fileUrl = response;
      this.form.ledReadHeadPortraiFile.isAdd = 1;
    },
    childClose() {
      this.currentRow = null;
      this.childDialog = false;
      this.fileList = [];
      this.form.ledReadHeadPortraiFile.isAdd = 0;
      this.$refs.form.resetFields();
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;

  .el-image__error {
    font-size: 12px;
  }

}
</style>
