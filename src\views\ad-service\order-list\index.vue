<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='onSearch'
    >
      <el-form-item label='手机号码' prop='mobile'>
        <el-input v-model="form.mobile" placeholder="请输入手机号码" />
      </el-form-item>

      <el-form-item label='投放渠道' prop='advertisingChannels'>
        <el-select v-model="form.advertisingChannels" clearable>
          <el-option
            v-for="item in $localDict['adChannel']"
            :key="item.dictValue"
            :label="item.dictName"
            :value="item.dictValue"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='商品' prop='productId'>
        <el-select
          v-model="form.productId"
          filterable
          clearable
          placeholder="请选择商品"
        >
          <el-option
            v-for="item in productOptions"
            :key="item.productId"
            :label="item.productName"
            :value="item.productId"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='下单时间' prop='placeDate'>
        <el-date-picker
          v-model="form.placeDate"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>

      <el-form-item label='购买时间' prop='date'>
        <el-date-picker
          v-model="form.date"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        />
      </el-form-item>

      <el-form-item label='是否付款' prop="paymentStatus">
        <el-select v-model="form.paymentStatus" clearable>
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            placeholder="请选择"
          />
        </el-select>
      </el-form-item>

      <el-form-item label='是否添加企微' prop='addWorkWechatStatus'>
        <el-select v-model="form.addWorkWechatStatus" clearable>
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            placeholder="请选择"
          />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" icon="el-icon-upload2" @click="onExportExcel">导出</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="advertisingChannels" label="投放渠道" align="center">
        <template slot-scope="scope">
          {{ scope.row.advertisingChannels | adChannel }}
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" align="center" />
      <el-table-column prop="paymentStatus" label="是否付款" align="center">
        <template slot-scope="scope">
          {{ scope.row.paymentStatus | yesOrNo }}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" label="订单金额" align="center" />
      <el-table-column prop="placeTime" label="下单时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.placeTime | transformTimeStamp('YYYY-MM-DD HH:mm:ss', '/') }}
        </template>
      </el-table-column>
      <el-table-column prop="paymentTime" label="购买时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.paymentTime | transformTimeStamp('YYYY-MM-DD HH:mm:ss', '/') }}
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="手机号码" align="center" />
      <el-table-column prop="realName" label="真实姓名" align="center" />
      <el-table-column prop="nickname" label="用户昵称" align="center">
        <template slot-scope="scope">
          {{ scope.row.nickname || '/' }}
        </template>
      </el-table-column>
      <el-table-column prop="addWorkWechatStatus" label="是否添加企微" align="center">
        <template slot-scope="scope">
          {{ scope.row.addWorkWechatStatus | yesOrNo }}
        </template>
      </el-table-column>
      <el-table-column prop="addWorkWechatNames" label="添加企微明细" align="center">
        <template slot-scope="scope">
          {{ scope.row.addWorkWechatNames || '/' }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="loadNextPage"
      />
    </div>
  </div>
</template>

<script>
import { exportExcel, handleDateControl } from '@/utils';

export default {
  filters: {
    adChannel(val) {
      const ChannelEnum = { BYTE_DANCE: '字节渠道', WECHAT: '微信渠道' };
      return ChannelEnum[val] || '/';
    },
    yesOrNo(val) {
      const JudgeEnum = { 0: '否', 1: '是' };
      return JudgeEnum[val] || '/';
    }
  },

  data() {
    return {
      form: {
        mobile: '',
        advertisingChannels: '',
        productId: '',
        date: '',
        paymentStatus: '',
        addWorkWechatStatus: '',
        placeDate: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: { page: 1, total: 0, limit: 10 },
      productOptions: [],
      options: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ]
    };
  },

  mounted() {
    this.getProductOptions();
    this.loadNextPage();
  },

  methods: {
    loadNextPage() {
      this.tableLoading = true;
      const params = this.getQueryParams();

      this.$post('getAdOrderList', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    },

    getQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.date);
      const placeDate = handleDateControl(formData.placeDate);
      formData.startPaymentTime = date[0];
      formData.endPaymentTime = date[1];
      formData.startPlaceTime = placeDate[0];
      formData.endPlaceTime = placeDate[1];
      delete formData.date;
      delete formData.placeDate;

      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },

    getProductOptions() {
      const params = { page: 1, rows: 99999, sName: '' };
      this.$post('getAdProductSelect', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.productOptions = body.data;
        }
      });
    },

    onExportExcel() {
      const params = this.getQueryParams();
      exportExcel('exportAdOrderList', params);
    },

    onSearch(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.loadNextPage();
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
