<template>
  <div class="yz-base-container">

    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='onSearch'
    >

      <el-form-item label='姓名' prop='userName'>
        <el-input v-model="form.userName" placeholder="请输入用户姓名" />
      </el-form-item>

      <el-form-item label='手机号' prop='mobile'>
        <el-input v-model="form.mobile" placeholder='请输入手机号' />
      </el-form-item>

      <el-form-item label='远智编码' prop='yzCode'>
        <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
      </el-form-item>

      <el-form-item label='是否推荐' prop='ifExpertRecommend'>
        <el-select v-model="form.ifExpertRecommend" placeholder="请选择是否推荐">
          <el-option label="是" :value="1" />
          <el-option label="否" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item label="加入时间" prop="joinTime">
        <el-date-picker
          v-model="form.joinTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='onSearch("reset")' />
      </div>

    </el-form>

    <div class="table-tools">
      <el-button type="primary" size="mini" @click="onEdit()">添加用户</el-button>
    </div>

    <!-- 表格 -->
    <el-table ref="table" v-loading="tableLoading" border size="small" class="table-container" :data="tableData">
      <el-table-column prop="userName" label="姓名" align="center" />
      <el-table-column prop="yzCode" label="远智编号" align="center" />
      <el-table-column prop="joinTime" label="加入时间" align="center" />
      <el-table-column prop="welfareNum" label="参加活动次数" align="center" />
      <el-table-column prop="welfareDurationDisplay" label="累计公益时长" align="center" />
      <el-table-column prop="monthWelfareDurationDisplay" label="月公益时长" align="center" />
      <el-table-column label="是否推荐" align="center" :formatter="handleRecommend" />
      <el-table-column label="推荐介绍" align="center" :formatter="handleRecommendDesc" />
      <el-table-column align="center" label="操作">
        <template #default="{ row }">
          <el-button type="text" size="small" @click="onEdit(row)">编辑</el-button>
          <el-button type="text" size="small" @click="onRecommend(row)">
            {{ getIsRecommend(row) ? '取消' : '' }}达人推荐
          </el-button>
          <el-button type="text" size="small" @click="onDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination :total='pager.total' :page.sync="pager.pageNum" :limit.sync="pager.pageSize" @pagination="getData" />
    </div>

    <!-- 推荐弹框 -->
    <common-dialog
      show-footer
      title="推荐介绍"
      :visible.sync="recommendVisible"
      confirmText="马上推荐"
      cancelText="取消推荐"
      width="350px"
      @confirm="confirmRecommend"
      @cancel="recommendVisible = false"
      @close="recommendVisible = false"
    >
      <div v-loading="recommendLoading" class="recommend-input">
        <el-input v-model="recommendDesc" size="small" placeholder="请输入推荐介绍" maxlength="6" show-word-limit />
      </div>
    </common-dialog>

    <!-- 新增|编辑弹框 -->
    <dialog-user :visible.sync="userVisible" :row="editRow" @confirm="editRow ? getData() : onSearch()" />
  </div>
</template>

<script>
import DialogUser from './components/dialog-user.vue';

const IS_YES = 1;
export default {
  components: { DialogUser },
  data() {
    return {
      tableLoading: true,
      recommendLoading: false,
      userVisible: false,
      editRow: {},
      tableData: [],
      form: { userName: '', mobile: '', yzCode: '', joinTime: [] },
      recommendRow: -1,
      recommendVisible: false,
      recommendDesc: '',
      pager: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      try {
        this.tableLoading = true;
        const params = { ...this.pager, ...this.form };
        // 处理开始和结束时间
        if (params.joinTime?.length) {
          [params.joinBeginTime, params.joinEndTime] = params.joinTime;
        }
        delete params.joinTime;
        delete params.total;
        const { code, body } = await this.$http({
          method: 'post',
          url: '/welfareUserInfo/page',
          data: params,
          json: true
        });
        if (code !== '00') return;
        this.pager.total = body.recordsTotal;
        this.tableData = body.data;
      } finally {
        this.tableLoading = false;
      }
    },
    // 是否 推荐
    getIsRecommend(row) {
      return row.ifExpertRecommend === IS_YES;
    },
    // 处理是否推荐显示
    handleRecommend(row) {
      return this.getIsRecommend(row) ? '是' : '否';
    },
    // 处理推荐介绍显示
    handleRecommendDesc(row) {
      return this.getIsRecommend(row) ? row.recommendDesc : '/';
    },
    // btn 搜索 | 重置
    onSearch(type) {
      if (this.tableLoading) return;
      if (type === 'reset') {
        this.$refs.searchForm.resetFields();
      }
      this.pager.pageNum = 1;
      this.getData();
    },
    // btn 确定|取消 推荐
    onRecommend(row) {
      if (this.getIsRecommend(row)) {
        this.$confirm('确定取消推荐公益人员吗？', '', { type: 'warning' }).then(async(e) => {
          if (e !== 'confirm') return;
          this.tableLoading = true;
          const params = {
            id: row.id,
            ifExpertRecommend: 0,
            recommendDesc: row.recommendDesc
          };
          const { code } = await this.$http({
            method: 'post',
            url: `/welfareUserInfo/updateRecommend`,
            data: params,
            json: true
          });
          this.tableLoading = false;
          if (code !== '00') return;
          this.$message.success('取消推荐成功');
          this.getData();
        }).catch(() => { });
      } else {
        this.recommendRow = row;
        this.recommendDesc = '';
        this.recommendVisible = true;
      }
    },
    // 确定推荐
    async confirmRecommend() {
      if (this.recommendLoading) return;
      if (!this.recommendDesc) {
        return this.$message.error('请输入推荐介绍');
      }
      try {
        this.recommendLoading = true;
        const params = {
          id: this.recommendRow.id,
          ifExpertRecommend: 1,
          recommendDesc: this.recommendDesc
        };
        const { code } = await this.$http({
          method: 'post',
          url: `/welfareUserInfo/updateRecommend`,
          data: params,
          json: true
        });
        this.tableLoading = false;
        if (code !== '00') return;
        this.$message.success('推荐成功');
        this.getData();
      } finally {
        this.recommendLoading = false;
        this.recommendVisible = false;
      }
    },
    // btn 编辑
    onEdit(row) {
      this.editRow = row;
      this.userVisible = true;
    },
    // btn 删除
    onDelete(row) {
      this.$confirm('确认删除公益人员吗？', '', { type: 'warning' }).then(async(e) => {
        if (e !== 'confirm') return;
        this.tableLoading = true;
        const { code } = await this.$http({
          method: 'get',
          url: `/welfareUserInfo/delete/${row.id}`
        });
        this.tableLoading = false;
        if (code !== '00') return;
        this.$message.success('删除成功');
        this.getData();
      }).catch(() => {
        this.tableLoading = false;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.yz-search-form {
  margin: 20px 0;
}

.table-tools {
  text-align: right;
  margin-bottom: 10px;
}

.recommend-input {
  padding: 20px;
}
</style>
