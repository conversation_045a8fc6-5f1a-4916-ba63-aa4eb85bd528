<template>
  <el-dialog
    title="选择受理人"
    :visible.sync="dialogVisible"
    width="500px"
    append-to-body
    @close="handleClose"
  >
    <el-form
      ref="handlerForm"
      :model="handlerForm"
      label-width="100px"
      size="small"
    >
      <el-form-item label="受理人" prop="handler">
        <common-select
          v-model="handlerForm.handler"
          type="handler"
          placeholder="请选择受理人"
          @change="handleHandlerChange"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="confirmAddHandler" :loading="loading"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "HandlerSelector",
  components: {
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  props: {
    visible: {
      type: <PERSON>olean,
      default: false,
    },
    receiveEmpId: {
      type: [String, Number],
      default: "",
    },
    receiveEmpName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      loading: false,
      handlerForm: {
        handler: "",
        handlerObj: null,
      },
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.resetForm();
      }
    },
  },
  methods: {
    // 重置表单
    resetForm() {
      this.handlerForm = {
        handler: "",
        handlerObj: null,
      };
    },

    // 处理受理人选择变更
    handleHandlerChange(val, option) {
      console.log("受理人选择:", val, option);
      this.handlerForm.handlerObj = option;
    },

    // 确认添加受理人
    confirmAddHandler() {
      if (!this.handlerForm.handler) {
        this.$message.warning("请选择受理人");
        return;
      }

      const handlerId = this.handlerForm.handler;
      const handlerName =
        this.handlerForm.handlerObj?.label || this.handlerForm.handler;

      // 双向绑定，更新receiveEmpId和receiveEmpName
      this.$emit("update:receiveEmpId", handlerId);
      this.$emit("update:receiveEmpName", handlerName);

      this.handleClose();
    },

    // 取消选择
    handleCancel() {
      this.handleClose();
    },

    // 关闭弹窗
    handleClose() {
      this.$emit("update:visible", false);
      this.resetForm();
    },
  },
};
</script>

<style lang="scss" scoped></style>
