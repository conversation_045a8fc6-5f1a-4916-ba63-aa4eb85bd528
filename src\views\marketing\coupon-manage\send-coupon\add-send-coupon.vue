<template>
  <common-dialog
    :show-footer="true"
    is-full
    width="60%"
    :title="title"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='派发优惠券' prop='couponId'>
          <yz-select
            v-model="form.couponId"
            url="/puCoupon/findBaseList.do"
            method="post"
            :props="{
              label: 'couponName',
              value: 'couponId',
              query: 'couponName'
            }"
          />
        </el-form-item>
        <el-form-item width="200" label='派发对象' prop='pieRollTeacherName'>
          <div class='yz-table-btnbox'>
            <el-button type="primary" size="small" icon="el-icon-plus" @click="addTeach">添加</el-button>
          </div>

          <!-- 关联 -->
          <el-table
            v-loading="tableLoading"
            border
            size="small"
            style="width: 100%"
            header-cell-class-name='table-cell-header'
            :data="teachList"
          >
            <el-table-column prop="empName" label="助学老师" align="center" />
            <el-table-column prop="dpName" label="所在部门" align="center" />
            <el-table-column prop="date" label="操作" align="center" fixed="right" width="150px">
              <template slot-scope="scope">
                <div class="yz-button-area">
                  <el-button type="text" @click="moveTeach(scope.row)">移除</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

      </el-form>

      <!-- 选择老师 弹窗 -->
      <select-teacher
        :childTeachRow="childTeachRow"
        :visible.sync="teachVisible"
        @refresh-list="teachChildRow"
        @selectTeach="teachChildRow"
      />
    </div>

  </common-dialog>
</template>

<script>
import selectTeacher from './select-teacher';
export default {
  components: {
    selectTeacher
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    // 校验券有效期
    const checkScopeUse = (rule, value, callback) => {
      // 关闭后=underfind(再次进入不验证)  移除后=underfind length报错
      if (this.teachList === undefined) {
        callback();
        return;
      }
      if (this.teachList.length === 0) {
        callback('请添加表格数据');
      } else {
        callback();
      }
    };
    return {
      show: false,
      tableLoading: false,
      teachVisible: false,
      selectType: '1',
      teachList: [],
      teachRow: [],
      childTeachRow: [],
      page: 1,
      limit: 10,
      total: 0,
      getEmpNameList: [],
      findBaseList: [],
      selectList: [
        {
          empId: ''
        }
      ],
      form: {
        couponName: '',
        pieRollTeacherName: '',
        couponId: '',
        puPieRollChildList: []
      },
      rules: {
        couponId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        pieRollTeacherName: [
          { required: true, validator: checkScopeUse, trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    addTeach(row) {
      this.teachVisible = true;
      this.childTeachRow = this.teachList;
    },
    teachChildRow(childRow) {
      this.teachList = childRow;
    },

    // 移除
    moveTeach(val) {
      this.form.couponName = '';
      this.teachList = this.teachList.filter(item => {
        return item.userId !== val.userId;
      });
    },

    open() {},
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form));
          this.teachList.map(item => {
            formData.puPieRollChildList.push({
              'pieRollTeacherName': item.empName,
              'pieRollTeacherId': item.userId
            });
          });
          this.$post('addPieRoll', formData, { json: true }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.close();
            }
          });
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('refresh-list', true);
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-table-btnbox{
  text-align: left;
}
.loadMoreSelect{
  margin-bottom: 10px;
}
::v-deep .el-table{
margin-top: 10px;
}
</style>
