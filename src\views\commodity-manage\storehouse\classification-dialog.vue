<template>
  <common-dialog title="分类管理" isFull :visible.sync='show' @open="init" @close='close'>
    <div class="main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='状态' prop='status'>
          <el-select v-model="form.status" placeholder="请选择">
            <el-option
              v-for="item in $localDict['status']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label='二级分类' prop='goodsTypeName'>
          <el-input v-model="form.goodsTypeName" placeholder='请输入问题分类' />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        :data="tableData"
        height="calc(100vh - 220px)"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
      >
        <el-table-column label="排序" align="center" prop="sort" />
        <el-table-column label="一级分类" align="center" prop="pGoodsTypeName" />
        <el-table-column label="二级分类" align="center" prop="goodsTypeName" />
        <el-table-column label="分类图标" align="center" prop="goodsTypePic">
          <template slot-scope="scope">
            <img :src="scope.row.goodsTypePic | splitOssUrl" style="object-fit: contain;width: 100%;">
          </template>
        </el-table-column>
        <el-table-column label="关联课程" align="center" prop="goodsNum" />
        <el-table-column label="最后操作人" align="center" prop="updateUserName" />
        <el-table-column label="操作时间" align="center" prop="updateTime" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            {{ scope.row.status | tansformStatus }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="updateStatus(scope.row)">{{ scope.row.status===1?'禁用':'启用' }}</el-button>
              <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>

    <!-- 子弹窗 -->
    <common-dialog showFooter :title="adTitle" :visible="adVisible" @confirm="submitClassification" @close="childClose">
      <div class="children-main">
        <el-form
          ref="classificationForm"
          class="form"
          size='mini'
          :model="classificationForm"
          label-width="120px"
          :rules="rules"
        >
          <el-form-item label='一级分类' prop='pGoodsTypeId'>
            <el-select v-model="classificationForm.pGoodsTypeId" v-loadmore="loadMore" placeholder="请选择">
              <el-option
                v-for="item in classification"
                :key="item.goodsTypeId"
                :label="item.goodsTypeName"
                :value="item.goodsTypeId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label='二级分类' prop='goodsTypeName'>
            <el-input v-model="classificationForm.goodsTypeName" placeholder="请输入二级分类名称" maxlength="20" />
          </el-form-item>

          <el-form-item label='展示顺序' prop='sort'>
            <el-input-number
              v-model="classificationForm.sort"
              placeholder='请输入展示顺序'
              :precision="0"
              class="yz-input-number"
              :min="0"
              :max="1000000000"
              :controls="false"
            />
          </el-form-item>

          <el-form-item label='分类图标' prop='fileUrl'>
            <upload-file
              :max-limit="1"
              :file-list='fileList'
              @remove="handleRemoveImg"
              @success="uploadSuccess"
            />
          </el-form-item>

          <el-form-item label='是否启用' prop='status'>
            <el-radio-group v-model="classificationForm.status">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      adVisible: false,
      adTitle: '添加',
      tableLoading: false,
      dialogVisible: false,
      selectPage: 0,
      fileList: [],
      originUrl: '', // 原图片路径
      classification: [],
      classificationForm: {
        goodsTypeLevel: '2', // 默认2
        pGoodsTypeId: '',
        goodsNum: 0,
        goodsTypeName: '',
        pGoodsTypeName: '',
        status: '',
        sort: undefined,
        fileUrl: '',
        goodsTypePicFile: {
          isAdd: 1,
          fileUrl: ''
        }
      },
      form: {
        goodsTypeName: '',
        status: '',
        goodsTypeLevel: '2'// 默认2
      },
      rules: {
        pGoodsTypeId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsTypeName: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        sort: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        fileUrl: [
          { required: true, message: '请上传分类图标', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      },
      currentEditGoodsTypeId: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      this.getTableList();
      this.getClassification();
    },
    updateStatus(row) {
      const data = {
        goodsTypeId: row.goodsTypeId,
        status: row.status === 1 ? 2 : 1
      };
      this.$post('updateCfStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleEdit(row) {
      this.currentEditGoodsTypeId = row.goodsTypeId;
      this.adTitle = '编辑';
      this.$post('getClassificationInfo', { goodsTypeId: row.goodsTypeId }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.fileList.push({ url: ossUri + body.goodsTypePic });
          this.originUrl = body.goodsTypePic;
          this.classificationForm.pGoodsTypeId = body.pGoodsTypeId;
          this.classificationForm.goodsNum = body.goodsNum;
          this.classificationForm.goodsTypeName = body.goodsTypeName;
          this.classificationForm.pGoodsTypeName = body.pGoodsTypeName;
          this.classificationForm.status = body.status;
          this.classificationForm.sort = body.sort;
          this.classificationForm.fileUrl = body.goodsTypePic;
          this.classificationForm.goodsTypePicFile.fileUrl = body.goodsTypePic;
          this.classificationForm.pGoodsTypeId = body.pGoodsTypeId;
          this.classificationForm.pGoodsTypeId = body.pGoodsTypeId;
        }
      });
      this.adVisible = true;
    },
    submitClassification() {
      this.$refs['classificationForm'].validate((valid) => {
        if (valid) {
          let apiKey = 'addClassification';
          const formData = JSON.parse(JSON.stringify(this.classificationForm));
          delete formData.fileUrl;
          const data = {
            ...formData
          };
          if (this.currentEditGoodsTypeId) {
            apiKey = 'editClassification';
            data.goodsTypeId = this.currentEditGoodsTypeId;
            if (this.originUrl === this.classificationForm.fileUrl) {
              formData.goodsTypePicFile.isAdd = 0;
            }
          }
          this.$post(apiKey, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.adVisible = false;
              this.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    loadMore() {
      this.selectPage += 1;
      this.getClassification();
    },
    async getClassification() {
      const data = {
        start: this.selectPage * 7,
        length: 7,
        goodsTypeLevel: '1',
        goodsTypeName: ''
      };
      const { fail, body } = await this.$post('getSelectCfList', data);
      if (!fail) {
        if (body.data.length > 0) {
          const ops = this.classification.concat(body.data);
          this.classification = [].concat(ops);
        } else {
          this.selectPage -= 1;
        }
      }
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getClassificationList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    handleAdd() {
      this.currentEditGoodsTypeId = null;
      this.adTitle = '新增';
      this.adVisible = true;
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
      this.$refs['searchForm'].resetFields();
      this.classification = [];
    },
    childClose() {
      this.adVisible = false;
      this.fileList = [];
      this.$refs['classificationForm'].resetFields();
    },
    handleRemoveImg({ file, fileList }) {
      this.classificationForm.goodsTypePicFile.fileUrl = '';
      this.classificationForm.fileUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.classificationForm.fileUrl = response;
      this.classificationForm.goodsTypePicFile.fileUrl = response;
    }
  }
};
</script>
<style lang="scss" scoped>
.main{
  padding:20px;
  overflow: auto;
  .yz-table-btnbox{
    margin-top:16px;
  }
}
.children-main{
  padding:20px;
}
.hide{
    ::v-deep .el-upload--picture-card{
      display: none;
    }
}
</style>
