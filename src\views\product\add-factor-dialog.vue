<template>
  <common-dialog
    :title="isEdit ? '编辑系数' : '添加系数'"
    width="1200px"
    :showFooter="true"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-row :gutter="20" type="flex" align="middle">
        <el-col :span="2">*选择商品</el-col>
        <el-col :span="6">
          <infinite-selects
            ref="selectGoodsRef"
            v-model="selectGoods"
            placeholder='请选择'
            apiKey="findGoodsSelectList"
            keyName="goodsShelfName"
            valueName='goodsShelfId'
            :param="goodsParams"
            :default-option="goodsDefaultOption"
            :disabled="isEdit"
            filterable
            remote
            :remote-method="remoteMethod"
            @change="selectChange"
          />
        </el-col>
      </el-row>
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" @click="addFactor">新增</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
        :row-class-name="tableRowClassName"
      >
        <el-table-column prop="audit" label="个人系数" align="center" />
        <el-table-column prop="groupTeamAudit" label="助学主管团队系数" align="center" />
        <el-table-column prop="dpTeamAudit" label="助学校长团队系数" align="center" />
        <el-table-column prop="campusTeamAudit" label="大区团队系数" align="center" />
        <el-table-column prop="campusSubordinateTeamAudit" label="大区校长所辖助学校长系数" align="center" />
        <el-table-column prop="startTime" label="生效时间" align="center" :width="300">
          <template slot-scope="scope">
            {{ scope.row.startTime + ' ~ ' + scope.row.endTime }}
          </template>
        </el-table-column>
        <el-table-column prop="createUserName" label="创建人" align="center" />
        <el-table-column prop="status" label="是否启用" align="center">
          <template slot-scope="scope">
            {{ scope.row.status == '0' ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column prop="isRisingStar" label="是否爆单" align="center">
          <template slot-scope="scope">
            {{ scope.row.isRisingStar == '0' ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column prop="isRanking" label="是否排行" align="center">
          <template slot-scope="scope">
            {{ scope.row.isRanking == '0' ? '否' : '是' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" prop="enable" width="100">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="edit(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <add-edit :visible.sync="addFactorVisible" :row="currentEditRow" @saveData="saveData" />
    </div>
  </common-dialog>
</template>

<script>
import addEdit from './add-edit';
export default {
  components: {
    addEdit
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      addFactorVisible: false,
      currentEditRow: {},
      tableLoading: false,
      tableData: [],
      show: false,
      isEdit: false,
      selectGoods: '',
      goodsParams: {},
      goodsDefaultOption: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    getTableList() {
      this.tableLoading = true;
      const { goodsShelfId, goodsShelfName } = this.rowData;
      const params = {
        goodsShelfId: goodsShelfId
      };
      this.$post('queryByGoodsShelfId', params).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          if (body) {
            this.tableData = body.auditList;
            this.selectGoods = goodsShelfId;
            this.selectGoodsData = { goodsShelfId, goodsShelfName };
          } else {
            this.tableData = [];
          }
        }
      });
    },
    open() {
      if (this.rowData) {
        this.goodsParams = {
          goodsShelfName: this.rowData.goodsShelfName
        };
        this.$nextTick(() => {
          this.isEdit = true;
          this.getTableList();
          this.$refs['selectGoodsRef'].resetGetOptions();
        });
      }
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    addFactor() {
      if (!this.selectGoods) {
        this.$message.warning('请选择商品!');
        return !1;
      }
      this.currentEditRow = null;
      this.addFactorVisible = true;
    },
    tableRowClassName({ row, rowIndex }) {
      row.rowIndex = rowIndex;
    },
    edit(row) {
      this.currentEditRow = row;
      this.addFactorVisible = true;
    },
    saveData(v) {
      if (this.currentEditRow) {
        this.$set(this.tableData, this.currentEditRow.rowIndex, v);
      } else {
        this.tableData.unshift(v);
      }
    },
    selectChange() {
      const data = this.$refs['selectGoodsRef'].afterFilterOptions;
      this.selectGoodsData = data.find(item => item.goodsShelfId === this.selectGoods);
    },
    remoteMethod(val) {
      this.goodsParams = {
        goodsShelfName: val
      };
      this.$nextTick(() => {
        this.$refs['selectGoodsRef'].resetGetOptions();
      });
    },
    submit() {
      if (this.tableData.length === 0) {
        return !1;
      }
      const { goodsShelfId, goodsShelfName } = this.selectGoodsData;
      const data = {
        goodsShelfId: goodsShelfId,
        goodsShelfName: goodsShelfName,
        auditList: this.tableData
      };
      if (this.isEdit) {
        data.id = this.rowData.id;
      }
      this.$post('saveOrUpdate', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.close();
          this.$parent.getTableList();
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
</style>
