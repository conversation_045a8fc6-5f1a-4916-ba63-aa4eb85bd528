<template>
  <infinite-select
    v-model="val"
    placeholder="请选择课程名称"
    :options='ops'
    :disabled='disabled'
    valName='courseId'
    label='courseName'
    @change='change'
    @loadMore='loadMore'
    @search='search'
  />
</template>

<script>
import infiniteSelect from './infinite-select';

export default {
  name: 'CourseSelect',
  components: {
    infiniteSelect
  },

  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      page: 1,
      size: 10,
      ops: [],
      val: '',
      name: '',
      isAll: false,
      loading: false
    };
  },
  watch: {
    value(v) {
      this.val = v;
    }
  },
  mounted() {
    this.val = this.value;
    this.findAllCourse();
  },
  methods: {
    change() {
      this.$emit('input', this.val);
    },
    search(val) {
      this.name = val;
      this.page = 1;
      this.findAllCourse();
    },
    loadMore() {
      if (!this.loading && !this.isAll) {
        this.findAllCourse();
      }
    },
    async findAllCourse() {
      this.loading = true;
      const { fail, body } = await this.$post('courseSelect', {
        start: (this.page - 1) * this.size,
        length: this.size
        // isAllow: 1,
        // courseName: this.name
      });
      setTimeout(() => {
        this.loading = false;
      }, 300);
      if (!fail) {
        const ops = this.page === 1 ? body.data : this.ops.concat(body.data);
        this.ops = [].concat(ops);
        this.isAll = !body || body.data.length === 0;
        this.$emit('getAllOps', this.ops);
        if (this.page === 1 && body.data.length === 0) {
          this.$message.warning('暂无数据');
        }
        if (!this.isAll) {
          this.page += 1;
        }
      }
    }
  }
};
</script>
