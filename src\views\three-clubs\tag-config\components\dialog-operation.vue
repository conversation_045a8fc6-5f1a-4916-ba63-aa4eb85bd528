<template>
  <div>
    <common-dialog
      show-footer
      width="500px"
      :title="row ? '编辑' : '新增'"
      :visible.sync="show"
      confirmText="保存"
      @open="open"
      @confirm="submit"
      @close="close"
    >
      <div v-loading="loading" class="dialog-main">
        <el-form ref="form" class="form" size="mini" :model="form" label-width="100px" :rules="rules">

          <el-form-item label='内容大类' prop='tagCategory'>
            <el-select v-model="form.tagCategory" placeholder="请选择内容大类">
              <el-option v-for="item in tagOpts" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>

          <el-form-item label='内容分类' prop='tagName'>
            <el-input v-model="form.tagName" placeholder="请输入内容分类" maxlength="4" show-word-limit />
          </el-form-item>

          <el-form-item label='状态' prop='status'>
            <el-radio-group v-model="form.status">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </common-dialog>
  </div>
</template>

<script>
import { TagCategory } from '@/dict';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      tagOpts: TagCategory,
      form: {
        tagCategory: '',
        tagName: '',
        status: 1
      },
      rules: {
        tagCategory: [{ required: true, message: '请选择内容大类', trigger: 'change' }],
        tagName: [
          { required: true, message: '请输入内容分类', trigger: 'blur' },
          { max: 4, message: '长度不能超过 4 个字符', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态' }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    async open() {
      if (this.row) {
        this.form = JSON.parse(JSON.stringify({ ...this.row }));
      }
    },
    submit() {
      if (this.loading) return;
      this.$refs['form'].validate(async(valid) => {
        if (valid) {
          try {
            this.loading = true;
            const params = {
              id: this.row?.id,
              tagCategory: this.form.tagCategory,
              tagName: this.form.tagName,
              status: this.form.status
            };
            const url = this.row ? '/businessTagCategoryConfig/updateById' : '/businessTagCategoryConfig/add';
            const { code } = await this.$http({
              method: 'post',
              url,
              data: params,
              json: true
            });
            if (code !== '00') return;
            this.show = false;
            this.$emit('confirm');
            this.$message.success('保存成功');
            this.close();
          } finally {
            this.loading = false;
          }
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
    }
  }
};
</script>
