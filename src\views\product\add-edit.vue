<template>
  <common-dialog
    :show-footer="true"
    width="700px"
    :title="isEdit ? '编辑系数值' : '新增系数值'"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="190px"
        :rules="rules"
      >
        <el-form-item label='个人系数' prop='audit'>
          <el-input
            v-model="form.audit"
          />
        </el-form-item>
        <el-form-item label='助学主管团队系数' prop='groupTeamAudit'>
          <el-input
            v-model="form.groupTeamAudit"
          />
        </el-form-item>
        <el-form-item label='助学校长团队系数' prop='dpTeamAudit'>
          <el-input
            v-model="form.dpTeamAudit"
          />
        </el-form-item>
        <el-form-item label='大区团队系数' prop='campusTeamAudit'>
          <el-input
            v-model="form.campusTeamAudit"
          />
        </el-form-item>
        <el-form-item label='大区校长所辖助学校长系数' prop='campusSubordinateTeamAudit'>
          <el-input
            v-model="form.campusSubordinateTeamAudit"
          />
        </el-form-item>
        <el-form-item label='生效时间' prop='validTime'>
          <el-date-picker
            v-model="form.validTime"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <el-form-item label='是否启用' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label='是否爆单' prop='isRisingStar'>
          <el-radio-group v-model="form.isRisingStar">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label='是否排行' prop='isRanking'>
          <el-radio-group v-model="form.isRanking">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
  </common-dialog>
</template>

<script>
import { handleDateControl } from '@/utils';
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      isEdit: false,
      currentEditRow: {},
      form: {
        audit: '',
        groupTeamAudit: '',
        dpTeamAudit: '',
        campusTeamAudit: '',
        campusSubordinateTeamAudit: '',
        validTime: '',
        status: 1,
        isRisingStar: '1',
        isRanking: '1'
      },
      rules: {
        audit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        groupTeamAudit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        dpTeamAudit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        campusTeamAudit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        campusSubordinateTeamAudit: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        validTime: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    open() {
      if (this.row) {
        this.row.isRanking = String(this.row.isRanking);
        const v = JSON.parse(JSON.stringify(this.row));
        this.form = v;
        this.form.validTime = [v.startTime, v.endTime];
        this.isEdit = true;
      }
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const formData = JSON.parse(JSON.stringify(this.form));
          const validTime = handleDateControl(formData.validTime);
          formData.startTime = validTime[0];
          formData.endTime = validTime[1];
          delete formData.validTime;
          const params = {
            ...formData
          };
          this.show = false;
          this.$emit('saveData', params);
        }
      });
    },
    close() {
      // this.$refs['form'].resetFields();
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.el-select{
  width: 48%;
  margin-right: 2%;
}
.el-select:nth-child(2){
  margin-right: 0px;
}
.commission .el-input--mini{
  width: 49%;
  margin-left: 10px;
}
.commission{
  margin-bottom: 10px;
}
.time-box{
  margin-left: 31%;
}
.timeIpt{
  width: 15%;
  height: 20px;
  margin: 0 10px;
}
</style>
