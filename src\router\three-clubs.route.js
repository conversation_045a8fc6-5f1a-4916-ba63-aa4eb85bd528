import Layout from '@/layout';

// app 圈子改版 三大社团管理 相关路由配置
export default [
  {
    path: '/three-clubs',
    component: Layout,
    meta: {
      title: '三大社团管理',
      icon: 'el-icon-setting',
      breadcrumb: false
    },
    children: [
      {
        path: 'tab-config',
        component: () => import('@/views/three-clubs/tab-config/index'),
        meta: {
          title: '社团管理'
        }
      },
      {
        path: 'clubs-config',
        component: () => import('@/views/three-clubs/clubs-config/index'),
        meta: {
          title: '社团配置'
        }
      },
      {
        path: 'commonweal-user-config',
        component: () =>
          import('@/views/three-clubs/commonweal-expert-config/index'),
        meta: {
          title: '公益人员管理'
        }
      },
      {
        path: 'tag-config',
        component: () => import('@/views/three-clubs/tag-config/index'),
        meta: {
          title: '内容归类'
        }
      },
      {
        path: 'interest-sphere-config',
        component: () =>
          import('@/views/three-clubs/interest-sphere-config/index'),
        meta: {
          title: '兴趣圈管理'
        }
      },
      {
        path: 'tutor-a-topic-config',
        component: () =>
          import('@/views/three-clubs/tutor-a-topic-config/index'),
        meta: {
          title: '导师 & 热议配置'
        }
      },
      {
        path: 'community-approval-list',
        component: () => import('@/views/three-clubs/community-approval/index'),
        meta: {
          title: '社团审批列表'
        }
      },
      {
        path: 'expert-management-list',
        component: () => import('@/views/three-clubs/expert-management/index'),
        meta: {
          title: '达人管理及审批'
        }
      }
    ]
  }
];
