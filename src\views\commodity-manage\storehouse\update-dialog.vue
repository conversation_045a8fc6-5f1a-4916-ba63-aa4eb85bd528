<template>
  <common-dialog
    show-footer
    :title="title"
    is-full
    width="95%"
    :visible.sync='show'
    @open="init"
    @confirm="submit"
    @close='close'
  >
    <div v-if="show" class="main">
      <el-form
        ref="form"
        class="form"
        size='mini'
        :model="form"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label='商品一级分类' prop='goodsClass'>
          <infinite-selects
            ref="classification"
            v-model="form.goodsClass"
            api-key="getSelectCfList"
            key-name="goodsTypeName"
            value-name='goodsTypeId'
            :default-option="goodsClassOption"
            :param="{
              goodsTypeLevel: '1',
              goodsTypeName: ''
            }"
            @change="getName"
          />
        </el-form-item>

        <!-- [未]当选择了课程商品后，出现二级分类，其他商品暂不出现，二级分类取分类库 -->
        <el-form-item label='二级分类' prop='goodsTypeId'>
          <infinite-selects
            ref="secendSelect"
            v-model="form.goodsTypeId"
            :default-option="level2Option"
            api-key="getSelectCfList"
            key-name="goodsTypeName"
            value-name='goodsTypeId'
            :param="secendSelectParms"
          />
        </el-form-item>

        <el-form-item label='商品名称' prop='goodsName'>
          <el-input
            v-model="form.goodsName"
            placeholder="请输入"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='供应渠道' prop='supplyChannel'>
          <infinite-selects
            v-model="form.supplyChannel"
            api-key="getSupplierSelects"
            :default-option="supplyChannelOption"
            key-name="channelName"
            value-name='channelId'
            :param="{sName:''}"
            @change="updateCourseSelect"
          />
        </el-form-item>

        <el-form-item v-if="form.goodsClass !== 5" label='关联课程' prop='mappingId'>
          <remote-search-selects
            ref="courseSelect"
            key="courseSelect"
            v-model="form.mappingId"
            :disabled="form.supplyChannel=='' || form.supplyChannel== null "
            :props="{apiName: 'courseSelect', value: 'courseId', label: 'courseName', query: 'courseName'}"
            :param="courseSelectParams"
            :default-option="courseDefaultOption"
            @changeVal="setMappingName"
          />
        </el-form-item>

        <el-form-item v-if="form.goodsClass === 5" label='关联课程' prop='mappingId'>
          <remote-search-selects
            ref="trainSelect"
            key="TrainSelect"
            v-model="form.mappingId"
            :props="{apiName: 'getTrainSelectCfList', value: 'trailingId', label: 'courseName', query: 'courseName'}"
            :param="trainSelectParams"
            :default-option="trainCampOption"
            @changeVal="setMappingName"
          />
        </el-form-item>

        <el-form-item label='结算价' prop='settlementPrice'>
          <input-number v-model="form.settlementPrice" :length='10' />
        </el-form-item>

        <el-form-item label='列表页简介' prop='goodsBaseIntroduce'>
          <el-input
            v-model="form.goodsBaseIntroduce"
            type="textarea"
            placeholder="请输入"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label='列表图片（正方形图）' prop='listImgUrl'>
          <upload-file
            :max-limit="1"
            :file-list='listImgs'
            @remove="handleRemoveImg"
            @success="uploadSuccess"
          />
        </el-form-item>

        <el-form-item label='详细轮播图' prop='carousel'>
          <upload-file
            :max-limit="4"
            :file-list='bannerFileList'
            @remove="bannerRemoveImg"
            @success="bannerUploadSuccess"
          />
        </el-form-item>

        <!-- <el-form-item label='课程介绍' prop='goodsIntroduce'>
          <div>
            <UE :max-word-count="4000" @ready="initUE" />
          </div>
        </el-form-item> -->

        <el-form-item label='课程介绍' prop='introduce'>
          <div>
            <upload-file
              :max-limit="1"
              :file-list='goodsIntroduceFiles'
              @remove="introduceRemoveImg"
              @success="introduceUploadSuccess"
            />
          </div>
        </el-form-item>

        <el-form-item label='是否可用' prop='status'>
          <el-radio-group v-model="form.status">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="2">否</el-radio>
          </el-radio-group>
        </el-form-item>

      </el-form>
    </div>
  </common-dialog>
</template>
<script>
import { ossUri } from '@/config/request';
import { splitChar } from '@/utils';
export default {
  // components: {
  //   UE: resolve => require(['@/components/UE'], resolve)
  // },
  props: {
    title: {
      type: String,
      default: '新增'
    },
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    const validateSpace = (rule, value, callback) => {
      if (value.trim() === '') {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    return {
      courseSelectParams: {
        courseName: '',
        courseSupplyChannelId: ''
      },
      secendSelectParms: {
        goodsTypeLevel: '2',
        // goodsTypeName: '',
        pGoodsTypeId: '',
        status: '1'
      },
      trainSelectParams: {
        courseType: 'PU_TRAIN',
        courseName: ''
      },
      courseDefaultOption: null,
      trainCampOption: null,
      goodsClassOption: null,
      supplyChannelOption: null,
      level2Option: null,
      UE: null,
      listImgs: [],
      bannerFileList: [],
      goodsIntroduceFiles: [], // 课程介绍
      withoutNetworkPath: [],
      show: false,
      rules: {
        goodsClass: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsTypeId: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        goodsName: [
          { required: true, validator: validateSpace, message: '请输入', trigger: 'blur' }
        ],
        listImgUrl: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        goodsBaseIntroduce: [
          { required: true, validator: validateSpace, trigger: 'blur' }
        ],
        // goodsIntroduce: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        introduce: [
          { required: true, message: '请上传', trigger: 'blur' }
        ],
        supplyChannel: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        settlementPrice: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        carousel: [
          { required: true, message: '请上传图片', trigger: 'change' }
        ],
        mappingId: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      form: {
        goodsClass: '', // 默认选择课程
        goodsClassName: '',
        goodsName: '',
        goodsTypeId: '',
        goodsTypeName: '',
        goodsBaseIntroduce: '',
        mappingName: '',
        // goodsPicFile: '',
        // goodsBannerFileList: '',
        // goodsIntroduce: '',
        introduce: '',
        goodsIntroducePic: '',
        settlementPrice: '',
        supplyChannel: '',
        status: 1,
        purchaseNum: '',
        mappingId: '',
        listImgUrl: '',
        // 列表图片
        goodsPicFile: {
          fileUrl: '',
          isAdd: 1
        },
        // 课程介绍图
        goodsIntroduceFile: {
          fileUrl: '',
          isAdd: 0
        },
        // 轮播图片
        goodsBannerFileList: [],
        carousel: null
      },
      originData: null
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
    // 'form.goodsClass'(val) {
    //   if (val !== 5) {

    //   }
    // }
  },
  beforeDestroy() {
    window.onscroll = null;
  },
  methods: {
    // 更新关联课程下拉
    updateCourseSelect(value) {
      console.log('执行了');
      this.form.mappingId = '';
      this.courseSelectParams.courseSupplyChannelId = value;

      if (this.form.goodsClass !== 5) {
        this.$refs.courseSelect.resetGetOptions();
      }
    },
    introduceRemoveImg({ file, fileList }) {
      this.form.goodsIntroduceFile.fileUrl = '';
      this.form.introduce = '';
    },
    introduceUploadSuccess({ response, file, fileList }) {
      this.form.goodsIntroduceFile.fileUrl = response;
      this.form.introduce = response;
      this.form.goodsIntroduceFile.isAdd = 1;
    },
    setMappingName({ value, label, source }) {
      this.form.mappingName = label;
      // console.log(value, label, source);
    },
    reset() {
      this.$nextTick(() => {
        this.$refs['form'].resetFields();
      });
    },
    init() {
      if (this.id) {
        this.$post('getCommodityInfo', { goodsBaseId: this.id }).then(res => {
          const { fail, body } = res;
          if (!fail) {
            // 回显分页下拉组件
            this.goodsClassOption = {
              goodsTypeName: body.goodsClassName,
              goodsTypeId: body.goodsClass
            };
            this.supplyChannelOption = {
              channelName: body.channelName,
              channelId: body.supplyChannel
            };
            this.level2Option = {
              goodsTypeName: body.goodsTypeName,
              goodsTypeId: body.goodsTypeId
            };
            if (body.goodsClass === 5) {
              this.trainCampOption = {
                trailingId: body.mappingId,
                courseName: body.mappingName
              };
            } else {
              this.courseDefaultOption = {
                courseName: body.mappingName,
                courseId: body.mappingId
              };
            }

            // console.log(this.courseDefaultOption);
            // console.log(this.trainCampOption);
            // this.courseDefaultOption = {
            //   courseName: body.mappingName,
            //   courseId: body.mappingId
            // };

            const bannerList = splitChar(body.goodsBannerList);
            this.originData = body;
            this.originData.bannerList = bannerList;
            this.form.goodsName = body.goodsName;
            this.form.goodsClass = body.goodsClass;
            this.form.goodsClassName = body.goodsClassName;
            this.form.goodsTypeId = body.goodsTypeId;
            this.form.goodsTypeName = body.goodsTypeName;
            this.form.goodsBaseIntroduce = body.goodsBaseIntroduce;
            // this.form.goodsIntroduce = body.goodsIntroduce;
            this.form.settlementPrice = body.settlementPrice;
            this.form.supplyChannel = body.supplyChannel;
            this.form.status = body.status;
            this.form.purchaseNum = body.purchaseNum;
            this.form.mappingId = body.mappingId;
            this.form.listImgUrl = body.goodsPic;
            this.form.mappingName = body.mappingName;
            this.form.carousel = true;

            bannerList.forEach(url => {
              this.withoutNetworkPath.push({ fileUrl: url, isAdd: 0 });
              this.bannerFileList.push({ url: ossUri + url });
            });

            this.listImgs.push({
              url: ossUri + body.goodsPic
            });

            this.goodsIntroduceFiles.push({
              url: ossUri + body.goodsIntroducePic
            });
            this.form.goodsIntroduceFile.fileUrl = body.goodsIntroducePic;
            this.form.introduce = body.goodsIntroducePic;

            // setTimeout(() => {
            // this.UE.setContent(this.form.goodsIntroduce);
            // }, 2000);
            // this.secendSelectParms.goodsTypeName = body.goodsClassName;

            console.log(body.supplyChannel, 'body.supplyChannel');
            if (body.goodsClass === 5) {
              // this.courseSelectParams.courseSupplyChannelId = body.supplyChannel;
              console.log(this.trainSelectParams, 'this.trainSelectParams');
              setTimeout(() => {
                // this.$refs.trainSelect._clear();
              }, 200);
            }
            console.log(this.form.supplyChannel, 'this.form.supplyChannel');
            setTimeout(() => {
              this.$refs.secendSelect.resetGetOptions();
            }, 500);
          }
        });
      }
    },
    initUE(instance) {
      this.init();
      this.UE = instance;
    },
    getName(val) {
      const data = this.$refs['classification'].afterFilterOptions;
      const obj = data.find(item => item.goodsTypeId === val);
      this.form.goodsClassName = obj.goodsTypeName;
      this.form.mappingId = '';
      console.log(obj);
      console.log(this.form.goodsClass);
      // this.secendSelectParms.goodsTypeName = this.form.goodsClassName;
      this.secendSelectParms.pGoodsTypeId = obj.goodsTypeId;
      this.form.goodsTypeId = '';
      this.$refs.secendSelect.resetGetOptions();
    },
    submit() {
      // this.form.goodsIntroduce = this.UE.getContent();
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let apikey = 'addCommodity';
          const formData = JSON.parse(JSON.stringify(this.form));
          const data = {
            ...formData
          };
          delete data.carousel;
          data.goodsPicFile.fileUrl = this.form.listImgUrl;
          data.goodsBannerFileList = this.withoutNetworkPath;

          if (this.id) {
            apikey = 'editCommodity';
            data.goodsBaseId = this.id;
            // 如果图片没改 isAdd为 0
            if (this.originData.goodsPic === this.form.listImgUrl) {
              data.goodsPicFile.isAdd = 0;
            }
          }

          this.$post(apikey, data, {
            headers: {
              'Content-Type': 'application/json'
            }
          }).then(res => {
            const { fail } = res;
            if (!fail) {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.show = false;
              this.$parent.getTableList();
            }
          });
        } else {
          return false;
        }
      });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    },
    handleRemoveImg({ file, fileList }) {
      this.form.listImgUrl = '';
    },
    uploadSuccess({ response, file, fileList }) {
      this.form.listImgUrl = response;
    },
    bannerRemoveImg({ file, fileList }) {
      this.bannerFileList = fileList;
      if (this.bannerFileList.length <= 0) {
        this.form.carousel = null;
      }
      let url = '';
      if (file.response) {
        url = file.response;
      } else {
        url = file.url;
      }
      let index = 0;
      this.withoutNetworkPath.find((item, i) => {
        if (item.fileUrl === url) {
          index = i;
        }
      });
      this.withoutNetworkPath.splice(index, 1);
    },
    bannerUploadSuccess({ response, file, fileList }) {
      this.form.carousel = true;
      this.bannerFileList = fileList;
      this.withoutNetworkPath.push({ fileUrl: response, isAdd: 1 });
    }
  }
};
</script>
<style lang="scss" scoped>
.main{
  padding:20px;
}
</style>
