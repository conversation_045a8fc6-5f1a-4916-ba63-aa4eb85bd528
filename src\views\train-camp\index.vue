<template>
  <div class='yz-base-container'>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='训练营名称' prop='trainingName'>
        <el-input v-model="form.trainingName" placeholder="请输入" />
      </el-form-item>

      <el-form-item label='是否启用' prop='enable'>
        <el-select v-model="form.enable" clearable>
          <el-option label="启用" value="1" />
          <el-option label="禁用" value="2" />
        </el-select>
      </el-form-item>

      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>

    </el-form>

    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="success" size="small" @click="addCamp">新增训练营</el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="trainingName" label="训练营名称" align="center" />
      <el-table-column label="课时管理" align="center" prop="tradeType">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="showCourseHour(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="关卡设置" align="center" prop="tradeType">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="changeLevel(scope.row)">设置</el-button>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="训练周期" align="center" prop="trainingDay">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <span v-if="scope.row.trainingDay">{{ scope.row.trainingDay }}天</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="学习人数" align="center" prop="trainTotal">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="studyCount(scope.row)">{{ scope.row.trainTotal }}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="trainSuccessTotal" label="挑战成功人数" align="center" />
      <el-table-column prop="test" label="成功率" align="center">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button v-if="scope.row.trainSuccessTotal && scope.row.trainTotal" type="text" @click="studyCount(scope.row)">
              {{ (scope.row.trainSuccessTotal / scope.row.trainTotal*100).toFixed(2) }}%
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="自动退费详情" align="center" prop="tradeType">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="refundDetails(scope.row)">查看</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="enable" label="状态" align="center">
        <template slot-scope="scope">
          <el-tag :type="scope.row.enable === '1'? 'success':'danger'">
            {{ scope.row.enable === '1' ? '启用':'禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" prop="enable">
        <template slot-scope="scope">
          <div class="yz-button-area">
            <el-button type="text" @click="updateStatus(scope.row)">{{ scope.row.enable === '2'?'启用':'禁用' }}</el-button>
            <el-button type="text" @click="editCamp(scope.row)">编辑</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>

    <add-edit :visible.sync="aeVisible" :row="currentEditRow" />
    <!-- 课时管理 -->
    <course-hour :visible.sync="chVisible" :row="currentEditRow" />
    <!-- 关卡设置 -->
    <level-dialog :visible.sync="ldVisible" :row="currentEditRow" />
    <!-- 学习人数 -->
    <study-dialog :visible.sync="studyVisible" :row="currentEditRow" />
    <!-- 退费详情 -->
    <refund-details :visible.sync="rdVisible" :row="currentEditRow" />

  </div>
</template>
<script>
import addEdit from './add-edit';
import courseHour from './course-hour';
import levelDialog from './components/level-dialog';
import studyDialog from './components/study-dialog';
import refundDetails from './components/refund-details';
export default {
  components: {
    addEdit,
    courseHour,
    levelDialog,
    studyDialog,
    refundDetails
  },
  data() {
    return {
      tableLoading: false,
      aeVisible: false,
      chVisible: false,
      ldVisible: false,
      studyVisible: false,
      rdVisible: false,
      currentEditRow: null,
      currdirectoryId: null,
      form: {
        enable: '',
        trainingName: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getTrainCampList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    // 新增训练营
    addCamp() {
      this.currentEditRow = null;
      this.aeVisible = true;
    },
    editCamp(row) {
      this.currentEditRow = row;
      this.aeVisible = true;
    },
    // 课时管理
    showCourseHour(row) {
      this.currentEditRow = row;
      this.chVisible = true;
    },
    // 关卡设置
    changeLevel(row) {
      this.currentEditRow = row;
      this.ldVisible = true;
    },
    // 学习人数
    studyCount(row) {
      this.currentEditRow = row;
      this.studyVisible = true;
    },
    refundDetails(row) {
      this.currentEditRow = row;
      this.rdVisible = true;
    },
    // 启用禁用
    updateStatus(row) {
      const params = {
        enable: row.enable === '1' ? '2' : '1',
        trainingId: row.trainingId
      };
      this.$post('trainCampStatus', params, { json: true })
        .then(res => {
          const { fail } = res;
          if (!fail) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.getTableList();
          }
        });
    }

  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
.yz-search-form{
  margin-bottom: 20px;
}
</style>
