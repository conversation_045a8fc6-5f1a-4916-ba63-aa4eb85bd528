<template>
  <el-radio-group v-model="value" :disabled="disabled" class="vertical-radio-group">
    <div v-for="(item, index) in gradeOptions" :key="index" class="radio-item">
      <el-radio :label="item.value">{{ item.label }}</el-radio>
    </div>
  </el-radio-group>
</template>

<script>
export default {
  name: 'SjGradeMedal',
  props: {
    sjGradeType: {
      type: Number,
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      value: this.sjGradeType,
      gradeOptions: [
        { value: 1, label: 'V1初学乍到（0分~25000分）' },
        { value: 2, label: 'V2游学四方（25001分~50000分）' },
        { value: 3, label: 'V3学而有志（50001分~125000分）' },
        { value: 4, label: 'V4上进青年（125001分~225000分）' },
        { value: 5, label: 'V5上进青年（225001分~350000分）' },
        { value: 6, label: 'V6上进青年（350001分~500000分）' },
        { value: 7, label: 'V7上进青年（500001分~675000分）' },
        { value: 8, label: 'V8上进青年（675001分-875000分）' }
      ]
    };
  },
  watch: {
    sjGradeType(val) {
      this.value = val;
    },
    value(val) {
      this.$emit('update:sjGradeType', val);
    }
  },
  methods: {
    // 验证当前选择是否有效
    validate() {
      if (!this.value) {
        return { valid: false, message: '请选择获取条件' };
      }
      return { valid: true };
    }
  }
};
</script>

<style lang="scss" scoped>
.vertical-radio-group {
  display: flex;
  flex-direction: column;

  .radio-item {
    height: 32px;
    display: flex;
    align-items: center;

    .el-radio {
      margin-right: 10px;
    }
  }
}
</style>
