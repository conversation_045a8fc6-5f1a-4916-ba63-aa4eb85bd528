<template>
  <common-dialog
    title="官方运营人员管理"
    width="1000px"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='100px'
        @submit.native.prevent='search'
      >
        <el-form-item label='姓名' prop='name'>
          <el-input v-model="form.name" type="text" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item label='状态' prop='status'>
          <el-select
            v-model="form.status"
            clearable
            placeholder="请选择状态"
          >
            <el-option label="已启用" :value="1" />
            <el-option label="已禁用" :value="0" />
          </el-select>
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>
      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <span class="title">凡是添加到此名单中的助学老师，佣金将不再计算。</span>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">添加</el-button>
      </div>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="empName" label="姓名" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.status === 0 ? '已禁用': '已启用' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUser" label="编辑" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 0 " size="mini" type="primary" plain @click="handleStatus(scope.row.empId, 1)">启用</el-button>
            <el-button v-else size="mini" type="danger" plain @click="handleStatus(scope.row.empId, 0)">禁用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
      <add-manager-dialog :visible.sync="addManagerVisible" @getTableList="getTableList" />
    </div>
  </common-dialog>
</template>

<script>
import addManagerDialog from './add-manager-dialog';
export default {
  components: {
    addManagerDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      addManagerVisible: false,
      form: {
        name: '',
        status: ''
      },
      tableLoading: false,
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    handleAdd() {
      this.addManagerVisible = true;
    },
    handleSubmit(empId, status) {
      const params = { empId: empId, status };
      this.$post('updateOperateUserStatus', params, { json: true }).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleStatus(empId, status) {
      if (status === 1) {
        this.handleSubmit(empId, status);
      } else {
        this.$confirm('是否禁用此用户？禁用后产生的分销订单将计算佣金。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleSubmit(empId, status);
        });
      }
    },
    getSerQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      return {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
    },
    getTableList() {
      this.tableLoading = true;
      const params = this.getSerQueryParams();
      this.$post('getOperateUserList', params, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          if (body) {
            this.tableData = body.data;
            this.pagination.total = body.recordsTotal;
          } else {
            this.tableData = [];
          }
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    open() {
      this.getTableList();
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.yz-table-btnbox {
  margin-top: 20px;
  position: relative;
  .title {
    position: absolute;
    left: 0;
    color: red;
  }
}

.material-text {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
