<template>
  <common-dialog
    :show-footer="false"
    is-full
    width="600px"
    title="数据统计"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <div class="header">
        <h3>{{ title }}</h3>
        <span class="title">统计时间</span>
        <!-- <el-select v-model="recently" filterable placeholder="请选择">
          <el-option label="最近30天" value="1" />
          <el-option label="最近15天" value="2" />
          <el-option label="最近7天" value="3" />
        </el-select> -->
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          value-format="yyyy-MM-dd"
          start-placeholder="开始日期"
          end-placeholder="结束日期"

          :picker-options="pickerOptions"
          @change='changeTimeRange'
        />
        <!--  :clearable='false' -->
        <div class="statis-box">
          <div class="item" :class="statisType === 1 ?'statisType': ''" @click="changStatis(1,'领取量')">
            <p>领取量 (张)</p>
            <span>{{ receivedNum || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 2 ?'statisType': ''" @click="changStatis(2,'使用量')">
            <p>使用量 (张)</p>
            <span>{{ usedNum || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 3 ?'statisType': ''" @click="changStatis(3,'用券金额')">
            <p>用券金额 (元)</p>
            <span>{{ couponAmount/100 || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 4 ?'statisType': ''" @click="changStatis(4,'支付订单数')">
            <p>支付订单数</p>
            <span>{{ orderCount || 0 }}</span>
          </div>
          <div class="item" :class="statisType === 5 ?'statisType': ''" @click="changStatis(5,'订单总金额')">
            <p>订单总金额(元)</p>
            <span>{{ orderAmount/100 || 0 }}</span>
          </div>

        </div>
      </div>
      <h3 class="tableTitle">{{ tableTitle }}  趋势图</h3>
      <div ref='chart' class="chart" />
      <h3>具体数据</h3>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="updateUserName" label="日期" align="center">
          <template slot-scope="scope">
            {{ scope.row.day | changeLimitTime }}
          </template>
        </el-table-column>
        <el-table-column prop="receivedNum" label="领取量" align="center" />
        <el-table-column prop="usedNum" label="使用量" align="center" />
        <el-table-column prop="couponAmount" label="用券金额" align="center">
          <template slot-scope="scope">
            {{ scope.row.couponAmount/100 }}
          </template>
        </el-table-column>
        <el-table-column prop="orderCount" label="支付订单数" align="center" />
        <el-table-column prop="orderAmount" label="订单总金额" align="center">
          <template slot-scope="scope">
            {{ scope.row.orderAmount/100 }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
var echarts = require('echarts');
import { formatTimeStamp } from '@/utils';
export default {
  components: {},
  filters: {
    changeLimitTime(val) {
      return formatTimeStamp(val, 'YYYY-MM-DD');
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
      option: {}
    },
    id: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      recently: '1',
      statisType: 1,
      // 默认最近7天
      timeRange: [formatTimeStamp(new Date().getTime() - 3600 * 1000 * 24 * 7, 'YYYY-MM-DD'), formatTimeStamp(new Date().getTime(), 'YYYY-MM-DD')],
      tableData: [],
      xAxisData: [], // 图表x轴数据
      seriesData: [], // 表内数据
      tableTitle: '领取量',
      // tableTitleDay: '近7天',
      receivedNum: '', // 领取数据
      usedNum: '', // 使用数量
      couponAmount: '', // 用券金额
      orderCount: '', // 支付订单数
      orderAmount: '', // 订单总金额

      receivedNumList: [], // 领取数据总计
      usedNumList: [], // 使用数量总计
      couponAmountList: [], // 用券金额总计
      orderCountList: [], // 支付订单数总计
      orderAmountList: [], // 订单总金额总计

      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近7天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime((start).getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近15天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近30天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }]
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},

  methods: {
    initChart() {
      const option = {
        // title: {
        //   text: this.tableTitle
        // },
        tooltip: {},
        xAxis: {
          splitNumber: 9,
          data: this.xAxisData
        },
        yAxis: {},
        series: [{
          name: '数量',
          type: 'line',
          data: this.seriesData,
          smooth: true
        }]
      };
      this.$nextTick(() => {
        // dom已存在，需销毁
        echarts.dispose(this.$refs.chart);
        const chart = echarts.init(this.$refs.chart);
        chart.setOption(option);
      });
    },
    open() {
      this.getTableList();
      this.initChart();
    },
    changStatis(type, title) {
      this.statisType = type;
      console.log(title);
      this.tableTitle = title;
      this.seriesData = type === 1 ? this.receivedNumList : this.seriesData;
      this.seriesData = type === 2 ? this.usedNumList : this.seriesData;
      this.seriesData = type === 3 ? this.couponAmountList : this.seriesData;
      this.seriesData = type === 4 ? this.orderCountList : this.seriesData;
      this.seriesData = type === 5 ? this.orderAmountList : this.seriesData;
      this.initChart();
    },
    changeTimeRange(val) {
      if (!val) {
        this.timeRange = [];
        return;
      }

      this.getTableList();
    },
    getTableList() {
      const startTime = new Date(this.timeRange[0]).getTime();
      const endTime = new Date(this.timeRange[1]).getTime();
      const data = {
        startTime: startTime,
        endTime: endTime,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      let summaryUrl = 'summaryCoupon'; // 总统计
      let listUrl = 'listCoupon'; // 列表图分页
      let allListUrl = 'listFigureCoupon'; // 总列表图
      if (this.id) {
        data['couponId'] = this.id;
        summaryUrl = 'summaryByCouponId';
        listUrl = 'listByCouponId';
        allListUrl = 'listFigureByCouponId';
      }
      this.$post(summaryUrl, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail && body) {
          this.receivedNum = body.receivedNum;
          this.usedNum = body.usedNum;
          this.couponAmount = body.couponAmount;
          this.orderCount = body.orderCount;
          this.orderAmount = body.orderAmount;
        }
      });
      this.$post(listUrl, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
      this.$post(allListUrl, data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.xAxisData = [];

          // 返回数据只有有数据的天数，x轴需要补充时间段内的天数
          for (let i = startTime; i <= endTime; i = i + 24 * 60 * 60 * 1000) {
            this.xAxisData.push(formatTimeStamp(i, 'YYYY-MM-DD'));
          }

          this.seriesData = [];
          this.receivedNumList = [];
          this.usedNumList = [];
          this.couponAmountList = [];
          this.orderCountList = [];
          this.orderAmountList = [];

          const groupData = {};
          // 若x轴没数据 则填充为0

          body.map(item => {
            const day = formatTimeStamp(item.day, 'YYYY-MM-DD');
            groupData[day] = item;
          });
          console.log('groupData');
          console.log(groupData);

          this.xAxisData.forEach(item => {
            const numData = groupData[item];
            if (numData) {
              this.receivedNumList.push(numData.receivedNum);
              this.usedNumList.push(numData.usedNum);
              this.couponAmountList.push(numData.couponAmount / 100);
              this.orderCountList.push(numData.orderCount);
              this.orderAmountList.push(numData.orderAmount / 100);
            } else {
              this.receivedNumList.push(0);
              this.usedNumList.push(0);
              this.couponAmountList.push(0);
              this.orderCountList.push(0);
              this.orderAmountList.push(0);
            }
          });

          if (this.statisType === 1) {
            this.seriesData = this.receivedNumList;
          } else if (this.statisType === 2) {
            this.seriesData = this.usedNumList;
          } else if (this.statisType === 3) {
            this.seriesData = this.couponAmountList;
          } else if (this.statisType === 4) {
            this.seriesData = this.orderCountList;
          } else if (this.statisType === 5) {
            this.seriesData = this.orderAmountList;
          }

          this.initChart();
          console.log(this.xAxisData);
          console.log(this.seriesData);
          // this.changStatis(1, '领取量');
        }
      });
    },
    submit() {
      this.getTableList();
    },
    close() {
      this.$emit('refresh-list', true);
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
  .tableTitle{
    margin: 0;
    font-size: 20px;
  }
  .chart {
    width: 800px;
    height: 400px;
  }
  .statisType{
    color: #478cb2;
  }
  .header{
    margin-bottom: 20px;
    .title{
      font-size: 16px;
    }
    .el-select{
      width: 150px;
      margin: 0 20px;
    }
    .el-input__inner{
      width: 500px;
      margin: 0 20px;
    }
    .statis-box{
      display: flex;
      margin-top: 30px;

      .item{
        width: 120px;
        height: 70px;
        text-align: center;
        margin-right: 20px;
        padding: 3px;
        cursor: pointer;
        P{
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 10px;
        }
        span{
          font-size: 18px;
          font-weight: 600;
        }
      }
      .item:nth-child(2){
        margin-right: 60px;
      }
    }
  }
  .header .el-input__inner{
    height: 32px;
    line-height: 32px;
  }
::v-deep .el-date-editor .el-range-separator{
    line-height: 25px;
  }
::v-deep .el-date-editor .el-range__icon{
  line-height: 25px;
}
::v-deep .el-date-editor .el-range__close-icon{
  line-height: 25px;
}

</style>
