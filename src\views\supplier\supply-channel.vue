<template>
  <common-dialog is-full title="供应渠道管理" :visible.sync='show' @open="init" @close='close'>
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='状态' prop='allow'>
          <el-select v-model="form.allow" placeholder="请选择">
            <el-option
              v-for="item in $localDict['status']"
              :key="item.dictValue"
              :label="item.dictName"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="供应渠道" prop="channelName">
          <el-input v-model="form.channelName" />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox'>
        <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新增</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        ref="table"
        v-loading="tableLoading"
        border
        size="small"
        height="calc(100vh - 225px)"
        :data="tableData"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
      >
        <el-table-column label="供应渠道" align="center" prop="channelName" />
        <el-table-column label="分成比例" align="center" prop="proportion">
          <template slot-scope="scope">
            {{ scope.row.proportion+'%' }}
          </template>
        </el-table-column>
        <el-table-column label="关联角色" align="center" prop="roleName" />
        <el-table-column label="最后操作人" align="center" prop="updateUserName" />
        <el-table-column label="操作时间" align="center" prop="updateTime" />
        <el-table-column label="状态" align="center" prop="allow">
          <template slot-scope="scope">
            <el-tag :type="scope.row.allow == 1 ? 'success' : 'danger'">
              {{ scope.row.allow | tansformStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150px">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="handleUpdateStatus(scope.row)">
                {{ scope.row.allow=== '1' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

      <!-- 弹窗 -->
      <update-channel-dialog
        :id="currentEditChannelId"
        :title="ucdTitle"
        :visible.sync="ucdVisible"
        @refresh="getTableList"
      />

    </div>
  </common-dialog>
</template>
<script>
import { pagination } from '@/config/constant';
import updateChannelDialog from './update-channel-dialog';
export default {
  components: {
    updateChannelDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      ucdVisible: false,
      currentEditChannelId: null,
      form: {
        allow: '',
        channelName: ''
      },
      ucdTitle: '添加渠道',
      tableData: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        ...pagination
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {
      this.getTableList();
    },
    handleUpdateStatus(row) {
      const data = {
        channelId: row.channelId,
        allow: row.allow === '1' ? '2' : '1'
      };
      this.$post('updateSupplierStatus', data).then(res => {
        const { fail } = res;
        if (!fail) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.getTableList();
        }
      });
    },
    handleEdit(row) {
      this.currentEditChannelId = row.channelId;
      this.ucdTitle = '编辑';
      this.ucdVisible = true;
    },
    handleAdd() {
      this.currentEditChannelId = null;
      this.ucdTitle = '新增';
      this.ucdVisible = true;
    },
    getTableList() {
      this.tableLoading = true;
      const data = {
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...this.form
      };
      this.$post('getSupplierList', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox{
  margin-top:20px;
}
</style>
