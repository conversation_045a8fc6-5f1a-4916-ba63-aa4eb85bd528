<template>
  <common-dialog
    is-full
    title="报名明细"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='学业编码' prop='learnId'>
          <el-input v-model="form.learnId" placeholder="请输入学业编码" />
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='学员姓名' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入学员姓名" />
        </el-form-item>

        <el-form-item label='学员手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入手机号码" />
        </el-form-item>

        <el-form-item label='状态' prop='enroStatus'>
          <el-select v-model="form.enroStatus" clearable placeholder="请选择">
            <el-option label="进行中" :value="0" />
            <el-option label="挑战成功" :value="1" />
            <el-option label="挑战失败" :value="2" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 按钮区 -->
      <div class='yz-table-btnbox' style="margin: 20px 0">
        <el-button type="success" size="small" icon="el-icon-upload2" @click="exportListExcel">导出表格</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        height="calc(100vh - 290px)"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >

        <el-table-column prop="semesterName" label="学员身份" align="center" />
        <el-table-column prop="readPlanName" label="读书计划" align="center" />
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="userName" label="学员姓名" align="center" />
        <el-table-column prop="enroTime" label="报名时间" align="center" />
        <el-table-column prop="planDays" label="目标打卡天数" align="center">
          <template slot-scope="scope">
            <el-link type="primary" @click="seeDetails(scope.row)">
              {{ scope.row.planDays }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="clockNum" label="已打卡天数" align="center" />
        <el-table-column prop="notClockNum" label="缺卡天数" align="center" />
        <el-table-column prop="notStartNum" label="未打卡天数" align="center" />
        <el-table-column prop="enroStatus" label="状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.enroStatus | enroStatus }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
    <learning :visible.sync="lVisible" :row="row" :title="title" />
  </common-dialog>
</template>
<script>
import learning from '../learning-progress/index';
import { exportExcel } from '@/utils';
export default {
  components: {
    learning
  },
  filters: {
    enroStatus(val) {
      console.log(val);
      // if (!val) return;
      const data = {
        '0': '进行中',
        '1': '挑战成功',
        '2': '挑战失败'
      };
      return data[val];
    },
    channel(val) {
      if (!val) return '';
      const data = {
        WECHAT: '微信',
        APP: 'APP'
      };
      return data[val];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      tableLoading: false,
      show: false,
      lVisible: false,
      form: {
        userName: '',
        mobile: '',
        enroStatus: '',
        learnId: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      },
      row: null,
      title: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    seeDetails(row) {
      this.title = row.userName + ' ' + row.readPlanName + '' + row.semesterName + ' ' + '打卡情况';
      this.row = row;
      this.lVisible = true;
    },
    exportListExcel() {
      const data = this.handleQueryParams();
      exportExcel('readExportList', data);
    },
    init() {
      this.getTableList();
    },
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        semesterId: this.userId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit,
        ...formData
      };
      return data;
    },

    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      console.log(data);
      this.$post('getSignUpInfo', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.pagination.total = body.recordsTotal;
          this.tableData = body.data;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    close() {
      this.$refs['searchForm'].resetFields();
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>
<style lang="scss" scoped>

</style>
