<template>
  <div>
    <!-- 表单 -->
    <el-form
      ref='searchForm'
      class='yz-search-form'
      size='mini'
      :model='form'
      label-width='120px'
      @submit.native.prevent='search'
    >
      <el-form-item label='助学老师' prop='empName'>
        <el-input v-model="form.empName" placeholder="请输入助学老师姓名" />
      </el-form-item>
      <div class="search-reset-box">
        <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
      </div>
    </el-form>
    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <span style="opacity: 0;">·</span>
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="empName" label="助学老师" align="center" />
      <el-table-column prop="annual" label="年度" align="center" />
      <el-table-column prop="empType" label="用户类型" align="center" />
      <el-table-column prop="roleType" label="角色" align="center" />
      <el-table-column prop="empStatus" label="是否在职" align="center" />
      <el-table-column prop="campusName" label="校区" align="center" />
      <el-table-column prop="dpName" label="部门" align="center" />
      <el-table-column prop="groupName" label="小组" align="center" />
      <el-table-column prop="income" label="职业教育累计绩效" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleKpiDetail(scope.row)">{{ scope.row.income }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <!-- 弹框区 -->
    <kpi-month-dialog :visible.sync="kpiMonthlVisible" :empId="empId" :empName="empName" :annual="annual" />
  </div>
</template>
<script>
import kpiMonthDialog from './kpi-month-dialog';

export default {
  components: {
    kpiMonthDialog
  },
  props: {
    orderParams: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableLoading: false,
      kpiMonthlVisible: false,
      empId: '',
      empName: '',
      annual: '',
      form: {
        empName: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    orderParams(val) {
      this.form.distributionGoodsName = val;
      this.getTableList();
    },
    immediate: true
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('queryEmployeePerformancePage', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    handleKpiDetail(row) {
      const { empId, empName, annual } = row;
      this.kpiMonthlVisible = true;
      this.empId = empId;
      this.empName = empName;
      this.annual = annual;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
.settle-txt{
  padding-bottom: 10px;
  color: red;
}
</style>
