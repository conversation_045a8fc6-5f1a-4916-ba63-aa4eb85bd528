<template>
  <common-dialog
    :visible.sync="dialogVisible"
    title="完结工单"
    width="600px"
    :show-footer="false"
    @close="handleClose"
  >
    <el-form
      ref="finishForm"
      :model="formData"
      :rules="rules"
      label-width="110px"
      size="small"
      class="!px-16 !py-10"
    >
      <el-form-item label="处理情况:" prop="handleStatus">
        <common-select
          v-model="formData.handleStatus"
          :type="
            orderType() === '1'
              ? 'consultHandleStatus'
              : 'complaintHandleStatus'
          "
          placeholder="请选择"
        />
      </el-form-item>

      <!-- 仅在orderType()==='1'时显示的表单项 -->
      <template v-if="orderType() === '1'">
        <el-form-item label="是否需要回复:">
          <common-select
            v-model="formData.needReply"
            type="workOrderNeedReply"
            placeholder="请选择"
          />
        </el-form-item>

        <el-form-item label="是否给予礼品:">
          <common-select
            v-model="formData.needGift"
            type="isOrNot_2"
            placeholder="请选择"
          />
        </el-form-item>

        <el-form-item label="礼品名称:">
          <el-input
            v-model="formData.needGiftName"
            placeholder="请输入礼品名称"
            :maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
      </template>

      <el-form-item label="处理备注:" prop="handleRemark">
        <el-input
          v-model="formData.handleRemark"
          type="textarea"
          :rows="4"
          placeholder="请输入处理备注"
          :maxlength="500"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          class="w-45"
          type="primary"
          @click="handleSave"
          :loading="loading"
          >保存</el-button
        >
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  name: "FinishDialog",
  components: {
    CommonDialog: () => import("@/components/common-dialog"),
    CommonSelect: () => import("@/components/formTools/CommonSelect"),
  },
  inject: ["orderType"],

  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    workOrderId: {
      type: [String, Number],
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      formData: {
        handleStatus: "",
        handleRemark: "",
        needReply: "",
        needGift: "",
        needGiftName: "",
      },
      rules: {
        handleStatus: [
          { required: true, message: "请选择处理情况", trigger: "change" },
        ],
      },
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        // 初始化表单，设置默认值
        this.initForm();
      }
    },
  },
  methods: {
    // 初始化表单
    initForm() {
      this.formData = {
        handleStatus: "",
        handleRemark: "",
        needReply: "",
        needGift: "",
        needGiftName: "",
      };
    },

    // 关闭弹窗
    handleClose() {
      this.$refs.finishForm.resetFields();
      this.$emit("update:visible", false);
    },

    // 保存完结信息
    handleSave() {
      this.$refs.finishForm.validate((valid) => {
        if (valid) {
          this.loading = true;

          // 构建保存数据
          const saveData = {
            type: this.orderType(),
            orderId: this.workOrderId,
            ...this.formData,
          };
          this.$post("finishWorkOrder", saveData, { json: true }).then(
            (res) => {
              const { ok, body, msg } = res;
              if (ok) {
                this.loading = false;
                this.$message.success("工单已成功完结");
                this.$emit("success");
                this.handleClose();
              } else {
                this.$message.error(msg || "完结失败");
              }
            }
          );
        }
      });
    },
  },
};
</script>

<style scoped>
.el-form {
  padding: 20px 10px;
}
</style>
