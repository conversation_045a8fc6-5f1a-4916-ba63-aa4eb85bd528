<template>
  <div>
    <!-- 表单 -->
    <open-packup>
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='商品名称' prop='distributionGoodsName'>
          <el-input v-model="form.distributionGoodsName" placeholder="请输入商品名称" />
        </el-form-item>
        <el-form-item label='商品id' prop='distributionGoodsId'>
          <el-input v-model="form.distributionGoodsId" placeholder="请输入商品id" />
        </el-form-item>
        <el-form-item label='订单状态' prop='orderState'>
          <el-select v-model="form.orderState" clearable>
            <el-option label="已支付" value="pay" />
            <el-option label="已退款" value="refund" />
          </el-select>
        </el-form-item>
        <el-form-item label='购买人姓名' prop='buyUserName'>
          <el-input v-model="form.buyUserName" placeholder="请输入购买人姓名" />
        </el-form-item>
        <el-form-item label='购买人手机号' prop='buyUserMobile'>
          <el-input v-model="form.buyUserMobile" placeholder="请输入购买人手机号" />
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='分销人姓名' prop='distributionUserName'>
          <el-input v-model="form.distributionUserName" placeholder="请输入分销人姓名" />
        </el-form-item>
        <el-form-item label='分销人手机号' prop='distributionMobile'>
          <el-input v-model="form.distributionMobile" placeholder="请输入分销人手机号" />
        </el-form-item>
        <el-form-item label='邀约人' prop='invitationUserName'>
          <el-input v-model="form.invitationUserName" placeholder="请输入邀约人" />
        </el-form-item>
        <el-form-item label='邀约人手机号码' prop='invitationMobile'>
          <el-input v-model="form.invitationMobile" placeholder="请输入邀约人手机号" />
        </el-form-item>
        <el-form-item label='付费时间' prop='payTime'>
          <el-date-picker
            v-model="form.payTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <el-form-item label='退费时间' prop='refundTime'>
          <el-date-picker
            v-model="form.refundTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placement="bottom-start"
          />
        </el-form-item>
        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>
      </el-form>
    </open-packup>
    <!-- 按钮区 -->
    <div class='yz-table-btnbox'>
      <el-button type="primary" size="small" plain :style="{ opacity: isBtn ? 1 : 0 }" @click="exportData">导出</el-button>
    </div>
    <!-- 表格 -->
    <el-table
      v-loading="tableLoading"
      border
      size="small"
      style="width: 100%"
      header-cell-class-name='table-cell-header'
      :data="tableData"
    >
      <el-table-column prop="distributionGoodsName" label="商品信息" align="center" />
      <el-table-column prop="distributionGoodsId" label="商品id" align="center" />
      <el-table-column prop="buyUserName" label="购买人" align="center" />
      <el-table-column prop="yzCode" label="远智编码" align="center" />
      <el-table-column prop="buyUserMobile" label="购买人手机号" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.buyUserMobile }}</div>
          <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.buyUserId)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="orderNo" label="订单号" align="center" />
      <el-table-column prop="orderState" label="订单状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.orderState === 'pay' ? '已支付' : '已退款' }}
        </template>
      </el-table-column>
      <el-table-column prop="payTime" label="付费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.payTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="refundTime" label="退费时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.refundTime | transformTimeStamp }}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" label="应付金额" align="center" />
      <el-table-column prop="payAmount" label="实缴金额" align="center" />
      <el-table-column prop="test" label="抵扣" width="120px" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.couponScale">优惠券抵扣: {{ scope.row.couponScale }}</p>
          <p v-if="scope.row.demurrageScale">滞留金抵扣: {{ scope.row.demurrageScale }}</p>
          <p v-if="scope.row.zmScale">智米抵扣: {{ scope.row.zmScale }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="actualRewardAmount" label="实际计佣金额" align="center" />
      <el-table-column prop="divideRatio" label="佣金比例" align="center" />
      <el-table-column prop="invitationEmpName" label="邀约人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.invitationEmpName }}</span>
          <el-tag v-if="scope.row.invitationEmpStatus === '2'" type="info" size="mini">离</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="invitationDivideRatio" label="邀约分成比例" align="center" />
      <el-table-column prop="invitationDivideAmount" label="邀约佣金" align="center" />
      <el-table-column prop="distributionEmpName" label="分销人" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.distributionEmpName }}</span>
          <el-tag v-if="scope.row.distributionEmpStatus === '2'" type="info" size="mini">离</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="distributionDivideRatio" label="分销分成比例" align="center" />
      <el-table-column prop="distributionDivideAmount" label="分销佣金" align="center" />
      <el-table-column prop="remark" label="备注" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.remark }}</p>
          <p>{{ scope.row.remarkTime | transformTimeStamp }}</p>
          <el-button type="text" @click="editMark(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <add-mark-dialog :visible.sync="markVisible" :distributionOrderNo="distributionOrderNo" :remark="remark" @getTableList="getTableList" />
  </div>
</template>
<script>
import { exportExcel, handleDateControl } from '@/utils';
// import batchImportDialog from './batch-import-dialog';
import LookMobile from '@/mixins/LookMobile';
import addMarkDialog from './add-mark-dialog';

export default {
  components: {
    // batchImportDialog
    addMarkDialog
  },
  filters: {
  },
  mixins: [LookMobile],
  props: {
    orderParams: {
      type: String,
      default: ''
    },
    empId: {
      type: String,
      default: ''
    },
    isBtn: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tableLoading: false,
      markVisible: false,
      form: {
        distributionGoodsName: '',
        orderState: '',
        distributionUserName: '',
        buyUserName: '',
        distributionMobile: '',
        buyUserMobile: '',
        payTime: '',
        yzCode: '',
        distributionGoodsId: '',
        invitationUserName: '',
        refundTime: '',
        invitationMobile: ''
      },
      distributionOrderNo: '',
      remark: '',
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    orderParams(val) {
      this.form.distributionGoodsName = val;
      this.getTableList();
    },
    empId() {
      this.getTableList();
    },
    immediate: true
  },
  mounted() {
    this.getTableList();
  },
  methods: {
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.payTime);
      formData.payStartDate = date[0];
      formData.payEndDate = date[1];
      const refundTime = handleDateControl(formData.refundTime);
      formData.refundStartDate = refundTime[0];
      formData.refundEndDate = refundTime[1];
      delete formData.payTime;
      delete formData.refundTime;
      if (this.empId) {
        formData.empId = this.empId;
      }
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    // 导出函数
    exportData() {
      const data = this.handleQueryParams();
      exportExcel('exportOrder', data);
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('distributionOrderList', data, { json: true }).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
      // 分销总额计算
      // this.$post('distributionOrderSettle').then(res => {
      //   const { fail, body } = res;
      //   if (!fail && body) {
      //     this.settledAmount = body.settledAmount;
      //     this.pendSettleAmount = body.pendSettleAmount;
      //   }
      // });
    },
    // 查询
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    editMark(rowData) {
      const { remark, distributionOrderNo } = rowData;
      this.distributionOrderNo = distributionOrderNo;
      this.remark = remark;
      this.markVisible = !this.markVisible;
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-table-btnbox {
  margin-top:16px;
}
</style>
