export const userType = {};

// 招生类型
export const recruitType = {
  1: '成教',
  2: '国开',
  3: '全日制',
  4: '自考',
  5: '研究生'
};

// 上课方式
export const courseType = {
  0: '直播',
  1: '录播',
  2: '硬盘推流'
};

// 会员卡类型
export const mebFreeType = {
  free: '免费体验卡',
  pay: '付费卡'
};

// 会员卡种类
export const cardSpecies = {
  year: '年卡',
  season: '季卡',
  mouth: '月卡',
  week: '周卡'
};

// 数据状态
export const status = {
  '1': '启用',
  '2': '禁用'
};

// 审核状态
export const approvalStatus = {
  1: '待审核',
  2: '已审核'
};

// 订单渠道
export const orderChannel = {
  WeChat: '微信',
  APP: 'APP'
};

// 分页器公共参数
export const pagination = {
  defaultCurrent: 1,
  defaultPageSize: 10,
  pageSizeOptions: [10, 40, 60, 100, 500],
  layout: 'prev, pager, next, jumper, sizes, total'
};

/** 列表表格高度
 *表格高度 = 100vh - 50px(顶部面包屑) - 42px(外边距内边距) - 70px(表单高度) - 43(按钮区高度) - 10px(底部外边距) -42px(分页高度)
 * calc(100vh - 232px)
 */
export const TABLE_HEIGHT = 'calc(100vh - 232px)';

