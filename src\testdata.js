// 本地字典
export default {
  // 会员卡类型
  mebFreeType: [
    { dictName: '免费体验卡', dictValue: 'free' },
    { dictName: '付费卡', dictValue: 'pay' }
  ],
  // 会员卡种类
  mebPayType: [
    { dictName: '年卡', dictValue: 'year' },
    { dictName: '季卡', dictValue: 'season' },
    { dictName: '月卡', dictValue: 'mouth' },
    { dictName: '周卡', dictValue: 'week' }
  ],
  // 抵扣方式
  deductType: [
    { dictName: '智米', dictValue: '1' },
    { dictName: '滞留金', dictValue: '2' }
  ],
  // 抵扣方式
  refundType: [
    { dictName: '现金', dictValue: '1' },
    { dictName: '智米', dictValue: '2' },
    { dictName: '滞留金', dictValue: '3' }
  ],
  // 数据状态
  status: [
    { dictName: '启用', dictValue: '1' },
    { dictName: '禁用', dictValue: '2' }
  ],
  // 上课方式
  courseType: [
    { dictName: '直播', dictValue: '0' },
    { dictName: '录播', dictValue: '1' },
    { dictName: '硬盘推流', dictValue: '2' },
    { dictName: '回放', dictValue: '3' }
  ],
  // 上架类型
  ShelfType: [
    { dictName: '单个套餐', dictValue: '1' },
    { dictName: '组合套餐', dictValue: '2' }
  ],
  // 购买渠道
  platform: [
    { dictName: '微信', dictValue: 'WECHAT' },
    { dictName: 'APP', dictValue: 'APP' }
  ],
  readPlanList: {
    'code': '00',
    'body': {
      'data': [
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': 83,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 1, // 状态 1.启用 2.禁用
          'applyNum': 10, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        },
        {
          'createUserId': null,
          'createUserName': null,
          'createTime': null,
          'updateUserId': null,
          'updateUserName': '张三', // 最后更新人姓名
          'updateTime': '2021-04-08 09:43:30', // 最后更新时间
          'readPlanId': 9, // 读书计划id
          'readPlanName': '读书计划', // 读书计划名称
          'planSummary': null,
          'learnBaseNum': null,
          'planDays': null,
          'actPrice': null,
          'marketPrice': null,
          'refundRule': null,
          'purchaseRule': null,
          'refundSupport': null,
          'listPic': null,
          'detailsPic': null,
          'featuredPic': null,
          'clockPic': null,
          'backgroundPic': null,
          'clockText': null,
          'shareTitle': null,
          'shareSummary': null,
          'status': 2, // 状态 1.启用 2.禁用
          'applyNum': 20, // 参与人数
          'menuDirectoryVos': null,
          'listPicFile': null,
          'detailsPicFile': null,
          'featuredPicFile': null,
          'clockPicFile': null,
          'backgroundPicFile': null
        }
      ],
      'recordsTotal': 1,
      'recordsFiltered': 1
    },
    'msg': '',
    'ok': true
  },
  getReadPlanList: {
    'code': '00',
    'body': {
      'createUserId': null,
      'createUserName': null,
      'createTime': null,
      'updateUserId': null,
      'updateUserName': null,
      'updateTime': null,
      'readPlanId': 9, // 读书计划id
      'readPlanName': '读书计划', // 读书计划名称
      'planSummary': '读书计划摘要', // 读书计划摘要
      'learnBaseNum': 1, // 学习基数
      'planDays': 180, // 计划天数
      'actPrice': 2.00, // 活动价格
      'marketPrice': 10.00, // 基本价格
      'refundRule': 'cash,zhimi,retention', // 退费规则
      'purchaseRule': 'zhimi,retention', // 购买规则
      'refundSupport': 1, // 是否支持退费
      'listPic': 'gs/5BEB53A72B1147B39C4C701A5A3D7493.jpg', // 列表图片
      'detailsPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 详情图片
      'featuredPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 精选图片
      'clockPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 如何打卡图片
      'backgroundPic': 'gs/1DECA0FF95AC463EAB745EA236F7DAA0.png', // 背景图片
      'clockText': '要记得打卡呦', // 打卡规则文案
      'shareTitle': '分享我分享我', // 分享标题
      'shareSummary': '康么', // 分享摘要
      'status': 1, // 状态
      'applyNum': 10, // 报名人数
      'menuDirectoryVos': [
        {
          'menuId': 7, // 菜单id
          'readPlanId': null, //
          'menuName': '菜单一', // 菜单名称
          'menuSummary': null, // 菜单摘要
          'sort': 1, // 排序
          'bookNum': null, // 书本数量
          'puMenuBookVOS': [
            {
              'menuId': 7, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            },
            {
              'menuId': 29, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            }
          ]
        }, {
          'menuId': 2, // 菜单id
          'readPlanId': null, //
          'menuName': '菜单一', // 菜单名称
          'menuSummary': null, // 菜单摘要
          'sort': 1, // 排序
          'bookNum': null, // 书本数量
          'puMenuBookVOS': [
            {
              'menuId': 7, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            },
            {
              'menuId': 29, //
              'bId': null, // 书本id
              'bName': null, // 书本名称
              'sort': 1, // 排序
              'planDay': null// 章节数量
            }
          ]
        }
      ],
      'listPicFile': null,
      'detailsPicFile': null,
      'featuredPicFile': null,
      'clockPicFile': null,
      'backgroundPicFile': null
    },
    'msg': '',
    'ok': true
  }
};

