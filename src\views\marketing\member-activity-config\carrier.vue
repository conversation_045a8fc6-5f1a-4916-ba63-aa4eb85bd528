<template>
  <common-dialog
    is-full
    title="事件数据"
    :visible.sync='show'
    @open="init"
    @close='close'
  >
    <hotZone v-if="show" :show='false' :target-type='targetType' :target-id='targetId' />
  </common-dialog>
</template>
<script>
import hotZone from '../../web-statistics/hot-zone/index';
export default {
  components: {
    hotZone
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [Number, String],
      default: null
    },
    targetType: {
      type: [Number, String],
      default: null
    },
    targetId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      show: false
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    init() {},
    close() {
      this.$emit('update:visible', false);
    }
  }
};
</script>
