<template>
  <el-select
    v-model="val"
    v-loadmore="loadmore"
    filterable
    remote
    clearable
    :remote-method="remoteMethod"
    :loading="loading"
    v-bind="$attrs"
    v-on="$listeners"
    @change="_change"
    @clear="_clear"
  >
    <el-option
      v-for="item in filterOptions"
      :key="item[props.value]"
      :label="item[props.label]"
      :value="item[props.value]"
    />
  </el-select>
</template>
<script>

export default {
  name: 'SearchSelects',
  props: {
    value: {
      type: [String, Number, Boolean, Object],
      default: null
    },
    props: {
      type: Object,
      default: function() {
        /**
         * apiName '接口key值'
         * value option的value字段名
         * label option的label字段名
         * query 远程查询的参数名
         */
        return {
          apiName: '',
          value: '',
          label: '',
          query: '',
          method: 'post'
        };
      }
    },
    param: {
      type: Object,
      default: function() {
        return {};
      }
    },
    defaultOption: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      val: null,
      options: [],
      queryParam: null,
      defaultSelection: null,
      loading: false,
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      },
      cancelToken: null, // 添加取消令牌
      type: 'init' // init 正常 more 更多
    };
  },
  computed: {
    filterOptions() {
      let arr = this.options;
      // console.log(arr, this.defaultSelection, 'this.defaultSelection');
      if (this.defaultSelection) {
        arr = this.options.filter((item) => {
          const keyName = this.props.label;
          const valueName = this.props.value;
          if (
            item[keyName] !== this.defaultSelection[keyName] &&
            item[valueName] !== this.defaultSelection[valueName]
          ) {
            return true;
          }
        });
        arr.push(this.defaultSelection);
      }
      return arr;
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(value) {
        this.val = value;
      }
    },
    param(val) {
      this.queryParam = val;
    },
    defaultOption: {
      immediate: true,
      handler(value) {
        this.defaultSelection = value;
      }
    }
  },
  mounted() {
    this.getOptions();
  },
  methods: {
    loadmore() {
      if (this.pagination.total < this.options.length) {
        return;
      }
      this.type = 'more';
      this.pagination.page += 1;
      this.getOptions();
    },
    getOptions(cancelToken) {
      this.loading = true;
      const data = {
        pageNum: this.pagination.page,
        pageSize: this.pagination.limit,
        ...this.param
      };
      console.log(this.param, 'this.param');
      const config = {
        json: true
      };

      // 只在 cancelToken 存在时添加
      if (cancelToken) {
        config.cancelToken = cancelToken;
      }
      console.log('🚀 ~ getOptions ~ config:', config);

      if (this.props.method === 'post') {
        this.$post(this.props.apiName, data, config)
          .then((res) => {
            const { fail, body } = res;
            if (!fail && body) {
              if (this.type === 'init') {
                this.options = body.data;
              } else {
                this.options = this.options.concat(body.data);
              }
              this.pagination.total = body.recordsTotal;
              if (this.options.length === 0) {
                this.defaultSelection = undefined;
              }
              this.loading = false;
            }
          })
          .catch((error) => {
            if (!this.$http.isCancel(error)) {
              console.error('请求错误:', error);
            }
          });
      } else {
        this.$http
          .get(this.props.apiName, { params: data, ...config })
          .then((res) => {
            const { fail, body } = res;
            if (!fail && body) {
              if (this.type === 'init') {
                this.options = body.data;
              } else {
                this.options = this.options.concat(body.data);
              }
              this.pagination.total = body.recordsTotal;
              if (this.options.length === 0) {
                this.defaultSelection = undefined;
              }
              this.loading = false;
            }
          })
          .catch((error) => {
            if (!this.$http.isCancel(error)) {
              console.error('请求错误:', error);
            }
          });
      }
    },
    resetGetOptions() {
      this.type = 'init';
      this.options = [];
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.param[this.props.query] = null;
      this.getOptions();
    },
    remoteMethod(query) {
      this.type = 'init';
      if (this.cancelToken) {
        this.cancelToken.cancel('取消请求');
        this.cancelToken = null; // 清除旧的 cancelToken
      }
      this.cancelToken = this.$http.CancelToken.source();
      this.options = [];
      this.defaultSelection = undefined;
      this.param[this.props.query] = query;
      this.pagination.page = 1;
      this.getOptions(this.cancelToken.token);
    },
    _clear() {
      this.type = 'init';
      this.options = [];
      this.pagination.page = 1;
      this.pagination.total = 0;
      this.param[this.props.query] = '';
      this.getOptions();
    },
    _change(value) {
      this.type = 'init';
      if (!value) {
        this.$emit('changeVal', { value, label: '' });
        return;
      }
      let obj = {};
      obj = this.filterOptions.find((item) => {
        return item[this.props.value] === value;
      });
      this.$emit('changeVal', {
        value,
        label: obj[this.props.label],
        source: obj
      });
      // this.resetGetOptions();
      // this.getOptions();
    }
  }
};
</script>
