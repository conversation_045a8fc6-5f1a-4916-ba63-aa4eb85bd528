<template>
  <el-select
    ref="parent"
    v-model="val"
    v-loadmore="loadmore"
    size="mini"
    class="search-select"
    placeholder="请选择"
    :popper-append-to-body="false"
    v-bind="$attrs"
    @change="_change"
    @visible-change="visibleChange"
    v-on="$listeners"
  >
    <el-input
      ref="keywordInput"
      v-model="keyword"
      prefix-icon="el-icon-search"
      class="search-input"
      @input="search"
    />
    <slot />
    <el-option
      v-for="item in options"
      :key="item[props.value] + generateKey()"
      :label="item[props.label]"
      :value="item[props.value]"
    />
  </el-select>
</template>
<script>
import { uuid } from '@/utils';
export default {
  name: 'SearchSelect',
  directives: {
    loadmore: {
      bind(el, binding) {
        // 获取element-ui定义好的scroll盒子
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function() {
          const CONDITION = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (CONDITION) {
            binding.value();
          }
        });
      }
    }
  },
  props: {
    value: {
      type: [String, Boolean, Number],
      required: true,
      default: null
    },
    props: {
      type: Object,
      default: function() {
        return { label: 'label', value: 'value' };
      }
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      val: this.value,
      keyword: '' // 关键字搜索内容
    };
  },
  watch: {
    value(val) {
      this.val = val;
    }
  },
  mounted() {
    this.$nextTick(() => {
      const eInput = this.$refs['keywordInput'].$el;
      const eDropdown = this.$refs['parent'].$el.getElementsByClassName('el-select-dropdown')[0];
      const eBar = this.$refs['parent'].$el.getElementsByClassName('el-scrollbar')[0];
      eDropdown.insertBefore(eInput, eBar);
    });
  },
  methods: {
    _change(value) {
      const selected = this.options.find(item => {
        return item[this.props.value] === value;
      });
      this.$emit('select', selected);
      this.$emit('input', value);
    },
    loadmore() {
      this.$emit('loadmore');
    },
    search(value) {
      this.$emit('search', value);
    },
    visibleChange(status) {
      if (!status) {
        this.keyword = null;
      } else {
        this.$emit('open');
      }
    },
    generateKey() {
      return uuid();
    }
  }
};
</script>
<style lang="scss" scoped>

.search-input {
  position: relative;
  ::v-deep .el-input__inner {
    cursor: text;
    border-top: none;
    border-left: none;
    border-right: none;
    border-radius: 0;
    height: 30px;
    line-height: 30px;
    font-size:12px;
    margin-bottom:2px;
    padding-right:0;
    margin:5px 10px 0 10px;
    width:calc(100% - 20px);
    border-color:#DCDFE6 !important;
  }

  ::v-deep .el-input__inner:focus{
    border-color:#DCDFE6 !important;
  }

  ::v-deep .el-input__icon {
   font-size: 12px !important;
   line-height: 35px !important;
  }
}

::v-deep .popper__arrow {
  display: none !important;
}

::v-deep .el-popper {
  margin-top: 1px;
  position: absolute !important;
  left:0 !important;
  top:22px !important;
}
::v-deep .el-scrollbar__bar.is-vertical{
  width: 4px;
}
::v-deep .el-select-dropdown__item{
  font-size: 12px;
  height: 28px;
  line-height: 28px;
}
::v-deep .el-select-dropdown__item.hover{
  color:#409eff !important;
  background-color: #ECF5FF !important;
}
::v-deep .el-select-dropdown__item.selected{
  color:#606266;
  font-weight: initial;
  background-color:#fff;
}
</style>
