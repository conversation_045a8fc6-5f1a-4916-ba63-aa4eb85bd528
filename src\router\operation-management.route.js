import Layout from '@/layout';

// 运营管理模块
export default [
  {
    path: '/operation-management',
    component: Layout,
    meta: {
      title: '运营管理',
      icon: 'el-icon-connection',
      breadcrumb: false
    },
    children: [
      {
        path: 'iron-powder-task-config',
        component: () =>
          import('@/views/operation-management/iron-powder-task-config/index'),
        meta: {
          title: '铁粉任务配置'
        }
      },
      {
        path: 'app-marketing-tool-config',
        component: () =>
          import('@/views/operation-management/app-marketing-tool-config/index'),
        meta: {
          title: 'App助学配置'
        }
      },
      {
        path: 'app-sign-in',
        component: () => import('@/views/sign-in-manage/index'),
        name: 'AppSignIn',
        meta: { title: 'APP签到' }
      }
    ]
  }
];
