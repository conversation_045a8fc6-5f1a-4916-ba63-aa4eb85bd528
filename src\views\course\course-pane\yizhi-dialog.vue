<template>
  <common-dialog
    title="选择课程"
    width="650px"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-form
        ref='searchForm'
        size='mini'
        :model='searchForm'
        label-width='80px'
      >
        <el-form-item label='选择目录' prop='allow'>
          <el-select
            v-model="searchForm.suject"
            filterable
            @change="handleSelection"
          >
            <el-option
              v-for="(item,index) in courseCatalog"
              :key="index"
              :label="item.projectName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <div>
        <el-table
          v-loading="tableLoading"
          border
          size="small"
          style="width: 100%"
          height="400px"
          header-cell-class-name='table-cell-header'
          :data="tableData"
        >
          <el-table-column prop="packageName" label="课程" align="center" />
          <el-table-column label="操作" align="center" width="150px">
            <template slot-scope="scope">
              <div class="yz-button-area">
                <el-button type="text" @click="selectionCourse(scope.row)">选择</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      courseCatalog: [],
      searchForm: {
        suject: ''
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    handleSelection(value) {
      this.tableLoading = true;
      for (let i = 0; i < this.courseCatalog.length; i++) {
        const item = this.courseCatalog[i];
        if (item.id === value) {
          this.tableLoading = false;
          this.tableData = item.packageList;
        }
      }
    },
    selectionCourse(row) {
      this.$emit('selected', row);
    },
    open() {
      this.$post('getYiZhiCourseList')
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.courseCatalog = body.data.list;
            console.log(body.data);
          }
        });
    },
    submit() {
    },
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
