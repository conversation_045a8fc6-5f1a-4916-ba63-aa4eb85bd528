<template>
  <common-dialog
    v-loading="tableLoading"
    :title="title"
    width="55%"
    :showFooter="true"
    :visible.sync="visible"
    class="result"
    @open="init"
    @close="onCloseBtn"
    @confirm="submit"
  >
    <el-form ref="resultForm" class="result-main" size="mini" :model="results" :rules="resultRule">
      <!-- 基础表单 -->
      <el-form-item label="结 果 名 称：" prop="name">
        <el-input v-model="results.name" :disabled="resultFlag === 1" class="eval-input" maxlength="30" show-word-limit placeholder="仅限于内部展示" />
      </el-form-item>
      <el-form-item label="测评结果图：" prop="resultImg" class="eval-resultImg">
        <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg" :disabled="resultFlag === 1" :file-list="results.resultSrcList" :imgWidth="750" @remove="removeHeadImg" @success="successHeadImg" />
      </el-form-item>
      <el-form-item label="底 色 配 置：" prop="backgroundColor">
        <template>
          <div class="eval-bgc">
            <el-input v-model="results.backgroundColor" style="width: 80%;" :disabled="resultFlag === 1" maxlength="7" show-word-limit placeholder="以#开头的7位字符" />
            <div class="eval-color" :style="{backgroundColor:results.backgroundColor}" />
          </div>
        </template>
      </el-form-item>
      <!-- 标签 -->
      <div class="main-tag">
        <div class="tag-btn">
          <span class="tag-span">添加对应标签：</span>
          <el-button type="primary" size="small" @click="openTags">新增标签</el-button>
          <span class="tag-span2">最多可添加5个</span>
        </div>
        <ul v-if="tagsList.length" class="tag-ul">
          <li class="tag-ls">已创建标签：</li>
          <li v-for="(item,index) in tagsList" :key="item.tagInfoId" class="tag-li">
            {{ item.tagInfoName }}
            <span class="li-span" @click="deleteTags(index)">x</span>
          </li>
        </ul>
      </div>
      <!-- 胶囊位 -->
      <el-form-item label="是否展示胶囊位" prop="capsuleFlag">
        <el-radio-group v-model="results.capsuleFlag" @change="capsuleChange">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-show="isCapsule" class="main-capsule">
        <el-form-item label="胶囊位图片：" :prop="isCapsule?'capsuleImg':''" class="eval-capsuleImg">
          <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg,image/gif" :imgWidth="718" :imgHeight="192" :file-list="results.capsuleSrcList" @remove="removeCapsuleImg" @success="successCapsuleImg" />
        </el-form-item>
        <el-form-item label="小程序appid：" :prop="isCapsule?'capsuleAppletId':''">
          <el-input v-model="results.capsuleAppletId" class="eval-input" maxlength="150" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="跳转路由地址：" :prop="isCapsule?'capsuleAppletUrl':''">
          <el-input v-model="results.capsuleAppletUrl" class="eval-input" maxlength="150" show-word-limit placeholder="请输入" />
        </el-form-item>
      </div>
      <!-- 导流 -->
      <el-form-item label="是否展示导流模块" prop="diversionFlag">
        <el-radio-group v-model="results.diversionFlag" @change="flagChange">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-show="isDiverste" class="main-capsule">
        <el-form-item label="导流标题：" :prop="isDiverste?'diversionTitle':''">
          <el-input v-model="results.diversionTitle" class="eval-input" maxlength="18" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="导流文案：">
          <el-input v-model="results.diversionContent" type="textarea" rows="3" class="eval-input" maxlength="50" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="请选择导流二维码：" :prop="isDiverste?'diversionQrType':''">
          <el-radio-group v-model="results.diversionQrType">
            <el-radio :label="1">对应跟进人的企微</el-radio>
            <el-radio :label="2">固定企微</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-show="results.diversionQrType === 1" class="diversion-tips">若用户当前无跟进人，将展示以下老师信息</div>
        <el-form-item label="老师名字：" :prop="isDiverste?'diversionTeacherName':''">
          <el-input v-model="results.diversionTeacherName" class="eval-input" maxlength="4" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="老师简介：" :prop="isDiverste?'diversionTeacherDescription':''">
          <el-input v-model="results.diversionTeacherDescription" class="eval-input" maxlength="6" show-word-limit placeholder="请输入" />
        </el-form-item>
        <el-form-item label="企微二维码：" :prop="isDiverste?'diversionTeacherQrCode':''" class="eval-teacherQrs">
          <upload-file :max-limit="1" :size="2" accept="image/png,image/jpeg" :imgWidth="112" :imgHeight="112" :file-list="results.teacherQrCodeList" @remove="removediversionTeacherQrCodeImg" @success="successdiversionTeacherQrCodeImg" />
        </el-form-item>
      </div>
    </el-form>
    <!-- 新增个人标签 -->
    <added-tags :visible="showTags" @on-close="closeTags" />
  </common-dialog>
</template>

<script>

import addedTags from './added-tags';

export default {
  components: { addedTags },
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: '' },
    ids: { type: [String, Number], default: null },
    types: { type: Number, default: 0 },
    resultFlag: { type: [String, Number], default: null }
  },
  data() {
    return {
      showTags: false,
      tableLoading: false,
      tagsList: [],
      isCapsule: true, // 是否展示胶囊位
      isDiverste: true, // 是否展示导流模块
      results: {
        name: '',
        tagIds: '',
        resultImg: '',
        backgroundColor: '',
        resultSrcList: [],
        selectNum: '',
        capsuleFlag: 1,
        capsuleImg: '',
        capsuleSrcList: [],
        capsuleAppletId: '',
        capsuleAppletUrl: '',
        diversionFlag: 1,
        diversionTitle: '',
        diversionContent: '',
        diversionQrType: 1,
        diversionTeacherName: '',
        diversionTeacherDescription: '',
        diversionTeacherQrCode: '',
        teacherQrCodeList: []
      },
      resultRule: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        resultImg: [{ required: true, message: '请上传图片', trigger: 'change' }],
        backgroundColor: [{
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (value === '' || value === undefined) {
              callback(new Error('请输入'));
              return;
            }
            const reg = /^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/;
            if (!reg.test(value)) {
              callback(new Error('请输入以#开头的7位字符'));
              return;
            }
            callback();
          }
        }],
        capsuleFlag: [{ required: true, message: '请选择', trigger: 'change' }],
        capsuleImg: [{ required: true, message: '请上传图片', trigger: 'change' }],
        capsuleAppletId: [{ required: true, message: '请输入', trigger: 'blur' }],
        capsuleAppletUrl: [{ required: true, message: '请输入', trigger: 'blur' }],
        diversionFlag: [{ required: true, message: '请选择', trigger: 'blur' }],
        diversionTitle: [{ required: true, message: '请输入', trigger: 'blur' }],
        diversionQrType: [{ required: true, message: '请选择', trigger: 'change' }],
        diversionTeacherName: [{ required: true, message: '请输入', trigger: 'blur' }],
        diversionTeacherDescription: [{ required: true, message: '请输入', trigger: 'blur' }],
        diversionTeacherQrCode: [{ required: true, message: '请上传图片', trigger: 'change' }]
      }
    };
  },
  computed: {
    evalDisables() {
      return Number(this.resultFlag) === 1;
    }
  },
  methods: {
    init() {
      console.log(111, this.resultRule, this.resultFlag);
      // 修改-获取详情
      if (this.ids) {
        this.tableLoading = true;
        this.$post('resultDetail', { id: this.ids })
          .then((res) => {
            const { code, body } = res;
            if (code === '00') {
              this.capsuleChange(body?.capsuleFlag);
              this.flagChange(body?.diversionFlag);
              body['capsuleSrcList'] = [{ url: body.capsuleImg || '' }];
              body['resultSrcList'] = [{ url: body.resultImg || '' }];
              body['teacherQrCodeList'] = [{ url: body.diversionTeacherQrCode || '' }];
              this.tagsList = body?.tagInfoList || [];
              console.log('修改-body', body);
              this.results = body;
            }
            this.tableLoading = false;
          }).catch(() => {
            this.tableLoading = false;
          });
      }
    },
    // 是否展示胶囊位
    capsuleChange(ev) {
      this.isCapsule = Boolean(ev === 1);
      for (const key in this.resultRule) {
        if (key === 'capsuleImg' || key === 'capsuleAppletId' || key === 'capsuleAppletUrl') {
          this.resultRule[key][0].required = this.isCapsule;
        }
      }
      console.log('是否展示胶囊位', this.isCapsule, this.resultRule);
    },
    // 是否展示导流模块
    flagChange(ev) {
      this.isDiverste = Boolean(ev === 1);
      for (const key in this.resultRule) {
        if (key === 'diversionTitle' || key === 'diversionQrType' || key === 'diversionTeacherName' || key === 'diversionTeacherDescription' || key === 'diversionTeacherQrCode') {
          this.resultRule[key][0].required = this.isDiverste;
        }
      }
      console.log('是否展示导流模块', this.isDiverste, this.resultRule);
    },
    // 成功上传头图
    successHeadImg(evt) {
      this.results.resultImg = evt?.response;
    },
    // 删除头图
    removeHeadImg() {
      this.results.resultImg = '';
      this.results.resultSrcList = [];
    },
    // 打开标签
    openTags() {
      if (this.tagsList.length >= 5) {
        this.$message.error('标签已达到5个，无法新增');
        return;
      }
      this.showTags = true;
    },
    // 关闭标签
    closeTags(obs) {
      const is = this.tagsList?.filter(taem => taem.tagInfoId === obs.tagInfoId);
      console.log('关闭标签-is', is);
      if (!is?.length) {
        this.tagsList.push(obs);
      } else this.$message({ message: '该标签已存在', type: 'warning' });
      this.showTags = false;
    },
    // 删除标签
    deleteTags(ins) {
      this.tagsList.splice(ins, 1);
    },
    // 成功上传胶囊位头图
    successCapsuleImg(evt) {
      this.results.capsuleImg = evt?.response;
    },
    // 删除胶囊位头图
    removeCapsuleImg() {
      this.results.capsuleImg = '';
      this.results.capsuleSrcList = [];
    },
    // 成功上传老师企微二维码
    successdiversionTeacherQrCodeImg(evt) {
      this.results.diversionTeacherQrCode = evt?.response;
    },
    // 删除老师企微二维码
    removediversionTeacherQrCodeImg() {
      this.results.diversionTeacherQrCode = '';
      this.results.teacherQrCodeList = [];
    },
    // 提交表单
    submit() {
      console.log('提交表单', this.results);
      this.$refs['resultForm'].validate((valid) => {
        if (valid) {
          // 构造个人标签Id
          let str = '';
          if (this.tagsList?.length) {
            this.tagsList.forEach(item => {
              str += `${item.tagInfoId},`;
            });
          }
          this.results.tagIds = str?.slice(0, -1) || '';
          console.log('删除标签', this.results);
          // 调用接口
          this.tableLoading = true;
          const usl = this.types === 1 ? 'resultUpdate' : 'resultAdd';
          this.$post(usl, this.results, { json: true }).then((res) => {
            const { code } = res;
            if (code === '00') {
              this.$message.success('提交成功');
              this.$emit('on-close', 1);
            }
            this.tableLoading = false;
          }).catch(() => {
            this.$message.error('提交失败');
            this.tableLoading = false;
          });
        }
      });
    },
    // 关闭当前弹窗
    onCloseBtn() {
      if (this.visible) {
        this.tagsList = [];
        this.results = {
          name: '',
          tagIds: '',
          resultImg: '',
          backgroundColor: '',
          resultSrcList: [],
          selectNum: '',
          capsuleFlag: 1,
          capsuleImg: '',
          capsuleSrcList: [],
          capsuleAppletId: '',
          capsuleAppletUrl: '',
          diversionFlag: 1,
          diversionTitle: '',
          diversionContent: '',
          diversionQrType: 1,
          diversionTeacherName: '',
          diversionTeacherDescription: '',
          diversionTeacherQrCode: '',
          teacherQrCodeList: []
        };
        this.$refs['resultForm'].resetFields();
        this.capsuleChange(1);
        this.flagChange(1);
        this.$emit('on-close', 0);
      }
    }
  }
};
</script>

<style lang="scss">
.result {
  &-main {
    margin: 30px 30px;
    height: 520px;
    .main-tag{
      margin: 20px 0;
      .tag-btn{
        width: 100%;
        display: flex;
        align-items: center;
        .tag-span2 {
          margin-left: 8px;
          font-size: 12px;
          color: #7c7c7c;
        }
      }
      .tag-ul {
        margin: 20px 11% 0;
        display: flex;
        align-items: center;
        .tag-ls {
          font-size: 13px;
        }
        .tag-li {
          padding: 0 4px;
          margin-right: 15px;
          line-height: 22px;
          text-align: center;
          font-size: 12px;
          border: 1px solid #adadad;
          border-radius: 4px;
          position: relative;
          .li-span {
            position: absolute;
            top: -10px;
            right: -8px;
            display: block;
            padding: 3px;
            line-height: 6px;
            font-size: 10px;
            text-align: center;
            background-color: #fff;
            border: 1px solid #adadad;
            border-radius: 50%;
            cursor: pointer;
          }
          .li-span:hover {
            color: rgb(255, 0, 0);
            border-color: rgb(255, 0, 0);
          }
        }
      }
    }
    .main-capsule {
      margin: 20px 12%;
      .diversion-tips {
        margin: 10px 6px 15px;
        font-size: 13px;
        color: #ff0101;
      }
    }
    .eval-bgc {
      width: 100%;
      display: flex;
      .eval-color {
        margin-left: 10px;
        width: 26px;
        height: 26px;
        border: 1px dashed #ff0101;
      }
    }
    .el-form-item {
      display: flex;
      .el-form-item__content {
        min-width: 40%;
      }
    }
  }
  .yz-common-dialog__footer {
    text-align: center !important;
  }
  .eval-resultImg,
  .eval-capsuleImg,
  .eval-teacherQrs {
    position: relative;
  }
  .eval-resultImg::after ,
  .eval-capsuleImg::after ,
  .eval-teacherQrs::after {
    position: absolute;
    left: 280px;
    bottom: 10px;
    font-size: 12px;
    color: #7c7c7c;
    content: '仅支持上传PNG/JPEG，尺寸为固定宽度750，高度不限，上传最大不能超过2M';
  }
  .eval-capsuleImg::after {
    content: '仅支持上传PNG/JPEG/GIF，尺寸为718*192，上传最大不能超过2M';
  }
  .eval-teacherQrs::after {
    content: '仅支持上传PNG/JPEG，尺寸为112*112，上传最大不能超过2M';
  }
}
</style>

