<template>
  <common-dialog
    is-full
    width="800px"
    title="学习人数"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='160px'
        @submit.native.prevent='search'
      >
        <el-form-item label='学业编码' prop='learnId'>
          <el-input v-model="form.learnId" placeholder="请输入学业编码" />
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>

        <el-form-item label='用户姓名' prop='userName'>
          <el-input v-model="form.userName" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label='挑战状态' prop='challengeState'>
          <el-select v-model="form.challengeState" clearable>
            <el-option label="挑战中" value="1" />
            <el-option label="挑战失败" value="2" />
            <el-option label="挑战成功" value="3" />
          </el-select>
        </el-form-item>

        <el-form-item label='是否在周期内完成关卡' prop='isComplete'>
          <el-select v-model="form.isComplete" clearable>
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item label='报名时间' prop='studyTime'>
          <el-date-picker
            v-model="form.studyTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column type="index" label="课时序号" width="100" align="center" />
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column label="学员姓名" align="center" prop="userName">
          <template slot-scope="scope">
            <div class="yz-button-area">
              <el-button type="text" @click="showStudyDetails(scope.row)"> {{ scope.row.userName }}</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="报名时间" align="center" prop="tradeType">
          <template slot-scope="scope">
            {{ scope.row.challengeStartTime | transformTimeStamp }}
          </template>
        </el-table-column>
        <el-table-column prop="classTimeNum" label="关卡总数" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.mobile }}</div>
            <el-button size="mini" type="primary" plain @click="getCompleteMobile(scope.row.userId)">查看</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="completeNum" label="已完成关卡" align="center" />
        <el-table-column label="是否在周期内完成关卡" align="center" prop="tradeType">
          <template slot-scope="scope">
            {{ scope.row.isComplete === 1? '是':'否' }}
          </template>
        </el-table-column>
        <el-table-column prop="averageScore" label="平均得分" align="center" />
        <el-table-column label="挑战状态" align="center" prop="tradeType">
          <template slot-scope="scope">
            {{ scope.row.challengeState | changeState }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页区 -->
    <div class="yz-table-pagination">
      <pagination
        :total='pagination.total'
        :page.sync="pagination.page"
        :limit.sync="pagination.limit"
        @pagination="getTableList"
      />
    </div>
    <study-details :visible.sync="stuDetailsVisible" :row="currentEditRow" :trainingId="trainingId" />
  </common-dialog>
</template>

<script>
import { handleDateControl } from '@/utils';
import studyDetails from './study-details.vue';
import LookMobile from '@/mixins/LookMobile';

export default {
  components: {
    studyDetails
  },
  filters: {
    changeState(val) {
      if (!val) return '';
      const obj = {
        '1': '挑战中',
        '2': '挑战失败',
        '3': '挑战成功'
      };
      return obj[val];
    }
  },
  mixins: [LookMobile],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      isEditLock: false,
      lockStatus: '1',
      stuDetailsVisible: false,
      currentEditRow: {},
      trainingId: '',
      form: {
        userName: '',
        challengeState: '',
        isComplete: '',
        studyTime: '',
        startTime: '',
        mobile: '',
        endTime: '',
        learnId: '',
        yzCode: ''
      },
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {

  },
  methods: {
    showStudyDetails(row) {
      this.currentEditRow = row;
      this.stuDetailsVisible = true;
    },
    // 处理参数
    handleQueryParams() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const date = handleDateControl(formData.studyTime);

      formData.startTime = date[0];
      formData.endTime = date[1];
      delete formData.time;
      const data = {
        ...formData,
        trainingId: this.row.trainingId,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      return data;
    },
    open() {
      this.trainingId = this.row.trainingId;
      this.getTableList();
    },
    // 获取列表数据
    getTableList() {
      this.tableLoading = true;
      const data = this.handleQueryParams();
      this.$post('getTrainUserSign', data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableLoading = false;
          this.tableData = body.data;
          this.pagination.total = body.recordsTotal;
        }
      });
    },
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },

    submit() {},
    close() {
      Object.assign(this.$data, this.$options.data.call(this));
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
