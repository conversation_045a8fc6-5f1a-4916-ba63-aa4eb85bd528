<template>
  <common-dialog
    is-full
    title="子订单详情"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="userName" label="用户" align="center">
          <template>
            {{ userName }}
          </template>
        </el-table-column>
        <el-table-column prop="goodsName" label="缴费名称" align="center" />
        <el-table-column prop="totalPrice" label="应缴金额（元）" align="center" />
        <el-table-column prop="payAmount" label="实缴金额（元）" align="center" />
        <el-table-column prop="zmScale" label="智米抵扣（元）" align="center" />
        <el-table-column prop="demurrageScale" label="滞留金抵扣（元）" align="center" />
        <el-table-column prop="orderDetailedNo" label="单据号" align="center" width="300" />
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    payNo: {
      type: [String, Number, Object],
      default: null
    },
    userName: {
      type: [String, Number, Object],
      default: null
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      pagination: {
        page: 1,
        total: 0,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {
  },
  methods: {
    open() {
      if (this.payNo) {
        this.getTableList();
      }
    },
    getTableList() {
      this.tableLoading = true;
      const params = {
        payNo: this.payNo,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };

      this.$post('getChildPayDetail', params)
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            if (Array.isArray(body.data)) {
              this.tableLoading = false;
              this.tableData = body.data;
              this.pagination.total = body.recordsTotal;
            }
          }
        });
    },
    close() {
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>

</style>
