<template>
  <div class="app-main">
    <div class="back">
      <span style="font-size: 12px">查看目标学员</span>
      <i class="el-icon-close" @click="$router.go(-1)" />
    </div>

    <div class="yz-base-container">
      <!-- 表单 -->
      <open-packup>
        <el-form
          ref="searchForm"
          class="yz-search-form"
          size="mini"
          label-width="160px"
          @submit.native.prevent="search"
        >
          <el-form-item label="添加训练营时间起止：" prop="startAddTcTime">
            <el-date-picker
              v-model="timeValue"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
              @change="(e) => changeTime(e, 1)"
            />
          </el-form-item>
          <el-form-item label="训练营开启时间起止：" prop="endAddTcTime">
            <el-date-picker
              v-model="timeValue2"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
              @change="(e) => changeTime(e, 2)"
            />
          </el-form-item>
          <el-form-item label="训练营关闭时间起止：" prop="startTcTime">
            <el-date-picker
              v-model="timeValue3"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              clearable
              @change="(e) => changeTime(e, 3)"
            />
          </el-form-item>

          <el-form-item label="学业编码：" prop="learnId">
            <el-input v-model="form.learnId" placeholder="请输入" clearable />
          </el-form-item>

          <el-form-item label="远智编码：" prop="yzCode">
            <el-input v-model="form.yzCode" placeholder="请输入" clearable />
          </el-form-item>

          <el-form-item label="学员姓名：" prop="stdName">
            <el-input v-model="form.stdName" placeholder="请输入" clearable />
          </el-form-item>

          <el-form-item label="证件号码：" prop="idCard">
            <el-input v-model="form.idCard" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="手机号：" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入" clearable />
          </el-form-item>
          <el-form-item label="年级：" prop="pfsnLevel">
            <el-select
              v-model="form.pfsnLevel"
              clearable
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="item in $dictJson['grade']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="院校：" prop="unvsId">
            <el-select
              v-model="form.unvsId"
              clearable
              filterable
              remote
              placeholder="请输入关键词"
              :loading="unvSLoading"
              :filter-method="getUnvSList"
              @change="getMajorList"
            >
              <el-option
                v-for="item in unvSList"
                :key="item.unvs_id"
                :label="item.unvs_name"
                :value="item.unvs_id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="专业：" prop="pfsnId">
            <el-select
              v-model="form.pfsnId"
              clearable
              filterable
              placeholder="请选择"
            >
              <el-option
                v-for="item in majorList"
                :key="item.pfsnId"
                :label="item.pfsnName"
                :value="item.pfsnId"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="报考层次：" prop="pfsnLevel">
            <el-select v-model="form.pfsnLevel" clearable placeholder="请选择">
              <el-option
                v-for="item in $dictJson['pfsnLevel']"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="自考考期：" prop="examinationId">
            <el-select
              v-model="form.examinationId"
              clearable
              filterable
              remote
              placeholder="请输入关键词"
              :loading="examLoading"
              :filter-method="getExaminationList"
            >
              <el-option
                v-for="item in examList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数据来源：" prop="dataSource">
            <el-select v-model="form.dataSource" placeholder="请选择" clearable>
              <el-option value="1" label="自动导入" />
              <el-option value="2" label="批量导入" />
            </el-select>
          </el-form-item>
          <el-form-item label="挑战状态：" prop="isSuccess">
            <el-select
              v-model="form.isSuccess"
              placeholder="请选择"
              clearable
              :disabled="isOpenDisable"
              @change="isSuccessChange"
            >
              <el-option value="0" label="挑战中" />
              <el-option value="1" label="挑战成功" />
              <el-option value="2" label="挑战失败" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否开启训练营：" prop="isOpen">
            <el-select
              v-model="form.isOpen"
              placeholder="请选择"
              clearable
              @change="isOpenChange"
            >
              <el-option value="1" label="开启" />
              <el-option value="0" label="未开启" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否领取通关礼品：" prop="giftReceiveFlag">
            <el-select
              v-model="form.giftReceiveFlag"
              placeholder="请选择"
              clearable
            >
              <el-option value="1" label="是" />
              <el-option value="0" label="否" />
            </el-select>
          </el-form-item>
          <el-form-item label="跟进部门：" prop="empDpId">
            <el-select
              v-model="form.empDpId"
              clearable
              filterable
              remote
              placeholder="请输入关键词"
              :loading="dpLoading"
              :filter-method="getDpList"
            >
              <el-option
                v-for="item in dpList"
                :key="item.dpId"
                :label="item.dpName"
                :value="item.dpId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="跟进人：" prop="empId">
            <el-select
              v-model="form.empId"
              clearable
              filterable
              remote
              placeholder="请输入关键词"
              :loading="empLoading"
              :filter-method="getEmpList"
            >
              <el-option
                v-for="item in empList"
                :key="item.empId"
                :label="item.empName"
                :value="item.empId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="班主任" prop="tutorName">
            <el-input v-model="form.tutorName" placeholder="请输入" clearable />
          </el-form-item>

          <div class="search-reset-box">
            <el-button
              type="primary"
              icon="el-icon-search"
              native-type="submit"
              size="mini"
            >搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="search(0)" />
          </div>
        </el-form>
      </open-packup>
      <!-- 按钮区 -->
      <div class="yz-table-btnbox">
        <el-button
          type="primary"
          size="small"
          @click="batchPushNotOpenCamp"
        >提醒未开启学员</el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleExport"
        >导出选中</el-button>
        <el-button
          type="primary"
          size="small"
          @click="handleExportAll"
        >导出全部</el-button>
      </div>

      <!-- 表格 -->
      <el-table
        ref="multipleTable"
        v-loading="loading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name="table-cell-header"
        :data="tableData"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection" align="center" />
        <el-table-column type="index" label="序号" align="center" />
        <el-table-column label="训练营类型" align="center">
          <template slot-scope="scope">
            {{ scope.row.recruitType | recruitTypeMethod }}
          </template>
        </el-table-column>

        <el-table-column
          prop="trainCampName"
          label="训练营名称"
          align="center"
        />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="stdName" label="学员姓名" align="center" />
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="pfsnName" label="院校专业" align="center" />
        <el-table-column
          prop="examinationName"
          label="自考考期"
          align="center"
        />
        <el-table-column
          prop="startAddTcTime"
          label="添加训练营时间"
          align="center"
        />
        <el-table-column prop="dataSource" label="数据来源" align="center">
          <template slot-scope="scope">
            {{ scope.row.dataSource == 1 ? "自动导入" : "批量导入" }}
          </template>
        </el-table-column>
        <el-table-column label="是否开启训练营" align="center">
          <template slot-scope="scope">
            {{ scope.row.isOpen == 1 ? "开启" : "未开启" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="trainCampOpenTime"
          label="训练营开启时间"
          align="center"
        />
        <el-table-column
          prop="trainCampEndTime"
          label="训练营关闭时间"
          align="center"
        />
        <el-table-column prop="barrierNum" label="关卡总数" align="center">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="
                $router.push({
                  path: `/students-info?id=${$route.query.id}&userId=${scope.row.userId}&only=1`,
                })
              "
            >{{ scope.row.barrierNum }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="completedBarrierNum"
          label="已完成关卡"
          align="center"
        />
        <el-table-column
          prop="giftReceiveFlag"
          label="是否领取通关礼品"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.row.giftReceiveFlag == 1 ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column label="挑战状态" align="center">
          <template slot-scope="scope">
            {{
              scope.row.isSuccess == 1
                ? "挑战成功"
                : scope.row.trainCampEndTime === null
                  ? ""
                  : scope.row.isSuccess == 0 &&
                    Date.parse(new Date()) >
                    Date.parse(scope.row.trainCampEndTime)
                    ? "挑战失败"
                    : "挑战中"
            }}
          </template>
        </el-table-column>
        <el-table-column prop="tutorName" label="班主任" align="center" />
        <el-table-column prop="empName" label="跟进人" align="center" />
        <el-table-column prop="empDpName" label="跟进部门" align="center" />
      </el-table>
      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total="pagination.total"
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { SplicingParams } from '@/utils';
import { downUri } from '@/config/request';
import { api } from '@/api';

export default {
  data() {
    return {
      loading: false,
      form: {},
      tableData: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      timeValue: '',
      timeValue2: '',
      timeValue3: '',
      unvSList: [],
      unvSLoading: false,
      majorList: [],
      examList: [],
      examLoading: false,
      multipleSelection: [],
      isOpenDisable: false,
      dpList: [],
      dpLoading: false,
      empList: [],
      empLoading: false
    };
  },
  created() {
    this.getTableList();
  },
  methods: {
    // 查询
    search(type) {
      if (type === 0) {
        this.timeValue = '';
        this.timeValue2 = '';
        this.timeValue3 = '';
        this.isOpenDisable = false;
        this.form = {};
      }
      this.pagination.page = 1;
      this.getTableList();
    },
    // 获取列表数据
    getTableList() {
      this.loading = true;
      const data = {
        ...this.form,
        trainCampId: this.$route.query.id,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
      };
      this.$post('checkedList', data)
        .then((res) => {
          if (res.ok) {
            this.tableData = res.body.data;
            this.pagination.total = res.body.recordsTotal;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    selectionChange(selection) {
      this.multipleSelection = selection;
    },
    // 导出选中数据
    handleExport() {
      if (this.multipleSelection.length === 0) {
        this.$message.warning('请选中导出数据');
        return false;
      }
      const data = {
        userIds: this.multipleSelection.map((v) => v.userId).join(),
        trainCampId: this.$route.query.id
      };
      const url = downUri + api['exportCheck'] + '?' + SplicingParams(data);
      window.open(url, '_blank');
      this.$refs.multipleTable.clearSelection();
    },
    // 导出全部
    handleExportAll() {
      const data = {
        ...this.form,
        trainCampId: this.$route.query.id
      };
      const url = downUri + api['exportAll'] + '?' + SplicingParams(data);
      window.open(url, '_blank');
    },
    // 筛选时间变化
    changeTime(e, num) {
      if (num === 1) {
        this.form.startAddTcTime = e[0] ?? '';
        this.form.endAddTcTime = e[1] ?? '';
      } else if (num === 2) {
        this.form.startTcTime = e[0] ?? '';
        this.form.startTcTimeEnd = e[1] ?? '';
      } else {
        this.form.endTcTimeStart = e[0] ?? '';
        this.form.endTcTime = e[1] ?? '';
      }
    },
    // 获取院校下拉数据
    getUnvSList(sName) {
      if (!sName) return;
      this.unvSLoading = true;
      this.$http({
        method: 'get',
        url: '/bdUniversity/findAllKeyValue.do',
        params: { page: 1, rows: 7, sName }
      })
        .then((res) => {
          if (res.ok) {
            this.unvSList = res.body.data;
          }
        })
        .finally(() => {
          this.unvSLoading = false;
        });
    },
    // 获取部门数据
    getDpList(sName) {
      if (!sName) return;
      this.dpLoading = true;
      this.$http({
        method: 'get',
        url: '/dep/getDpList.do',
        params: { page: 1, rows: 7, sName }
      })
        .then((res) => {
          if (res.ok) {
            this.dpList = res.body.data;
          }
        })
        .finally(() => {
          this.dpLoading = false;
        });
    },
    // 获取跟进人数据
    getEmpList(sName) {
      if (!sName) return;
      this.empLoading = true;
      this.$http({
        method: 'get',
        url: '/employ/getEmpList.do',
        params: { page: 1, rows: 7, sName }
      })
        .then((res) => {
          if (res.ok) {
            this.empList = res.body.data;
          }
        })
        .finally(() => {
          this.empLoading = false;
        });
    },
    // 获取专业数据
    getMajorList(sName) {
      delete this.form.pfsnId;
      if (!sName) return;
      this.$http({
        method: 'get',
        url: '/baseinfo/sPfsn.do',
        params: { page: 1, rows: 500, sName }
      }).then((res) => {
        if (res.ok) {
          this.majorList = res.body.data;
        }
      });
    },
    // 获取自考考期数据
    getExaminationList(key) {
      if (!key) return;
      this.examLoading = true;
      this.$http({
        method: 'post',
        url: '/zkExamination/findAllEffectiveExaminationList',
        params: {
          start: 0,
          length: 7,
          examinationName: key
        }
      })
        .then((res) => {
          if (res.ok) {
            this.examList = res.body.data;
          }
        })
        .finally(() => {
          this.examLoading = false;
        });
    },
    // 提醒未开启学员
    batchPushNotOpenCamp() {
      this.$http({
        method: 'post',
        url: '/tcTargetStudent/batchPushNotOpenCamp',
        params: { trainCampId: this.$route.query.id }
      }).then((res) => {
        if (res.ok) {
          this.$message.success('提醒成功');
        }
      });
    },
    // 挑战状态变化
    isSuccessChange(e) {
      if (e) {
        this.form.isOpen = '1';
      }
    },
    // 是否开启训练营变化
    isOpenChange(e) {
      if (e === '0') {
        this.isOpenDisable = true;
        delete this.form.isSuccess;
      } else {
        this.isOpenDisable = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.checkbox-css {
  margin: 40px 0 30px;
}
::v-deep .el-checkbox {
  margin-right: 23px;
}
.back {
  margin: 7px 15px 0 10px;
  color: #808c95;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 20px;
}
</style>
