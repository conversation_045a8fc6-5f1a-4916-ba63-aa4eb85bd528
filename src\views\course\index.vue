<template>
  <div class="yz-base-container">
    <el-tabs
      v-model="activeName"
      type="border-card"
      @tab-click="handleClick"
    >
      <el-tab-pane label="课程管理" name="course">
        <course />
      </el-tab-pane>
      <el-tab-pane label="课时管理" name="class" lazy>
        <class-pane />
      </el-tab-pane>
      <el-tab-pane label="试看管理" name="haveTry" lazy>
        <haveTry />
      </el-tab-pane>
      <el-tab-pane label="频道号管理" name="channel" lazy>
        <channel />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import course from './course-pane';
import classPane from './class-pane';
import channel from './channel-pane';
import haveTry from './have-try-pane';
export default {
  components: {
    course,
    classPane,
    channel,
    haveTry
  },
  data() {
    return {
      activeName: 'course'
    };
  },
  methods: {
    handleClick() {
    }
  }
};
</script>
<style lang="scss" scoped>
.yz-base-container {
  .el-tabs--border-card {
    box-shadow: none;
  }
}
</style>
