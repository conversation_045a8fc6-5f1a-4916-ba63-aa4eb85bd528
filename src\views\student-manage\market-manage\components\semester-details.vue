<template>
  <common-dialog
    is-full
    width="1000px"
    :title="title"
    :visible.sync='show'
    @open="open"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="realName" label="学员姓名" align="center" />
        <el-table-column prop="createTime" label="开始时间" align="center" />
        <el-table-column prop="learnStatus" label="学习状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.learnStatus | learnStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="learnCount" label="学习打卡" align="center">
          <template slot-scope="scope">
            {{ `${scope.row.learnCount}天` }}
          </template>
        </el-table-column>
        <el-table-column prop="lackCardCount" label="缺卡情况" align="center">
          <template slot-scope="scope">
            {{ `${scope.row.lackCardCount}次` }}
          </template>
        </el-table-column>
        <el-table-column prop="patchCardCount" label="剩余补卡" align="center">
          <template slot-scope="scope">
            {{ `${scope.row.patchCardCount}次` }}
          </template>
        </el-table-column>
        <el-table-column prop="runningStatus" label="跑步状态" align="center">
          <template slot-scope="scope">
            {{ scope.row.runningStatus | runningStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="runningCount" label="跑步打卡" align="center">
          <template slot-scope="scope">
            {{ `${scope.row.runningCount}次` }}
          </template>
        </el-table-column>
        <el-table-column prop="runningRemainDays" label="剩余天数" align="center">
          <template slot-scope="scope">
            {{ `${scope.row.runningRemainDays}天` }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </common-dialog>
</template>

<script>
export default {
  filters: {
    learnStatus(value) {
      if (!value) return;
      const data = {
        1: '进行中',
        2: '通过',
        3: '失败'
      };
      return data[value];
    },
    runningStatus(value) {
      if (!value) return;
      const data = {
        1: '进行中',
        2: '通过',
        3: '失败'
      };
      return data[value];
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [
        /* 模拟数据 */
        // {
        //   realName: '张学友',
        //   challengeTime: '2021-12-15 14:20:32',
        //   learnStatus: '进行中',
        //   learnCount: '19天',
        //   lackCardCount: '2次',
        //   patchCardCount: '5次',
        //   runningStatus: '进行中',
        //   runningCount: '20次',
        //   runningRemainDays: '9天'
        // }
      ]
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  methods: {
    open() {
      const { userId, challengeId, semesterId } = this.row;
      this.tableLoading = true;
      this.$http({
        method: 'get',
        url: '/scholarshipChallenge/getSemesterInfo',
        params: { userId, challengeId, semesterId }
      })
        .then(res => {
          const { fail, body } = res;
          if (!fail) {
            this.tableData = [body];
            this.tableLoading = false;
          }
        });
    },
    close() {
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>

</style>
