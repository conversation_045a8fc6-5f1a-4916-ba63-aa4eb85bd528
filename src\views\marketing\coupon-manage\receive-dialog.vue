<template>
  <common-dialog
    :show-footer="true"
    is-full
    title="领取详情"
    :visible.sync='show'
    @open="open"
    @confirm="submit"
    @close='close'
  >
    <div class="dialog-main">
      <!-- 表单 -->
      <el-form
        ref='searchForm'
        class='yz-search-form'
        size='mini'
        :model='form'
        label-width='120px'
        @submit.native.prevent='search'
      >
        <el-form-item label='学业编码' prop='learnId'>
          <el-input v-model="form.learnId" placeholder="请输入学业编码" />
        </el-form-item>
        <el-form-item label='远智编码' prop='yzCode'>
          <el-input v-model="form.yzCode" placeholder="请输入远智编码" />
        </el-form-item>
        <el-form-item label='用户姓名' prop='realName'>
          <el-input v-model="form.realName" placeholder="请输入用户姓名" />
        </el-form-item>
        <el-form-item label='手机号' prop='mobile'>
          <el-input v-model="form.mobile" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label='发放人' prop='sharerUserName'>
          <el-input v-model="form.sharerUserName" placeholder="请输入发放人" />
        </el-form-item>
        <el-form-item label="是否已使用" prop="enable">
          <el-select v-model="form.enable" clearable placeholder="请选择使用状态">
            <el-option label="是" value="1" />
            <el-option label="否" value="2" />
          </el-select>
        </el-form-item>

        <div class="search-reset-box">
          <el-button type="primary" icon='el-icon-search' native-type="submit" size="mini">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click='search(0)' />
        </div>

      </el-form>

      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        border
        size="small"
        style="width: 100%"
        header-cell-class-name='table-cell-header'
        :data="tableData"
      >
        <el-table-column prop="learnId" label="学业编码" align="center" />
        <el-table-column prop="yzCode" label="远智编码" align="center" />
        <el-table-column prop="realName" label="用户" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="receiveDate" label="领取时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.receiveTime | changeTime }}
          </template>
        </el-table-column>
        <el-table-column prop="sharerUserName" label="发放人" align="center" />
        <el-table-column prop="useDate" label="使用时间" align="center">
          <template slot-scope="scope">
            {{ scope.row.usageTime | changeTime }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区 -->
      <div class="yz-table-pagination">
        <pagination
          :total='pagination.total'
          :page.sync="pagination.page"
          :limit.sync="pagination.limit"
          @pagination="getTableList"
        />
      </div>

    </div>
  </common-dialog>
</template>

<script>
import { formatTimeStamp } from '@/utils';
export default {
  filters: {
    changeTime(val) {
      return formatTimeStamp(val, 'YYYY-MM-DD HH:mm:ss');
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      show: false,
      tableLoading: false,
      tableData: [],
      form: {
        mobile: '',
        realName: '',
        enable: '',
        sharerUserName: '',
        yzCode: '',
        learnId: ''
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      }
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    search(type) {
      if (type === 0) {
        this.$refs['searchForm'].resetFields();
      } else {
        this.pagination.page = 1;
        this.getTableList();
      }
    },
    getTableList() {
      const formData = JSON.parse(JSON.stringify(this.form));
      const data = {
        ...formData,
        start: (this.pagination.page - 1) * this.pagination.limit,
        length: this.pagination.limit
        // rollChildId: this.id
      };

      let url = '';
      // 1 优惠券领取详情
      // 2 子活动领取详情
      if (this.type === '1') {
        url = 'getCouponReceiveList';
        data['couponId'] = this.id;
      } else if (this.type === '2') {
        url = 'getReceiveDetailList';
        data['rollChildId'] = this.id;
      }
      //
      this.$post(url, data).then(res => {
        const { fail, body } = res;
        if (!fail) {
          this.tableData = body.data;
          this.pagination.total = Number(body.recordsTotal);
        }
      });
    },
    open() {
      this.getTableList();
    },
    submit() {
      this.$emit('refresh-list', true);
    },
    close() {
      this.$emit('refresh-list', true);
      Object.assign(this.$data, this.$options.data());
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang='scss' scoped>
.yz-search-form{
  margin-bottom: 20px;
}
</style>
