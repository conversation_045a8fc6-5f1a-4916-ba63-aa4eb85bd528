<template>
  <common-dialog
    :show-footer="true"
    width="800px"
    confirmText="开始导入"
    title="批量导入物流单号"
    :visible.sync="show"
    @confirm="submit"
    @close="close"
  >
    <div class="dialog-main">
      <el-form ref="searchForm" size="mini" :model="form" label-width="120px">
        <el-form-item label="模板：">
          <a :href="templateUrl" download>
            <el-button type="primary" plain>下载模板</el-button>
          </a>
        </el-form-item>

        <el-form-item label="选择文件：">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            :action="action"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :on-success="uploadSuccess"
            :name="field"
            :file-list="fileList"
            multiple
            :limit="1"
            :auto-upload="false"
          >
            <i class="el-icon-upload" />
            <div class="el-upload__text">将文件拖到此处，或<em>点击选择文件</em></div>
          </el-upload>
        </el-form-item>
      </el-form>

      <!-- 导入失败提示区域 -->
      <div v-if="importError" class="import-error-section">
        <div class="error-message">
          <i class="el-icon-warning" style="color: #F56C6C; margin-right: 8px;" />
          导入表格有误，请核对修改后再重新导入
        </div>
        <el-button
          type="primary"
          size="small"
          :disabled="!failedFileUrl"
          @click="downloadFailedFile"
        >
          下载导入失败表单
        </el-button>
      </div>
    </div>
  </common-dialog>
</template>

<script>
import { downUri } from '@/config/request';
import FileSaver from 'file-saver';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      form: {},
      fileList: [],
      field: 'multipartFile',
      templateUrl: downUri + '/excel/LogisticsTemplate.xlsx',
      action: '/giftGivingOrder/batchImportLogistics',
      importError: false,
      failedFileUrl: ''
    };
  },
  watch: {
    visible(val) {
      this.show = val;
    }
  },
  mounted() {},
  methods: {
    submit() {
      console.log(this.fileList);
      if (this.fileList.length > 0) {
        this.$confirm('确认批量导入物流单号？导入后将更新对应订单的物流信息，请谨慎操作！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$refs.upload.submit();
        });
      } else {
        this.$message.error('请选择需要导入的文件');
      }
    },
    handleChange(file, fileList) {
      this.fileList = fileList;
    },
    uploadSuccess(response, file, fileList) {
      this.$refs.upload.clearFiles();
      if (response.code === '00') {
        // 导入成功，隐藏错误提示
        this.importError = false;
        this.failedFileUrl = '';
        this.$alert(response.body, '提示', {
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.show = false;
          this.$emit('refresh');
        });
      } else {
        // 导入失败，显示错误提示
        this.importError = true;
        // 如果后端返回了失败文件的下载链接，保存它
        if (response.failedFileUrl) {
          this.failedFileUrl = response.failedFileUrl;
        }
        if (response.msg) {
          this.$message.error(response.msg);
        }
      }
    },
    handleExceed() {
      this.$message.error('每次只能上传一个文件');
    },
    // 下载失败文件
    downloadFailedFile() {
      if (this.failedFileUrl) {
        FileSaver.saveAs(this.failedFileUrl);
      } else {
        this.$message.error('暂无失败文件可下载');
      }
    },
    close() {
      // 关闭时重置错误状态
      this.importError = false;
      this.failedFileUrl = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-main {
  padding: 20px;
}

.import-error-section {
  margin-top: 20px;
  padding: 16px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;

  .error-message {
    margin-bottom: 12px;
    color: #f56c6c;
    font-size: 14px;
    display: flex;
    align-items: center;
  }
}
</style>
