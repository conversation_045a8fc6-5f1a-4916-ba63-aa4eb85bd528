<template>
  <common-dialog class="addstag" title="新增测评标签" width="50%" :showFooter="true" :visible.sync="visible" @close="onCloseBtn(null)" @confirm="submit">
    <el-form ref="addtagForm" class="addstag-main" size="mini" :model="addtag" :rules="addtagRule" label-width="120px">
      <el-form-item class="search-item" label="标签名称：" prop="tagInfoName">
        <el-select
          v-model="addtag.tagInfoName"
          filterable
          placeholder="请输入"
          :remote="true"
          clearable
          :loading="tagLoading"
          :remote-method="tagsRemote"
        >
          <el-option v-for="item in tagList" :key="item.tagInfoId" :label="item.tagInfoName" :value="item.tagInfoName" />
        </el-select>
      </el-form-item>
    </el-form>
  </common-dialog>
</template>

<script>
export default {
  props: {
    visible: { type: Boolean, default: false },
    showDefault: { type: Boolean, default: true },
    payNo: { type: [String, Number, Object], default: null },
    userName: { type: [String, Number, Object], default: null }
  },
  data() {
    return {
      tagLoading: false,
      tagList: [],
      addtag: {
        tagInfoName: '',
        remark: '',
        sort: ''
      },
      addtagRule: {
        title: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    };
  },
  watch: {
    'addtag.tagInfoName'(names) {
      const obs = this.tagList?.find(item => item.tagInfoName === names);
      console.log('watch-tagInfoName', names, obs);
      this.addtag.sort = obs?.sort || '';
      this.addtag.remark = obs?.remark || '';
      this.addtag.tagInfoId = obs?.tagInfoId || '';
    }
  },
  methods: {
    // 输入时查询
    tagsRemote(vas) {
      if (!vas) {
        this.$message({ message: '请输入测评标签名称', type: 'error' });
        return;
      }
      if (vas && vas.length >= 20) {
        this.$message({ message: '测评标签名称限20个汉字以内', type: 'error' });
        return;
      }
      this.addtag.tagInfoName = vas;
      console.log('输入时查询-addtag', this.addtag);
      this.tagLoading = true;
      this.$post('queryTagInfoBySearch', { search: vas }).then((res) => {
        const { code, body } = res;
        console.log('输入时查询-body', body);
        if (code === '00') {
          this.tagList = body || [];
        }
        this.tagLoading = false;
      }).catch((err) => {
        console.log('输入时查询-err', err);
        this.tagLoading = false;
      });
    },
    // 提交表单
    submit() {
      console.log('提交表单', this.addtag);
      this.$refs['addtagForm'].validate((valid) => {
        if (valid) {
          this.tagLoading = true;
          const obs = this.tagList?.find(item => item.tagInfoName === this.addtag.tagInfoName);
          console.log('提交表单-obs', obs);
          if (obs) {
            this.$message.success('提交成功');
            this.onCloseBtn({
              tagInfoId: obs.tagInfoId,
              tagInfoName: obs.tagInfoName
            });
          } else {
            this.$post('evalAddTagInfo', this.addtag, { json: true }).then((res) => {
              const { code, body } = res;
              console.log('输入时查询-body', body);
              if (code === '00' && body) {
                this.$message.success('提交成功');
                this.onCloseBtn({
                  tagInfoId: body,
                  tagInfoName: this.addtag.tagInfoName
                });
              }
              this.tagLoading = false;
            }).catch((err) => {
              console.log('输入时查询-err', err);
              this.tagLoading = false;
            });
          }
        }
      });
    },
    // 关闭当前标签弹窗
    onCloseBtn(obs = '') {
      console.log('关闭当前标签弹窗-obs', obs);
      if (this.visible) {
        this.$emit('on-close', obs);
        setTimeout(() => {
          this.tagList = [];
          this.addtag = {
            tagInfoName: '',
            remark: '',
            sort: ''
          };
        }, 10);
      }
    }
  }
};
</script>

<style lang="scss">
.addstag {
  &-main {
    margin: 30px;
    height: 280px;
  }
  &-main::-webkit-scrollbar{
    display: none;
  }
  .yz-common-dialog__footer {
    text-align: center !important;
  }
  .eval-inputs {
    width: 100%;
    .el-input__inner{
      text-align: left;
    }
  }
}
</style>

